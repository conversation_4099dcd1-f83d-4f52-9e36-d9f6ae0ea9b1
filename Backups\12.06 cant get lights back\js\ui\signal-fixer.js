// signal-fixer.js - Ultimate fix for indicator signal display issues
// This script runs last and fixes all recursion and indicator row problems

// Define required display constants
const INDICATOR_DISPLAY_NAMES = {
  rsi: 'RSI',
  stochRsi: 'STOCH RSI',
  williamsR: 'WILLIAMS %R',
  ultimateOscillator: 'ULT OSC',
  mfi: 'MFI',
  macd: 'MACD',
  bollingerBands: 'BOLL BANDS',
  adx: 'ADX',
  atr: 'ATR',
  vwap: 'VWAP',
  fractal: 'FRACTAL',
  volume: 'VOLUME',
  ml: 'ML',
  sentiment: 'SENTIMENT',
  entropy: 'ENTROPY',
  correlation: 'CORRELATION',
  time_anomaly: 'TIME ANOMALY'
};

// Global flags to prevent recursion
let isUpdatingSignals = false;
let isRebuilding = false;

// Prevent execution until DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('[SignalFixer] Installing universal signal fix...');
  
  // Wait to ensure all other scripts have had time to initialize
  setTimeout(installFixes, 1500);
});

// Install all fixes
function installFixes() {
  console.log('[SignalFixer] Installing all fixes...');
  
  // Wait a bit to ensure other scripts are loaded
  setTimeout(() => {
    // Fix updateAllSignalLights function
    fixUpdateAllSignalLights();
    
    // Setup consistent indicator row creation
    setupIndicatorRowCreation();
    
    // Setup mutation observer to monitor DOM changes
    setupMutationObserver();
    
    // Initialize signal lights if the function exists
    if (typeof window.initializeSignalLights === 'function') {
      window.initializeSignalLights();
    } else {
      console.warn('[SignalFixer] initializeSignalLights function not found');
    }
    
    console.log('[SignalFixer] All fixes applied successfully');
  }, 100);
}

// Fix updateAllSignalLights function
function fixUpdateAllSignalLights() {
  console.log('[SignalFixer] Enhancing updateAllSignalLights function...');
  
  // Store the original function
  const originalUpdateAllSignalLights = window.updateAllSignalLights;
  
  // Create an enhanced version that works with the optimized implementation
  window.updateAllSignalLights = function() {
    try {
      // Call the original function
      if (typeof originalUpdateAllSignalLights === 'function') {
        return originalUpdateAllSignalLights.apply(this, arguments);
      }
      
      // Fallback if original is not available
      console.warn('[SignalFixer] Original updateAllSignalLights not found, using fallback');
      
      // Simple fallback implementation
      const circles = document.querySelectorAll('.signal-circle');
      circles.forEach(circle => {
        if (!circle.innerText || circle.innerText.trim() === '') {
          const timeframe = circle.getAttribute('data-tf') || circle.getAttribute('data-timeframe') || '';
          if (timeframe) {
            circle.innerText = timeframe;
          }
        }
      });
    } catch (err) {
      console.error('[SignalFixer] Error in updateAllSignalLights:', err);
    }
  };
  
  // Copy any properties from the original function
  if (originalUpdateAllSignalLights) {
    Object.keys(originalUpdateAllSignalLights).forEach(key => {
      window.updateAllSignalLights[key] = originalUpdateAllSignalLights[key];
    });
  }
}

// Setup consistent indicator row creation
function setupIndicatorRowCreation() {
  console.log('[SignalFixer] Setting up consistent indicator row creation...');
  
  // Create a consistent indicator row function
  window.createConsistentIndicatorRow = function(indicator) {
    try {
      if (!indicator) {
        console.error('[SignalFixer] Cannot create row for undefined indicator');
        return document.createElement('tr');
      }
      
      console.log(`[SignalFixer] Creating consistent row for ${indicator}`);
      
      // Create row with proper data attributes
      const row = document.createElement('tr');
      row.className = 'indicator-row signal-row';
      row.setAttribute('data-indicator', indicator);
      row.setAttribute('data-ind', indicator);
      row.id = `indicator-row-${indicator}`;
      
      // Create name cell
      const nameCell = document.createElement('td');
      nameCell.className = 'signal-name indicator-name';
      nameCell.setAttribute('data-indicator', indicator);
      
      // Use proper display name
      const displayName = INDICATOR_DISPLAY_NAMES[indicator] || indicator.toUpperCase();
      nameCell.textContent = displayName;
      
      row.appendChild(nameCell);
      
      // Create mini-chart cell if needed
      const chartCell = document.createElement('td');
      chartCell.className = 'mini-chart-cell';
      const chartContainer = document.createElement('div');
      chartContainer.className = 'mini-chart-container';
      chartContainer.id = `${indicator}-chart-container`;
      chartCell.appendChild(chartContainer);
      row.appendChild(chartCell);
      
      // Create signal cells for each timeframe
      if (window.TIMEFRAMES && Array.isArray(window.TIMEFRAMES)) {
        window.TIMEFRAMES.forEach(tf => {
          const signalCell = document.createElement('td');
          signalCell.className = 'signal-light-cell';
          signalCell.setAttribute('data-timeframe', tf);
          
          // Create signal circle
          const signalCircle = document.createElement('div');
          signalCircle.className = 'signal-circle grey-light';
          signalCircle.id = `${indicator}-${tf}-signal`;
          signalCircle.setAttribute('data-ind', indicator);
          signalCircle.setAttribute('data-tf', tf);
          signalCircle.setAttribute('data-indicator', indicator);
          signalCircle.setAttribute('data-timeframe', tf);
          signalCircle.setAttribute('data-tooltip', `${displayName} (${tf}): No data`);
          signalCircle.innerText = tf;
          
          signalCell.appendChild(signalCircle);
          row.appendChild(signalCell);
        });
      }
      
      return row;
    } catch (err) {
      console.error(`[SignalFixer] Error creating row for ${indicator}:`, err);
      return document.createElement('tr'); // Return empty row to prevent errors
    }
  };
  
  // Override createIndicatorRow if it exists
  if (typeof window.createIndicatorRow !== 'undefined') {
    console.log('[SignalFixer] Overriding existing createIndicatorRow function');
    window.createIndicatorRow = window.createConsistentIndicatorRow;
  }
}

// Setup mutation observer to monitor DOM changes
function setupMutationObserver() {
  console.log('[SignalFixer] Setting up mutation observer...');
  
  const observer = new MutationObserver(mutations => {
    let needsRebuild = false;
    
    for (const mutation of mutations) {
      if (mutation.type === 'childList' && 
          (mutation.target.classList.contains('oracle-matrix') ||
           mutation.target.classList.contains('momentum-table'))) {
        needsRebuild = true;
        break;
      }
    }
    
    if (needsRebuild && !isRebuilding) {
      console.log('[SignalFixer] Detected changes to indicator table, scheduling rebuild...');
      setTimeout(() => {
        if (typeof window.rebuildAllIndicatorRows === 'function') {
          window.rebuildAllIndicatorRows();
        }
      }, 100);
    }
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

console.log('[SignalFixer] Script loaded successfully');
