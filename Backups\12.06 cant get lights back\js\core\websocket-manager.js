/**
 * Military-grade WebSocket manager with self-healing capabilities
 */
class WebSocketManager {
    constructor(options = {}) {
        this.config = {
            url: options.url || `ws://${window.location.host}/ws`,
            protocols: options.protocols || [],
            maxReconnectAttempts: options.maxReconnectAttempts || 10,
            initialReconnectDelay: options.initialReconnectDelay || 1000,
            maxReconnectDelay: options.maxReconnectDelay || 30000,
            pingInterval: options.pingInterval || 30000,
            pingTimeout: options.pingTimeout || 10000,
            messageQueueSize: options.messageQueueSize || 1000,
            ...options
        };

        this.state = {
            isConnected: false,
            isReconnecting: false,
            reconnectAttempts: 0,
            lastMessageTime: 0,
            messageQueue: [],
            pingTimer: null,
            reconnectTimer: null
        };

        this.callbacks = {
            onOpen: [],
            onClose: [],
            onError: [],
            onMessage: [],
            onReconnect: []
        };

        this.init();
    }

    init() {
        CoreUtils.log('info', 'WebSocketManager initializing...');
        this.connect();
        this.setupPing();
        this.setupEventListeners();
    }

    connect() {
        try {
            if (this.state.ws) {
                this.state.ws.close();
            }

            CoreUtils.log('info', 'Connecting to WebSocket...', { url: this.config.url });
            this.state.ws = new WebSocket(this.config.url, this.config.protocols);

            this.state.ws.onopen = () => this.handleOpen();
            this.state.ws.onclose = (event) => this.handleClose(event);
            this.state.ws.onerror = (error) => this.handleError(error);
            this.state.ws.onmessage = (event) => this.handleMessage(event);

        } catch (error) {
            CoreUtils.log('error', 'WebSocket connection error', error);
            this.handleError(error);
        }
    }

    handleOpen() {
        CoreUtils.log('info', 'WebSocket connected');
        this.state.isConnected = true;
        this.state.reconnectAttempts = 0;
        this.state.isReconnecting = false;

        this.callbacks.onOpen.forEach(callback => callback());
    }

    handleClose(event) {
        CoreUtils.log('warn', 'WebSocket disconnected', { code: event.code, reason: event.reason });
        this.state.isConnected = false;

        if (event.code !== 1000 && this.config.autoReconnect) {
            this.reconnect();
        }

        this.callbacks.onClose.forEach(callback => callback(event));
    }

    handleError(error) {
        CoreUtils.log('error', 'WebSocket error', error);
        this.state.isConnected = false;
        this.callbacks.onError.forEach(callback => callback(error));
    }

    handleMessage(event) {
        try {
            const message = CoreUtils.safeParseJSON(event.data);
            if (!message) return;

            this.state.lastMessageTime = Date.now();
            this.callbacks.onMessage.forEach(callback => callback(message));

            // Process message queue if necessary
            this.processMessageQueue();
        } catch (error) {
            CoreUtils.log('error', 'Message processing error', error);
        }
    }

    reconnect() {
        if (this.state.isReconnecting || this.state.reconnectAttempts >= this.config.maxReconnectAttempts) {
            return;
        }

        this.state.isReconnecting = true;
        this.state.reconnectAttempts++;

        const delay = Math.min(
            this.config.initialReconnectDelay * Math.pow(2, this.state.reconnectAttempts - 1),
            this.config.maxReconnectDelay
        );

        CoreUtils.log('info', `Reconnecting in ${delay}ms... (attempt ${this.state.reconnectAttempts}/${this.config.maxReconnectAttempts})`);

        clearTimeout(this.state.reconnectTimer);
        this.state.reconnectTimer = setTimeout(() => {
            this.connect();
            this.callbacks.onReconnect.forEach(callback => callback());
        }, delay);
    }

    send(message) {
        if (!this.state.isConnected) {
            CoreUtils.log('warn', 'WebSocket not connected, queuing message');
            this.queueMessage(message);
            return;
        }

        try {
            this.state.ws.send(JSON.stringify(message));
        } catch (error) {
            CoreUtils.log('error', 'Send error, queuing message', error);
            this.queueMessage(message);
        }
    }

    queueMessage(message) {
        if (this.state.messageQueue.length >= this.config.messageQueueSize) {
            CoreUtils.log('warn', 'Message queue full, dropping oldest message');
            this.state.messageQueue.shift();
        }

        this.state.messageQueue.push(message);
    }

    processMessageQueue() {
        if (!this.state.isConnected) return;

        while (this.state.messageQueue.length > 0) {
            try {
                const message = this.state.messageQueue.shift();
                this.state.ws.send(JSON.stringify(message));
            } catch (error) {
                CoreUtils.log('error', 'Error sending queued message', error);
                this.state.messageQueue.unshift(message);
                break;
            }
        }
    }

    setupPing() {
        this.state.pingTimer = setInterval(() => {
            if (this.state.isConnected && Date.now() - this.state.lastMessageTime > this.config.pingInterval) {
                try {
                    this.state.ws.send(JSON.stringify({ type: 'ping' }));
                } catch (error) {
                    CoreUtils.log('error', 'Ping error', error);
                }
            }
        }, this.config.pingInterval);
    }

    setupEventListeners() {
        // Add event listeners for system events
        window.addEventListener('beforeunload', () => this.cleanup());
    }

    addEventListener(type, callback) {
        if (!this.callbacks[type]) {
            CoreUtils.log('warn', `Unknown event type: ${type}`);
            return;
        }

        if (typeof callback !== 'function') {
            CoreUtils.log('error', 'Callback must be a function');
            return;
        }

        this.callbacks[type].push(callback);
    }

    removeEventListener(type, callback) {
        if (!this.callbacks[type]) return;
        this.callbacks[type] = this.callbacks[type].filter(cb => cb !== callback);
    }

    cleanup() {
        CoreUtils.log('info', 'WebSocketManager cleaning up...');
        
        if (this.state.ws) {
            this.state.ws.close();
            this.state.ws = null;
        }

        if (this.state.pingTimer) {
            clearInterval(this.state.pingTimer);
            this.state.pingTimer = null;
        }

        if (this.state.reconnectTimer) {
            clearTimeout(this.state.reconnectTimer);
            this.state.reconnectTimer = null;
        }

        this.callbacks = {
            onOpen: [],
            onClose: [],
            onError: [],
            onMessage: [],
            onReconnect: []
        };

        this.state.isConnected = false;
        this.state.isReconnecting = false;
        this.state.reconnectAttempts = 0;
    }

    getStatus() {
        return {
            isConnected: this.state.isConnected,
            isReconnecting: this.state.isReconnecting,
            reconnectAttempts: this.state.reconnectAttempts,
            lastMessageTime: this.state.lastMessageTime,
            messageQueueLength: this.state.messageQueue.length,
            pingInterval: this.config.pingInterval,
            pingTimeout: this.config.pingTimeout
        };
    }
}

// Export for browser
if (typeof window !== 'undefined') {
    window.WebSocketManager = WebSocketManager;
}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebSocketManager;
}
