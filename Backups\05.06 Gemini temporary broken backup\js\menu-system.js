/**
 * Unified Menu System - Combines menu handling and strategy management
 */
class MenuSystem {
  constructor() {
    // Menu state
    this.activeMenu = null
    this.menus = {
      strategy: 'strategyMenu',
      indicator: 'indicatorMenu',
      thresholds: 'thresholdsMenu',
      logic: 'logicMenu',
    }

    // Strategy state
    this.strategies = window.TRADING_STRATEGIES || {}
    this.currentStrategy = null

    // Initialize event listeners
    this.setupEventListeners()
  }

  setupEventListeners() {
    // Menu button click handlers
    document.querySelectorAll('.menu-button').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault()
        e.stopPropagation()
        const menuId = button.getAttribute('id').replace('Button', '')
        this.toggleMenu(menuId)
      })
    })

    // Click outside handler
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.menu-content') && !e.target.closest('.menu-button')) {
        this.closeAllMenus()
      }
    })

    // Strategy change handler
    document.addEventListener('strategyChanged', (e) => {
      this.handleStrategyChange(e.detail.strategyId)
    })
  }

  initialize() {
    // Initialize strategy management
    this.initializeStrategies()

    // Load saved strategy if available
    this.loadSavedStrategy()

    console.log('[MenuSystem] Initialized')
  }

  setupMenuButtons() {
    // Strategy menu
    this.setupMenuButton('strategyButton', 'strategyMenu')

    // Indicator menu
    this.setupMenuButton('toggleMenuButton', 'indicatorMenu')

    // Thresholds menu
    this.setupMenuButton('toggleThresholdsButton', 'thresholdsMenu')

    // Logic menu
    this.setupMenuButton('toggleLogicButton', 'logicMenu')
  }

  setupMenuButton(buttonId, menuId) {
    const button = document.getElementById(buttonId)
    if (!button) return

    button.addEventListener('click', (e) => {
      e.preventDefault()
      e.stopPropagation()
      this.toggleMenu(menuId)
    })
  }

  toggleMenu(menuId) {
    try {
      const menu = document.getElementById(menuId)
      if (!menu) {
        console.warn(`[MenuSystem] Menu not found: ${menuId}`)
        return
      }

      // Close all other menus
      Object.values(this.menus).forEach(id => {
        if (id !== menuId) {
          const otherMenu = document.getElementById(id)
          if (otherMenu) {
            otherMenu.classList.remove('active')
          }
        }
      })

      // Toggle the clicked menu
      menu.classList.toggle('active')

      // Update active menu reference
      if (menu.classList.contains('active')) {
        this.activeMenu = menuId
      } else {
        this.activeMenu = null
      }

      console.log(`[MenuSystem] Toggled menu: ${menuId}, active: ${menu.classList.contains('active')}`)
    } catch (error) {
      console.error(`[MenuSystem] Error toggling menu ${menuId}:`, error)
    }
  }

  setupClickOutsideHandler() {
    document.addEventListener('click', (e) => {
      if (!this.activeMenu) return

      const menu = document.getElementById(this.activeMenu)
      if (!menu) return

      // Close menu if click is outside
      if (!menu.contains(e.target)) {
        menu.classList.remove('active')
        this.activeMenu = null
      }
    })
  }

  initializeStrategies() {
    // Ensure TRADING_STRATEGIES exists
    if (!this.strategies) {
      console.error('[MenuSystem] TRADING_STRATEGIES not defined')
      return
    }

    // Initialize all strategies
    Object.keys(this.strategies).forEach(key => {
      this.initializeStrategy(key)
    })

    // Load saved strategy or use default
    this.loadSavedStrategy()
  }

  initializeStrategy(key) {
    const strategy = this.strategies[key]

    // Ensure strategy has all required properties
    if (!strategy.name) {
      strategy.name = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }

    if (!strategy.description) {
      strategy.description = `Strategy based on ${strategy.indicators ? strategy.indicators.join(', ') : 'various indicators'}.`
    }

    if (!strategy.color) {
      strategy.color = '#4CAF50' // Default green
    }
  }

  loadSavedStrategy() {
    const savedStrategy = localStorage.getItem('currentStrategy') || 'admiral_toa'
    if (this.strategies[savedStrategy]) {
      this.currentStrategy = savedStrategy
    } else {
      console.warn(`Saved strategy ${savedStrategy} not found, using default`)
      this.currentStrategy = 'admiral_toa'
    }
  }

  setupEventListeners() {
    // Strategy change events
    document.addEventListener('strategyChanged', (e) => {
      this.handleStrategyChange(e.detail.strategyId)
    })

    // Menu button fix events
    document.addEventListener('click', (e) => {
      this.handleMenuClick(e)
    })
  }

  handleStrategyChange(strategyId) {
    if (!this.strategies[strategyId]) return

    this.currentStrategy = strategyId
    localStorage.setItem('currentStrategy', strategyId)

    // Update UI
    this.updateStrategyUI(strategyId)

    // Notify other components
    this.dispatchEvent('strategyUpdated', { strategyId })
  }

  updateStrategyUI(strategyId) {
    const strategy = this.strategies[strategyId]
    if (!strategy) return

    // Update strategy name
    const strategyName = document.getElementById('strategyName')
    if (strategyName) {
      strategyName.textContent = strategy.name
    }

    // Update strategy description
    const strategyDesc = document.getElementById('strategyDescription')
    if (strategyDesc) {
      strategyDesc.textContent = strategy.description
    }

    // Update indicators
    this.updateIndicatorsForStrategy(strategy)
  }

  updateIndicatorsForStrategy(strategy) {
    const indicatorContainer = document.getElementById('indicatorContainer')
    if (!indicatorContainer) return

    // Clear existing indicators
    indicatorContainer.innerHTML = ''

    // Add new indicators
    strategy.indicators?.forEach(indicator => {
      const indicatorElement = this.createIndicatorElement(indicator)
      indicatorContainer.appendChild(indicatorElement)
    })
  }

  createIndicatorElement(indicator) {
    const element = document.createElement('div')
    element.className = 'indicator-item'
    element.textContent = this.formatIndicatorName(indicator)
    return element
  }

  formatIndicatorName(indicator) {
    return indicator.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  handleMenuClick(event) {
    // Prevent menu interference with strategy selector
    if (event.target.closest('.strategy-selector')) {
      this.closeAllMenus()
    }
  }

  closeAllMenus() {
    Object.values(this.menus).forEach(menuId => {
      const menu = document.getElementById(menuId)
      if (menu) {
        menu.classList.remove('active')
      }
    })
    this.activeMenu = null
  }

  dispatchEvent(type, detail) {
    const event = new CustomEvent(`menuSystem:${type}`, {
      detail,
    })
    document.dispatchEvent(event)
  }
}

// Create and expose singleton instance
const menuSystem = new MenuSystem()
window.MenuSystem = menuSystem

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  menuSystem.initialize()
})
