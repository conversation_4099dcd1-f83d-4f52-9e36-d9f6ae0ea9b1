/**
 * StarCrypt Clean Indicator System
 *
 * This implementation fixes:
 * - Prevents [object Object] in the UI
 * - Ensures exactly 7 signal lights per indicator
 * - Uses consistent 5-color logic with neutral always gray
 * - Prevents memory leaks and duplicate handlers
 * - Optimizes render performance
 */

// Prevent duplicate initialization
if (window.indicatorSystemInitialized) {
  console.log('Indicator system already initialized, skipping')
} else {
  // Set up indicators when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing clean indicator system')
    initializeIndicatorSystem()
  })
}

// Constants
const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
const SIGNAL_COLORS = {
  strongBuy: '#00FF00', // Bright green
  mildBuy: '#00AAFF', // Light blue
  neutral: '#808080', // Medium gray
  mildSell: '#FFA500', // Orange
  strongSell: '#FF0000', // Bright red
}

// Clear any existing intervals to prevent memory leaks
function cleanupExistingHandlers() {
  if (window.indicatorUpdateInterval) {
    clearInterval(window.indicatorUpdateInterval)
    console.log('Cleared existing indicator update interval')
  }

  if (window.chartInstances) {
    Object.values(window.chartInstances).forEach(chart => {
      try {
        if (chart && typeof chart.destroy === 'function') {
          chart.destroy()
        }
      } catch (e) {
        // Ignore errors during cleanup
      }
    })
    console.log('Cleaned up existing chart instances')
  }

  // Reset global containers
  window.chartInstances = {}
}

// Initialize the entire indicator system
function initializeIndicatorSystem() {
  // Clean up any existing handlers to prevent duplicates
  cleanupExistingHandlers()

  // Create container for our chart instances
  window.chartInstances = {}

  // Get all indicators required by current strategy
  const indicators = getRequiredIndicators()

  // Set up indicator table rows
  setupIndicatorRows(indicators)

  // Create charts with small delay to ensure DOM is ready
  setTimeout(() => {
    setupCharts(indicators)

    // Start periodic updates
    window.indicatorUpdateInterval = setInterval(() => {
      updateAllIndicators(indicators)
    }, 5000)

    // Mark as initialized
    window.indicatorSystemInitialized = true

    // Initial update
    updateAllIndicators(indicators)

    console.log('Indicator system initialization complete')
  }, 1000)
}

// Get indicators required by current strategy
function getRequiredIndicators() {
  try {
    const strategy = window.currentStrategy || 'admiral_toa'
    const strategies = window.TRADING_STRATEGIES || {}

    if (strategies[strategy] && Array.isArray(strategies[strategy].indicators)) {
      return strategies[strategy].indicators
    }

    return []
  } catch (e) {
    console.error('Error getting strategy indicators:', e)
    return []
  }
}

// Set up indicator rows in the table
function setupIndicatorRows(indicators) {
  try {
    // Find the momentum table
    const table = document.querySelector('.momentum-table tbody')
    if (!table) {
      console.error('Momentum table not found')
      return
    }

    // Process each indicator
    indicators.forEach(indicator => {
      if (typeof indicator !== 'string') {
        console.error('Invalid indicator:', indicator)
        return
      }

      // Check if row already exists
      let row = table.querySelector(`tr[data-indicator="${indicator}"]`)

      // Create row if it doesn't exist
      if (!row) {
        row = document.createElement('tr')
        row.setAttribute('data-indicator', indicator)

        // Add indicator name cell
        const nameCell = document.createElement('td')
        nameCell.className = 'indicator-name'
        nameCell.textContent = indicator.toUpperCase()
        row.appendChild(nameCell)

        // Add chart cell
        const chartCell = document.createElement('td')
        chartCell.className = 'chart-cell'

        // Create chart container
        const chartContainer = document.createElement('div')
        chartContainer.className = 'chart-container'
        chartContainer.id = `${indicator}-chart-container`
        chartCell.appendChild(chartContainer)

        // Add signal lights cell
        const lightsCell = document.createElement('td')
        lightsCell.className = 'signal-lights-cell'

        // Create container for lights
        const lightsContainer = document.createElement('div')
        lightsContainer.className = 'signal-lights-container'
        lightsCell.appendChild(lightsContainer)

        // Create 7 lights for each timeframe
        TIMEFRAMES.forEach(timeframe => {
          const light = document.createElement('div')
          light.className = 'signal-light'
          light.id = `${indicator}-${timeframe}-signal`
          light.setAttribute('data-timeframe', timeframe)
          light.setAttribute('data-indicator', indicator)
          light.title = `${timeframe.toUpperCase()} Signal`
          light.style.backgroundColor = SIGNAL_COLORS.neutral
          lightsContainer.appendChild(light)
        })

        // Add cells to row
        row.appendChild(nameCell)
        row.appendChild(chartCell)
        row.appendChild(lightsCell)

        // Add row to table
        table.appendChild(row)
      } else {
        // Update existing row - make sure it has signal lights
        let lightsCell = row.querySelector('.signal-lights-cell')
        if (!lightsCell) {
          lightsCell = document.createElement('td')
          lightsCell.className = 'signal-lights-cell'

          const lightsContainer = document.createElement('div')
          lightsContainer.className = 'signal-lights-container'
          lightsCell.appendChild(lightsContainer)

          // Create lights for each timeframe
          TIMEFRAMES.forEach(timeframe => {
            const light = document.createElement('div')
            light.className = 'signal-light'
            light.id = `${indicator}-${timeframe}-signal`
            light.setAttribute('data-timeframe', timeframe)
            light.setAttribute('data-indicator', indicator)
            light.title = `${timeframe.toUpperCase()} Signal`
            light.style.backgroundColor = SIGNAL_COLORS.neutral
            lightsContainer.appendChild(light)
          })

          row.appendChild(lightsCell)
        }
      }
    })

    console.log(`Set up ${indicators.length} indicator rows`)
  } catch (e) {
    console.error('Error setting up indicator rows:', e)
  }
}

// Set up charts for all indicators
function setupCharts(indicators) {
  try {
    indicators.forEach((indicator, index) => {
      // Small delay between each chart creation to prevent rendering issues
      setTimeout(() => {
        createChart(indicator)
      }, index * 50)
    })
  } catch (e) {
    console.error('Error setting up charts:', e)
  }
}

// Create chart for a specific indicator
function createChart(indicator) {
  try {
    // Find container
    const container = document.getElementById(`${indicator}-chart-container`)
    if (!container) {
      console.warn(`Chart container not found for ${indicator}`)
      return
    }

    // Clear existing content
    container.innerHTML = ''

    // Create canvas element
    const canvas = document.createElement('canvas')
    canvas.id = `${indicator}-chart`
    canvas.height = 40
    container.appendChild(canvas)

    // Initialize with Chart.js
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.error(`Could not get 2D context for ${indicator} chart`)
      return
    }

    // Create chart with minimal configuration
    const chart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: Array(24).fill(''),
        datasets: [{
          label: indicator.toUpperCase(),
          data: Array(24).fill(50),
          borderColor: 'rgba(128, 128, 128, 0.8)',
          backgroundColor: 'rgba(128, 128, 128, 0.2)',
          borderWidth: 1,
          pointRadius: 0,
          pointHoverRadius: 2,
        }],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: false,
        plugins: {
          legend: { display: false },
          tooltip: { enabled: true },
        },
        scales: {
          x: { display: false },
          y: {
            display: false,
            min: 0,
            max: 100,
          },
        },
      },
    })

    // Store chart instance
    window.chartInstances[indicator] = chart

    console.log(`Created chart for ${indicator}`)
  } catch (e) {
    console.error(`Error creating chart for ${indicator}:`, e)
  }
}

// Update all indicators
function updateAllIndicators(indicators) {
  try {
    // Get current timeframe
    const currentTimeframe = window.currentTf || '1h'

    // First update current timeframe indicators for immediate feedback
    indicators.forEach(indicator => {
      updateIndicator(indicator, currentTimeframe)
    })

    // Then update other timeframes with small delays
    let delay = 0
    TIMEFRAMES.forEach(timeframe => {
      if (timeframe === currentTimeframe) return // Skip current timeframe

      delay += 50
      setTimeout(() => {
        indicators.forEach(indicator => {
          updateIndicator(indicator, timeframe)
        })
      }, delay)
    })
  } catch (e) {
    console.error('Error updating indicators:', e)
  }
}

// Update a specific indicator for a specific timeframe
function updateIndicator(indicator, timeframe) {
  try {
    // Get indicator data
    const data = getIndicatorData(indicator, timeframe)

    // Update signal light
    updateSignalLight(indicator, timeframe, data)

    // Update chart if it's the current timeframe
    if (timeframe === (window.currentTf || '1h')) {
      updateChart(indicator, data)
    }
  } catch (e) {
    console.error(`Error updating ${indicator} for ${timeframe}:`, e)
  }
}

// Get indicator data (either from server or simulated)
function getIndicatorData(indicator, timeframe) {
  try {
    // Try to use real server data if available
    if (window.indicatorData &&
        window.indicatorData[timeframe] &&
        window.indicatorData[timeframe][indicator]) {
      return window.indicatorData[timeframe][indicator]
    }

    // Otherwise simulate data
    // For better visual feedback, slightly randomize data
    const baseValue = 50 + (Math.sin(Date.now() / 1000000 + indicator.length) * 25)
    const random = Math.random() * 10 - 5
    const value = Math.max(0, Math.min(100, baseValue + random))

    // Determine signal based on value
    let signal; let strength
    if (value > 70) {
      signal = 'buy'
      strength = (value - 70) / 30
    } else if (value > 55) {
      signal = 'buy'
      strength = (value - 55) / 15
    } else if (value < 30) {
      signal = 'sell'
      strength = (30 - value) / 30
    } else if (value < 45) {
      signal = 'sell'
      strength = (45 - value) / 15
    } else {
      signal = 'neutral'
      strength = 0.5
    }

    return {
      value,
      signal,
      strength,
      change: random,
    }
  } catch (e) {
    console.error(`Error getting data for ${indicator}/${timeframe}:`, e)
    return {
      value: 50,
      signal: 'neutral',
      strength: 0,
      change: 0,
    }
  }
}

// Update signal light with 5-color logic
function updateSignalLight(indicator, timeframe, data) {
  try {
    // Find the signal light
    const light = document.getElementById(`${indicator}-${timeframe}-signal`)
    if (!light) {
      console.warn(`Signal light element not found: ${indicator}-${timeframe}-signal`)
      return
    }

    // Ensure data has required properties
    if (!data || typeof data !== 'object') {
      console.warn('Invalid data provided to updateSignalLight:', data)
      return
    }

    // Initialize with neutral color
    let color = SIGNAL_COLORS.neutral
    let signalType = 'neutral'

    // Determine signal type and strength
    if (data.signal) {
      const signal = String(data.signal).toLowerCase()
      const strength = parseFloat(data.strength) || 0.5

      if (signal.includes('buy')) {
        signalType = strength > 0.7 ? 'strongBuy' : 'mildBuy'
      } else if (signal.includes('sell')) {
        signalType = strength > 0.7 ? 'strongSell' : 'mildSell'
      } else if (signal.includes('neutral') || signal === 'hold') {
        signalType = 'neutral'
      }

      // Get the appropriate color
      color = SIGNAL_COLORS[signalType] || SIGNAL_COLORS.neutral

      // Add/remove pulse class based on signal strength
      if (strength > 0.7) {
        light.classList.add('pulse')
      } else {
        light.classList.remove('pulse')
      }
    }

    // Apply the color
    light.style.backgroundColor = color

    // Update tooltip with more detailed information
    const value = data.value ? parseFloat(data.value).toFixed(2) : 'N/A'
    const strength = data.strength ? `${Math.round(parseFloat(data.strength) * 100)}%` : 'N/A'
    light.title = `${indicator.toUpperCase()} (${timeframe.toUpperCase()})\nSignal: ${signalType}\nValue: ${value}\nStrength: ${strength}`
  } catch (e) {
    console.error(`Error updating signal light for ${indicator}/${timeframe}:`, e)
  }
}

// Update chart for an indicator
function updateChart(indicator, data) {
  try {
    // Find chart instance
    const chart = window.chartInstances && window.chartInstances[indicator]
    if (!chart) return

    // Update data
    chart.data.datasets[0].data.push(data.value)
    chart.data.datasets[0].data.shift()

    // Update color based on signal
    let borderColor; let backgroundColor
    if (data.signal === 'buy') {
      borderColor = data.strength > 0.6 ?
        'rgba(0, 255, 0, 0.8)' : 'rgba(0, 170, 255, 0.8)'
      backgroundColor = data.strength > 0.6 ?
        'rgba(0, 255, 0, 0.2)' : 'rgba(0, 170, 255, 0.2)'
    } else if (data.signal === 'sell') {
      borderColor = data.strength > 0.6 ?
        'rgba(255, 0, 0, 0.8)' : 'rgba(255, 165, 0, 0.8)'
      backgroundColor = data.strength > 0.6 ?
        'rgba(255, 0, 0, 0.2)' : 'rgba(255, 165, 0, 0.2)'
    } else {
      borderColor = 'rgba(128, 128, 128, 0.8)'
      backgroundColor = 'rgba(128, 128, 128, 0.2)'
    }

    chart.data.datasets[0].borderColor = borderColor
    chart.data.datasets[0].backgroundColor = backgroundColor

    // Update chart
    chart.update()
  } catch (e) {
    console.error(`Error updating chart for ${indicator}:`, e)
  }
}

// Export functions to window for external access
window.updateAllIndicators = function () {
  updateAllIndicators(getRequiredIndicators())
}
