// websocket-handler-safe.js
// Complete ground-up rewrite to eliminate maximum call stack size exceeded errors

(function () {
  'use strict'

  // Constants
  const PORTS = [8080, 8081, 8082, 3000]
  const RECONNECT_DELAY_MS = 2000
  const MAX_RECONNECT_ATTEMPTS = 5
  const HEARTBEAT_INTERVAL_MS = 30000

  // WebSocket state
  let ws = null
  let heartbeatInterval = null
  let reconnectTimeout = null
  let reconnectAttempts = 0
  let currentPortIndex = 0

  // Safe message processing
  let messageProcessorRunning = false
  const messageQueue = []
  let processMessageWorker = null

  // Initialize window state
  window.connectionError = null
  window.indicatorsData = window.indicatorsData || {}
  window.currentStrategy = window.currentStrategy || 'unknown'
  window.currentPair = window.currentPair || 'BTC/USD'
  window.logMessages = window.logMessages || []

  // ==========================================================================
  // MESSAGE QUEUE PROCESSING - COMPLETELY SEPARATE FROM WEBSOCKET HANDLING
  // ==========================================================================

  /**
   * Adds a message to the processing queue
   * Uses a separate function to break any reference chains
   */
  function enqueueMessage(data) {
    try {
      if (!data) return

      // Create a safe copy by serializing and deserializing
      // This breaks any circular references that might cause stack overflow
      const safeData = JSON.parse(JSON.stringify(data))

      // Add message type and timestamp for tracking
      safeData._enqueuedAt = Date.now()

      // Add to queue
      messageQueue.push(safeData)

      // Start message processor if not already running
      startMessageProcessor()
    } catch (error) {
      console.error('Error adding message to queue:', error)
    }
  }

  /**
   * Starts the message processor if not already running
   */
  function startMessageProcessor() {
    if (messageProcessorRunning) return

    console.log('Starting message processor...')
    messageProcessorRunning = true

    // Use requestAnimationFrame for better browser timing
    processMessageWorker = requestAnimationFrame(processNextMessage)
  }

  /**
   * Processes the next message in the queue using requestAnimationFrame
   * This ensures we stay within browser rendering cycles and don't block the UI
   */
  function processNextMessage() {
    // Clear reference to prevent memory leaks
    processMessageWorker = null

    // If queue is empty, stop processing
    if (messageQueue.length === 0) {
      console.log('Message queue empty, stopping processor')
      messageProcessorRunning = false
      return
    }

    // Get next message from queue
    const message = messageQueue.shift()
    const processingDelay = Date.now() - message._enqueuedAt

    // Log for non-heartbeat messages
    if (message.type !== 'heartbeat') {
      console.log(`Processing ${message.type} message (delay: ${processingDelay}ms), queue size: ${messageQueue.length}`)
    }

    try {
      // Process based on message type
      handleMessage(message)
    } catch (error) {
      console.error('Error processing message:', error)
    }

    // Schedule next message processing with requestAnimationFrame
    // This ensures we stay within browser rendering cycles
    processMessageWorker = requestAnimationFrame(processNextMessage)
  }

  /**
   * Handles a processed message based on its type
   * Completely isolated from the message queue processing
   */
  function handleMessage(message) {
    if (!message || !message.type) return

    switch (message.type) {
      case 'heartbeat':
        // No UI updates needed, just acknowledge
        break

      case 'indicators':
        handleIndicatorMessage(message)
        break

      case 'price':
        handlePriceMessage(message)
        break

      case 'strategy_update':
        handleStrategyMessage(message)
        break

      case 'pair_update':
        handlePairMessage(message)
        break

      case 'alerts':
        handleAlertsMessage(message)
        break

      case 'error':
        handleErrorMessage(message)
        break

      default:
        console.log(`Unknown message type: ${message.type}`)
    }
  }

  // ==========================================================================
  // MESSAGE TYPE HANDLERS - EACH ISOLATED TO PREVENT CROSS-CONTAMINATION
  // ==========================================================================

  /**
   * Safely updates indicators with proper UI refreshes
   */
  function handleIndicatorMessage(message) {
    if (!message.indicators) {
      console.warn('Received indicators message with no data')
      return
    }

    try {
      // Log what we're receiving
      console.log(`Indicator update: ${Object.keys(message.indicators).length} timeframes`)

      // Safely update global state with deep copy
      window.indicatorsData = JSON.parse(JSON.stringify(message.indicators))

      // Schedule UI updates using setTimeout to break call stack
      setTimeout(() => {
        // Update signal lights if function exists
        if (typeof window.updateAllSignalLights === 'function') {
          console.log('Updating signal lights...')
          window.updateAllSignalLights()
        }

        // Update signal matrix if function exists
        if (typeof window.updateSignalMatrix === 'function') {
          console.log('Updating signal matrix...')
          window.updateSignalMatrix()
        }
      }, 16) // One frame at 60fps
    } catch (error) {
      console.error('Error processing indicator data:', error)
    }
  }

  /**
   * Safely updates price display
   */
  function handlePriceMessage(message) {
    if (!message.price || isNaN(parseFloat(message.price))) return

    try {
      const price = parseFloat(message.price)
      window.currentPrice = price

      // Update UI with new price
      if (typeof window.updatePriceDisplay === 'function') {
        window.updatePriceDisplay(price)
      }
    } catch (error) {
      console.error('Error processing price update:', error)
    }
  }

  /**
   * Safely updates strategy display
   */
  function handleStrategyMessage(message) {
    if (!message.strategy) return

    try {
      window.currentStrategy = message.strategy
      console.log(`Strategy updated to ${message.strategy}`)

      // Update UI with new strategy
      if (typeof window.updateStrategyDisplay === 'function') {
        window.updateStrategyDisplay(message.strategy)
      }
    } catch (error) {
      console.error('Error processing strategy update:', error)
    }
  }

  /**
   * Safely updates pair display
   */
  function handlePairMessage(message) {
    if (!message.pair) return

    try {
      window.currentPair = message.pair
      console.log(`Trading pair updated to ${message.pair}`)

      // Update UI with new pair
      if (typeof window.updatePairDisplay === 'function') {
        window.updatePairDisplay(message.pair)
      }
    } catch (error) {
      console.error('Error processing pair update:', error)
    }
  }

  /**
   * Safely updates alerts display
   */
  function handleAlertsMessage(message) {
    if (!message.alerts || !Array.isArray(message.alerts)) return

    try {
      window.recentAlerts = message.alerts

      // Update UI with new alerts
      if (typeof window.updateRecentAlerts === 'function') {
        window.updateRecentAlerts(message.alerts)
      }
    } catch (error) {
      console.error('Error processing alerts update:', error)
    }
  }

  /**
   * Handles error messages from server
   */
  function handleErrorMessage(message) {
    const errorMsg = message.message || 'Unknown server error'
    console.error(`Server error: ${errorMsg}`)

    if (window.logMessages) {
      window.logMessages.push(`[${new Date().toLocaleString()}] Server error: ${errorMsg}`)
      if (typeof window.updateLogger === 'function') {
        window.updateLogger()
      }
    }
  }

  // ==========================================================================
  // WEBSOCKET CONNECTION MANAGEMENT
  // ==========================================================================

  /**
   * Establishes a WebSocket connection or tries different ports
   */
  function connectWebSocket(specificPort) {
    // Clear any existing connection timers
    clearInterval(heartbeatInterval)
    clearTimeout(reconnectTimeout)

    // Reset connection error display
    const errorContainer = document.getElementById('connection-error')
    if (errorContainer) {
      errorContainer.style.display = 'none'
    }

    // Determine which port to use - ensure it's a number
    let port

    if (specificPort && typeof specificPort === 'number') {
      port = specificPort
    } else {
      port = PORTS[currentPortIndex]
      currentPortIndex = (currentPortIndex + 1) % PORTS.length
    }

    // Create WebSocket URL
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.hostname || 'localhost'
    // Don't use /ws path - the server doesn't have this endpoint
    const wsUrl = `${protocol}//${host}:${port}`

    console.log(`Connecting to WebSocket server at ${wsUrl}...`)

    try {
      // Create new WebSocket connection
      ws = new WebSocket(wsUrl)
      window.ws = ws // Make globally available

      // Set up event handlers with clear separation of concerns
      ws.onopen = handleWebSocketOpen
      ws.onclose = handleWebSocketClose
      ws.onerror = handleWebSocketError
      ws.onmessage = handleWebSocketMessage
    } catch (error) {
      console.error('Error creating WebSocket connection:', error)
      handleConnectionFailure()
    }
  }

  /**
   * Handles WebSocket open event
   */
  function handleWebSocketOpen() {
    console.log('WebSocket connection established')

    // Reset reconnection attempts on successful connection
    reconnectAttempts = 0
    window.connectionError = null

    // Clear any existing heartbeat interval
    clearInterval(heartbeatInterval)

    // Set up heartbeat to keep connection alive
    heartbeatInterval = setInterval(() => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'heartbeat' }))
      }
    }, HEARTBEAT_INTERVAL_MS)

    // Request initial data
    requestInitialData()

    // Update UI to show connection success
    updateConnectionStatus(true)
  }

  /**
   * Handles WebSocket close event
   */
  function handleWebSocketClose(event) {
    // Only log if not already reconnecting
    if (!reconnectTimeout) {
      console.log(`WebSocket connection closed: ${event.code} ${event.reason}`)
      handleConnectionFailure()
    }
  }

  /**
   * Handles WebSocket error event
   */
  function handleWebSocketError(error) {
    console.error('WebSocket error:', error)
    handleConnectionFailure()
  }

  /**
   * Handles incoming WebSocket messages
   * Simply adds to queue - processing happens elsewhere
   */
  function handleWebSocketMessage(event) {
    try {
      // Parse the message data
      const data = JSON.parse(event.data)

      // Add to processing queue - completely separate from WebSocket handling
      enqueueMessage(data)
    } catch (error) {
      console.error('Error parsing WebSocket message:', error)
    }
  }

  // No mock data functionality - strictly using real server data only

  /**
   * Handles connection failures and attempts reconnection
   */
  function handleConnectionFailure() {
    // Clear existing intervals and timeouts
    clearInterval(heartbeatInterval)
    clearTimeout(reconnectTimeout)

    // Close existing socket if needed
    if (ws) {
      try {
        ws.onclose = null // Prevent recursion
        ws.close()
      } catch (e) {
        // Ignore errors during close
      }
      ws = null
    }

    // Increment reconnection attempts
    reconnectAttempts++

    // Update UI to show connection issue
    updateConnectionStatus(false)

    // Check if we've exceeded max reconnection attempts
    if (reconnectAttempts > MAX_RECONNECT_ATTEMPTS) {
      console.error(`Failed to connect after ${MAX_RECONNECT_ATTEMPTS} attempts`)
      window.connectionError = 'Failed to connect to server after multiple attempts'
      showConnectionError()
      return
    }

    // Try to reconnect after delay, with increasing delay for backoff
    console.log(`Reconnecting in ${RECONNECT_DELAY_MS}ms (attempt ${reconnectAttempts} of ${MAX_RECONNECT_ATTEMPTS})`)
    reconnectTimeout = setTimeout(connectWebSocket, RECONNECT_DELAY_MS)
  }

  /**
   * Shows connection error in UI
   */
  function showConnectionError() {
    const errorContainer = document.getElementById('connection-error')
    if (!errorContainer) return

    errorContainer.style.display = 'block'
    errorContainer.innerHTML = `
      <div class="error-message">
        <strong>Connection Error:</strong> ${window.connectionError || 'Unable to connect to server'}
      </div>
      <button id="retry-connection" class="retry-button">Retry Connection</button>
    `

    // Add retry button functionality
    const retryButton = document.getElementById('retry-connection')
    if (retryButton) {
      retryButton.addEventListener('click', () => {
        reconnectAttempts = 0
        connectWebSocket()
      })
    }
  }

  /**
   * Updates connection status UI elements
   * @param {boolean} connected - Whether connected to server
   */
  function updateConnectionStatus(connected) {
    const connectionIndicator = document.getElementById('connection-status')
    if (connectionIndicator) {
      // Set the connection class
      connectionIndicator.className = connected ? 'connected' : 'disconnected'
      connectionIndicator.setAttribute('title', connected ? 'Connected to server' : 'Disconnected from server')
      connectionIndicator.innerHTML = ''
    }
  }

  // ==========================================================================
  // DATA REQUEST FUNCTIONS
  // ==========================================================================

  /**
   * Ensures WebSocket is connected before sending a message
   */
  function ensureWebSocketAndSend(message) {
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      // Connect first if needed
      connectWebSocket()

      // Wait a bit for connection to establish
      setTimeout(() => {
        if (ws && ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify(message))
        } else {
          console.error('Could not send message, WebSocket not connected')
        }
      }, 1000)
    } else {
      // Send immediately if already connected
      ws.send(JSON.stringify(message))
    }
  }

  /**
   * Requests initial data from server
   */
  function requestInitialData() {
    console.log('Requesting initial data from server...')

    // Request indicators for current strategy
    requestServerData('indicators', { strategy: window.currentStrategy })

    // Request current price
    requestServerData('price')

    // Request recent alerts
    requestServerData('alerts')
  }

  /**
   * Requests specific data from the server
   */
  function requestServerData(dataType, params = {}) {
    const message = {
      type: `request_${dataType}`,
      ...params,
    }

    ensureWebSocketAndSend(message)
  }

  /**
   * Updates strategy by sending request to server
   */
  function updateStrategy(strategyName) {
    console.log(`Requesting strategy switch to: ${strategyName}`)

    // Show loading overlay
    if (typeof window.showLoadingOverlay === 'function') {
      window.showLoadingOverlay(true, 'Switching strategy...')
    }

    // Request strategy change
    requestServerData('strategy_update', { strategy: strategyName })

    // Hide loading overlay after a delay
    setTimeout(() => {
      if (typeof window.showLoadingOverlay === 'function') {
        window.showLoadingOverlay(false)
      }
    }, 2000)
  }

  // ==========================================================================
  // INITIALIZATION
  // ==========================================================================

  // Connect WebSocket when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => connectWebSocket(8080))
  } else {
    // If DOM already loaded, connect now with default port
    connectWebSocket(8080)
  }

  // Expose public API
  window.wsHandler = {
    connect: connectWebSocket,
    requestData: requestServerData,
    updateStrategy,
  }
})()
