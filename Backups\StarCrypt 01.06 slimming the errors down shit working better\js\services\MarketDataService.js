class MarketDataService {
  constructor() {
    this.subscribers = []
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 3000
    this.isConnected = false
  }

  connect(wsUrl = 'wss://stream.binance.com:9443/ws/btcusdt@kline_1m') {
    if (this.ws) {
      this.disconnect()
    }

    this.ws = new WebSocket(wsUrl)

    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.notifySubscribers({ type: 'connection', status: 'connected' })
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.processMarketData(data)
      } catch (error) {
        console.error('Error processing message:', error)
      }
    }

    this.ws.onclose = () => {
      console.log('WebSocket disconnected')
      this.isConnected = false
      this.notifySubscribers({ type: 'connection', status: 'disconnected' })
      this.handleReconnect()
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error)
      this.isConnected = false
      this.notifySubscribers({
        type: 'error',
        message: 'Connection error',
        error: error.message,
      })
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
      this.isConnected = false
    }
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)

      setTimeout(() => {
        this.connect(this.ws?.url)
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('Max reconnection attempts reached')
      this.notifySubscribers({
        type: 'error',
        message: 'Failed to reconnect to market data feed',
        fatal: true,
      })
    }
  }

  processMarketData(data) {
    // Process different types of market data
    if (data.e === 'kline') {
      this.processKlineData(data)
    } else if (data.e === 'trade') {
      this.processTradeData(data)
    } else if (data.e === 'depthUpdate') {
      this.processDepthData(data)
    } else {
      console.log('Unhandled data type:', data)
    }
  }

  processKlineData(klineData) {
    const { k } = klineData
    const candle = {
      time: k.t,
      open: parseFloat(k.o),
      high: parseFloat(k.h),
      low: parseFloat(k.l),
      close: parseFloat(k.c),
      volume: parseFloat(k.v),
      isClosed: k.x,
    }

    this.notifySubscribers({
      type: 'kline',
      symbol: klineData.s,
      interval: k.i,
      data: candle,
    })
  }

  processTradeData(tradeData) {
    const trade = {
      id: tradeData.t,
      price: parseFloat(tradeData.p),
      quantity: parseFloat(tradeData.q),
      time: tradeData.T,
      isBuyerMaker: tradeData.m,
    }

    this.notifySubscribers({
      type: 'trade',
      symbol: tradeData.s,
      data: trade,
    })
  }

  processDepthData(depthData) {
    const depth = {
      lastUpdateId: depthData.u,
      bids: depthData.b.map(bid => ({
        price: parseFloat(bid[0]),
        quantity: parseFloat(bid[1]),
      })),
      asks: depthData.a.map(ask => ({
        price: parseFloat(ask[0]),
        quantity: parseFloat(ask[1]),
      })),
    }

    this.notifySubscribers({
      type: 'depth',
      symbol: depthData.s,
      data: depth,
    })
  }

  subscribe(callback) {
    if (typeof callback === 'function') {
      this.subscribers.push(callback)
      return () => this.unsubscribe(callback)
    }
  }

  unsubscribe(callback) {
    this.subscribers = this.subscribers.filter(sub => sub !== callback)
  }

  notifySubscribers(data) {
    this.subscribers.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('Error in subscriber callback:', error)
      }
    })
  }

  // Helper methods for different data types
  subscribeKline(symbol, interval, callback) {
    // In a real implementation, this would send a subscription message to the WebSocket
    console.log(`Subscribing to ${symbol}@kline_${interval}`)

    return this.subscribe((data) => {
      if (data.type === 'kline' && data.symbol === symbol && data.interval === interval) {
        callback(data)
      }
    })
  }

  subscribeTrades(symbol, callback) {
    // In a real implementation, this would send a subscription message to the WebSocket
    console.log(`Subscribing to ${symbol}@trade`)

    return this.subscribe((data) => {
      if (data.type === 'trade' && data.symbol === symbol) {
        callback(data)
      }
    })
  }

  subscribeOrderBook(symbol, callback) {
    // In a real implementation, this would send a subscription message to the WebSocket
    console.log(`Subscribing to ${symbol}@depth`)

    return this.subscribe((data) => {
      if (data.type === 'depth' && data.symbol === symbol) {
        callback(data)
      }
    })
  }
}

// Export as singleton
export const marketDataService = new MarketDataService()
