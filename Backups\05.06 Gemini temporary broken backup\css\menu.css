.menu-item .menu-item-icon {
  margin-right: 10px;
  font-size: 14px;
  width: 16px;
  text-align: center;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-item .menu-item-shortcut {
  margin-left: auto;
  font-size: 11px;
  opacity: 0.6;
  letter-spacing: 0.5px;
  padding-left: 16px;
}

.menu-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 6px 8px;
  padding: 0;
  cursor: default;
  pointer-events: none;
}

/* Strategy Selector Specific */
.menu-item.strategy-option {
  padding: 12px 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.strategy-name {
  font-weight: 500;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  width: 100%;
}

.strategy-description {
  font-size: 11px;
  opacity: 0.7;
  line-height: 1.4;
  margin-top: 2px;
}

/* Scrollbar Styling */
.menu-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.menu-content::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
  border-radius: 3px;
}

.menu-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.menu-content::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}
