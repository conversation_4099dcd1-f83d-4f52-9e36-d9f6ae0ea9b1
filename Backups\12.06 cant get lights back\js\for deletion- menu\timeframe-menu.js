/**
 * @module TimeframeMenu
 * @description Menu system for trading timeframes
 */

import { MenuController } from './menu-controller.js';
import { MenuUtils } from './menu-utils.js';
import { TimeframeManager } from '../timeframe-manager.js';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../utils/error-handler.js';
import { PerformanceMonitor } from '../utils/performance-monitor.js';

export class TimeframeMenu extends MenuController {
    constructor() {
        super();
        
        // Timeframe-specific configuration
        this.config = {
            menuId: 'timeframe-menu',
            buttonId: 'timeframe-button',
            containerId: 'timeframe-container',
            helperId: 'timeframe-helper',
            animationDuration: 300,
            timeframes: [
                '1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M'
            ],
            showHelp: true
        };
        
        // Timeframe manager
        this.timeframeManager = new TimeframeManager();
        
        // Error handling
        this.errorHandler = new ErrorHandler({
            maxErrors: 5,
            recoveryDelay: 2000,
            errorThreshold: 0.1
        });
        
        // Performance monitoring
        this.performance = new PerformanceMonitor({
            targetFPS: 60,
            maxLatency: 100,
            batchInterval: 100
        });
        
        // Menu state
        this.state = {
            currentTimeframe: null,
            timeframes: [],
            isInitialized: false,
            isLoading: false,
            starCryptAvailable: false,
            error: null
        };
    }

    /**
     * Wait for StarCrypt to be available
     * @returns {Promise<boolean>} Resolves when StarCrypt is available
     */
    async waitForStarCrypt() {
        return new Promise((resolve) => {
            const checkStarCrypt = () => {
                if (window.StarCrypt) {
                    this.state.starCryptAvailable = true;
                    resolve(true);
                } else {
                    setTimeout(checkStarCrypt, 100); // Check again in 100ms
                }
            };
            checkStarCrypt();
        });
    }

    /**
     * Initialize timeframe menu
     */
    async init() {
        try {
            // Wait for StarCrypt to be available
            await this.waitForStarCrypt();

            // Initialize base menu
            await super.init();
            
            // Initialize timeframe manager
            await this.timeframeManager.init();
            
            // Get timeframes
            this.state.timeframes = this.getTimeframes();
            
            // Create menu structure
            this.createMenuStructure();
            
            // Add event listeners
            this.setupEventListeners();
            
            // Set initial state
            this.state.isInitialized = true;
            this.state.currentTimeframe = this.timeframeManager.getCurrentTimeframe();
            
            // Dispatch initialized event
            this.dispatch('initialized', {
                timeframes: this.state.timeframes,
                currentTimeframe: this.state.currentTimeframe
            });
            
        } catch (error) {
            this.handleError(error, 'init');
        }
    }

    /**
     * Get available timeframes
     * @returns {Array} List of timeframes
     * @private
     */
    getTimeframes() {
        try {
            // Validate timeframes
            const timeframes = this.config.timeframes;
            if (!Array.isArray(timeframes)) {
                throw new Error('Timeframes must be an array');
            }
            
            // Sort timeframes
            return timeframes.sort((a, b) => {
                const aNum = parseFloat(a);
                const bNum = parseFloat(b);
                const aUnit = a.replace(/\d+/g, '');
                const bUnit = b.replace(/\d+/g, '');
                
                // Order: m (minutes), h (hours), d (days), w (weeks), M (months)
                const unitOrder = {
                    m: 1,
                    h: 2,
                    d: 3,
                    w: 4,
                    M: 5
                };
                
                const aOrder = unitOrder[aUnit] || 0;
                const bOrder = unitOrder[bUnit] || 0;
                
                if (aOrder === bOrder) {
                    return aNum - bNum;
                }
                
                return aOrder - bOrder;
            });
            
        } catch (error) {
            this.handleError(error, 'get-timeframes');
            return [];
        }
    }

    /**
     * Create menu structure
     * @private
     */
    createMenuStructure() {
        try {
            // Get menu element
            const menu = MenuUtils.getMenuElement(this.config.menuId);
            if (!menu) return;
            
            // Clear existing items
            menu.innerHTML = '';
            
            // Add timeframe items
            this.state.timeframes.forEach(timeframe => {
                const item = MenuUtils.addMenuItem(this.config.menuId, {
                    id: `timeframe-${timeframe}`,
                    text: this.formatTimeframe(timeframe),
                    className: 'timeframe-item',
                    style: {
                        cursor: 'pointer',
                        padding: '8px 16px',
                        borderRadius: '4px',
                        transition: 'background-color 0.2s'
                    },
                    events: {
                        click: () => this.handleTimeframeClick(timeframe),
                        mouseenter: () => this.handleTimeframeHover(timeframe),
                        mouseleave: () => this.handleTimeframeHoverEnd()
                    }
                });
                
                // Add ARIA attributes
                item.setAttribute('role', 'menuitem');
                item.setAttribute('aria-label', `Switch to ${this.formatTimeframe(timeframe)} timeframe`);
            });
            
            // Add helper content if enabled
            if (this.config.showHelp) {
                this.addHelperContent();
            }
            
        } catch (error) {
            this.handleError(error, 'create-menu-structure');
        }
    }

    /**
     * Format timeframe for display
     * @param {string} timeframe - Timeframe string
     * @returns {string} Formatted timeframe
     * @private
     */
    formatTimeframe(timeframe) {
        try {
            const num = parseFloat(timeframe);
            const unit = timeframe.replace(/\d+/g, '');
            
            switch (unit) {
                case 'm':
                    return `${num} Minute`;
                case 'h':
                    return `${num} Hour`;
                case 'd':
                    return `${num} Day`;
                case 'w':
                    return `${num} Week`;
                case 'M':
                    return `${num} Month`;
                default:
                    return timeframe;
            }
            
        } catch (error) {
            this.handleError(error, 'format-timeframe');
            return timeframe;
        }
    }

    /**
     * Add helper content
     * @private
     */
    addHelperContent() {
        try {
            // Get helper element
            const helper = document.getElementById(this.config.helperId);
            if (!helper) return;
            
            // Add helper content
            helper.innerHTML = `
                <h3>Timeframe Selection Guide</h3>
                <p>Select a timeframe to view different market perspectives.</p>
                <ul>
                    <li>Click to switch timeframe</li>
                    <li>Hover for timeframe description</li>
                    <li>Use arrow keys for navigation</li>
                </ul>
            `;
            
            // Add ARIA attributes
            helper.setAttribute('role', 'region');
            helper.setAttribute('aria-label', 'Timeframe selection guide');
            
        } catch (error) {
            this.handleError(error, 'add-helper-content');
        }
    }

    /**
     * Handle timeframe click
     * @param {string} timeframe - Selected timeframe
     * @private
     */
    async handleTimeframeClick(timeframe) {
        try {
            // Set loading state
            this.state.isLoading = true;
            
            // Update timeframe
            await this.timeframeManager.switchTimeframe(timeframe);
            
            // Update menu state
            this.state.currentTimeframe = timeframe;
            
            // Update UI
            this.updateUI();
            
            // Dispatch change event
            this.dispatch('timeframe-change', {
                timeframe,
                timestamp: Date.now()
            });
            
            // Close menu
            this.close();
            
        } catch (error) {
            this.handleError(error, 'handle-timeframe-click');
        } finally {
            this.state.isLoading = false;
        }
    }

    /**
     * Handle timeframe hover
     * @param {string} timeframe - Hovered timeframe
     * @private
     */
    handleTimeframeHover(timeframe) {
        try {
            // Update helper content
            this.updateHelperContent(timeframe);
            
            // Add hover class
            const item = document.getElementById(`timeframe-${timeframe}`);
            if (item) {
                item.classList.add('hover');
            }
            
        } catch (error) {
            this.handleError(error, 'handle-timeframe-hover');
        }
    }

    /**
     * Handle timeframe hover end
     * @private
     */
    handleTimeframeHoverEnd() {
        try {
            // Remove hover classes
            document.querySelectorAll('.timeframe-item.hover')
                .forEach(item => item.classList.remove('hover'));
            
            // Reset helper content
            this.resetHelperContent();
            
        } catch (error) {
            this.handleError(error, 'handle-timeframe-hover-end');
        }
    }

    /**
     * Update helper content
     * @param {string} timeframe - Timeframe to display
     * @private
     */
    updateHelperContent(timeframe) {
        try {
            // Get helper element
            const helper = document.getElementById(this.config.helperId);
            if (!helper) return;
            
            // Update content
            helper.innerHTML = `
                <h3>${this.formatTimeframe(timeframe)}</h3>
                <p>View market data at this timeframe resolution.</p>
                <div class="timeframe-details">
                    <div>Current: ${this.formatTimeframe(this.state.currentTimeframe)}</div>
                    <div>New: ${this.formatTimeframe(timeframe)}</div>
                </div>
            `;
            
        } catch (error) {
            this.handleError(error, 'update-helper-content');
        }
    }

    /**
     * Reset helper content
     * @private
     */
    resetHelperContent() {
        try {
            // Get helper element
            const helper = document.getElementById(this.config.helperId);
            if (!helper) return;
            
            // Reset content
            helper.innerHTML = '';
            
        } catch (error) {
            this.handleError(error, 'reset-helper-content');
        }
    }

    /**
     * Update UI based on current state
     * @private
     */
    updateUI() {
        try {
            // Get menu element
            const menu = MenuUtils.getMenuElement(this.config.menuId);
            if (!menu) return;
            
            // Update timeframe items
            this.state.timeframes.forEach(timeframe => {
                const item = document.getElementById(`timeframe-${timeframe}`);
                if (item) {
                    // Add active class if current timeframe
                    if (timeframe === this.state.currentTimeframe) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                }
            });
            
            // Update button text
            const button = document.getElementById(this.config.buttonId);
            if (button) {
                button.textContent = this.formatTimeframe(this.state.currentTimeframe);
            }
            
        } catch (error) {
            this.handleError(error, 'update-ui');
        }
    }

    /**
     * Setup event listeners
     * @private
     */
    setupEventListeners() {
        try {
            // Listen for timeframe changes
            this.timeframeManager.on('timeframe-change', (data) => {
                this.handleTimeframeChange(data);
            });
            
            // Listen for menu events
            this.on('menu-close', () => this.handleMenuClose());
            this.on('error', (error) => this.handleError(error));
            
        } catch (error) {
            this.handleError(error, 'setup-event-listeners');
        }
    }

    /**
     * Handle timeframe change
     * @param {Object} data - Timeframe change data
     * @private
     */
    handleTimeframeChange(data) {
        try {
            // Update state
            this.state.currentTimeframe = data.timeframe;
            
            // Update UI
            this.updateUI();
            
        } catch (error) {
            this.handleError(error, 'handle-timeframe-change');
        }
    }

    /**
     * Handle menu close
     * @private
     */
    handleMenuClose() {
        try {
            // Reset helper content
            this.resetHelperContent();
            
            // Remove hover classes
            this.handleTimeframeHoverEnd();
            
        } catch (error) {
            this.handleError(error, 'handle-menu-close');
        }
    }

    /**
     * Handle errors
     * @param {Error} error - Error object
     * @param {string} context - Context where error occurred
     * @private
     */
    handleError(error, context) {
        try {
            // Log error
            console.error(`[TimeframeMenu] Error in ${context}:`, error);
            
            // Track error
            this.errorHandler.track(error, context);
            
            // Set error state
            this.state.error = error;
            
            // Dispatch error event
            this.dispatch('error', {
                error,
                context,
                timestamp: Date.now()
            });
            
            // Try to recover
            if (this.errorHandler.shouldRecover()) {
                this.recoverFromError();
            }
            
        } catch (error) {
            console.error('[TimeframeMenu] Error handling failed:', error);
        }
    }

    /**
     * Attempt to recover from error
     * @private
     */
    recoverFromError() {
        try {
            // Reset state
            this.state = {
                currentTimeframe: null,
                timeframes: [],
                isInitialized: false,
                isLoading: false,
                error: null
            };
            
            // Reinitialize
            this.init();
            
        } catch (error) {
            console.error('[TimeframeMenu] Recovery failed:', error);
        }
    }

    /**
     * Clean up resources
     */
    cleanup() {
        try {
            // Cleanup base menu
            super.cleanup();
            
            // Cleanup timeframe manager
            this.timeframeManager.cleanup();
            
            // Reset state
            this.state = {
                currentTimeframe: null,
                timeframes: [],
                isInitialized: false,
                isLoading: false,
                error: null
            };
            
            // Reset error handler
            this.errorHandler.reset();
            
            // Reset performance monitor
            this.performance.reset();
            
        } catch (error) {
            console.error('[TimeframeMenu] Cleanup failed:', error);
        }
    }

    /**
     * Destroy menu
     */
    destroy() {
        try {
            // Cleanup
            this.cleanup();
            
            // Reset references
            this.timeframeManager = null;
            this.errorHandler = null;
            this.performance = null;
            
        } catch (error) {
            console.error('[TimeframeMenu] Destruction failed:', error);
        }
    }
}

export const TimeframeMenu = {
    instance: null,
    getInstance: function() {
        if (!TimeframeMenu.instance) {
            TimeframeMenu.instance = new TimeframeMenu();
        }
        return TimeframeMenu.instance;
    },
    initialize: async function() {
        const instance = TimeframeMenu.getInstance();
        await instance.waitForStarCrypt();
        instance.initializeMenu();
    }
};

// Initialize when StarCrypt is ready
if (window.StarCrypt) {
    TimeframeMenu.initialize();
} else {
    window.addEventListener('StarCryptReady', TimeframeMenu.initialize);
}
