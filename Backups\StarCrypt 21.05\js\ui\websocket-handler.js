// websocket-handler.js
// Handles WebSocket connection and message processing for StarCrypt

(function () {
  // WebSocket connection
  window.ws = null
  let reconnectAttempts = 0
  const MAX_RECONNECT_ATTEMPTS = 5 // Maximum reconnection attempts
  const RECONNECT_DELAY = 2000 // 2 seconds
  const PORTS_TO_TRY = [8080, 8081, 8082, 3000] // Try multiple ports
  let currentPortIndex = 0

  // Queue for WebSocket messages to prevent stack overflow
  const messageQueue = []
  let isProcessingQueue = false

  // Connect to WebSocket server
  window.connectWebSocket = function (specificPort) {
    console.log('Connecting to WebSocket server...')

    // Don't create multiple connections
    if (window.ws && window.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected')
      return
    }

    try {
      // Close existing connection if any
      if (window.ws) {
        window.ws.close()
      }

      // Use specific port if provided, otherwise use the next port in the sequence
      let port
      if (specificPort) {
        port = specificPort
      } else {
        // Try ports in sequence
        port = PORTS_TO_TRY[currentPortIndex]
        // Update for next attempt if needed
        currentPortIndex = (currentPortIndex + 1) % PORTS_TO_TRY.length
      }

      console.log(`Attempting to connect to WebSocket server on port ${port}`)
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Attempting WebSocket connection on port ${port}`)
        if (window.updateLogger) window.updateLogger()
      }

      // Create new WebSocket connection
      window.ws = new WebSocket(`ws://localhost:${port}`)

      // Store the port we're using
      window._wsPort = port

      // Set a connection timeout
      const connectionTimeout = setTimeout(() => {
        if (window.ws && window.ws.readyState !== WebSocket.OPEN) {
          console.log(`Connection timeout for port ${port}`)
          window.ws.close()
          handleConnectionFailure()
        }
      }, 3000) // 3 second timeout

      // Connection opened
      window.ws.addEventListener('open', (event) => {
        // Clear the connection timeout
        clearTimeout(connectionTimeout)

        console.log('WebSocket connection established')
        reconnectAttempts = 0

        // Log connection
        if (window.logMessages) {
          window.logMessages.push(`[${new Date().toLocaleString()}] WebSocket connection established`)
          if (window.updateLogger) window.updateLogger()
        }

        // Request initial data
        requestInitialData()
      })

      // Listen for messages
      window.ws.addEventListener('message', (event) => {
        try {
          // Check if event.data is empty or not a valid JSON string
          if (!event.data || typeof event.data !== 'string' || event.data.trim() === '') {
            console.log('Received empty or invalid WebSocket message, ignoring')
            return
          }

          const data = JSON.parse(event.data)

          // Validate that data is an object with a type property
          if (!data || typeof data !== 'object' || !data.type) {
            console.log('Received WebSocket message without proper type, ignoring:', data)
            return
          }

          // Process message based on type
          processWebSocketMessage(data)

          // Dispatch custom event for other modules to listen to
          const customEvent = new CustomEvent('websocketMessage', { detail: data })
          document.dispatchEvent(customEvent)
        } catch (error) {
          // Only log the first few errors to avoid console spam
          if (!window._wsErrorCount) window._wsErrorCount = 0
          window._wsErrorCount++

          if (window._wsErrorCount <= 5) {
            console.error(`Error processing WebSocket message (${window._wsErrorCount}/5):`, error)

            if (window.logMessages) {
              window.logMessages.push(`[${new Date().toLocaleString()}] Error processing WebSocket message: ${error.message}`)
              if (window.updateLogger) window.updateLogger()
            }
          } else if (window._wsErrorCount === 6) {
            console.error('Too many WebSocket errors, suppressing further error messages')

            if (window.logMessages) {
              window.logMessages.push(`[${new Date().toLocaleString()}] Too many WebSocket errors, suppressing further error messages`)
              if (window.updateLogger) window.updateLogger()
            }
          }
        }
      })

      // Connection closed
      window.ws.addEventListener('close', (event) => {
        // Clear the connection timeout
        clearTimeout(connectionTimeout)

        console.log('WebSocket connection closed')

        if (window.logMessages) {
          window.logMessages.push(`[${new Date().toLocaleString()}] WebSocket connection closed on port ${window._wsPort}`)
          if (window.updateLogger) window.updateLogger()
        }

        // Handle connection failure
        handleConnectionFailure()
      })

      // Connection error
      window.ws.addEventListener('error', (event) => {
        // Clear the connection timeout
        clearTimeout(connectionTimeout)

        console.error('WebSocket error:', event)

        if (window.logMessages) {
          window.logMessages.push(`[${new Date().toLocaleString()}] WebSocket error on port ${window._wsPort}`)
          if (window.updateLogger) window.updateLogger()
        }

        // Don't try to reconnect here - let the close event handle it
        // This prevents duplicate reconnection attempts
      })
    } catch (error) {
      console.error('Error connecting to WebSocket:', error)

      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Error connecting to WebSocket: ${error.message}`)
        if (window.updateLogger) window.updateLogger()
      }

      // Handle connection failure
      handleConnectionFailure()
    }
  }

  // Handle connection failure
  function handleConnectionFailure() {
    // Attempt to reconnect
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++

      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Attempting to reconnect (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})...`)
        if (window.updateLogger) window.updateLogger()
      }

      // Try next port in the sequence
      setTimeout(() => window.connectWebSocket(), RECONNECT_DELAY)
    } else {
      // If we've tried all ports multiple times, show error message
      console.error('Maximum reconnection attempts reached. Server connection failed.')

      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] ERROR: Maximum reconnection attempts reached. Server connection failed.`)
        if (window.updateLogger) window.updateLogger()
      }

      // Show error message to user
      const errorContainer = document.getElementById('error-container')
      if (errorContainer) {
        errorContainer.innerHTML = `
          <div class="error-message">
            <h3>Server Connection Failed</h3>
            <p>Unable to connect to the data server after multiple attempts.</p>
            <p>Please check that the server is running and try again.</p>
            <button id="retry-connection" class="retry-button">Retry Connection</button>
          </div>
        `
        errorContainer.style.display = 'flex'

        // Add event listener to retry button
        const retryButton = document.getElementById('retry-connection')
        if (retryButton) {
          retryButton.addEventListener('click', () => {
            errorContainer.style.display = 'none'
            reconnectAttempts = 0
            window.connectWebSocket()
          })
        }
      }

      // Reset reconnect attempts for future tries
      setTimeout(() => {
        reconnectAttempts = 0
        window.connectWebSocket()
      }, 30000) // Try again after 30 seconds
    }
  }

  // Ensure WebSocket is connected before sending message
  window.ensureWebSocketAndSend = function (message) {
    if (window.ws && window.ws.readyState === WebSocket.OPEN) {
      window.ws.send(message)
      return true
    }
    // Try to reconnect
    connectWebSocket()

    // Queue message to be sent after connection
    setTimeout(() => {
      if (window.ws && window.ws.readyState === WebSocket.OPEN) {
        window.ws.send(message)
        return true
      }
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Failed to send message: WebSocket not connected`)
        if (window.updateLogger) window.updateLogger()
      }
      return false
    }, 1000)
  }

  // Request initial data from server
  function requestInitialData() {
    // Request data for current pair and timeframe
    const pair = window.currentPair || 'xbtusdt'
    const timeframe = window.currentTf || '1h'
    const strategy = window.currentStrategy || 'admiral_toa'

    // Send request for all timeframes
    const timeframes = window.useLowTimeframes ? window.LOW_TIMEFRAMES : window.TIMEFRAMES

    if (window.logMessages) {
      window.logMessages.push(`[${new Date().toLocaleString()}] Requesting initial data for ${pair} (${strategy})`)
      if (window.updateLogger) window.updateLogger()
    }

    // Request data for each timeframe
    timeframes.forEach(tf => {
      window.ws.send(JSON.stringify({
        type: 'requestIndicators',
        pair,
        timeframe: tf,
        strategy,
      }))
    })
  }

  // Process WebSocket message - adds message to queue
  function processWebSocketMessage(data) {
    if (data) {
      messageQueue.push(data)
      if (!isProcessingQueue) {
        processMessageQueue()
      }
    }
  }

  // Process messages from the queue one at a time to prevent stack overflow
  function processMessageQueue() {
    if (messageQueue.length === 0) {
      isProcessingQueue = false
      return
    }

    isProcessingQueue = true

    // Get the next message from the queue
    const data = messageQueue.shift()

    try {
      // Skip logging for heartbeat messages
      if (data.type === 'heartbeat') {
        // Continue to next message immediately
        setTimeout(processMessageQueue, 0)
        return
      }

      // Handle empty or invalid data
      if (!data || typeof data !== 'object') {
        console.log('Received invalid WebSocket message data, ignoring')
        // Continue to next message
        setTimeout(processMessageQueue, 0)
        return
      }

      // Log message (except for high-frequency updates)
      if (data.type !== 'priceUpdate' && window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Received ${data.type || 'unknown'} message`)
        if (typeof window.updateLogger === 'function') window.updateLogger()
      }

      // Process message based on type
      switch (data.type) {
        case 'connection_established':
          // Server connection established
          if (window.logMessages) {
            window.logMessages.push(`[${new Date().toLocaleString()}] Connected to StarCrypt server: ${data.message}`)
            if (window.updateLogger) window.updateLogger()
          }
          break

        case 'indicators':
          // Received indicator data
          if (!window.indicatorsData) window.indicatorsData = {}
          if (!window.indicatorsData[data.timeframe]) window.indicatorsData[data.timeframe] = {}

          // Sanitize data: ensure indicators is an object and values are arrays if expected
          const sanitizedIndicators = Array.isArray(data.indicators) ? data.indicators.map(ind => ({ ...ind, data: Array.isArray(ind.data) ? ind.data.map(val => Number.isFinite(val) ? val : 0) : [], labels: Array.isArray(ind.labels) ? ind.labels.map(label => String(label)) : [] })) : {}
          Object.assign(window.indicatorsData[data.timeframe], sanitizedIndicators)

          // Update signal lights
          if (typeof window.updateAllSignalLights === 'function') {
            window.updateAllSignalLights()
          }

          // Update mini charts
          if (typeof window.updateMiniCharts === 'function') {
            window.updateMiniCharts(data.pair)
          }
          break

        case 'priceUpdate':
          // Update current price
          window.currentPrice = data.price

          // Update price display
          const priceElement = document.getElementById('currentPrice')
          if (priceElement) {
            const previousPrice = parseFloat(priceElement.getAttribute('data-price') || '0')
            const newPrice = parseFloat(data.price)

            // Update price
            priceElement.textContent = `${data.pair.toUpperCase()}: $${newPrice.toFixed(2)}`
            priceElement.setAttribute('data-price', newPrice.toString())

            // Add price direction class
            if (newPrice > previousPrice) {
              priceElement.classList.remove('price-down')
              priceElement.classList.add('price-up', 'price-flash')
            } else if (newPrice < previousPrice) {
              priceElement.classList.remove('price-up')
              priceElement.classList.add('price-down', 'price-flash')
            }

            // Remove flash class after animation
            setTimeout(() => {
              priceElement.classList.remove('price-flash')
            }, 500)
          }
          break

        case 'strategy_change_confirmation':
          // Strategy change confirmed
          if (window.logMessages) {
            window.logMessages.push(`[${new Date().toLocaleString()}] Strategy change confirmed: ${data.message}`)
            if (window.updateLogger) window.updateLogger()
          }

          // Update UI
          if (typeof window.updateSignalMatrix === 'function') {
            window.updateSignalMatrix()
          }
          break

        case 'coin_change_confirmation':
          // Coin change confirmed
          if (window.logMessages) {
            window.logMessages.push(`[${new Date().toLocaleString()}] Coin change confirmed: ${data.message}`)
            if (window.updateLogger) window.updateLogger()
          }
          break

        case 'error':
          // Error message
          if (window.logMessages) {
            window.logMessages.push(`[${new Date().toLocaleString()}] Server error: ${data.message}`)
            if (window.updateLogger) window.updateLogger()
          }
          break

        case 'indicators':
          // Update indicators data
          if (data.indicators) {
            window.indicatorsData = data.indicators
            // Update UI with new data
            if (typeof window.updateAllSignalLights === 'function') {
              window.updateAllSignalLights()
            }
            if (typeof window.updateSignalMatrix === 'function') {
              window.updateSignalMatrix()
            }
          } else {
            console.warn('Received indicators message without data')
          }
          break

        case 'price':
          // Update price if valid
          if (data.price && !isNaN(parseFloat(data.price))) {
            window.currentPrice = parseFloat(data.price)
            // Update UI with new price
            if (typeof window.updatePriceDisplay === 'function') {
              window.updatePriceDisplay(window.currentPrice)
            }
          } else {
            console.warn('Received price message with invalid price data')
          }
          break

        case 'strategy_update':
          // Update strategy if valid
          if (data.strategy && typeof data.strategy === 'string') {
            window.currentStrategy = data.strategy
            console.log(`Strategy updated to ${data.strategy}`)
            // Update UI with new strategy
            if (typeof window.updateStrategyDisplay === 'function') {
              window.updateStrategyDisplay(data.strategy)
            }
          } else {
            console.warn('Received strategy_update message with invalid strategy data')
          }
          break

        case 'pair_update':
          // Update trading pair if valid
          if (data.pair && typeof data.pair === 'string') {
            window.currentPair = data.pair
            console.log(`Trading pair updated to ${data.pair}`)
            // Update UI with new pair
            if (typeof window.updatePairDisplay === 'function') {
              window.updatePairDisplay(data.pair)
            }
          } else {
            console.warn('Received pair_update message with invalid pair data')
          }
          break

        case 'alerts':
          // Update alerts if valid
          if (data.alerts && Array.isArray(data.alerts)) {
            window.recentAlerts = data.alerts
            // Update UI with new alerts
            if (typeof window.updateRecentAlerts === 'function') {
              window.updateRecentAlerts(data.alerts)
            }
          } else {
            console.warn('Received alerts message with invalid alerts data')
          }
          break

        default:
          // Unknown message type
          console.log(`Unknown message type: ${data.type}`)
          break
      }
    } catch (error) {
      console.error('Error processing WebSocket message:', error)
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Error processing WebSocket message: ${error.message}`)
        if (window.updateLogger) window.updateLogger()
      }
    } finally {
      // Continue processing the queue with a small delay to prevent stack overflow
      setTimeout(processMessageQueue, 0)

      // Ensure we have a WebSocket connection
      if (!window.ws || window.ws.readyState !== WebSocket.OPEN) {
        console.error('WebSocket not connected, cannot request data')
        if (window.logMessages) {
          window.logMessages.push(`[${new Date().toLocaleString()}] ERROR: WebSocket not connected, cannot request data`)
          if (window.updateLogger) window.updateLogger()
        }
        return false
      }

      // Request data for current strategy
      const strategy = window.currentStrategy || 'admiral_toa'

      // Send data request to server
      window.ws.send(JSON.stringify({
        type: 'data_request',
        strategy,
        timestamp: Date.now(),
      }))

      console.log(`Data request sent to server for strategy: ${strategy}`)
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Data request sent to server for strategy: ${strategy}`)
        if (window.updateLogger) window.updateLogger()
      }

      return true
    }
  }

  // Add global error handler
  window.onerror = function (message, source, lineno, colno, error) {
    console.error('Global error caught:', message, 'at', `${source}:${lineno}:${colno}`, error)
    if (window.logMessages) {
      window.logMessages.push(`Global error: ${message} at line ${lineno} in ${source}`)
      if (window.updateLogger) window.updateLogger()
    }
  }

  // Connect WebSocket when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', connectWebSocket)
  } else {
    // DOM already loaded
    connectWebSocket()
  }
})() // Closing IIFE
