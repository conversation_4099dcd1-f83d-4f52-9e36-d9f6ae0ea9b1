# StarCrypt Architecture

**Last Updated:** 2025-06-10

## 1. Core Architecture

### 1.1 System Overview

StarCrypt is a real-time cryptocurrency trading platform with the following key components:

```mermaid
┌─────────────────────────────────────────────────────────────┐
│                      Frontend (Browser)                     │
│  ┌─────────────┐    ┌─────────────┐    ┌──────────────┐  │
│  │  UI Layer   │    │  State      │    │  WebSocket   │  │
│  │  (Vanilla JS)│    │  Management │    │  Core        │  │
│  └──────┬──────┘    └──────┬──────┘    └──────┬───────┘  │
│         │                   │                   │         │
│  ┌──────▼──────┐    ┌──────▼──────┐    ┌──────▼───────┐  │
│  │  Signal     │    │  Strategy   │    │  Data        │  │
│  │  System     │    │  Manager    │    │  Processing  │  │
│  └─────────────┘    └─────────────┘    └──────────────┘  │
└───────────────────────────┬───────────────────────────────┘
                            │
                            │ WebSocket
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                      Backend (Node.js)                    │
│  ┌─────────────────────────────────────────────────────┐  │
│  │  WebSocket Server                                │  │
│  └───────────────────────────┬───────────────────────┘  │
│                              │                           │
│  ┌───────────────────────────▼───────────────────────┐  │
│  │  Market Data Processor                          │  │
│  └───────────────────────────┬───────────────────────┘  │
│                              │                           │
│  ┌───────────────────────────▼───────────────────────┐  │
│  │  Exchange API Connector                          │  │
│  └───────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────┘
```

### 1.2 Key Components

1. **WebSocket Core (`js/websocket-core.js`)**
   - Handles all WebSocket connections
   - Manages reconnection logic
   - Processes incoming and outgoing messages
   - Maintains connection state

2. **Signal System (`js/signal-manager.js`)**
   - Processes and manages trading signals
   - <PERSON>les signal visualization
   - Manages signal history

3. **Strategy Manager (`js/strategy-manager.js`)**
   - Manages trading strategies
   - Handles strategy configuration
   - Processes strategy updates

4. **UI Components**
   - Charting system
   - Signal matrix
   - Control panels
   - Theme system

## 2. WebSocket System

The WebSocket system is implemented in `js/websocket-core.js` and provides:

### Key Features

- Automatic reconnection
- Message queuing and batching
- Error handling and recovery
- Connection health monitoring

### Initialization

```javascript
const wsCore = new WebSocketCore({
    url: 'wss://your-websocket-endpoint',
    maxReconnectAttempts: 10,
    reconnectInterval: 1000,
    maxReconnectInterval: 30000,
    pingInterval: 30000,
    pongTimeout: 5000
});
```

## 3. Data Flow

1. **Market Data**
   - Received via WebSocket
   - Processed by data handlers
   - Distributed to UI components

2. **User Actions**
   - Captured by UI event handlers
   - Processed by appropriate managers
   - Sent to backend via WebSocket if needed

## 4. Style Guide

### CSS Organization

- `css/theme.css` - Global theming and variables
- `css/animations.css` - All animations and transitions
- `css/components/` - Component-specific styles
  - `chart.css` - Chart-related styles
  - `signals.css` - Signal display styles
  - `controls.css` - UI controls

## 5. Development Guidelines

### Code Style

- Use ES6+ features
- Follow consistent naming conventions
- Document public APIs with JSDoc
- Keep functions focused and small

### Error Handling

- Use try/catch for async operations
- Implement proper error boundaries
- Log errors with context
- Provide user feedback for recoverable errors

## 6. Deployment

### Production Build

1. Minify JavaScript and CSS
2. Optimize images
3. Enable gzip compression
4. Set appropriate cache headers

## 7. Troubleshooting

### Common Issues

1. **Connection Drops**
   - Check network stability
   - Verify WebSocket endpoint
   - Review server logs

2. **Performance Issues**
   - Check for memory leaks
   - Optimize render cycles
   - Review WebSocket message volume

## 8. Future Improvements

### Technical Debt

- [ ] Implement proper state management
- [ ] Add comprehensive tests
- [ ] Improve error recovery
- [ ] Optimize rendering performance

### Features

- [ ] Add more indicators
- [ ] Implement backtesting
- [ ] Add user authentication
- [ ] Support mobile devices

## 9. License

This project is proprietary software. All rights reserved.
