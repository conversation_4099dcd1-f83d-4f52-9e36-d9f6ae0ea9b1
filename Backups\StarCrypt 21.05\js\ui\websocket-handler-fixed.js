// websocket-handler-fixed.js
// Complete rewrite to solve maximum call stack size exceeded errors

(function () {
  // WebSocket connection variables
  const DEFAULT_PORT = 8080
  const pingInterval = null
  const reconnectTimeout = null
  let reconnectAttempts = 0
  const isReconnecting = false
  window.connectionError = null

  // Create a separate message processing system with a dedicated worker-like approach
  const messageQueue = []
  let isProcessingQueue = false
  const processingWorker = null
  const lastProcessedMessageId = 0

  // Process WebSocket message by adding to queue
  function processWebSocketMessage(data) {
    try {
      // Add message to queue as a stringified copy to break any object references
      if (data) {
        // Create a safe copy of the data to prevent circular references
        const safeData = JSON.parse(JSON.stringify(data))
        messageQueue.push(safeData)

        // Log for debugging
        console.log(`Added message to queue: ${safeData.type || 'unknown'}, Queue size: ${messageQueue.length}`)

        // Start processing queue if not already processing
        if (!isProcessingQueue) {
          // Use setTimeout to break the call stack
          setTimeout(processMessageQueue, 0)
        }
      }
    } catch (error) {
      console.error('Error adding message to queue:', error)
    }
  }

  // Set a maximum limit on consecutive processing to prevent browser hangs
  const MAX_CONSECUTIVE_PROCESSING = 10
  let consecutiveProcessingCount = 0

  // Process messages from the queue one at a time with additional safeguards
  function processMessageQueue() {
    // Reset the processing flag if the queue is empty
    if (messageQueue.length === 0) {
      console.log('Message queue empty, resetting processing state')
      isProcessingQueue = false
      consecutiveProcessingCount = 0
      return
    }

    // Set processing flag
    isProcessingQueue = true

    // Increment consecutive processing counter
    consecutiveProcessingCount++

    // If we've processed too many messages consecutively, take a breather
    if (consecutiveProcessingCount >= MAX_CONSECUTIVE_PROCESSING) {
      console.log(`Processed ${consecutiveProcessingCount} messages consecutively, taking a breather`)
      consecutiveProcessingCount = 0
      setTimeout(processMessageQueue, 50) // Longer timeout to give browser a break
      return
    }

    // Get the next message from the queue
    try {
      const data = messageQueue.shift()
      console.log(`Processing message: ${data?.type || 'unknown'}, Remaining in queue: ${messageQueue.length}`)

      // Skip logging for heartbeat messages
      if (data.type === 'heartbeat') {
        // Continue to next message immediately
        setTimeout(processMessageQueue, 0)
        return
      }

      // Handle empty or invalid data
      if (!data || typeof data !== 'object') {
        console.log('Received invalid WebSocket message data, ignoring')
        // Continue to next message
        setTimeout(processMessageQueue, 0)
        return
      }

      // Log message (except for high-frequency updates)
      if (data.type !== 'priceUpdate' && window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Received ${data.type || 'unknown'} message`)
        if (typeof window.updateLogger === 'function') window.updateLogger()
      }

      // Process message based on type
      switch (data.type) {
        case 'indicators':
          // Update indicators data
          if (data.indicators) {
            // Log diagnostics about what indicators we're receiving
            console.log('Received indicator data:', Object.keys(data.indicators).length, 'timeframes')
            for (const tf in data.indicators) {
              console.log(`Timeframe ${tf}:`, Object.keys(data.indicators[tf]).length, 'indicators')
            }

            // Make a deep copy to avoid reference issues
            try {
              window.indicatorsData = JSON.parse(JSON.stringify(data.indicators))
              console.log('Indicator data properly copied to window.indicatorsData')

              // Force update signal lights - ensure colors are applied
              console.log('Triggering signal light update...')

              // Update UI with new data after a short delay to ensure DOM is ready
              setTimeout(() => {
                if (typeof window.updateAllSignalLights === 'function') {
                  window.updateAllSignalLights()
                  console.log('Signal lights update triggered')
                } else {
                  console.error('updateAllSignalLights function not available')
                }

                if (typeof window.updateSignalMatrix === 'function') {
                  window.updateSignalMatrix()
                  console.log('Signal matrix update triggered')
                } else {
                  console.error('updateSignalMatrix function not available')
                }
              }, 100)
            } catch (error) {
              console.error('Error processing indicator data:', error)
            }
          } else {
            console.warn('Received indicators message without data')
          }
          break

        case 'price':
          // Update price
          if (data.price && !isNaN(parseFloat(data.price))) {
            window.currentPrice = parseFloat(data.price)
            // Update UI with new price
            if (typeof window.updatePriceDisplay === 'function') {
              window.updatePriceDisplay(window.currentPrice)
            }
          }
          break

        case 'strategy_update':
          // Update strategy
          if (data.strategy) {
            window.currentStrategy = data.strategy
            console.log(`Strategy updated to ${data.strategy}`)
            // Update UI with new strategy
            if (typeof window.updateStrategyDisplay === 'function') {
              window.updateStrategyDisplay(data.strategy)
            }
          }
          break

        case 'pair_update':
          // Update trading pair
          if (data.pair) {
            window.currentPair = data.pair
            console.log(`Trading pair updated to ${data.pair}`)
            // Update UI with new pair
            if (typeof window.updatePairDisplay === 'function') {
              window.updatePairDisplay(data.pair)
            }
          }
          break

        case 'alerts':
          // Update alerts
          if (data.alerts && Array.isArray(data.alerts)) {
            window.recentAlerts = data.alerts
            // Update UI with new alerts
            if (typeof window.updateRecentAlerts === 'function') {
              window.updateRecentAlerts(data.alerts)
            }
          }
          break

        case 'error':
          // Log error
          console.error(`Server error: ${data.message || 'Unknown error'}`)
          if (window.logMessages) {
            window.logMessages.push(`[${new Date().toLocaleString()}] Server error: ${data.message || 'Unknown error'}`)
            if (window.updateLogger) window.updateLogger()
          }
          break

        default:
          // Unknown message type
          console.log(`Unknown message type: ${data.type}`)
          break
      }
    } catch (error) {
      console.error('Error processing WebSocket message:', error)
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Error processing WebSocket message: ${error.message}`)
        if (typeof window.updateLogger === 'function') window.updateLogger()
      }
    } finally {
      // After processing a message (or handling an error), always schedule the next message processing
      // with a small delay to prevent stack overflow
      setTimeout(() => {
        try {
          processMessageQueue()
        } catch (e) {
          // Last resort error handling to ensure we don't break the message processing loop
          console.error('Fatal error in message processing loop:', e)
          isProcessingQueue = false // Reset processing flag

          // Try to restart the message processing after a longer delay
          setTimeout(() => {
            isProcessingQueue = false
            consecutiveProcessingCount = 0
            if (messageQueue.length > 0) {
              processMessageQueue()
            }
          }, 1000)
        }
      }, 10) // Slight delay to prevent immediate recursion
    }
  }

  // Request data from server - NO MOCK DATA, only real data
  window.requestServerData = function () {
    console.log('Requesting real data from server...')

    if (window.logMessages) {
      window.logMessages.push(`[${new Date().toLocaleString()}] Requesting real data from server`)
      if (window.updateLogger) window.updateLogger()
    }

    // Ensure we have a WebSocket connection
    if (!window.ws || window.ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocket not connected, cannot request data')
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] ERROR: WebSocket not connected, cannot request data`)
        if (window.updateLogger) window.updateLogger()
      }
      return false
    }

    // Request data for current strategy
    const strategy = window.currentStrategy || 'admiral_toa'

    // Send data request to server
    window.ws.send(JSON.stringify({
      type: 'data_request',
      strategy,
      timestamp: Date.now(),
    }))

    console.log(`Data request sent to server for strategy: ${strategy}`)
    if (window.logMessages) {
      window.logMessages.push(`[${new Date().toLocaleString()}] Data request sent to server for strategy: ${strategy}`)
      if (window.updateLogger) window.updateLogger()
    }

    return true
  }

  // Ensure WebSocket is connected before sending message
  window.ensureWebSocketAndSend = function (message) {
    if (window.ws && window.ws.readyState === WebSocket.OPEN) {
      window.ws.send(message)
      return true
    }
    // Try to reconnect
    connectWebSocket()

    // Queue message to be sent after connection
    setTimeout(() => {
      if (window.ws && window.ws.readyState === WebSocket.OPEN) {
        window.ws.send(message)
        return true
      }
      console.error('WebSocket still not connected, cannot send message')
      return false
    }, 1000)
  }

  // Request initial data from server
  function requestInitialData() {
    // Send initial data request
    window.ws.send(JSON.stringify({
      type: 'init',
      client: 'web',
      timestamp: Date.now(),
    }))

    console.log('Initial data request sent to server')
    if (window.logMessages) {
      window.logMessages.push(`[${new Date().toLocaleString()}] Initial data request sent to server`)
      if (window.updateLogger) window.updateLogger()
    }
  }

  // Handle connection failure
  function handleConnectionFailure() {
    // Attempt to reconnect
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++

      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Attempting to reconnect (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})...`)
        if (window.updateLogger) window.updateLogger()
      }

      // Try next port in the sequence
      setTimeout(() => connectWebSocket(), RECONNECT_DELAY)
    } else {
      // If we've tried all ports multiple times, show error message
      console.error('Maximum reconnection attempts reached. Server connection failed.')

      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] ERROR: Maximum reconnection attempts reached. Server connection failed.`)
        if (window.updateLogger) window.updateLogger()
      }

      // Show error message to user
      const errorContainer = document.getElementById('error-container')
      if (errorContainer) {
        errorContainer.innerHTML = `
          <div class="error-message">
            <h3>Server Connection Failed</h3>
            <p>Unable to connect to the data server after multiple attempts.</p>
            <p>Please check that the server is running and try again.</p>
            <button id="retry-connection" class="retry-button">Retry Connection</button>
          </div>
        `
        errorContainer.style.display = 'flex'

        // Add event listener to retry button
        const retryButton = document.getElementById('retry-connection')
        if (retryButton) {
          retryButton.addEventListener('click', () => {
            errorContainer.style.display = 'none'
            reconnectAttempts = 0
            connectWebSocket()
          })
        }
      }

      // Reset reconnect attempts for future tries
      setTimeout(() => {
        reconnectAttempts = 0
        connectWebSocket()
      }, 30000) // Try again after 30 seconds
    }
  }

  // Connect to WebSocket server
  window.connectWebSocket = function (specificPort) {
    console.log('Connecting to WebSocket server...')

    // Don't create multiple connections
    if (window.ws && window.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected')
      return
    }

    try {
      // Close existing connection if any
      if (window.ws) {
        window.ws.close()
      }

      // Use specific port if provided, otherwise use the next port in the sequence
      let port
      if (specificPort) {
        port = specificPort
      } else {
        // Try ports in sequence
        port = PORTS_TO_TRY[currentPortIndex]
        // Update for next attempt if needed
        currentPortIndex = (currentPortIndex + 1) % PORTS_TO_TRY.length
      }

      console.log(`Attempting to connect to WebSocket server on port ${port}`)
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Attempting WebSocket connection on port ${port}`)
        if (window.updateLogger) window.updateLogger()
      }

      // Create new WebSocket connection
      window.ws = new WebSocket(`ws://localhost:${port}`)

      // Store the port we're using
      window._wsPort = port

      // Set a connection timeout
      const connectionTimeout = setTimeout(() => {
        if (window.ws && window.ws.readyState !== WebSocket.OPEN) {
          console.log(`Connection timeout for port ${port}`)
          window.ws.close()
          handleConnectionFailure()
        }
      }, 3000) // 3 second timeout

      // Connection opened
      window.ws.addEventListener('open', (event) => {
        // Clear the connection timeout
        clearTimeout(connectionTimeout)

        console.log('WebSocket connection established')
        reconnectAttempts = 0

        // Log connection
        if (window.logMessages) {
          window.logMessages.push(`[${new Date().toLocaleString()}] WebSocket connection established`)
          if (window.updateLogger) window.updateLogger()
        }

        // Request initial data
        requestInitialData()
      })

      // Listen for messages
      window.ws.addEventListener('message', (event) => {
        try {
          // Check if event.data is empty or not a valid JSON string
          if (!event.data || typeof event.data !== 'string' || event.data.trim() === '') {
            console.log('Received empty or invalid WebSocket message, ignoring')
            return
          }

          const data = JSON.parse(event.data)

          // Validate that data is an object with a type property
          if (!data || typeof data !== 'object' || !data.type) {
            console.log('Received WebSocket message without proper type, ignoring:', data)
            return
          }

          // Process message (via queue)
          processWebSocketMessage(data)
        } catch (error) {
          // Only log the first few errors to avoid console spam
          if (!window._wsErrorCount) window._wsErrorCount = 0
          window._wsErrorCount++

          if (window._wsErrorCount <= 5) {
            console.error(`Error processing WebSocket message (${window._wsErrorCount}/5):`, error)

            if (window.logMessages) {
              window.logMessages.push(`[${new Date().toLocaleString()}] Error processing WebSocket message: ${error.message}`)
              if (window.updateLogger) window.updateLogger()
            }
          } else if (window._wsErrorCount === 6) {
            console.error('Too many WebSocket errors, suppressing further error messages')

            if (window.logMessages) {
              window.logMessages.push(`[${new Date().toLocaleString()}] Too many WebSocket errors, suppressing further error messages`)
              if (window.updateLogger) window.updateLogger()
            }
          }
        }
      })

      // Connection closed
      window.ws.addEventListener('close', (event) => {
        // Clear the connection timeout
        clearTimeout(connectionTimeout)

        console.log('WebSocket connection closed')

        if (window.logMessages) {
          window.logMessages.push(`[${new Date().toLocaleString()}] WebSocket connection closed on port ${window._wsPort}`)
          if (window.updateLogger) window.updateLogger()
        }

        // Handle connection failure
        handleConnectionFailure()
      })

      // Connection error
      window.ws.addEventListener('error', (event) => {
        // Clear the connection timeout
        clearTimeout(connectionTimeout)

        console.error('WebSocket error:', event)

        if (window.logMessages) {
          window.logMessages.push(`[${new Date().toLocaleString()}] WebSocket error on port ${window._wsPort}`)
          if (window.updateLogger) window.updateLogger()
        }

        // Don't try to reconnect here - let the close event handle it
        // This prevents duplicate reconnection attempts
      })
    } catch (error) {
      console.error('Error connecting to WebSocket:', error)

      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Error connecting to WebSocket: ${error.message}`)
        if (window.updateLogger) window.updateLogger()
      }

      // Handle connection failure
      handleConnectionFailure()
    }
  }

  // Connect WebSocket when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', connectWebSocket)
  } else {
    // DOM already loaded
    connectWebSocket()
  }
})()
