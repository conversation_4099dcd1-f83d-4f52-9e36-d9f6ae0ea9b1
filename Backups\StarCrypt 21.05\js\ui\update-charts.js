// update-charts.js
// Modularized updateCharts logic for StarCrypt
// Ensures Chart.js receives correct data formats and logs all chart updates for debugging

(function () {
  // Defensive logging utility
  function logChartData(label, data) {
    if (window.logMessages) {
      window.logMessages.push(`[${new Date().toLocaleString()}] [ChartLog] ${label}: ${JSON.stringify(data)}`)
      if (window.updateLogger) window.updateLogger()
    } else {
      console.log(`[ChartLog] ${label}:`, data)
    }
  }

  // Utility: ensure arrays for Chart.js
  function toArray(val) {
    if (Array.isArray(val)) return val
    if (val == null) return []
    return [val]
  }

  // Main chart update logic
  window.updateCharts = function (tf, pair) {
    // Update TradingView chart when coin changes
    updateTradingViewChart(pair)

    // Update main chart
    const mainChartCanvas = document.getElementById('mainChart')
    if (!mainChartCanvas) {
      logChartData('Main chart canvas not found', { tf, pair })
      return
    }
    const ctx = mainChartCanvas.getContext('2d')
    if (!ctx) {
      logChartData('Main chart context not found', { tf, pair })
      return
    }

    // Get data from global
    const chartData = (window.chartDataSources && window.chartDataSources[tf] && window.chartDataSources[tf][pair]) || {}
    // Defensive: ensure arrays for Chart.js
    const labels = toArray(chartData.labels)
    const datasets = Array.isArray(chartData.datasets) ? chartData.datasets : (chartData.datasets ? [chartData.datasets] : [])

    // Log data before passing to Chart.js
    logChartData('Preparing to update Chart.js', { tf, pair, dataPoints: labels.length })

    // If a Chart.js instance exists, destroy it first (avoid context errors)
    if (window.mainChartInstance && typeof window.mainChartInstance.destroy === 'function') {
      window.mainChartInstance.destroy()
    }

    // Defensive: avoid empty chart
    if (labels.length === 0 || datasets.length === 0) {
      logChartData('No data for chart', { tf, pair })
      return
    }

    // Create new Chart.js chart with enhanced options
    window.mainChartInstance = new Chart(ctx, {
      type: 'line',
      data: {
        labels,
        datasets,
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: {
          duration: 500,
          easing: 'easeOutQuart',
        },
        plugins: {
          legend: {
            display: true,
            labels: {
              color: '#00FFFF',
              font: {
                family: 'Orbitron',
                size: 12,
              },
            },
          },
          tooltip: {
            enabled: true,
            backgroundColor: 'rgba(0, 10, 20, 0.9)',
            titleColor: '#00FFFF',
            bodyColor: '#FFFFFF',
            borderColor: '#00FFFF',
            borderWidth: 1,
            padding: 10,
            displayColors: true,
            titleFont: {
              family: 'Orbitron',
              size: 14,
              weight: 'bold',
            },
            bodyFont: {
              family: 'Orbitron',
              size: 12,
            },
          },
        },
        scales: {
          x: {
            display: true,
            grid: {
              color: 'rgba(0, 255, 255, 0.1)',
              borderColor: 'rgba(0, 255, 255, 0.3)',
            },
            ticks: {
              color: '#00FFFF',
              font: {
                family: 'Orbitron',
                size: 10,
              },
            },
          },
          y: {
            display: true,
            grid: {
              color: 'rgba(0, 255, 255, 0.1)',
              borderColor: 'rgba(0, 255, 255, 0.3)',
            },
            ticks: {
              color: '#00FFFF',
              font: {
                family: 'Orbitron',
                size: 10,
              },
            },
          },
        },
      },
    })
    logChartData('Chart.js updated', { tf, pair, dataPoints: labels.length })

    // Update mini charts after main chart
    updateMiniCharts(pair)
  }

  // Update TradingView chart
  function updateTradingViewChart(pair) {
    // Convert pair to TradingView format
    const pairMap = {
      xbtusdt: 'KRAKEN:XBTUSD',
      ethusdt: 'KRAKEN:ETHUSD',
      ltcusdt: 'KRAKEN:LTCUSD',
      xrpusdt: 'KRAKEN:XRPUSD',
      adausdt: 'KRAKEN:ADAUSD',
      solusdt: 'KRAKEN:SOLUSD',
      dotusdt: 'KRAKEN:DOTUSD',
      dogeusdt: 'KRAKEN:DOGEUSD',
    }

    const tvSymbol = pairMap[pair] || 'KRAKEN:XBTUSD'

    // Check if TradingView widget exists
    if (window.tradingViewWidget) {
      // Update the existing widget with new symbol
      try {
        window.tradingViewWidget.setSymbol(tvSymbol, {
          interval: '60', // Default to 1h
          timeframe: '1D',
        })
        logChartData('TradingView chart updated', { pair, tvSymbol })
      } catch (error) {
        logChartData('Error updating TradingView chart', { error: error.message })

        // If updating fails, recreate the widget
        createTradingViewWidget(tvSymbol)
      }
    } else {
      // Create new widget
      createTradingViewWidget(tvSymbol)
    }
  }

  // Create TradingView widget
  function createTradingViewWidget(symbol) {
    const container = document.getElementById('tradingview-container')
    if (!container) {
      logChartData('TradingView container not found', {})
      return
    }

    // Clear container
    container.innerHTML = ''

    // Create new widget
    try {
      window.tradingViewWidget = new TradingView.widget({
        container_id: 'tradingview-container',
        symbol,
        interval: '60', // Default to 1h
        timezone: 'exchange',
        theme: 'dark',
        style: '1',
        locale: 'en',
        toolbar_bg: '#0a0a1a',
        enable_publishing: false,
        hide_top_toolbar: false,
        hide_side_toolbar: false,
        allow_symbol_change: true,
        save_image: true,
        studies: ['RSI@tv-basicstudies', 'MACD@tv-basicstudies'],
        show_popup_button: true,
        popup_width: '1000',
        popup_height: '650',
        autosize: true,
      })
      logChartData('TradingView widget created', { symbol })
    } catch (error) {
      logChartData('Error creating TradingView widget', { error: error.message })
    }
  }

  // Enhanced mini chart creation with data validation to prevent 't.filter is not a function' errors
  window.createMiniChart = function (containerId, data, options = {}) {
    // Enhanced data validation for Chart.js compatibility
    if (!Array.isArray(data.values) || data.values.length === 0) {
      logChartData('Skipping mini chart creation due to empty or invalid data', { containerId, data })
      return null // Abort if no data or invalid
    }
    if (!Array.isArray(data.labels)) {
      logChartData(`Mini chart data.labels is not an array for ${containerId}:`, data)
      return null // Prevent chart creation if data is invalid
    }
    const container = document.getElementById(containerId)
    if (!container) {
      logChartData(`Mini chart container not found: ${containerId}`, {})
      return null
    }
    // Clear existing canvas
    container.innerHTML = ''
    // Create canvas element (must be a canvas for Chart.js)
    const canvas = document.createElement('canvas')
    canvas.width = options.width || 100
    canvas.height = options.height || 40
    canvas.className = 'mini-chart-canvas'
    container.appendChild(canvas)

    const ctx = canvas.getContext('2d')
    if (!ctx) {
      logChartData(`Mini chart context not found for ${containerId}`, {})
      return null
    }

    // Log data and options for debugging hover errors
    logChartData('Mini chart data before creation', { containerId, data, options })

    // Prepare chart data with validation
    const chartData = {
      labels: data.labels,
      datasets: [{
        label: options.label || '',
        data: data.values,
        borderColor: 'rgba(0, 123, 255, 0.8)', // Default color, can be overridden
        backgroundColor: 'rgba(0, 123, 255, 0.5)',
        borderWidth: 1,
        fill: true,
      }],
    }

    // Chart options with tooltip callback for safe filtering
    const chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      animation: { duration: 0 }, // Disable animation for performance
      plugins: {
        legend: { display: false },
        tooltip: {
          enabled: true,
          callbacks: {
            title(tooltipItems) {
              try {
                if (tooltipItems && tooltipItems.length > 0) {
                  return tooltipItems[0].label || options.label || ''
                }
                return ''
              } catch (e) {
                console.error('Error in tooltip title callback:', e)
                return ''
              }
            },
            label(context) {
              try {
                let label = context.dataset.label || ''
                if (label) {
                  label += ': '
                }
                if (context.parsed.y !== null) {
                  label += context.parsed.y
                }
                return label
              } catch (e) {
                console.error('Error in tooltip label callback:', e)
                return 'Invalid data'
              }
            },
          },
        },
      },
      scales: {
        x: { display: false },
        y: { display: false },
      },
    }

    // Apply options overrides
    if (options.signalType) {
      // Set color based on signal for consistency
      if (options.signalType === 'buy') {
        chartData.datasets[0].borderColor = 'rgba(0, 255, 0, 0.8)'
        chartData.datasets[0].backgroundColor = 'rgba(0, 255, 0, 0.2)'
      } else if (options.signalType === 'sell') {
        chartData.datasets[0].borderColor = 'rgba(255, 0, 0, 0.8)'
        chartData.datasets[0].backgroundColor = 'rgba(255, 0, 0, 0.2)'
      } else {
        chartData.datasets[0].borderColor = 'rgba(128, 128, 128, 0.8)'
        chartData.datasets[0].backgroundColor = 'rgba(128, 128, 128, 0.2)'
      }
    }

    // Create and store chart instance
    const miniChart = new Chart(ctx, {
      type: 'line', // Ensure line chart type
      data: chartData,
      options: { ...chartOptions, ...options.chartOptions }, // Merge user options
    })

    logChartData(`Mini chart created: ${containerId}`, { dataPoints: data.values.length })
    return miniChart
  }

  // Update all mini charts
  function updateMiniCharts(pair) {
    // Get indicator data for the current pair and timeframe
    const tf = window.currentTf || '1h'
    const indicatorData = window.indicatorsData?.[tf] || {}

    // Update mini charts for each indicator
    Object.keys(indicatorData).forEach(indicator => {
      const data = indicatorData[indicator]
      if (!data || !data.history) return

      // Get or create container for this indicator
      let container = document.getElementById(`mini-chart-${indicator}`)
      if (!container) {
        container = document.createElement('div')
        container.id = `mini-chart-${indicator}`
        // Append to a parent container, assuming 'mini-charts-container' based on app structure
        const parentContainer = document.getElementById('mini-charts-container')
        if (parentContainer) {
          parentContainer.appendChild(container)
          logChartData(`Created missing container for ${indicator}`, { containerId: `mini-chart-${indicator}` })
        } else {
          logChartData(`Parent container 'mini-charts-container' not found for ${indicator}`, {})
          return // Skip if parent not found
        }
      }

      // Now create or update the mini chart
      const miniChartInstance = createMiniChart(`mini-chart-${indicator}`, {
        labels: Array(data.history.length).fill(''),
        values: data.history,
      }, { signalType: indicator })

      logChartData(`Mini chart created or updated: ${indicator}`, { dataPoints: data.history.length })
    })

    logChartData('All mini charts updated', { pair, tf })
  }

  // Initialize charts when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      // Initial chart setup
      const initialPair = window.currentPair || 'xbtusdt'
      const initialTf = window.currentTf || '1h'
      window.updateCharts(initialTf, initialPair)
    })
  } else {
    // DOM already loaded
    const initialPair = window.currentPair || 'xbtusdt'
    const initialTf = window.currentTf || '1h'
    window.updateCharts(initialTf, initialPair)
  }
})()
