/**
 * @module ThresholdMenu
 * @description Menu system for indicator thresholds
 */

import { MenuController } from './menu-controller.js'
import { MenuUtils } from './menu-utils.js'
import { ThresholdManager } from '../threshold-manager.js'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/error-handler.js'
import { PerformanceMonitor } from '../utils/performance-monitor.js'

export class ThresholdMenu extends MenuController {
  constructor() {
    super()

    // Threshold-specific configuration
    this.config = {
      menuId: 'threshold-menu',
      buttonId: 'threshold-button',
      containerId: 'threshold-container',
      helperId: 'threshold-helper',
      animationDuration: 300,
      maxThresholds: 10,
      showHelp: true,
      defaultThresholds: {
        upper: 70,
        lower: 30,
      },
    }

    // Threshold manager
    this.thresholdManager = new ThresholdManager()

    // Error handling
    this.errorHandler = new ErrorHandler({
      maxErrors: 5,
      recoveryDelay: 2000,
      errorThreshold: 0.1,
    })

    // Performance monitoring
    this.performance = new PerformanceMonitor({
      targetFPS: 60,
      maxLatency: 100,
      batchInterval: 100,
    })

    // Menu state
    this.state = {
      thresholds: {},
      currentIndicator: null,
      isInitialized: false,
      isLoading: false,
      error: null,
    }
  }

  /**
     * Initialize threshold menu
     */
  async init() {
    try {
      // Initialize base menu
      await super.init()

      // Initialize threshold manager
      await this.thresholdManager.init()

      // Create menu structure
      this.createMenuStructure()

      // Add event listeners
      this.setupEventListeners()

      // Set initial state
      this.state.isInitialized = true
      this.state.thresholds = this.thresholdManager.getThresholds()

      // Dispatch initialized event
      this.dispatch('initialized', {
        thresholds: this.state.thresholds,
        currentIndicator: this.state.currentIndicator,
      })
    } catch (error) {
      this.handleError(error, 'init')
    }
  }

  /**
     * Create menu structure
     * @private
     */
  createMenuStructure() {
    try {
      // Get menu element
      const menu = MenuUtils.getMenuElement(this.config.menuId)
      if (!menu) return

      // Clear existing items
      menu.innerHTML = ''

      // Add threshold controls
      this.addThresholdControls()

      // Add helper content if enabled
      if (this.config.showHelp) {
        this.addHelperContent()
      }
    } catch (error) {
      this.handleError(error, 'create-menu-structure')
    }
  }

  /**
     * Add threshold controls
     * @private
     */
  addThresholdControls() {
    try {
      // Get menu element
      const menu = MenuUtils.getMenuElement(this.config.menuId)
      if (!menu) return

      // Add threshold sliders
      const sliderConfig = {
        className: 'threshold-slider',
        style: {
          width: '100%',
          height: '4px',
          borderRadius: '2px',
          margin: '8px 0',
        },
      }

      // Add upper threshold
      const upper = MenuUtils.addMenuItem(this.config.menuId, {
        id: 'upper-threshold',
        text: 'Upper Threshold',
        className: 'threshold-item',
        style: {
          padding: '8px 16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        },
        events: {
          input: (e) => this.handleThresholdChange('upper', e.target.value),
        },
      })

      // Add upper slider
      const upperSlider = MenuUtils.addMenuItem(this.config.menuId, {
        ...sliderConfig,
        id: 'upper-slider',
        type: 'range',
        min: '30',
        max: '100',
        value: this.config.defaultThresholds.upper,
        className: 'threshold-slider upper',
      })

      // Add lower threshold
      const lower = MenuUtils.addMenuItem(this.config.menuId, {
        id: 'lower-threshold',
        text: 'Lower Threshold',
        className: 'threshold-item',
        style: {
          padding: '8px 16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        },
        events: {
          input: (e) => this.handleThresholdChange('lower', e.target.value),
        },
      })

      // Add lower slider
      const lowerSlider = MenuUtils.addMenuItem(this.config.menuId, {
        ...sliderConfig,
        id: 'lower-slider',
        type: 'range',
        min: '0',
        max: '70',
        value: this.config.defaultThresholds.lower,
        className: 'threshold-slider lower',
      })

      // Add ARIA attributes
      upper.setAttribute('role', 'slider')
      lower.setAttribute('role', 'slider')
    } catch (error) {
      this.handleError(error, 'add-threshold-controls')
    }
  }

  /**
     * Add helper content
     * @private
     */
  addHelperContent() {
    try {
      // Get helper element
      const helper = document.getElementById(this.config.helperId)
      if (!helper) return

      // Add helper content
      helper.innerHTML = `
                <h3>Threshold Configuration Guide</h3>
                <p>Configure indicator thresholds to define signal conditions.</p>
                <ul>
                    <li>Drag sliders to adjust thresholds</li>
                    <li>Upper threshold: Signal becomes overbought</li>
                    <li>Lower threshold: Signal becomes oversold</li>
                </ul>
            `

      // Add ARIA attributes
      helper.setAttribute('role', 'region')
      helper.setAttribute('aria-label', 'Threshold configuration guide')
    } catch (error) {
      this.handleError(error, 'add-helper-content')
    }
  }

  /**
     * Handle threshold change
     * @param {string} type - Threshold type (upper/lower)
     * @param {number} value - New threshold value
     * @private
     */
  async handleThresholdChange(type, value) {
    try {
      // Set loading state
      this.state.isLoading = true

      // Update threshold
      await this.thresholdManager.setThreshold(type, value)

      // Update menu state
      this.state.thresholds = this.thresholdManager.getThresholds()

      // Update UI
      this.updateUI()

      // Dispatch change event
      this.dispatch('threshold-change', {
        type,
        value,
        thresholds: this.state.thresholds,
        timestamp: Date.now(),
      })
    } catch (error) {
      this.handleError(error, 'handle-threshold-change')
    } finally {
      this.state.isLoading = false
    }
  }

  /**
     * Update UI based on current state
     * @private
     */
  updateUI() {
    try {
      // Update threshold values
      const upperSlider = document.getElementById('upper-slider')
      const lowerSlider = document.getElementById('lower-slider')

      if (upperSlider) {
        upperSlider.value = this.state.thresholds.upper
      }

      if (lowerSlider) {
        lowerSlider.value = this.state.thresholds.lower
      }

      // Update button text
      const button = document.getElementById(this.config.buttonId)
      if (button) {
        button.textContent = `Thresholds: ${this.state.thresholds.lower} - ${this.state.thresholds.upper}`
      }
    } catch (error) {
      this.handleError(error, 'update-ui')
    }
  }

  /**
     * Setup event listeners
     * @private
     */
  setupEventListeners() {
    try {
      // Listen for threshold changes
      this.thresholdManager.on('threshold-change', (data) => {
        this.handleThresholdChange(data.type, data.value)
      })

      // Listen for menu events
      this.on('menu-close', () => this.handleMenuClose())
      this.on('error', (error) => this.handleError(error))
    } catch (error) {
      this.handleError(error, 'setup-event-listeners')
    }
  }

  /**
     * Handle menu close
     * @private
     */
  handleMenuClose() {
    try {
      // Reset helper content
      this.resetHelperContent()
    } catch (error) {
      this.handleError(error, 'handle-menu-close')
    }
  }

  /**
     * Handle errors
     * @param {Error} error - Error object
     * @param {string} context - Context where error occurred
     * @private
     */
  handleError(error, context) {
    try {
      // Log error
      console.error(`[ThresholdMenu] Error in ${context}:`, error)

      // Track error
      this.errorHandler.track(error, context)

      // Set error state
      this.state.error = error

      // Dispatch error event
      this.dispatch('error', {
        error,
        context,
        timestamp: Date.now(),
      })

      // Try to recover
      if (this.errorHandler.shouldRecover()) {
        this.recoverFromError()
      }
    } catch (error) {
      console.error('[ThresholdMenu] Error handling failed:', error)
    }
  }

  /**
     * Attempt to recover from error
     * @private
     */
  recoverFromError() {
    try {
      // Reset state
      this.state = {
        thresholds: {},
        currentIndicator: null,
        isInitialized: false,
        isLoading: false,
        error: null,
      }

      // Reinitialize
      this.init()
    } catch (error) {
      console.error('[ThresholdMenu] Recovery failed:', error)
    }
  }

  /**
     * Clean up resources
     */
  cleanup() {
    try {
      // Cleanup base menu
      super.cleanup()

      // Cleanup threshold manager
      this.thresholdManager.cleanup()

      // Reset state
      this.state = {
        thresholds: {},
        currentIndicator: null,
        isInitialized: false,
        isLoading: false,
        error: null,
      }

      // Reset error handler
      this.errorHandler.reset()

      // Reset performance monitor
      this.performance.reset()
    } catch (error) {
      console.error('[ThresholdMenu] Cleanup failed:', error)
    }
  }

  /**
     * Destroy menu
     */
  destroy() {
    try {
      // Cleanup
      this.cleanup()

      // Reset references
      this.thresholdManager = null
      this.errorHandler = null
      this.performance = null
    } catch (error) {
      console.error('[ThresholdMenu] Destruction failed:', error)
    }
  }
}

// Export singleton instance
const thresholdMenu = new ThresholdMenu()
export default thresholdMenu
