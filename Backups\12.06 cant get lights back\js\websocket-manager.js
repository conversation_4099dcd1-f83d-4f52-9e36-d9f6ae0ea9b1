/**
 * WebSocket Manager - Handles WebSocket connections with automatic reconnection
 * and message queueing
 */
class WebSocketManager {
    constructor(url, protocols = []) {
        // Connection settings
        this.url = url;
        this.protocols = protocols;
        this.ws = null;
        
        // Reconnection settings
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectInterval = 1000; // Start with 1 second
        this.maxReconnectInterval = 30000; // Max 30 seconds
        this.connectionTimeout = 5000; // 5 second connection timeout
        this.connectionTimeoutId = null;
        this.shouldReconnect = true;
        
        // Message handling
        this.messageQueue = [];
        this.messageHandlers = new Map();
        this.processedMessages = new Set();
        this.messageCounter = 0;
        this.maxProcessedMessages = 1000;
        this.processingDepth = 0; // Track recursive call depth
        this.maxProcessingDepth = 10; // Maximum allowed recursive depth
        
        // Connection state
        this.isConnected = false;
        this.isConnecting = false;
        
        // Ping/pong
        this.pingInterval = 30000; // 30 seconds
        this.pingTimeout = 10000; // 10 seconds to receive pong
        this.pingIntervalId = null;
        this.pingTimeoutId = null;
        this.lastPongTime = 0;
        
        // Error tracking
        this.consecutiveErrors = 0;
        this.maxConsecutiveErrors = 5;
        this.errorResetTime = 30000; // 30 seconds to reset error count
        
        // Bind methods
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.send = this.send.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onOpen = this.onOpen.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
        this.handlePing = this.handlePing.bind(this);
        this.handlePong = this.handlePong.bind(this);
        this.checkConnection = this.checkConnection.bind(this);
    }
    
    /**
     * Connect to the WebSocket server
     */
    connect() {
        if (this.isConnected || this.isConnecting) {
            console.log('[WebSocket] Already connected or connecting');
            return;
        }
        
        this.isConnecting = true;
        console.log(`[WebSocket] Connecting to ${this.url}...`);
        
        try {
            this.ws = new WebSocket(this.url, this.protocols);
            this.ws.binaryType = 'arraybuffer';
            
            // Set up event handlers
            this.ws.onopen = this.onOpen;
            this.ws.onmessage = this.onMessage;
            this.ws.onclose = this.onClose;
            this.ws.onerror = this.onError;
            
            // Set connection timeout
            this.connectionTimeoutId = setTimeout(() => {
                if (!this.isConnected) {
                    console.warn('[WebSocket] Connection timeout');
                    this.ws.close();
                }
            }, this.connectionTimeout);
            
        } catch (error) {
            console.error('[WebSocket] Error creating WebSocket:', error);
            this.isConnecting = false;
            this.scheduleReconnect();
        }
    }
    
    /**
     * Disconnect from the WebSocket server with proper cleanup
     */
    disconnect() {
        if (!this.ws) {
            console.log('[WebSocket] No WebSocket instance to disconnect');
            return;
        }

        this.shouldReconnect = false;
        this.clearTimers();
        
        try {
            if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
                this.ws.close(1000, 'User disconnected');
            }
        } catch (error) {
            console.error('[WebSocket] Error during disconnect:', error);
        }
        
        // Ensure WebSocket is properly cleaned up
        this.ws = null;
        this.isConnected = false;
        this.isConnecting = false;
        
        // Clear any remaining message handlers
        this.messageHandlers.clear();
        this.messageQueue.length = 0;
        
        // Reset processing state
        this.processingDepth = 0;
        this.consecutiveErrors = 0;
    }
    
    /**
     * Send a message through the WebSocket
     * @param {Object} data - The data to send
     * @returns {Boolean} - Whether the message was sent or queued
     */
    send(data) {
        if (!this.isConnected) {
            console.log('[WebSocket] Not connected, queueing message:', data);
            this.messageQueue.push(data);
            return false;
        }
        
        try {
            const message = typeof data === 'string' ? data : JSON.stringify(data);
            this.ws.send(message);
            return true;
        } catch (error) {
            console.error('[WebSocket] Error sending message:', error);
            this.messageQueue.push(data);
            return false;
        }
    }
    
    /**
     * Register a message handler for a specific message type
     * @param {String} type - The message type to handle
     * @param {Function} handler - The handler function
     */
    onMessageType(type, handler) {
        if (typeof handler !== 'function') {
            console.error('[WebSocket] Handler must be a function');
            return;
        }
        
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, new Set());
        }
        
        this.messageHandlers.get(type).add(handler);
        
        // Return unsubscribe function
        return () => {
            if (this.messageHandlers.has(type)) {
                this.messageHandlers.get(type).delete(handler);
            }
        };
    }
    
    /**
     * Handle WebSocket open event
     */
    onOpen() {
        console.log('[WebSocket] Connected');
        
        clearTimeout(this.connectionTimeoutId);
        this.isConnected = true;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.lastPongTime = Date.now();
        
        // Start ping/pong
        this.startPingPong();
        
        // Process any queued messages
        this.processQueue();
        
        // Dispatch connected event
        this.dispatchEvent('connected');
    }
    
    /**
     * Handle WebSocket message event with anti-recursion protection
     * @param {MessageEvent} event - The message event
     */
    async onMessage(event) {
        // Track recursion depth to prevent stack overflows
        this.processingDepth = (this.processingDepth || 0) + 1;
        const messageDepth = this.processingDepth;
        
        // Safety check - prevent infinite recursion
        if (messageDepth > this.maxProcessingDepth) {
            console.error(`[WebSocket] Max processing depth (${this.maxProcessingDepth}) exceeded, dropping message`);
            this.processingDepth--;
            return;
        }

        try {
            // Check error threshold
            if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                const timeSinceLastError = Date.now() - (this.lastErrorTime || 0);
                if (timeSinceLastError < this.errorResetTime) {
                    console.warn(`[WebSocket] Too many consecutive errors (${this.consecutiveErrors}), dropping message`);
                    return;
                } else {
                    // Reset error counter after cooldown
                    this.consecutiveErrors = 0;
                    console.log('[WebSocket] Error cooldown expired, resuming message processing');
                }
            }

            let data = event.data;
            
            // Generate a unique ID for this message
            const messageId = `msg_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
            
            // Skip if we've already processed this message
            if (this.processedMessages.has(messageId)) {
                console.debug(`[WebSocket] Skipping duplicate message: ${messageId}`);
                this.processingDepth--;
                return;
            }
            
            try {
                // Handle binary data if needed
                if (data instanceof ArrayBuffer) {
                    data = this.handleBinaryData(data);
                } else if (typeof data === 'string') {
                    try {
                        data = JSON.parse(data);
                    } catch (e) {
                        // Not JSON, use as is
                        console.debug('[WebSocket] Received non-JSON message:', data);
                    }
                }
                
                // Add message metadata
                if (data && typeof data === 'object') {
                    // Don't overwrite existing IDs to prevent message loops
                    data._messageId = data._messageId || messageId;
                    data._receivedAt = Date.now();
                    data._processingDepth = messageDepth;
                    
                    // Add to processed messages with cleanup
                    this.processedMessages.add(messageId);
                    if (this.processedMessages.size > this.maxProcessedMessages) {
                        // Remove oldest messages first
                        const firstId = Array.from(this.processedMessages)[0];
                        this.processedMessages.delete(firstId);
                    }
                }
                
                // Handle pong messages immediately
                if (data && data.type === 'pong') {
                    this.handlePong(data);
                    this.processingDepth--;
                    return;
                }
                
                // Process the message asynchronously to prevent stack buildup
                await this.processMessageWithRecovery(data, messageId, messageDepth);
                
            } catch (parseError) {
                this.handleProcessingError(parseError, 'parse', {
                    messageId,
                    rawData: event.data,
                    depth: messageDepth
                });
            }
            
        } catch (error) {
            this.handleProcessingError(error, 'process', {
                depth: this.processingDepth
            });
            
        } finally {
            // Always decrement processing depth
            this.processingDepth = Math.max(0, (this.processingDepth || 1) - 1);
        }
    }
    
    /**
     * Process a message with error recovery and recursion protection
     * @param {Object} data - The message data to process
     * @param {string} messageId - Unique message ID
     * @param {number} depth - Current processing depth
     */
    async processMessageWithRecovery(data, messageId, depth) {
        try {
            // Skip if this is a system message that's already been processed
            if (data && data.type && data._wsProcessed) {
                console.debug(`[WebSocket] Skipping already processed message: ${data.type}`);
                return;
            }
            
            // Mark as processed to prevent loops
            if (data && typeof data === 'object') {
                data._wsProcessed = true;
            }
            
            // Process message based on type
            if (data && data.type && this.messageHandlers.has(data.type)) {
                const handlers = Array.from(this.messageHandlers.get(data.type));
                
                // Process handlers in sequence with small delays
                for (const handler of handlers) {
                    try {
                        // Add a small delay to prevent stack overflow
                        await new Promise(resolve => setTimeout(resolve, 1));
                        
                        // Execute handler with timeout
                        await Promise.race([
                            handler(data),
                            new Promise((_, reject) => 
                                setTimeout(() => reject(new Error('Handler timeout')), 5000)
                            )
                        ]);
                        
                        this.consecutiveErrors = 0; // Reset on successful processing
                        
                    } catch (error) {
                        this.consecutiveErrors++;
                        this.lastErrorTime = Date.now();
                        
                        console.error(`[WebSocket] Error in handler for ${data.type} (${this.consecutiveErrors}/${this.maxConsecutiveErrors}):`, {
                            error,
                            messageId,
                            handler: handler.name || 'anonymous',
                            depth
                        });
                        
                        if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                            console.error(`[WebSocket] Max consecutive errors reached, pausing message processing`);
                            this.dispatchEvent('error', {
                                type: 'max_errors_reached',
                                message: 'Maximum consecutive errors reached',
                                count: this.consecutiveErrors,
                                lastError: error.message
                            });
                            break;
                        }
                    }
                }
            }
            
            // Dispatch to global handler if not a system message
            if (!data || !data.type || !data.type.startsWith('system.')) {
                try {
                    this.dispatchEvent('message', data);
                } catch (dispatchError) {
                    console.error('[WebSocket] Error in global message handler:', dispatchError);
                }
            }
            
        } catch (error) {
            this.handleProcessingError(error, 'process_message', {
                messageId,
                depth,
                messageType: data?.type
            });
        }
    }
    
    /**
     * Handle processing errors consistently
     * @param {Error} error - The error that occurred
     * @param {string} stage - The processing stage where the error occurred
     * @param {Object} context - Additional context about the error
     */
    handleProcessingError(error, stage, context = {}) {
        this.consecutiveErrors++;
        this.lastErrorTime = Date.now();
        
        const errorInfo = {
            error: error.message,
            stack: error.stack,
            stage,
            ...context,
            consecutiveErrors: this.consecutiveErrors,
            timestamp: new Date().toISOString()
        };
        
        console.error(`[WebSocket] Error in ${stage}:`, errorInfo);
        
        // Dispatch error event
        try {
            this.dispatchEvent('error', {
                type: 'processing_error',
                message: `Error in ${stage}`,
                ...errorInfo
            });
        } catch (dispatchError) {
            console.error('[WebSocket] Error dispatching error event:', dispatchError);
        }
        
        // If we have too many errors, consider reconnecting
        if (this.consecutiveErrors >= this.maxConsecutiveErrors * 2) {
            console.warn('[WebSocket] Critical error threshold reached, attempting to reconnect...');
            this.reconnect();
        }
    }
    
    /**
     * Handle WebSocket close event
     */
    onClose(event) {
        console.log(`[WebSocket] Disconnected (code: ${event.code}, reason: ${event.reason || 'No reason provided'})`);
        
        this.clearTimers();
        this.isConnected = false;
        this.isConnecting = false;
        
        if (this.shouldReconnect) {
            this.scheduleReconnect();
        }
        
        // Dispatch disconnected event
        this.dispatchEvent('disconnected', {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean
        });
    }
    
    /**
     * Handle WebSocket error event
     */
    onError(error) {
        console.error('[WebSocket] Error:', error);
        this.dispatchEvent('error', error);
    }
    
    /**
     * Process the message queue
     */
    processQueue() {
        if (!this.isConnected || this.messageQueue.length === 0) {
            return;
        }
        
        // Process messages in chunks to avoid blocking
        const chunkSize = 10;
        const chunk = this.messageQueue.splice(0, chunkSize);
        
        for (const message of chunk) {
            this.send(message);
        }
        
        // Process next chunk on next tick
        if (this.messageQueue.length > 0) {
            setTimeout(() => this.processQueue(), 0);
        }
    }
    
    /**
     * Schedule a reconnection attempt
     */
    scheduleReconnect() {
        if (!this.shouldReconnect || this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.warn('[WebSocket] Max reconnection attempts reached');
            this.dispatchEvent('reconnect_failed');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = Math.min(
            this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
            this.maxReconnectInterval
        );
        
        console.log(`[WebSocket] Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (!this.isConnected && !this.isConnecting) {
                this.connect();
            }
        }, delay);
    }
    
    /**
     * Start ping/pong mechanism
     */
    startPingPong() {
        // Clear any existing timers
        this.clearTimers();
        
        // Send initial ping
        this.handlePing();
        
        // Set up ping interval
        this.pingIntervalId = setInterval(() => {
            this.handlePing();
        }, this.pingInterval);
    }
    
    /**
     * Handle ping
     */
    handlePing() {
        if (!this.isConnected) return;
        
        // Check if we've missed too many pongs
        if (Date.now() - this.lastPongTime > this.pingInterval + this.pingTimeout) {
            console.warn('[WebSocket] No pong received, forcing reconnect');
            this.ws.close(4000, 'No pong received');
            return;
        }
        
        try {
            // Send ping
            this.ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
            
            // Set pong timeout
            if (this.pingTimeoutId) {
                clearTimeout(this.pingTimeoutId);
            }
            
            this.pingTimeoutId = setTimeout(() => {
                console.warn('[WebSocket] Pong timeout, reconnecting...');
                this.ws.close(4001, 'Pong timeout');
            }, this.pingTimeout);
            
        } catch (error) {
            console.error('[WebSocket] Error sending ping:', error);
        }
    }
    
    /**
     * Handle pong
     */
    handlePong(data) {
        this.lastPongTime = Date.now();
        
        if (this.pingTimeoutId) {
            clearTimeout(this.pingTimeoutId);
            this.pingTimeoutId = null;
        }
        
        const latency = Date.now() - (data.timestamp || 0);
        this.dispatchEvent('pong', { latency });
    }
    
    /**
     * Clear all timers
     */
    clearTimers() {
        if (this.connectionTimeoutId) {
            clearTimeout(this.connectionTimeoutId);
            this.connectionTimeoutId = null;
        }
        
        if (this.pingIntervalId) {
            clearInterval(this.pingIntervalId);
            this.pingIntervalId = null;
        }
        
        if (this.pingTimeoutId) {
            clearTimeout(this.pingTimeoutId);
            this.pingTimeoutId = null;
        }
    }
    
    /**
     * Dispatch an event
     */
    dispatchEvent(type, detail = {}) {
        try {
            const event = new CustomEvent(`websocket:${type}`, { detail });
            document.dispatchEvent(event);
        } catch (error) {
            console.error(`[WebSocket] Error dispatching ${type} event:`, error);
        }
    }
    /**
     * Handle binary data (can be overridden by subclasses)
     */
    handleBinaryData(data) {
        return String.fromCharCode.apply(null, new Uint8Array(data));
    }
}

// Export for CommonJS and browser
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebSocketManager;
} else if (typeof window !== 'undefined') {
    // Only expose the class, not an instance
    window.WebSocketManager = WebSocketManager;
}
