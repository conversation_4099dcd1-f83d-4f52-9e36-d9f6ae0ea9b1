const tf = require('@tensorflow/tfjs-node')
const path = require('path')
const fs = require('fs').promises
const config = require('../../config/ai-config')
const { performance } = require('perf_hooks')

class AIUtils {
  constructor() {
    this.models = new Map()
    this.modelCache = new Map()
    this.isInitialized = false
    this.performanceMetrics = {
      inferenceTime: 0,
      inferenceCount: 0,
      avgInferenceTime: 0,
      errors: [],
    }
  }

  /**
   * Initialize the AI utilities
   */
  async initialize() {
    if (this.isInitialized) return

    try {
      // Ensure models directory exists
      await fs.mkdir(config.persistence.path, { recursive: true })

      // Load models if autoLoad is enabled
      if (config.persistence.autoLoad) {
        await this.loadAllModels()
      }

      this.isInitialized = true
      console.log('AI Utils initialized')
    } catch (error) {
      console.error('Error initializing AI Utils:', error)
      throw error
    }
  }

  /**
   * Create a new model based on configuration
   */
  createModel(inputShape) {
    const model = tf.sequential()

    // Add layers from config
    config.model.layers.forEach(layerConfig => {
      const layer = this.createLayer(layerConfig, inputShape)
      if (layer) model.add(layer)
    })

    // Compile the model
    model.compile({
      optimizer: this.createOptimizer(config.model.optimizer, config.training.learningRate),
      loss: config.model.loss,
      metrics: config.model.metrics,
    })

    return model
  }

  /**
   * Create a layer based on configuration
   */
  createLayer(layerConfig, inputShape) {
    const { type, ...params } = layerConfig

    switch (type.toLowerCase()) {
      case 'dense':
        return tf.layers.dense({ ...params, inputShape })
      case 'lstm':
        return tf.layers.lstm({ ...params, inputShape })
      case 'dropout':
        return tf.layers.dropout(params)
      case 'conv1d':
        return tf.layers.conv1d({ ...params, inputShape })
      case 'maxpooling1d':
        return tf.layers.maxPooling1d(params)
      case 'flatten':
        return tf.layers.flatten()
      default:
        console.warn(`Unknown layer type: ${type}`)
        return null
    }
  }

  /**
   * Create an optimizer based on configuration
   */
  createOptimizer(optimizerType, learningRate) {
    switch (optimizerType.toLowerCase()) {
      case 'adam':
        return tf.train.adam(learningRate)
      case 'sgd':
        return tf.train.sgd(learningRate)
      case 'rmsprop':
        return tf.train.rmsprop(learningRate)
      case 'adagrad':
        return tf.train.adagrad(learningRate)
      case 'adadelta':
        return tf.train.adadelta(learningRate)
      case 'adamax':
        return tf.train.adamax(learningRate)
      default:
        console.warn(`Unknown optimizer: ${optimizerType}, using Adam`)
        return tf.train.adam(learningRate)
    }
  }

  /**
   * Train a model with the given data
   */
  async trainModel(model, xTrain, yTrain, xVal, yVal) {
    const callbacks = []

    // Add early stopping
    if (config.training.earlyStopping) {
      callbacks.push(
        tf.callbacks.earlyStopping({
          monitor: 'val_loss',
          patience: config.training.earlyStopping.patience,
          minDelta: config.training.earlyStopping.minDelta,
        }),
      )
    }

    // Add model checkpointing
    if (config.persistence.enabled && config.persistence.autoSave) {
      callbacks.push({
        onEpochEnd: async (epoch, logs) => {
          await this.saveModel(model, `epoch_${epoch}`)
        },
      })
    }

    // Train the model
    const history = await model.fit(xTrain, yTrain, {
      epochs: config.training.epochs,
      batchSize: config.training.batchSize,
      validationSplit: config.training.validationSplit,
      validationData: xVal && yVal ? [xVal, yVal] : undefined,
      callbacks,
      verbose: config.performance.logLevel === 'debug' ? 1 : 0,
    })

    return history.history
  }

  /**
   * Make predictions with the model
   */
  async predict(model, inputData) {
    const startTime = performance.now()

    try {
      // Convert input data to tensor
      const inputTensor = tf.tensor2d(inputData, [inputData.length, inputData[0].length])

      // Make prediction
      const prediction = await model.predict(inputTensor).array()

      // Calculate performance metrics
      const endTime = performance.now()
      const inferenceTime = endTime - startTime

      this.performanceMetrics.inferenceTime += inferenceTime
      this.performanceMetrics.inferenceCount++
      this.performanceMetrics.avgInferenceTime =
        this.performanceMetrics.inferenceTime / this.performanceMetrics.inferenceCount

      return prediction
    } catch (error) {
      this.performanceMetrics.errors.push({
        timestamp: new Date().toISOString(),
        error: error.message,
        stack: error.stack,
      })
      throw error
    }
  }

  /**
   * Save a model to disk
   */
  async saveModel(model, modelName = 'latest') {
    if (!config.persistence.enabled) return

    try {
      const savePath = path.join(config.persistence.path, modelName)
      await model.save(`file://${savePath}`)
      console.log(`Model saved to ${savePath}`)

      // Update model cache
      this.modelCache.set(modelName, {
        model,
        timestamp: Date.now(),
        metadata: {
          savedAt: new Date().toISOString(),
          inputShape: model.inputs[0].shape,
          outputShape: model.outputs[0].shape,
          trainableParams: model.countParams(),
          layers: model.layers.map(layer => ({
            name: layer.name,
            type: layer.getClassName(),
            outputShape: layer.outputShape,
            trainableParams: layer.countParams(),
          })),
        },
      })

      return savePath
    } catch (error) {
      console.error('Error saving model:', error)
      throw error
    }
  }

  /**
   * Load a model from disk
   */
  async loadModel(modelName = 'latest') {
    if (!config.persistence.enabled) {
      throw new Error('Model persistence is disabled in config')
    }

    try {
      const modelPath = path.join(config.persistence.path, modelName)
      const model = await tf.loadLayersModel(`file://${modelPath}/model.json`)

      // Add to cache
      this.models.set(modelName, model)

      console.log(`Model loaded from ${modelPath}`)
      return model
    } catch (error) {
      console.error('Error loading model:', error)
      throw error
    }
  }

  /**
   * Load all models from the models directory
   */
  async loadAllModels() {
    if (!config.persistence.enabled) return

    try {
      const modelDirs = await fs.readdir(config.persistence.path)

      for (const dir of modelDirs) {
        try {
          await this.loadModel(dir)
        } catch (err) {
          console.warn(`Skipping invalid model directory: ${dir}`, err)
        }
      }
    } catch (error) {
      if (error.code !== 'ENOENT') {
        console.error('Error loading models:', error)
        throw error
      }
      // Directory doesn't exist yet, which is fine
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      modelsLoaded: this.models.size,
      modelsCached: this.modelCache.size,
      lastUpdated: new Date().toISOString(),
    }
  }

  /**
   * Clean up resources
   */
  dispose() {
    // Dispose all models
    this.models.forEach(model => model.dispose())
    this.models.clear()
    this.modelCache.clear()
    this.isInitialized = false
  }
}

// Singleton instance
const aiUtils = new AIUtils()

module.exports = aiUtils
