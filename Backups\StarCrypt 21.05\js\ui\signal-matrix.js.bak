// StarCrypt Signal Matrix Module
// Handles rendering and updating the signal matrix/grid, including the dedicated volume signal light row

// --- CONFIG ---
// List of all possible indicators (should match those in TRADING_STRATEGIES)
const ALL_INDICATORS = [
  'rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williams<PERSON>', 'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'
];

// --- SIGNAL MATRIX RENDERING ---
// Always render all indicators from ALL_INDICATORS, but only highlight enabled ones
function createSignalMatrix(containerId, timeframes, enabledIndicators) {
  const container = document.getElementById(containerId);
  if (!container) {
    console.error('Signal matrix container not found:', containerId);
    return;
  }
  container.innerHTML = '';

  // Header row
  const headerRow = document.createElement('div');
  headerRow.className = 'signal-matrix-row signal-matrix-header';
  headerRow.appendChild(document.createElement('div')); // Empty corner
  timeframes.forEach(tf => {
    const tfDiv = document.createElement('div');
    tfDiv.className = 'signal-matrix-header-cell';
    tfDiv.innerText = tf;
    headerRow.appendChild(tfDiv);
  });
  container.appendChild(headerRow);

  // Indicator rows: always render all indicators
  ALL_INDICATORS.forEach(ind => {
    const row = createSignalRow(ind, timeframes);
    container.appendChild(row);
  });

  // Dedicated volume row (always last, only if not already present)
  if (!ALL_INDICATORS.includes('volume')) {
    const volumeRow = createSignalRow('volume', timeframes, true);
    container.appendChild(volumeRow);
  }
}

function createSignalRow(indicator, timeframes, isVolumeRow = false) {
  const row = document.createElement('div');
  row.className = 'signal-matrix-row signal-row';
  row.setAttribute('data-indicator', indicator);
  if (isVolumeRow) row.classList.add('volume-row');

  // Label cell
  const labelCell = document.createElement('div');
  labelCell.className = 'signal-matrix-label';
  labelCell.innerText = indicator.toUpperCase();
  row.appendChild(labelCell);

  // Signal lights
  timeframes.forEach(tf => {
    const cell = document.createElement('div');
    cell.className = 'signal-matrix-cell';
    const light = document.createElement('div');
    light.className = 'signal-circle';
    light.setAttribute('data-ind', indicator);
    light.setAttribute('data-tf', tf);
    cell.appendChild(light);
    row.appendChild(cell);
  });
  return row;
}

// --- SIGNAL MATRIX UPDATE ---
// Update visibility of rows based on enabled indicators
// Refactored: always show all indicator rows, only highlight those that are enabled
function updateSignalMatrix() {
  try {
    const signalRows = document.querySelectorAll('.signal-row');
    // Get enabled indicators from global state (should be managed by indicator menu)
    const enabledIndicators = window.enabledIndicators || [];
    signalRows.forEach(row => {
      const ind = row.getAttribute('data-indicator');
      if (!ind) return;
      // Visually enable or disable row based on whether indicator is enabled
      if (enabledIndicators.includes(ind)) {
        row.classList.add('active');
        row.classList.remove('disabled');
      } else {
        row.classList.remove('active');
        row.classList.add('disabled');
      }
    });
    // Force update all signal lights to reflect the new enabled indicators
    if (window.updateAllSignalLights) window.updateAllSignalLights();
  } catch (e) {
    console.error('updateSignalMatrix error:', e);
  }
}

// Simple debounce function implementation
function debounce(func, wait) {
  let timeout;
  return function(...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

// Create debounced version of updateSignalMatrix
const updateSignalMatrixDebounced = debounce(updateSignalMatrix, 500);

// Export functions to global scope
window.createSignalMatrix = createSignalMatrix;
window.updateSignalMatrix = updateSignalMatrix;
window.updateSignalMatrixDebounced = updateSignalMatrixDebounced;
