const express = require('express');
const WebSocket = require('ws');
const fetch = require('node-fetch').default;
const url = require('url');

// Kraken WebSocket setup
const KRAKEN_WS_URL = 'wss://ws.kraken.com/';
const krakenWs = new WebSocket(KRAKEN_WS_URL);
const clients = new Set(); // Track connected clients

// Create express app
const app = express();

// Use port 8080 for WebSocket as that's what the client expects
// Check if port is in use first
const wss = new WebSocket.Server({ port: 8080, clientTracking: true })
  .on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.error('WebSocket server port 8080 is already in use. Using alternative port 8081.');
      // Try alternative port
      return new WebSocket.Server({ port: 8081, clientTracking: true });
    } else {
      console.error(`WebSocket server error: ${error.message}`);
    }
  });

// Fixed port handling for asynchronous errors
const server = app.listen(3000, () => {
  console.log(`StarCrypt Enterprise server running on port ${server.address().port}`);
});
server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error('Express server port 3000 is already in use. Using alternative port 3001.');
    const backupServer = app.listen(3001, () => {
      console.log(`StarCrypt Enterprise server running on port 3001`);
    });
  } else {
    console.error(error);
  }
});

// Add proper error handling to the WebSocket server
wss.on('error', (error) => {
  console.error(`WebSocket server error: ${error.message}`);
});

// Kraken WebSocket connection
krakenWs.on('open', () => {
  console.log('Connected to Kraken WebSocket');
  
  // Subscribe to ticker data for supported pairs
  const subscribeMsg = {
    event: 'subscribe',
    pair: ['XBT/USD', 'ETH/USD', 'LTC/USD', 'XRP/USD', 'ADA/USD', 'SOL/USD', 'DOT/USD', 'DOGE/USD'],
    subscription: {
      name: 'ticker'
    }
  };
  krakenWs.send(JSON.stringify(subscribeMsg));
});

// Handle incoming messages from Kraken
krakenWs.on('message', (data) => {
  try {
    const message = JSON.parse(data);
    
    // Forward ticker updates to all connected clients
    if (Array.isArray(message) && message[2] === 'ticker') {
      const [channelID, tickerData] = message;
      broadcast(JSON.stringify({
        type: 'kraken_ticker',
        data: tickerData
      }));
    }
  } catch (error) {
    console.error('Error processing Kraken message:', error);
  }
});

// Handle Kraken WebSocket errors
krakenWs.on('error', (error) => {
  console.error('Kraken WebSocket error:', error);
});

// Handle Kraken WebSocket close
krakenWs.on('close', () => {
  console.log('Disconnected from Kraken WebSocket. Attempting to reconnect...');
  setTimeout(() => {
    console.log('Reconnecting to Kraken WebSocket...');
    // Reconnect logic would go here
  }, 5000);
});

// Helper function to broadcast to all clients
function broadcast(data) {
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(data);
    }
  });
}

// Handle WebSocket connections
const originalOnConnection = wss.on.bind(wss, 'connection');
wss.removeAllListeners('connection');
wss.on('connection', (ws) => {
  console.log('New client connected');
  clients.add(ws);
  
  // Keep existing connection handling
  originalOnConnection(ws);
  
  ws.on('close', () => {
    console.log('Client disconnected');
    clients.delete(ws);
  });
  
  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    clients.delete(ws);
  });
  // Set isAlive flag for heartbeat
  ws.isAlive = true;
  
  // Handle pong responses to keep the connection alive
  ws.on('pong', () => {
    ws.isAlive = true;
  });
  
  // Handle WebSocket messages
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log(`Received message: ${data.type}`);
      
      // Handle strategy change
      if (data.type === 'strategy_change') {
        const strategy = data.strategy;
        console.log(`Strategy changed to: ${strategy}`);
        
        // Send confirmation back to client
        ws.send(JSON.stringify({
          type: 'strategy_change_confirmation',
          strategy: strategy,
          status: 'success',
          message: `Strategy successfully changed to ${TRADING_STRATEGIES[strategy].name}`
        }));
        
        // Send updated indicators for the strategy
        const strategyIndicators = TRADING_STRATEGIES[strategy].indicators;
        ws.send(JSON.stringify({
          type: 'strategy_indicators',
          strategy: strategy,
          indicators: strategyIndicators
        }));
      }
      
      // Handle coin change
      if (data.type === 'coin_change') {
        const pair = data.pair;
        console.log(`Coin changed to: ${pair}`);
        
        // Validate the pair
        if (SUPPORTED_PAIRS.includes(pair)) {
          // Send confirmation back to client
          ws.send(JSON.stringify({
            type: 'coin_change_confirmation',
            pair: pair,
            status: 'success',
            message: `Coin successfully changed to ${pair}`
          }));
          
          // Fetch data for the new coin and send it to the client
          fetchOHLC(pair, DEFAULT_TIMEFRAME, true)
            .then(() => calculateIndicatorsAllTimeframes(pair))
            .then(() => {
              ws.send(JSON.stringify({
                type: 'coin_data_updated',
                pair: pair,
                status: 'success'
              }));
            })
            .catch(error => {
              console.error(`Error fetching data for ${pair}: ${error.message}`);
              ws.send(JSON.stringify({
                type: 'coin_data_updated',
                pair: pair,
                status: 'error',
                message: `Error fetching data for ${pair}`
              }));
            });
        } else {
          ws.send(JSON.stringify({
            type: 'coin_change_confirmation',
            pair: pair,
            status: 'error',
            message: `Unsupported pair: ${pair}`
          }));
        }
      }
    } catch (error) {
      console.error(`Error processing WebSocket message: ${error.message}`);
      ws.send(JSON.stringify({
        type: 'error',
        message: `Error processing message: ${error.message}`
      }));
    }
  });
  
  // Handle WebSocket errors
  ws.on('error', (error) => {
    // Properly log errors instead of sending undefined
    console.error(`WebSocket client error: ${error.message || 'Unknown error'}`);
  });
  
  // Send initial data to client
  ws.send(JSON.stringify({
    type: 'connection_established',
    message: 'Connected to StarCrypt server',
    supportedPairs: SUPPORTED_PAIRS,
    supportedTimeframes: TIMEFRAMES,
    defaultPair: DEFAULT_PAIR,
    defaultTimeframe: DEFAULT_TIMEFRAME,
    defaultStrategy: DEFAULT_STRATEGY,
    strategies: Object.keys(TRADING_STRATEGIES).map(key => ({
      id: key,
      name: TRADING_STRATEGIES[key].name,
      indicators: TRADING_STRATEGIES[key].indicators
    }))
  }));
});

const port = 3000;

// Add cleanup on server close
process.on('SIGINT', () => {
  console.log('Shutting down...');
  krakenWs.close();
  wss.close();
  process.exit();
});

app.use(express.static('.'));
app.use(express.json());

// Configuration
const DEFAULT_PAIR = 'xbtusdt';
const DEFAULT_TIMEFRAME = '1h';
const DEFAULT_STRATEGY = 'admiral_toa';
const SUPPORTED_PAIRS = ['xbtusdt', 'ethusdt', 'ltcusdt', 'xrpusdt', 'adausdt', 'solusdt', 'dotusdt', 'dogeusdt'];
const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
const INTERVALS = { '1m': 1, '5m': 5, '15m': 15, '1h': 60, '4h': 240, '1d': 1440, '1w': 10080 };
const TIMEFRAME_SECONDS = { '1m': 60, '5m': 300, '15m': 900, '1h': 3600, '4h': 14400, '1d': 86400, '1w': 604800 };
const RATE_LIMIT_REQUESTS = 15; // Not currently used but kept for future rate limiting implementation
const RATE_LIMIT_WINDOW = 60000; // Not currently used but kept for future rate limiting implementation
const MAX_CANDLES = 720;
const REQUEST_DELAY = 1000; // 1 second delay between API requests to avoid rate limiting

// Data storage
let historicalData = {};
let indicatorsData = {};
let lastLivePrice = {};
let subscriptions = new Map();

const KRAKEN_PAIRS = {
  'xbtusdt': 'XBTUSD',
  'ethusdt': 'ETHUSD',
  'ltcusdt': 'LTCUSD',
  'xrpusdt': 'XRPUSD',
  'adausdt': 'ADAUSD',
  'solusdt': 'SOLUSD',
  'dotusdt': 'DOTUSD',
  'dogeusdt': 'DOGEUSD'
};

// Strategy helpers
function calculateStrategyScores(indicators, weights) {
  let weightedSum = 0;
  const rationale = [];
  Object.keys(weights).forEach(ind => {
    const indData = indicators[ind];
    if (!indData?.signalClass) return;
    let score = 0;
    switch (indData.signalClass) {
      case 'degen-buy': score = 2; break;
      case 'mild-buy': score = 1; break;
      case 'mild-sell': score = -1; break;
      case 'degen-sell': score = -2; break;
    }
    weightedSum += score * weights[ind];
    if (score !== 0) rationale.push(`${ind.toUpperCase()}: ${indData.signalClass} (${(score * weights[ind]).toFixed(2)})`);
  });
  return { weightedSum, rationale };
}

function generateStrategySignal({ weightedSum, rationale }, baseTooltip) {
  let signal, color, signalClass, confidence;
  if (weightedSum >= 6) {
    signal = 'degen-buy';
    color = '#00FF00';
    signalClass = 'degen-buy';
    confidence = Math.min(0.95, 0.5 + (weightedSum * 0.05));
  } else if (weightedSum >= 3) {
    signal = 'mild-buy';
    color = '#0000FF';
    signalClass = 'mild-buy';
    confidence = Math.min(0.95, 0.5 + (weightedSum * 0.05));
  } else if (weightedSum <= -6) {
    signal = 'degen-sell';
    color = '#FF0000';
    signalClass = 'degen-sell';
    confidence = Math.min(0.95, 0.5 + (-weightedSum * 0.05));
  } else if (weightedSum <= -3) {
    signal = 'mild-sell';
    color = '#FFA500';
    signalClass = 'mild-sell';
    confidence = Math.min(0.95, 0.5 + (-weightedSum * 0.05));
  } else {
    signal = 'neutral';
    color = '#808080';
    signalClass = 'neutral';
    confidence = 0.5;
  }
  const tooltip = `${baseTooltip}: ${rationale.length ? rationale.join(', ') : 'No strong signals'}. Confidence: ${(confidence * 100).toFixed(0)}%`;
  return { signal, color, signalClass, confidence, tooltip };
}

// Trading strategies
const TRADING_STRATEGIES = {
  admiral_toa: {
    name: 'Admiral T.O.A. Convergence',
    indicators: ['rsi', 'stochRsi', 'macd', 'bollingerBands', 'adx', 'volume', 'williamsR', 'ultimateOscillator', 'mfi', 'vwap', 'fractal', 'atr'],
    weights: { rsi: 0.25, macd: 0.2, stochRsi: 0.1, bollingerBands: 0.1, adx: 0.1, volume: 0.05, williamsR: 0.05, ultimateOscillator: 0.05, mfi: 0.05, vwap: 0.025, fractal: 0.025, atr: 0.025 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.admiral_toa.weights);
      return generateStrategySignal(scores, 'Balanced momentum and trend signals');
    }
  },
  momentum_blast: {
    name: 'Momentum Blast',
    indicators: ['rsi', 'stochRsi', 'macd', 'williamsR', 'mfi', 'volume'],
    weights: { rsi: 0.25, macd: 0.2, stochRsi: 0.15, williamsR: 0.15, mfi: 0.15, volume: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.momentum_blast.weights);
      return generateStrategySignal(scores, 'Strong momentum indicators aligned');
    }
  },
  tight_convergence: {
    name: 'Tight Convergence',
    indicators: ['bollingerBands', 'atr', 'adx', 'vwap', 'macd'],
    weights: { bollingerBands: 0.3, atr: 0.25, adx: 0.2, vwap: 0.15, macd: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.tight_convergence.weights);
      return generateStrategySignal(scores, 'Volatility and trend convergence');
    }
  },
  top_bottom_feeder: {
    name: 'Top Bottom Feeder',
    indicators: ['rsi', 'stochRsi', 'williamsR', 'ultimateOscillator', 'bollingerBands', 'mfi'],
    weights: { rsi: 0.25, williamsR: 0.2, stochRsi: 0.2, ultimateOscillator: 0.15, bollingerBands: 0.1, mfi: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.top_bottom_feeder.weights);
      return generateStrategySignal(scores, 'Overbought/oversold conditions detected');
    }
  },
  scalping_sniper: {
    name: 'Scalping Sniper',
    indicators: ['rsi', 'macd', 'volume', 'bollingerBands', 'atr'],
    weights: { rsi: 0.3, macd: 0.25, volume: 0.2, bollingerBands: 0.15, atr: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.scalping_sniper.weights);
      return generateStrategySignal(scores, 'Short-term momentum and volume spike');
    }
  },
  trend_rider: {
    name: 'Trend Rider',
    indicators: ['macd', 'adx', 'bollingerBands', 'rsi', 'volume'],
    weights: { macd: 0.3, adx: 0.25, bollingerBands: 0.2, rsi: 0.15, volume: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.trend_rider.weights);
      return generateStrategySignal(scores, 'Sustained trend direction');
    }
  },
  fractal_surge: {
    name: 'Fractal Surge',
    indicators: ['atr', 'macd', 'volume', 'fractal', 'bollingerBands'],
    weights: { atr: 0.3, macd: 0.25, volume: 0.2, fractal: 0.15, bollingerBands: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.fractal_surge.weights);
      return generateStrategySignal(scores, 'Volatility breakout with momentum');
    }
  },
  sentiment_blaster: {
    name: 'Sentiment Blaster',
    indicators: ['rsi', 'macd', 'volume', 'mfi', 'williamsR'],
    weights: { rsi: 0.3, macd: 0.25, volume: 0.2, mfi: 0.15, williamsR: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.sentiment_blaster.weights);
      return generateStrategySignal(scores, 'Sentiment-driven momentum strategy');
    }
  },
  vwap_guardian: {
    name: 'VWAP Guardian',
    indicators: ['vwap', 'rsi', 'macd', 'volume', 'bollingerBands'],
    weights: { vwap: 0.35, rsi: 0.2, macd: 0.2, volume: 0.15, bollingerBands: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.vwap_guardian.weights);
      return generateStrategySignal(scores, 'VWAP-based trend following');
    }
  },
  correlation_hunter: {
    name: 'Correlation Hunter',
    indicators: ['correlation', 'rsi', 'macd', 'volume', 'bollingerBands'],
    weights: { correlation: 0.35, rsi: 0.2, macd: 0.2, volume: 0.15, bollingerBands: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.correlation_hunter.weights);
      return generateStrategySignal(scores, 'Cross-asset correlation analysis');
    }
  },
  random_walk: {
    name: 'Random Walk Prototype',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'volume', 'williamsR'],
    weights: { rsi: 0.2, macd: 0.2, bollingerBands: 0.15, adx: 0.15, volume: 0.15, williamsR: 0.15 },
    logic: (indicators) => {
      // Add some randomness to the strategy
      const randomFactor = Math.random() * 2 - 1; // Between -1 and 1
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.random_walk.weights);
      scores.weightedSum += randomFactor * 3; // Add random component
      return generateStrategySignal(scores, 'Random walk with technical bias');
    }
  },

  quantum_entropy: {
    name: 'Quantum Entropy',
    indicators: ['atr', 'adx', 'bollingerBands'],
    weights: { atr: 0.4, adx: 0.3, bollingerBands: 0.3 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.quantum_entropy.weights);
      return generateStrategySignal(scores, 'High volatility and trend entropy');
    }
  },

  time_anomaly: {
    name: 'Time Anomaly',
    indicators: ['rsi', 'macd', 'time_anomaly'],
    weights: { rsi: 0.3, macd: 0.3, time_anomaly: 0.4 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.time_anomaly.weights);
      return generateStrategySignal(scores, 'Statistical anomaly in price returns');
    }
  },

  ml_predictor: {
    name: 'ML Predictor',
    indicators: ['rsi', 'macd'],
    weights: { rsi: 0.5, macd: 0.5 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.ml_predictor.weights);
      return generateStrategySignal(scores, 'Awaiting ML model integration');
    }
  },

  neural_network_navigator: {
    name: 'AI Neural Network Navigator',
    indicators: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    weights: { rsi: 0.15, macd: 0.15, bollingerBands: 0.1, ml: 0.2, sentiment: 0.15, entropy: 0.1, correlation: 0.1, time_anomaly: 0.05 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.neural_network_navigator.weights);
      return generateStrategySignal(scores, 'AI neural network pattern recognition');
    }
  },

  deep_learning_diver: {
    name: 'AI Deep Learning Diver',
    indicators: ['rsi', 'stochRsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation'],
    weights: { rsi: 0.15, stochRsi: 0.15, macd: 0.15, ml: 0.2, sentiment: 0.15, volume: 0.1, correlation: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.deep_learning_diver.weights);
      return generateStrategySignal(scores, 'Deep learning order flow analysis');
    }
  },

  ai_pattern_prophet: {
    name: 'AI Pattern Prophet',
    indicators: ['bollingerBands', 'macd', 'adx', 'ml', 'entropy', 'time_anomaly', 'fractal'],
    weights: { bollingerBands: 0.15, macd: 0.15, adx: 0.1, ml: 0.2, entropy: 0.15, time_anomaly: 0.15, fractal: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.ai_pattern_prophet.weights);
      return generateStrategySignal(scores, 'AI pattern recognition and completion probability');
    }
  },

  machine_learning_momentum: {
    name: 'AI ML Momentum Master',
    indicators: ['rsi', 'macd', 'williamsR', 'ml', 'volume', 'sentiment', 'correlation'],
    weights: { rsi: 0.15, macd: 0.15, williamsR: 0.1, ml: 0.2, volume: 0.15, sentiment: 0.15, correlation: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.machine_learning_momentum.weights);
      return generateStrategySignal(scores, 'ML-enhanced momentum analysis');
    }
  },

  sentiment_analysis_surfer: {
    name: 'AI Sentiment Analysis Surfer',
    indicators: ['rsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
    weights: { rsi: 0.1, macd: 0.1, ml: 0.15, sentiment: 0.3, volume: 0.15, correlation: 0.1, entropy: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.sentiment_analysis_surfer.weights);
      return generateStrategySignal(scores, 'Social sentiment and market mood analysis');
    }
  },

  cross_asset_nebula: {
    name: 'Cross-Asset Nebula',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'atr'],
    weights: { rsi: 0.2, macd: 0.2, bollingerBands: 0.2, adx: 0.2, atr: 0.2 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.cross_asset_nebula.weights);
      return generateStrategySignal(scores, 'Cross-asset correlation analysis');
    }
  },

  time_warp_scalper: {
    name: 'Time Warp Scalper',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'macd', 'williamsR'],
    weights: { rsi: 0.2, stochRsi: 0.2, bollingerBands: 0.2, macd: 0.2, williamsR: 0.2 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.time_warp_scalper.weights);
      return generateStrategySignal(scores, 'Multi-timeframe scalping analysis');
    }
  }
};

// Indicator calculations (unchanged)
function calculateRSI(closes, period = 14) {
  if (closes.length < period + 1) return {};
  let gains = 0, losses = 0;
  for (let i = 1; i <= period; i++) {
    const diff = closes[i] - closes[i - 1];
    if (diff >= 0) gains += diff;
    else losses -= diff;
  }
  let avgGain = gains / period;
  let avgLoss = losses / period;
  const rs = avgGain / (avgLoss || 1);
  const rsi = 100 - (100 / (1 + rs));

  // Determine signal class based on RSI value
  let signalClass = 'neutral';
  if (rsi > 70) {
    signalClass = 'degen-sell';
  } else if (rsi > 60) {
    signalClass = 'mild-sell';
  } else if (rsi < 30) {
    signalClass = 'degen-buy';
  } else if (rsi < 40) {
    signalClass = 'mild-buy';
  }

  return {
    value: rsi,
    overbought: rsi > 70,
    oversold: rsi < 30,
    signalClass,
    color: rsi > 70 ? '#FF0000' : rsi > 60 ? '#FFA500' : rsi < 30 ? '#00FF00' : rsi < 40 ? '#0000FF' : '#808080'
  };
}

function calculateStochRSI(closes, period = 14) {
  const rsiValues = closes.slice(-period - 1).map((_, i) => calculateRSI(closes.slice(i, i + period + 1)).value).filter(v => v);
  if (rsiValues.length < period) return {};
  const rsi = rsiValues[rsiValues.length - 1];
  const high = Math.max(...rsiValues);
  const low = Math.min(...rsiValues);
  const stoch = ((rsi - low) / (high - low || 1)) * 100;

  // Determine signal class based on StochRSI value
  let signalClass = 'neutral';
  if (stoch > 80) {
    signalClass = 'degen-sell';
  } else if (stoch > 70) {
    signalClass = 'mild-sell';
  } else if (stoch < 20) {
    signalClass = 'degen-buy';
  } else if (stoch < 30) {
    signalClass = 'mild-buy';
  }

  return {
    value: stoch,
    overbought: stoch > 80,
    oversold: stoch < 20,
    signalClass,
    color: stoch > 80 ? '#FF0000' : stoch > 70 ? '#FFA500' : stoch < 20 ? '#00FF00' : stoch < 30 ? '#0000FF' : '#808080'
  };
}

function calculateMACD(closes, fast = 12, slow = 26, signal = 9) {
  if (closes.length < Math.max(fast, slow, signal)) return {};

  // Calculate EMAs
  const emaFast = calculateEMA(closes, fast);
  const emaSlow = calculateEMA(closes, slow);
  const macd = emaFast - emaSlow;

  // Calculate signal line (EMA of MACD)
  // We need to calculate the EMA of the MACD line over the last 'signal' periods
  // Since we only have a single MACD value, we'll create a synthetic MACD history
  const macdHistory = [];
  for (let i = 0; i < closes.length; i++) {
    if (i < slow - 1) {
      // Not enough data for slow EMA yet
      macdHistory.push(0);
    } else {
      const fastEMA = calculateEMA(closes.slice(0, i + 1), fast);
      const slowEMA = calculateEMA(closes.slice(0, i + 1), slow);
      macdHistory.push(fastEMA - slowEMA);
    }
  }

  const signalLine = calculateEMA(macdHistory.slice(-signal), signal);
  const histogram = macd - signalLine;

  // MACD interpretation:
  // Strong Bullish: MACD > Signal & Histogram > 0.1 (Green)
  // Mild Bullish: MACD > Signal & Histogram <= 0.1 (Blue)
  // Neutral: MACD ≈ Signal (Grey)
  // Mild Bearish: MACD < Signal & Histogram >= -0.1 (Orange)
  // Strong Bearish: MACD < Signal & Histogram < -0.1 (Red)

  let macdSignal = 'neutral';
  if (macd > signalLine && histogram > 0.1) {
    macdSignal = 'strong bullish';
  } else if (macd > signalLine) {
    macdSignal = 'mild bullish';
  } else if (macd < signalLine && histogram < -0.1) {
    macdSignal = 'strong bearish';
  } else if (macd < signalLine) {
    macdSignal = 'mild bearish';
  }

  // Determine signal class based on MACD values
  let signalClass = 'neutral';
  if (macd > signalLine && histogram > 0.1) {
    signalClass = 'degen-buy';
  } else if (macd > signalLine) {
    signalClass = 'mild-buy';
  } else if (macd < signalLine && histogram < -0.1) {
    signalClass = 'degen-sell';
  } else if (macd < signalLine) {
    signalClass = 'mild-sell';
  }

  return {
    macd,
    signal: signalLine,
    histogram,
    macdSignal,
    overbought: macd < signalLine && histogram < -0.1,
    oversold: macd > signalLine && histogram > 0.1,
    signalClass,
    color: macd > signalLine && histogram > 0.1 ? '#00FF00' : macd > signalLine ? '#0000FF' : macd < signalLine && histogram < -0.1 ? '#FF0000' : macd < signalLine ? '#FFA500' : '#808080'
  };
}

function calculateBollingerBands(closes, period = 20, stdDev = 2) {
  if (closes.length < period) return {};

  // Calculate SMA and standard deviation
  const sma = calculateSMA(closes, period);
  const variance = closes.slice(-period).reduce((sum, c) => sum + Math.pow(c - sma, 2), 0) / period;
  const std = Math.sqrt(variance);

  // Calculate bands
  const upper = sma + stdDev * std;
  const lower = sma - stdDev * std;
  const currentPrice = closes[closes.length - 1];

  // Calculate position as percentage within the bands (0% = lower band, 100% = upper band)
  const position = ((currentPrice - lower) / (upper - lower || 1)) * 100;

  // Calculate bandwidth (volatility indicator)
  const bandwidth = ((upper - lower) / sma) * 100;

  // Bollinger Bands interpretation:
  // Strong Sell: Position > 90% (Red)
  // Mild Sell: Position 80-90% (Orange)
  // Neutral: Position 20-80% (Grey)
  // Mild Buy: Position 10-20% (Blue)
  // Strong Buy: Position < 10% (Green)

  let bbSignal = 'neutral';
  if (position > 90) {
    bbSignal = 'strong sell';
  } else if (position > 80) {
    bbSignal = 'mild sell';
  } else if (position < 10) {
    bbSignal = 'strong buy';
  } else if (position < 20) {
    bbSignal = 'mild buy';
  }

  // Determine signal class based on Bollinger Bands position
  let signalClass = 'neutral';
  if (position > 90) {
    signalClass = 'degen-sell';
  } else if (position > 80) {
    signalClass = 'mild-sell';
  } else if (position < 10) {
    signalClass = 'degen-buy';
  } else if (position < 20) {
    signalClass = 'mild-buy';
  }

  return {
    upper,
    lower,
    sma,
    position,
    bandwidth,
    bbSignal,
    overbought: position > 90,
    oversold: position < 10,
    signalClass,
    color: position > 90 ? '#FF0000' : position > 80 ? '#FFA500' : position < 10 ? '#00FF00' : position < 20 ? '#0000FF' : '#808080'
  };
}

function calculateATR(highs, lows, closes, period = 14) {
  // Defensive logging and error handling for ATR
  if (!global.logMessages) global.logMessages = [];
  const logMessages = global.logMessages;
  // Validate input arrays
  if (!Array.isArray(highs) || !Array.isArray(lows) || !Array.isArray(closes)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Invalid input arrays.`);
    return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
  }
  if (highs.length < period + 1 || lows.length < period + 1 || closes.length < period + 1) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Not enough data (highs:${highs.length}, lows:${lows.length}, closes:${closes.length}, period:${period})`);
    return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
  }
  // Check for non-numeric values
  if (!highs.every(Number.isFinite) || !lows.every(Number.isFinite) || !closes.every(Number.isFinite)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Non-numeric values detected in input arrays.`);
    return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
  }
  try {
    // Calculate True Range for each candle
    const trs = [];
    for (let i = 1; i < highs.length; i++) {
      const high = highs[i];
      const low = lows[i];
      const prevClose = closes[i - 1];
      if (!Number.isFinite(high) || !Number.isFinite(low) || !Number.isFinite(prevClose)) {
        logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Non-finite TR input at i=${i} (high:${high}, low:${low}, prevClose:${prevClose})`);
        return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
      }
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      if (!Number.isFinite(tr)) {
        logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Non-finite TR calculated at i=${i} (tr:${tr})`);
        return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
      }
      trs.push(tr);
    }
    // Calculate ATR as the SMA of True Range values
    const atr = calculateSMA(trs.slice(-period), period);
    // Calculate average ATR over a longer period for comparison
    const longPeriod = period * 2;
    const avgAtr = calculateSMA(trs.slice(-longPeriod), longPeriod);
    // Calculate ATR ratio (current ATR / average ATR)
    const ratio = atr / (avgAtr || 1);
    // Calculate ATR as percentage of price
    const currentPrice = closes[closes.length - 1];
    if (!Number.isFinite(currentPrice) || currentPrice === 0) {
      logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Invalid current price (${currentPrice})`);
      return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
    }
    const atrPercent = (atr / currentPrice) * 100;
    // ATR interpretation:
    let volatility = 'normal';
    if (ratio > 1.5) volatility = 'very high';
    else if (ratio > 1.2) volatility = 'high';
    else if (ratio > 0.8) volatility = 'normal';
    else if (ratio > 0.5) volatility = 'low';
    else volatility = 'very low';
    // Determine signal class based on ATR ratio
    let signalClass = 'neutral';
    if (ratio > 1.5) {
      signalClass = 'degen-sell';
    } else if (ratio > 1.2) {
      signalClass = 'mild-sell';
    } else if (ratio < 0.5) {
      signalClass = 'degen-buy';
    } else if (ratio < 0.8) {
      signalClass = 'mild-buy';
    }
    let color = '#808080';
    if (ratio > 1.5) color = '#FF0000';
    else if (ratio > 1.2) color = '#FFA500';
    else if (ratio < 0.5) color = '#00FF00';
    else if (ratio < 0.8) color = '#0000FF';
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: ATR=${atr}, ATR%=${atrPercent}, Ratio=${ratio}, Volatility=${volatility}, SignalClass=${signalClass}, Color=${color}`);
    return {
      value: atr,
      atrPercent,
      ratio,
      volatility,
      overbought: ratio > 1.2,
      oversold: ratio < 0.8,
      signalClass,
      color
    };
  } catch (e) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Exception: ${e.message}`);
    return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
  }
}

function calculateMFI(highs, lows, closes, volumes, period = 14) {
  if (closes.length < period + 1) return {};
  let posMF = 0, negMF = 0;
  for (let i = 1; i <= period; i++) {
    const typical = (highs[i] + lows[i] + closes[i]) / 3;
    const prevTypical = (highs[i - 1] + lows[i - 1] + closes[i - 1]) / 3;
    const rawMF = typical * volumes[i];
    if (typical > prevTypical) posMF += rawMF;
    else negMF += rawMF;
  }
  const mfi = 100 - (100 / (1 + (posMF / (negMF || 1))));
  return {
    value: mfi,
    overbought: mfi > 80,
    oversold: mfi < 20,
    color: mfi > 80 ? '#FF0000' : mfi > 70 ? '#FFA500' : mfi < 20 ? '#00FF00' : mfi < 30 ? '#0000FF' : '#808080'
  };
}

function calculateWilliamsR(highs, lows, closes, period = 14) {
  // Defensive logging and error handling for Williams %R
  if (!global.logMessages) global.logMessages = [];
  const logMessages = global.logMessages;
  if (!Array.isArray(highs) || !Array.isArray(lows) || !Array.isArray(closes)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Invalid input arrays.`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  if (highs.length < period || lows.length < period || closes.length < 1) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Not enough data to calculate Williams %R (need ${period}, got highs:${highs.length}, lows:${lows.length}, closes:${closes.length})`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  const high = Math.max(...highs.slice(-period));
  const low = Math.min(...lows.slice(-period));
  const close = closes[closes.length - 1];
  if (isNaN(high) || isNaN(low) || isNaN(close)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: NaN detected in input values (high:${high}, low:${low}, close:${close})`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  if (high === low) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Division by zero (high == low == ${high})`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  const wr = ((high - close) / (high - low)) * -100;
  if (isNaN(wr) || !isFinite(wr)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Invalid Williams %R value calculated: ${wr}`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  let color = '#808080'; // Default neutral color
  if (wr > -20) color = '#FF0000';
  else if (wr > -30) color = '#FFA500';
  else if (wr < -80) color = '#00FF00';
  else if (wr < -70) color = '#0000FF';
  logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Williams %R = ${wr.toFixed(2)} (color: ${color})`);
  return {
    value: wr,
    overbought: wr > -20,
    oversold: wr < -80,
    color
  };
}

function calculateADX(highs, lows, closes, period = 14) {
  if (highs.length < period + 1) return {};
  const dmPlus = highs.slice(1).map((h, i) => Math.max(h - highs[i], 0));
  const dmMinus = lows.slice(1).map((l, i) => Math.max(lows[i] - l, 0));
  const trs = highs.slice(1).map((h, i) => Math.max(h - lows[i + 1], Math.abs(h - closes[i]), Math.abs(lows[i + 1] - closes[i])));
  const smoothedPlus = calculateSMA(dmPlus, period);
  const smoothedMinus = calculateSMA(dmMinus, period);
  const smoothedTR = calculateSMA(trs, period);
  const diPlus = (smoothedPlus / smoothedTR) * 100;
  const diMinus = (smoothedMinus / smoothedTR) * 100;
  const dx = Math.abs(diPlus - diMinus) / (diPlus + diMinus || 1) * 100;
  const adx = calculateSMA([dx], period);

  // ADX trend strength interpretation:
  // Very Strong Trend: ADX > 50 (Red)
  // Strong Trend: ADX 35-50 (Orange)
  // Moderate Trend: ADX 25-35 (Grey)
  // Mild Trend: ADX 20-25 (Blue)
  // Weak Trend: ADX < 20 (Green)

  let trendStrength = 'moderate';
  if (adx > 50) trendStrength = 'very strong';
  else if (adx > 35) trendStrength = 'strong';
  else if (adx > 25) trendStrength = 'moderate';
  else if (adx > 20) trendStrength = 'mild';
  else trendStrength = 'weak';

  // Determine signal class based on ADX value
  // For ADX, we use a different approach since it measures trend strength, not direction
  // Very strong trends (high ADX) can indicate potential reversals (sell in strong uptrends, buy in strong downtrends)
  // Weak trends (low ADX) can indicate potential continuation (buy in weak uptrends, sell in weak downtrends)
  let signalClass = 'neutral';
  if (adx > 50) {
    signalClass = 'convergence'; // Very strong trend - potential reversal point
  } else if (adx > 35) {
    signalClass = 'mild-sell'; // Strong trend - caution
  } else if (adx < 20) {
    signalClass = 'degen-buy'; // Weak trend - potential for new trend development
  } else if (adx < 25) {
    signalClass = 'mild-buy'; // Mild trend - watching for trend development
  }

  return {
    value: adx,
    trendStrength,
    overbought: adx > 35, // Strong trend is considered overbought
    oversold: adx < 20,   // Weak trend is considered oversold
    signalClass,
    color: adx > 50 ? '#FF0000' : adx > 35 ? '#FFA500' : adx < 20 ? '#00FF00' : adx < 25 ? '#0000FF' : '#808080'
  };
}

function calculateUltimateOscillator(highs, lows, closes, short = 7, mid = 14, long = 28) {
  if (closes.length < long + 1) return {};
  const bp = closes.slice(1).map((_, i) => closes[i+1] - Math.min(lows[i + 1], closes[i]));
  const tr = closes.slice(1).map((_, i) => Math.max(highs[i + 1], closes[i]) - Math.min(lows[i + 1], closes[i]));
  const avg7 = bp.slice(-short).reduce((a, b) => a + b, 0) / tr.slice(-short).reduce((a, b) => a + b, 1);
  const avg14 = bp.slice(-mid).reduce((a, b) => a + b, 0) / tr.slice(-mid).reduce((a, b) => a + b, 1);
  const avg28 = bp.slice(-long).reduce((a, b) => a + b, 0) / tr.slice(-long).reduce((a, b) => a + b, 1);
  const uo = ((4 * avg7 + 2 * avg14 + avg28) / 7) * 100;
  return {
    value: uo,
    overbought: uo > 70,
    oversold: uo < 30,
    color: uo > 70 ? '#FF0000' : uo > 60 ? '#FFA500' : uo < 30 ? '#00FF00' : uo < 40 ? '#0000FF' : '#808080'
  };
}

function calculateVWAP(highs, lows, closes, volumes) {
  if (!volumes.length) return {};
  const typical = highs.map((h, i) => (h + lows[i] + closes[i]) / 3);
  const vwap = typical.reduce((sum, t, i) => sum + t * volumes[i], 0) / volumes.reduce((a, b) => a + b, 1);
  const position = closes[closes.length - 1] > vwap ? 'above' : 'below';
  return {
    value: vwap,
    position,
    overbought: position === 'above',
    oversold: position === 'below',
    color: position === 'below' ? '#00FF00' : '#FF0000'
  };
}

function calculateVolumeSpike(volumes, period = 14) {
  if (volumes.length < period) return {};
  const avg = calculateSMA(volumes, period);
  const current = volumes[volumes.length - 1];
  const ratio = current / avg;
  const spikePercent = ratio * 100;

  // Determine signal class based on volume spike percentage
  let signalClass = 'neutral';
  if (ratio > 2.0) {
    signalClass = 'degen-sell'; // Very high volume often indicates selling climax
  } else if (ratio > 1.5) {
    signalClass = 'mild-sell';
  } else if (ratio < 0.3) {
    signalClass = 'degen-buy'; // Very low volume can indicate accumulation
  } else if (ratio < 0.7) {
    signalClass = 'mild-buy';
  }

  return {
    spike: ratio > 1.5,
    spikePercent,
    avg,
    current,
    value: spikePercent, // Normalize to percentage for consistent display
    overbought: ratio > 1.5,
    oversold: ratio < 0.5,
    signalClass,
    color: ratio > 2.0 ? '#FF0000' : ratio > 1.5 ? '#FFA500' : ratio < 0.3 ? '#00FF00' : ratio < 0.7 ? '#0000FF' : '#808080'
  };
}

function calculateFractal(highs, lows, period = 5) {
  if (highs.length < period * 2 + 1) return {};
  const mid = Math.floor(period / 2);
  const lastHigh = highs[highs.length - mid - 1];
  const lastLow = lows[lows.length - mid - 1];

  // Check for fractal patterns
  const isHighFractal = highs.slice(-period * 2).every((h, i) => i === period - 1 || h <= lastHigh);
  const isLowFractal = lows.slice(-period * 2).every((l, i) => i === period - 1 || l >= lastLow);

  // Calculate strength of the fractal pattern
  const highStrength = isHighFractal ? Math.max(...highs.slice(-period * 2)) / lastHigh - 1 : 0;
  const lowStrength = isLowFractal ? 1 - Math.min(...lows.slice(-period * 2)) / lastLow : 0;

  // Determine the fractal type and strength
  let fractalType = 'none';
  let fractalValue = 0;
  let fractalStrength = 0;

  if (isHighFractal && isLowFractal) {
    // Both high and low fractals - determine which is stronger
    if (highStrength > lowStrength) {
      fractalType = 'bullish';
      fractalValue = 1;
      fractalStrength = highStrength;
    } else {
      fractalType = 'bearish';
      fractalValue = -1;
      fractalStrength = lowStrength;
    }
  } else if (isHighFractal) {
    fractalType = 'bullish';
    fractalValue = 1;
    fractalStrength = highStrength;
  } else if (isLowFractal) {
    fractalType = 'bearish';
    fractalValue = -1;
    fractalStrength = lowStrength;
  }

  // Determine color based on fractal type and strength
  let color = '#808080'; // Default neutral color
  let overbought = false;
  let oversold = false;

  if (fractalType === 'bullish') {
    if (fractalStrength > 0.05) {
      color = '#00FF00'; // Strong bullish - green
      oversold = true;
    } else {
      color = '#0000FF'; // Mild bullish - blue
    }
  } else if (fractalType === 'bearish') {
    if (fractalStrength > 0.05) {
      color = '#FF0000'; // Strong bearish - red
      overbought = true;
    } else {
      color = '#FFA500'; // Mild bearish - orange
    }
  }

  return {
    breakout: fractalType,
    value: fractalValue * 100, // Normalize to percentage for consistent display
    strength: fractalStrength,
    overbought,
    oversold,
    color
  };
}

function calculateSentiment() {
  const score = Math.random() * 2 - 1; // -1 to 1
  return {
    score,
    color: score > 0.5 ? '#00FF00' : score > 0 ? '#0000FF' : score < -0.5 ? '#FF0000' : score < 0 ? '#FFA500' : '#808080'
  };
}

function calculateEntropy(closes, period = 20) {
  if (closes.length < period) return {};
  const returns = closes.slice(1).map((c, i) => Math.log(c / closes[i]));
  const histogram = returns.reduce((acc, r) => {
    const bin = Math.floor(r * 100);
    acc[bin] = (acc[bin] || 0) + 1;
    return acc;
  }, {});
  const probs = Object.values(histogram).map(v => v / returns.length);
  const entropy = -probs.reduce((sum, p) => sum + p * Math.log2(p || 1), 0);
  const normalizedEntropy = entropy / Math.log2(period);

  return {
    value: normalizedEntropy * 100, // Normalize to percentage for consistent display
    overbought: normalizedEntropy > 0.8,
    oversold: normalizedEntropy < 0.4,
    color: normalizedEntropy > 0.8 ? '#FF0000' : normalizedEntropy > 0.6 ? '#FFA500' : normalizedEntropy < 0.2 ? '#00FF00' : normalizedEntropy < 0.4 ? '#0000FF' : '#808080'
  };
}

function calculateCorrelation(closes, refCloses = closes) {
  if (closes.length < 20 || refCloses.length < 20) return {};
  const x = closes.slice(-20);
  const y = refCloses.slice(-20);
  const mx = calculateSMA(x, x.length);
  const my = calculateSMA(y, y.length);
  const cov = x.reduce((sum, xi, i) => sum + (xi - mx) * (y[i] - my), 0) / x.length;
  const sx = Math.sqrt(x.reduce((sum, xi) => sum + Math.pow(xi - mx, 2), 0) / x.length);
  const sy = Math.sqrt(y.reduce((sum, yi) => sum + Math.pow(yi - my, 2), 0) / y.length);
  const corr = cov / (sx * sy || 1);
  return {
    value: corr,
    color: corr > 0.7 ? '#00FF00' : corr > 0.5 ? '#0000FF' : corr < -0.7 ? '#FF0000' : corr < -0.5 ? '#FFA500' : '#808080'
  };
}

function calculateTimeAnomaly(closes, period = 20) {
  if (closes.length < period * 2) return {};
  const returns = closes.slice(1).map((c, i) => c / closes[i] - 1);
  const recentStd = Math.sqrt(returns.slice(-period).reduce((sum, r) => sum + r * r, 0) / period);
  const pastStd = Math.sqrt(returns.slice(-period * 2, -period).reduce((sum, r) => sum + r * r, 0) / period);
  const anomaly = recentStd / (pastStd || 1);

  return {
    value: anomaly * 100, // Normalize to percentage for consistent display
    overbought: anomaly > 1.5,
    oversold: anomaly < 0.5,
    color: anomaly > 1.5 ? '#FF0000' : anomaly > 1.2 ? '#FFA500' : anomaly < 0.5 ? '#00FF00' : anomaly < 0.8 ? '#0000FF' : '#808080'
  };
}

function calculateML(closes, period = 20) {
  // In a real system, this would use a trained machine learning model
  // For now, we'll simulate ML predictions based on recent price patterns
  if (closes.length < period) return {};
  
  // Get recent price action
  const recentPrices = closes.slice(-period);
  const priceChange = (recentPrices[recentPrices.length - 1] / recentPrices[0] - 1) * 100;
  const rsi = calculateRSI(closes).value || 50;
  const volatility = calculateATR([], [], closes).value || 1;
  
  // Simulated ML model with bias toward recent price action and RSI
  const randomness = (Math.random() * 20) - 10; // ±10% random component
  let predictionValue = 50 + (priceChange * 2) + (rsi - 50) * 0.5 + randomness;
  predictionValue = Math.max(0, Math.min(100, predictionValue)); // Clamp between 0-100
  
  // Determine signal based on prediction value
  let signal, signalClass, color;
  if (predictionValue > 70) {
    signal = 'strong_buy';
    signalClass = 'bullish';
    color = '#00FF00'; // Green
  } else if (predictionValue > 55) {
    signal = 'buy';
    signalClass = 'bullish';
    color = '#0000FF'; // Blue
  } else if (predictionValue < 30) {
    signal = 'strong_sell';
    signalClass = 'bearish';
    color = '#FF0000'; // Red
  } else if (predictionValue < 45) {
    signal = 'sell';
    signalClass = 'bearish';
    color = '#FFA500'; // Orange
  } else {
    signal = 'neutral';
    signalClass = 'neutral';
    color = '#808080'; // Gray
  }
  
  return {
    value: predictionValue,
    signal,
    signalClass,
    color,
    tooltip: `ML prediction: ${signal} (${predictionValue.toFixed(1)}%)`
  };
}

function calculateEMA(data, period) {
  const k = 2 / (period + 1);
  return data.reduce((ema, val, i) => i === 0 ? val : val * k + ema * (1 - k), data[0]);
}

function calculateSMA(data, period) {
  if (data.length < period) return 0;
  return data.slice(-period).reduce((a, b) => a + b, 0) / period;
}

// Track last fetch time for each pair/timeframe to avoid duplicate API calls
const lastFetchTime = {};
const FETCH_COOLDOWN = 30000; // 30 seconds cooldown between fetches for the same pair/timeframe

// Track in-progress fetches to prevent duplicate requests
const activeFetches = {};

// Fetch Kraken OHLC data
async function fetchOHLC(pair, timeframe, forceUpdate = false) {
  try {
    // Create a unique key for this pair/timeframe combination
    const fetchKey = `${pair}-${timeframe}`;

    // Check if there's already an active fetch for this pair/timeframe
    if (activeFetches[fetchKey]) {
      // Wait for the existing fetch to complete instead of starting a new one
      console.log(`Waiting for in-progress fetch of ${pair}/${timeframe}...`);
      return await activeFetches[fetchKey];
    }

    // Check if we already have data and if it's recent enough
    const now = Date.now();
    const lastFetch = lastFetchTime[fetchKey] || 0;

    // If we have data and it's recent enough, use the cached data unless force update is requested
    if (!forceUpdate &&
        historicalData[pair]?.[timeframe]?.length > 0 &&
        now - lastFetch < FETCH_COOLDOWN) {
      console.log(`Using cached OHLC data for ${pair}/${timeframe} (${historicalData[pair][timeframe].length} candles)`);
      return historicalData[pair][timeframe];
    }

    // Create a promise for this fetch and store it in activeFetches
    const fetchPromise = (async () => {
      try {
        // Update the last fetch time to prevent duplicate fetches
        lastFetchTime[fetchKey] = now;

        const krakenPair = KRAKEN_PAIRS[pair];
        if (!krakenPair) {
          console.warn(`No Kraken pair mapping for ${pair}`);
          return [];
        }

        const interval = INTERVALS[timeframe];
        console.log(`Fetching OHLC data for ${pair}/${timeframe} from Kraken API: ${krakenPair}, interval: ${interval}`);
        const response = await fetch(`https://api.kraken.com/0/public/OHLC?pair=${krakenPair}&interval=${interval}`);
        const data = await response.json();

        if (data.error && data.error.length > 0) {
          console.warn(`Kraken API error for ${pair}/${timeframe}: ${data.error.join(', ')}`);
          return [];
        }

        // Check if we have valid data
        const resultKey = Object.keys(data.result).find(key => key !== 'last');
        if (!resultKey || !data.result[resultKey] || !Array.isArray(data.result[resultKey])) {
          console.warn(`Invalid data format from Kraken for ${pair}/${timeframe}`);
          return [];
        }

        const candles = data.result[resultKey].slice(-MAX_CANDLES).map(c => ({
          t: parseInt(c[0]) * 1000,
          o: parseFloat(c[1]),
          h: parseFloat(c[2]),
          l: parseFloat(c[3]),
          c: parseFloat(c[4]),
          v: parseFloat(c[6]),
          x: parseInt(c[0]) * 1000
        }));

        historicalData[pair] = historicalData[pair] || {};
        historicalData[pair][timeframe] = candles;

        console.log(`Successfully fetched OHLC for ${pair}/${timeframe}: ${candles.length} candles`);
        return candles;
      } catch (apiError) {
        console.error(`Error fetching from Kraken API for ${pair}/${timeframe}: ${apiError.message}`);
        return [];
      } finally {
        // Remove this fetch from active fetches when done
        delete activeFetches[fetchKey];
      }
    })();

    // Store the promise in activeFetches
    activeFetches[fetchKey] = fetchPromise;

    // Return the promise result
    return await fetchPromise;
  } catch (e) {
    console.error(`Error in fetchOHLC for ${pair}/${timeframe}: ${e.message}`);
    return [];
  }
}

// Fetch live price
async function fetchLivePrice(pair) {
  try {
    const krakenPair = KRAKEN_PAIRS[pair];
    if (!krakenPair) {
      console.warn(`No Kraken pair mapping for ${pair}`);
      return null;
    }
    const response = await fetch(`https://api.kraken.com/0/public/Ticker?pair=${krakenPair}`);
    const data = await response.json();

    // Check for errors in the response
    if (data.error && data.error.length > 0) {
      console.warn(`Kraken API error for ${pair}: ${data.error.join(', ')}`);
      return null;
    }

    // Check if we have valid data
    if (!data.result || !data.result[Object.keys(data.result)[0]]) {
      console.warn(`Invalid data format from Kraken for ${pair}`);
      return null;
    }

    // Get the ticker data using the first key in the result object
    // This is more robust than assuming the key is exactly krakenPair
    const resultKey = Object.keys(data.result)[0];
    const ticker = data.result[resultKey];

    const priceData = {
      price: parseFloat(ticker.c[0]),
      bid: parseFloat(ticker.b[0]),
      ask: parseFloat(ticker.a[0]),
      timestamp: Date.now()
    };
    console.log(`Successfully fetched price for ${pair}: $${priceData.price}`);
    return priceData;
  } catch (e) {
    console.error(`Error fetching price for ${pair}: ${e.message}`);
    return null;
  }
}

// Calculate indicators and signals
function calculateAllIndicators(pair, timeframe) {
  const candles = historicalData[pair]?.[timeframe] || [];
  if (candles.length < 20) {
    console.warn(`Not enough candles for ${pair}/${timeframe} to calculate indicators: ${candles.length}`);
    return {};
  }

  const highs = candles.map(c => c.h);
  const lows = candles.map(c => c.l);
  const closes = candles.map(c => c.c);
  const volumes = candles.map(c => c.v);

  // Calculate all indicators and add timeframe information to each
  const indicators = {};

  // Helper function to add timeframe to indicator data
  const addTimeframeToIndicator = (_, indData) => {
    if (!indData || typeof indData !== 'object') return indData;
    return { ...indData, timeframe };
  };

  // List of all expected indicator keys
  const allIndicatorKeys = [
    'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'mfi', 'williamsR', 'adx', 'ultimateOscillator',
    'vwap', 'volume', 'fractal', 'sentiment', 'entropy', 'correlation', 'time_anomaly', 'ml'
  ];

  // Calculate each indicator and add timeframe information, with robust logging and default fallback
  allIndicatorKeys.forEach(key => {
    let value;
    switch (key) {
      case 'rsi': value = calculateRSI(closes); break;
      case 'stochRsi': value = calculateStochRSI(closes); break;
      case 'macd': value = calculateMACD(closes); break;
      case 'bollingerBands': value = calculateBollingerBands(closes); break;
      case 'atr': value = calculateATR(highs, lows, closes); break;
      case 'mfi': value = calculateMFI(highs, lows, closes, volumes); break;
      case 'williamsR': value = calculateWilliamsR(highs, lows, closes); break;
      case 'adx': value = calculateADX(highs, lows, closes); break;
      case 'ultimateOscillator': value = calculateUltimateOscillator(highs, lows, closes); break;
      case 'vwap': value = calculateVWAP(highs, lows, closes, volumes); break;
      case 'volume': value = calculateVolumeSpike(volumes); break;
      case 'fractal': value = calculateFractal(highs, lows); break;
      case 'sentiment': value = calculateSentiment(); break;
      case 'entropy': value = calculateEntropy(closes); break;
      case 'correlation': value = calculateCorrelation(closes); break;
      case 'time_anomaly': value = calculateTimeAnomaly(closes); break;
      case 'ml': value = calculateML(closes); break;
      default: value = undefined;
    }
    if (value === undefined || value === null || (typeof value === 'number' && isNaN(value))) {
      console.warn(`Indicator ${key} for ${pair}/${timeframe} is missing or invalid, using default.`);
      indicators[key] = { value: 'N/A', color: '#808080', timeframe };
    } else {
      indicators[key] = addTimeframeToIndicator(key, value);
    }
  });

  // Add a metadata field to help with debugging
  indicators.metadata = {
    timeframe,
    calculatedAt: new Date().toISOString(),
    candleCount: candles.length
  };

  // Ensure every expected indicator is present in the result
  allIndicatorKeys.forEach(key => {
    if (!indicators[key]) {
      indicators[key] = { value: 'N/A', color: '#808080', timeframe };
      console.warn(`Indicator ${key} missing for ${pair}/${timeframe}, setting to default.`);
    }
  });

  // Log summary of missing/invalid indicators
  const missing = allIndicatorKeys.filter(k => indicators[k] && indicators[k].value === 'N/A');
  if (missing.length > 0) {
    console.warn(`Missing/invalid indicators for ${pair}/${timeframe}: ${missing.join(', ')}`);
  }

  // Find the strategy for this pair from subscriptions
  let strategy = 'admiral_toa'; // Default strategy

  // Look for a subscription that matches this pair
  for (const [_, sub] of subscriptions.entries()) {
    if (sub.pair === pair && sub.strategy) {
      strategy = sub.strategy;
      break;
    }
  }

  // Get the strategy definition
  const strat = TRADING_STRATEGIES[strategy];
  if (!strat) {
    console.warn(`Strategy ${strategy} not found, using admiral_toa`);
    strategy = 'admiral_toa';
  }

  // Collect the indicators needed for this strategy
  const stratIndicators = {};
  strat.indicators.forEach(ind => {
    if (indicators[ind]) {
      stratIndicators[ind] = indicators[ind];
    } else {
      console.warn(`Indicator ${ind} required by strategy ${strategy} not available`);
    }
  });

  // Apply the strategy logic
  const { signal, color, signalClass, convergenceSteps, metrics } = strat.logic(stratIndicators);

  // Log the strategy results for debugging
  console.log(`Strategy ${strategy} for ${pair}/${timeframe} produced signal: ${signal}, color: ${color}`);
  if (convergenceSteps) {
    console.log(`Convergence steps: Buy=${convergenceSteps.buy.length}, Sell=${convergenceSteps.sell.length}`);
  }

  indicators.convergenceSteps = convergenceSteps;

  // Add strategy information to metadata instead of as a separate indicator
  indicators.metadata.strategy = {
    name: strat.name,
    signal,
    color,
    signalClass,
    metrics,
    helperText: strat.helperText
  };

  indicatorsData[pair] = indicatorsData[pair] || {};
  indicatorsData[pair][timeframe] = indicators;

  return indicators;
}

// WebSocket handling
// Ensure we use consistent pair mappings throughout the code
// Note: We're removing PAIR_MAPPINGS and using KRAKEN_PAIRS consistently

// In the WebSocket connection handler
wss.on('connection', (ws, req) => {
  // Initialize connection properties
  ws.isAlive = true;

  // Get client IP for logging
  const ip = req.socket.remoteAddress;
  console.log(`WebSocket client connected from ${ip}`);

  // Parse query parameters
  const query = req ? url.parse(req.url, true).query : {};
  const initialPair = query.pair || DEFAULT_PAIR;
  console.log(`Initial pair from URL query: ${initialPair}`);

  // Function to calculate indicators for all timeframes
  function calculateIndicatorsAllTimeframes(pair) {
    const result = {};
    TIMEFRAMES.forEach(tf => {
      result[tf] = calculateAllIndicators(pair, tf);
    });
    return result;
  }

  ws.on('message', async (message) => {
    try {
      const msg = JSON.parse(message);
      console.log('Received message:', msg.type);

      if (msg.type === 'subscribe') {
        const pair = (msg.pair || initialPair).toLowerCase();
        const strategy = msg.strategy || DEFAULT_STRATEGY;
        const timeframe = msg.timeframe || DEFAULT_TIMEFRAME;

        if (!SUPPORTED_PAIRS.includes(pair)) {
          ws.send(JSON.stringify({ type: 'error', message: `Unsupported pair: ${pair}. Supported: ${SUPPORTED_PAIRS.join(', ')}` }));
          return;
        }

        // Store subscription info
        subscriptions.set(ws, { pair, strategy, timeframe });

        // Only fetch data for the subscribed pair
        console.log(`Client subscribed to ${pair}`);
        const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
        for (const tf of timeframes) {
          try {
            const updateKey = `${pair}-${tf}`;
            const now = Date.now();

            // Check if we already have data for this pair/timeframe
            const hasData = historicalData[pair]?.[tf]?.length > 0;
            const lastFetch = lastFetchTime[updateKey] || 0;
            const dataAge = now - lastFetch;

            // Only fetch if we don't have data or it's older than 30 seconds
            if (!hasData || dataAge > 30000) {
              await fetchOHLC(pair, tf);
              // Update the last periodic update time to prevent immediate re-fetch
              lastPeriodicUpdate[updateKey] = now;
            } else {
              console.log(`Using existing data for ${pair}/${tf} (${historicalData[pair][tf].length} candles, ${Math.floor(dataAge/1000)}s old)`);
            }

            // Send historical data to the client
            ws.send(JSON.stringify({ type: 'historicalData', pair, timeframe: tf, data: historicalData[pair]?.[tf] || [] }));
          } catch (e) {
            console.error(`Error fetching OHLC for ${pair}/${tf}: ${e.message}`);
            continue; // Skip failed timeframes
          }
        }

        // Calculate and send indicators for all timeframes
        const allIndicators = calculateIndicatorsAllTimeframes(pair);
        TIMEFRAMES.forEach(tf => {
          // Double-check that the timeframe is correctly set in the data
          const indicators = allIndicators[tf] || {};

          if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
            console.error(`Timeframe mismatch in subscription: expected ${tf} but got ${indicators.metadata?.timeframe}`);
            // Fix the metadata
            if (!indicators.metadata) indicators.metadata = {};
            indicators.metadata.timeframe = tf;
          }

          // Verify all indicators have the correct timeframe
          Object.keys(indicators).forEach(ind => {
            if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
              if (indicators[ind].timeframe !== tf) {
                console.error(`Indicator ${ind} has incorrect timeframe in subscription: ${indicators[ind].timeframe} (expected ${tf})`);
                indicators[ind].timeframe = tf; // Fix the timeframe
              }
            }
          });

          ws.send(JSON.stringify({
            type: 'indicators',
            pair,
            timeframe: tf,
            data: indicators
          }));
        });

        // Fetch live price
        try {
          const priceData = await fetchLivePrice(pair);
          console.log(`Successfully fetched price for ${pair}: $${priceData?.price}`);
          lastLivePrice[pair] = priceData;
          ws.send(JSON.stringify({ type: 'livePrice', pair, data: priceData }));
        } catch (e) {
          console.error(`Error fetching price for ${pair}: ${e.message}`);
        }
      } else if (msg.type === 'requestIndicators') {
        try {
          // Handle requestIndicators message
          const pair = msg.pair.toLowerCase();
          const tf = msg.timeframe;
          console.log(`Client requested indicators for ${pair}/${tf}`);

          // Validate the timeframe
          if (!TIMEFRAMES.includes(tf)) {
            console.warn(`Invalid timeframe requested: ${tf}`);
            ws.send(JSON.stringify({
              type: 'error',
              message: `Timeframe '${tf}' not supported. Valid timeframes: ${TIMEFRAMES.join(', ')}`,
              requestType: 'requestIndicators'
            }));
            return;
          }

          // Log the timeframe to help with debugging
          console.log(`Processing indicators request for timeframe: ${tf}`);

          // Double-check that we're using the correct timeframe throughout the process
          if (!TIMEFRAMES.includes(tf)) {
            console.error(`Invalid timeframe: ${tf}. This should have been caught earlier.`);
            ws.send(JSON.stringify({
              type: 'error',
              message: `Invalid timeframe: ${tf}`,
              requestType: 'requestIndicators'
            }));
            return;
          }

          // Validate the strategy if provided
          if (msg.strategy && !TRADING_STRATEGIES[msg.strategy]) {
            console.warn(`Invalid strategy requested: ${msg.strategy}`);
            ws.send(JSON.stringify({
              type: 'error',
              message: `Strategy '${msg.strategy}' not found. Using current strategy.`
            }));
            // Don't return, just don't update the strategy
          } else if (msg.strategy) {
            // Update subscription with strategy if provided and valid
            if (subscriptions.has(ws)) {
              const sub = subscriptions.get(ws);
              sub.strategy = msg.strategy;
              subscriptions.set(ws, sub);
            } else {
              subscriptions.set(ws, { pair, strategy: msg.strategy });
            }
          } else {
            // No strategy provided, use existing or default
            if (!subscriptions.has(ws)) {
              subscriptions.set(ws, { pair: 'xbtusdt', strategy: 'admiral_toa' });
            }
          }

          // Make sure we have data for this pair/timeframe
          const updateKey = `${pair}-${tf}`;
          const now = Date.now();

          // Check if we've updated this pair/timeframe recently in the periodic update
          const lastUpdate = lastPeriodicUpdate[updateKey] || 0;
          const timeSinceLastUpdate = now - lastUpdate;

          // Only update if it's been more than 60 seconds since the last periodic update
          if (timeSinceLastUpdate > 60000) {
            // Check if we need to force update based on the last fetch time
            const lastFetch = lastFetchTime[updateKey] || 0;
            const shouldForceUpdate = now - lastFetch > 60000;

            // Fetch the data
            if (shouldForceUpdate) {
              await fetchOHLC(pair, tf, true);
            } else {
              await fetchOHLC(pair, tf, false);
            }

            // Update the last periodic update time
            lastPeriodicUpdate[updateKey] = now;

            // Calculate indicators
            const indicators = calculateAllIndicators(pair, tf);

            // Double-check that the timeframe is correctly set in the data
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Timeframe mismatch in periodic update: expected ${tf} but got ${indicators.metadata?.timeframe}`);
              // Fix the metadata
              if (!indicators.metadata) indicators.metadata = {};
              indicators.metadata.timeframe = tf;
            }

            // Verify all indicators have the correct timeframe
            Object.keys(indicators).forEach(ind => {
              if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
                if (indicators[ind].timeframe !== tf) {
                  console.error(`Indicator ${ind} has incorrect timeframe in periodic update: ${indicators[ind].timeframe} (expected ${tf})`);
                  indicators[ind].timeframe = tf; // Fix the timeframe
                }
              }
            });

            // Send indicator updates to all subscribed clients
            subscriptions.forEach(({ pair: subPair }, ws) => {
              if (subPair === pair && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                  type: 'indicators',
                  pair,
                  timeframe: tf,
                  data: indicators
                }));
              }
            });
          } else {
            console.log(`Skipping update for ${pair}/${tf} - last updated ${Math.floor(timeSinceLastUpdate/1000)}s ago`);
          }

          // Add a small delay between timeframe updates to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY / 2));
        } catch (tfError) {
          console.error(`Error updating ${pair}/${tf}: ${tfError.message}`);
        }
      } else if (msg.type === 'selectPair') {
        // Handle selectPair message
        const pair = msg.pair.toLowerCase();
        console.log(`Client selected pair: ${pair}`);

        // Store subscription info
        if (subscriptions.has(ws)) {
          const sub = subscriptions.get(ws);
          sub.pair = pair;
          subscriptions.set(ws, sub);
        } else {
          subscriptions.set(ws, { pair, strategy: 'admiral_toa' });
        }

        // Fetch data for the selected pair
        for (const tf of TIMEFRAMES) {
          try {
            const updateKey = `${pair}-${tf}`;
            const now = Date.now();

            // Check if we already have data for this pair/timeframe
            const hasData = historicalData[pair]?.[tf]?.length > 0;
            const lastFetch = lastFetchTime[updateKey] || 0;
            const dataAge = now - lastFetch;

            // Only fetch if we don't have data or it's older than 30 seconds
            if (!hasData || dataAge > 30000) {
              await fetchOHLC(pair, tf);
              // Update the last periodic update time to prevent immediate re-fetch
              lastPeriodicUpdate[updateKey] = now;
            } else {
              console.log(`Using existing data for ${pair}/${tf} (${historicalData[pair][tf].length} candles, ${Math.floor(dataAge/1000)}s old)`);
            }

            // Send historical data to the client
            ws.send(JSON.stringify({ type: 'historicalData', pair, timeframe: tf, data: historicalData[pair]?.[tf] || [] }));

            // Calculate and send indicators
            const indicators = calculateAllIndicators(pair, tf);

            // Double-check that the timeframe is correctly set in the data
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Timeframe mismatch in selectPair: expected ${tf} but got ${indicators.metadata?.timeframe}`);
              // Fix the metadata
              if (!indicators.metadata) indicators.metadata = {};
              indicators.metadata.timeframe = tf;
            }

            // Verify all indicators have the correct timeframe
            Object.keys(indicators).forEach(ind => {
              if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
                if (indicators[ind].timeframe !== tf) {
                  console.error(`Indicator ${ind} has incorrect timeframe in selectPair: ${indicators[ind].timeframe} (expected ${tf})`);
                  indicators[ind].timeframe = tf; // Fix the timeframe
                }
              }
            });

            ws.send(JSON.stringify({
              type: 'indicators',
              pair,
              timeframe: tf,
              data: indicators || {}
            }));
          } catch (e) {
            console.error(`Error fetching OHLC for ${pair}/${tf}: ${e.message}`);
          }
        }

        // Send acknowledgement
        ws.send(JSON.stringify({ type: 'pairSelected', pair }));
      } else if (msg.type === 'setStrategy') {
        // Handle setStrategy message
        const strategy = msg.strategy;
        console.log(`Client set strategy: ${strategy}`);

        // Validate the strategy exists
        if (!TRADING_STRATEGIES[strategy]) {
          console.warn(`Invalid strategy requested: ${strategy}`);
          ws.send(JSON.stringify({
            type: 'error',
            message: `Strategy '${strategy}' not found. Using default strategy.`
          }));
          return;
        }

        // Store subscription info
        if (subscriptions.has(ws)) {
          const sub = subscriptions.get(ws);
          sub.strategy = strategy;
          subscriptions.set(ws, sub);
        } else {
          // Default to xbtusdt if no pair is set
          subscriptions.set(ws, { pair: 'xbtusdt', strategy });
        }

        // Send acknowledgement
        ws.send(JSON.stringify({ type: 'strategySet', strategy }));

        // Recalculate indicators with the new strategy and send updates
        const pair = subscriptions.get(ws).pair;
        for (const tf of TIMEFRAMES) {
          try {
            const indicators = calculateAllIndicators(pair, tf);

            // Double-check that the timeframe is correctly set in the data
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Timeframe mismatch in setStrategy: expected ${tf} but got ${indicators.metadata?.timeframe}`);
              // Fix the metadata
              if (!indicators.metadata) indicators.metadata = {};
              indicators.metadata.timeframe = tf;
            }

            // Verify all indicators have the correct timeframe
            Object.keys(indicators).forEach(ind => {
              if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
                if (indicators[ind].timeframe !== tf) {
                  console.error(`Indicator ${ind} has incorrect timeframe in setStrategy: ${indicators[ind].timeframe} (expected ${tf})`);
                  indicators[ind].timeframe = tf; // Fix the timeframe
                }
              }
            });

            ws.send(JSON.stringify({
              type: 'indicators',
              pair,
              timeframe: tf,
              data: indicators || {}
            }));
          } catch (e) {
            console.error(`Error calculating indicators for ${pair}/${tf} with strategy ${strategy}: ${e.message}`);
          }
        }
      }
    } catch (e) {
      console.error(`WebSocket message error: ${e.message}`);
    }
  });

  ws.on('close', () => {
    console.log('WebSocket client disconnected');
    subscriptions.delete(ws);
  });

  // Handle pong messages for heartbeat
  ws.on('pong', () => {
    ws.isAlive = true;
  });
});

// Track the last time we updated each pair/timeframe in the periodic update
const lastPeriodicUpdate = {};

// Periodic data updates - optimized to reduce API rate limiting issues
setInterval(async () => {
  // Only update data for pairs that have active subscriptions
  const activePairs = new Set([...subscriptions.values()].map(sub => sub.pair));
  console.log(`Updating data for active pairs: ${[...activePairs].join(', ')}`);

  for (const pair of [...activePairs]) {
    try {
      // Update price data first (most important for real-time trading)
      const price = await fetchLivePrice(pair);
      if (price && price.price > 0) {
        lastLivePrice[pair] = price;
        // Send price updates to all subscribed clients
        subscriptions.forEach(({ pair: subPair }, ws) => {
          if (subPair === pair && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'livePrice',
              pair,
              data: price
            }));
          }
        });
      }

      // Update OHLC data for each timeframe
      for (const tf of TIMEFRAMES) {
        try {
          const updateKey = `${pair}-${tf}`;
          const now = Date.now();

          // Check if we've updated this pair/timeframe recently in the periodic update
          const lastUpdate = lastPeriodicUpdate[updateKey] || 0;
          const timeSinceLastUpdate = now - lastUpdate;

          // Only update if it's been more than 60 seconds since the last periodic update
          if (timeSinceLastUpdate > 60000) {
            // Check if we need to force update based on the last fetch time
            const lastFetch = lastFetchTime[updateKey] || 0;
            const shouldForceUpdate = now - lastFetch > 60000;

            // Fetch the data
            if (shouldForceUpdate) {
              await fetchOHLC(pair, tf, true);
            } else {
              await fetchOHLC(pair, tf, false);
            }

            // Update the last periodic update time
            lastPeriodicUpdate[updateKey] = now;

            // Calculate indicators
            const indicators = calculateAllIndicators(pair, tf);

            // Double-check that the timeframe is correctly set in the data
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Timeframe mismatch in periodic update: expected ${tf} but got ${indicators.metadata?.timeframe}`);
              // Fix the metadata
              if (!indicators.metadata) indicators.metadata = {};
              indicators.metadata.timeframe = tf;
            }

            // Verify all indicators have the correct timeframe
            Object.keys(indicators).forEach(ind => {
              if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
                if (indicators[ind].timeframe !== tf) {
                  console.error(`Indicator ${ind} has incorrect timeframe in periodic update: ${indicators[ind].timeframe} (expected ${tf})`);
                  indicators[ind].timeframe = tf; // Fix the timeframe
                }
              }
            });

            // Send indicator updates to all subscribed clients
            subscriptions.forEach(({ pair: subPair }, ws) => {
              if (subPair === pair && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                  type: 'indicators',
                  pair,
                  timeframe: tf,
                  data: indicators
                }));
              }
            });
          } else {
            console.log(`Skipping update for ${pair}/${tf} - last updated ${Math.floor(timeSinceLastUpdate/1000)}s ago`);
          }

          // Add a small delay between timeframe updates to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY / 2));
        } catch (tfError) {
          console.error(`Error updating ${pair}/${tf}: ${tfError.message}`);
        }
      }

      // Add a delay between pairs to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));
    } catch (pairError) {
      console.error(`Error updating ${pair}: ${pairError.message}`);
    }
  }
}, 60000); // Update every minute

// Heartbeat mechanism to detect and clean up dead connections

// Check for dead connections every 30 seconds
setInterval(() => {
  wss.clients.forEach(ws => {
    if (!ws.isAlive) {
      console.log('Terminating inactive WebSocket connection');
      subscriptions.delete(ws);
      return ws.terminate();
    }

    ws.isAlive = false;
    ws.ping(() => {});
  });
}, 30000);

console.log(`StarCrypt Enterprise server running on port ${port}`);
console.log(`WebSocket server running on ws://localhost:8080`);
console.log(`Supported timeframes: ${TIMEFRAMES.join(', ')}`);
console.log(`Supported strategies: ${Object.keys(TRADING_STRATEGIES).join(', ')}`);
console.log(`Supported pairs: ${Object.keys(KRAKEN_PAIRS).join(', ')}`);