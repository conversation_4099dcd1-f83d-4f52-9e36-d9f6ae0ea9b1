/* Mini-chart styles */
.mini-chart-container {
    position: relative;
    width: 100%;
    height: 80px;
    margin: 5px 0;
    background: rgba(0, 10, 20, 0.5); /* Lighter background */
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 5px;
    overflow: hidden;
    box-shadow: none; /* Removed box shadow */
}

.mini-chart-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 15; /* Further increased to ensure it's above all overlays */
    box-shadow: none; /* Remove any shadow that might be affecting the chart */
    background: transparent !important; /* Force transparent background */
}

/* Apply to all canvas elements in mini charts */
.mini-chart canvas {
    z-index: 15;
    box-shadow: none !important;
    background: transparent !important;
}

.live-readout {
    position: absolute;
    top: 2px;
    left: 3px;
    width: auto; /* Auto width to fit content */
    font-family: 'Orbitron', sans-serif; /* Back to Orbitron for consistency */
    font-size: 8px; /* Larger font size for better readability */
    font-weight: bold;
    letter-spacing: normal; /* Normal letter spacing */
    color: #00FFFF;
    text-shadow: 0 0 2px rgba(0, 255, 255, 0.8); /* Sharper text shadow */
    z-index: 10; /* Increased z-index to ensure it's above other elements */
    background: rgba(0, 0, 0, 0.7); /* Darker background for better contrast */
    padding: 1px 2px; /* Slightly more padding */
    border-radius: 2px;
    white-space: nowrap;
    overflow: visible; /* Make overflow visible */
    max-width: none; /* Remove max width restriction */
    border: 1px solid rgba(0, 255, 255, 0.3); /* More visible border */
    transform: none; /* No scaling - causes blurriness */
    line-height: 1.2; /* Better line height */
}

.timestamp {
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-family: 'Orbitron', sans-serif;
    font-size: 8px;
    color: rgba(0, 255, 255, 0.7);
    z-index: 2;
    background: rgba(0, 10, 20, 0.7);
    padding: 1px 3px;
    border-radius: 2px;
}

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: 0;
    pointer-events: none; /* Ensures the grid doesn't interfere with chart interactions */
}

.indicator-label {
    position: absolute;
    top: 5px;
    left: 10px;
    font-family: 'Orbitron', sans-serif;
    font-size: 10px;
    font-weight: bold;
    color: rgba(0, 255, 255, 0.7);
    text-transform: uppercase;
    z-index: 2;
    background: rgba(0, 10, 20, 0.7);
    padding: 2px 5px;
    border-radius: 3px;
}

/* Remove any potential overlay from mini-chart elements */
.mini-chart::before,
.mini-chart::after {
    display: none !important;
    content: none !important;
    box-shadow: none !important;
    background: none !important;
}

/* Ensure chart data is visible */
.mini-chart .chart-line,
.mini-chart path,
.mini-chart line {
    z-index: 20 !important;
    stroke-width: 1.5px !important; /* Make lines more visible */
    opacity: 1 !important;
}

/* Signal circle tooltip styles */
.signal-circle {
    position: relative;
    cursor: pointer;
    /* Enhanced styling for better visibility */
    width: 33px;
    height: 33px;
    border-radius: 50%;
    box-shadow: 0 0 12px rgba(0, 255, 255, 0.5);
    margin: 1px;
    transition: all 0.2s ease;
}

.signal-circle::after {
    content: attr(title);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 10, 20, 0.95);
    color: #00FFFF;
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid rgba(0, 255, 255, 0.5);
    white-space: nowrap;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
}

.signal-circle:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Status icons for live readouts */
.status-icon {
    margin-left: 5px;
    font-size: 10px;
}

.status-up {
    color: #00FF00;
}

.status-down {
    color: #FF0000;
}

/* Animation for value updates */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Chart tooltip styles */
.chartjs-tooltip {
    background: rgba(0, 10, 20, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.5) !important;
    border-radius: 5px !important;
    color: #00FFFF !important;
    font-family: 'Orbitron', sans-serif !important;
    padding: 10px !important;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
    z-index: 1000 !important;
}

/* Ensure signal matrix has proper spacing */
.signal-matrix {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.signal-row {
    display: flex;
    align-items: center;
    gap: 5px;
}

.indicator-name {
    width: 120px;
    font-family: 'Orbitron', sans-serif;
    font-size: 12px;
    color: #00FFFF;
    text-transform: uppercase;
}

.signal-cell {
    width: 33px;
    height: 33px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Ensure all indicators are visible */
[data-indicator] {
    display: flex !important;
}

/* Enhanced signal circle color classes */
.green-light {
    background-color: rgba(0, 255, 0, 0.3) !important;
    border: 2px solid #00FF00 !important;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.6) !important;
}

.blue-light {
    background-color: rgba(0, 100, 255, 0.3) !important;
    border: 2px solid #0064FF !important;
    box-shadow: 0 0 15px rgba(0, 100, 255, 0.6) !important;
}

.orange-light {
    background-color: rgba(255, 165, 0, 0.3) !important;
    border: 2px solid #FFA500 !important;
    box-shadow: 0 0 15px rgba(255, 165, 0, 0.6) !important;
}

.red-light {
    background-color: rgba(255, 0, 0, 0.3) !important;
    border: 2px solid #FF0000 !important;
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.6) !important;
}

.grey-light {
    background-color: rgba(128, 128, 128, 0.3) !important;
    border: 2px solid #808080 !important;
    box-shadow: 0 0 15px rgba(128, 128, 128, 0.6) !important;
}

/* Signal classes for trade direction */
.degen-buy {
    background-color: rgba(0, 255, 0, 0.3) !important;
    border: 3px solid #00FF00 !important;
    box-shadow: 0 0 18px rgba(0, 255, 0, 0.7) !important;
}

.mild-buy {
    background-color: rgba(0, 100, 255, 0.3) !important;
    border: 3px solid #0064FF !important;
    box-shadow: 0 0 18px rgba(0, 100, 255, 0.7) !important;
}

.neutral {
    background-color: rgba(128, 128, 128, 0.3) !important;
    border: 3px solid #808080 !important;
    box-shadow: 0 0 18px rgba(128, 128, 128, 0.7) !important;
}

.mild-sell {
    background-color: rgba(255, 165, 0, 0.3) !important;
    border: 3px solid #FFA500 !important;
    box-shadow: 0 0 18px rgba(255, 165, 0, 0.7) !important;
}

.degen-sell {
    background-color: rgba(255, 0, 0, 0.3) !important;
    border: 3px solid #FF0000 !important;
    box-shadow: 0 0 18px rgba(255, 0, 0, 0.7) !important;
}
