/* ============================================
   THEME TOGGLE COMPONENT
   ============================================ */

/* Container */
#themeToggleContainer {
    --toggle-height: 2.5rem;
    --toggle-padding: 0.5rem 1rem;
    --toggle-gap: 0.5rem;
    --toggle-radius: 2rem;
    --toggle-font-size: 0.9rem;
    --toggle-icon-size: 1.1em;
    --toggle-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    display: flex;
    align-items: center;
    margin: 0 0.5rem;
    position: relative;
    z-index: 100;
    
    /* Animation for container entrance */
    opacity: 0;
    transform: translateY(-5px);
    transition: var(--toggle-transition);
}

#themeToggleContainer.ready {
    opacity: 1;
    transform: translateY(0);
}

/* Base button styles */
.theme-toggle {
    /* Layout */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--toggle-gap);
    padding: var(--toggle-padding);
    height: var(--toggle-height);
    min-width: 6rem;
    
    /* Visual styling */
    border: 1px solid transparent;
    border-radius: var(--toggle-radius);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--toggle-font-size);
    font-weight: 500;
    line-height: 1;
    cursor: pointer;
    user-select: none;
    
    /* Effects */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: var(--toggle-transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
    
    /* Hide until initialized */
    opacity: 0;
    transform: scale(0.9);
}

/* Ready state - smooth entrance */
.theme-toggle--ready {
    opacity: 1;
    transform: scale(1);
}

/* Hover and focus states */
.theme-toggle:not(:disabled):hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-toggle:not(:disabled):active {
    transform: translateY(1px) scale(0.99);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.theme-toggle:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb, 0, 180, 216), 0.3);
}

/* Theme icon */
.theme-icon {
    font-size: var(--toggle-icon-size);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
    will-change: transform;
}

/* Theme text */
.theme-text {
    font-weight: 500;
    transition: opacity 0.3s ease, transform 0.3s ease;
    white-space: nowrap;
}

/* Theme-specific styles */
[data-theme="dark"] .theme-toggle {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

[data-theme="dark"] .theme-toggle:hover {
    background: var(--accent-color);
    color: #0a0a1a;
    border-color: transparent;
}

[data-theme="light"] .theme-toggle {
    background: rgba(0, 0, 0, 0.03);
    border-color: rgba(0, 0, 0, 0.1);
    color: var(--text-primary);
}

[data-theme="light"] .theme-toggle:hover {
    background: var(--accent-color);
    color: white;
    border-color: transparent;
}

[data-theme="cosmic"] .theme-toggle {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.1), rgba(106, 90, 205, 0.1));
    border: 1px solid rgba(138, 43, 226, 0.3);
    color: #e0e0e0;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

[data-theme="cosmic"] .theme-toggle:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    color: white;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
    border-color: transparent;
    box-shadow: 0 0 15px rgba(138, 43, 226, 0.5);
}

/* Responsive styles */
@media (max-width: 1200px) {
    #themeToggleContainer {
        --toggle-padding: 0.4rem 0.8rem;
        --toggle-font-size: 0.85rem;
    }
    
    .theme-text {
        display: none;
    }
    
    .theme-toggle {
        padding: 0.5rem;
        min-width: var(--toggle-height);
        width: var(--toggle-height);
        height: var(--toggle-height);
        border-radius: 50%;
    }
    
    /* Hide tooltips on mobile */
    .theme-toggle::after {
        display: none;
    }
}

/* Animations */
@keyframes themePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animation classes */
.theme-toggle.animate .theme-icon {
    animation: themePulse 0.5s ease;
}

.theme-toggle:hover .theme-icon {
    transform: rotate(30deg);
}

.theme-toggle.theme-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
                background-color 0.3s ease,
                border-color 0.3s ease,
                color 0.3s ease;
}

/* Tooltip */
.theme-toggle::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: calc(100% + 8px);
    left: 50%;
    transform: translateX(-50%) translateY(5px);
    padding: 0.4rem 0.8rem;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(4px);
}

.theme-toggle:hover::after {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

/* Accessibility: Focus ring for keyboard navigation */
.theme-toggle:focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    #themeToggleContainer,
    .theme-toggle,
    .theme-icon,
    .theme-text,
    .theme-toggle::after {
        transition: none !important;
        animation: none !important;
    }
    
    .theme-toggle:hover {
        transform: none !important;
    }
    
    .theme-toggle:active {
        transform: scale(0.98) !important;
    }
}

/* Screen reader only text */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
