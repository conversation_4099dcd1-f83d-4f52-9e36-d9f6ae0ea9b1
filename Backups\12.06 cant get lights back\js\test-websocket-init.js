/**
 * Test WebSocket Initialization
 * Verifies that all WebSocket components are properly loaded and initialized
 */

document.addEventListener('DOMContentLoaded', () => {
    console.log('=== WebSocket Initialization Test ===');
    
    // Check if WebSocketManager is available
    if (typeof WebSocketManager === 'undefined') {
        console.error('❌ WebSocketManager is not defined');
    } else {
        console.log('✅ WebSocketManager is available');
        console.log('   - Version:', WebSocketManager.VERSION || 'N/A');
    }
    
    // Check if WebSocketProcessorV2 is available
    if (typeof WebSocketProcessorV2 === 'undefined') {
        console.error('❌ WebSocketProcessorV2 is not defined');
    } else {
        console.log('✅ WebSocketProcessorV2 is available');
    }
    
    // Check if global instances are available
    if (typeof window.wsManager === 'undefined') {
        console.warn('⚠️  wsManager is not available (should be set by websocket-init.js)');
    } else {
        console.log('✅ wsManager instance is available');
    }
    
    if (typeof window.wsProcessor === 'undefined') {
        console.warn('⚠️  wsProcessor is not available (should be set by websocket-init.js)');
    } else {
        console.log('✅ wsProcessor instance is available');
    }
    
    console.log('=== End of WebSocket Initialization Test ===');
});
