/**
 * TradingView Widget Initialization
 * Handles the initialization of the TradingView widget with proper datafeed loading
 */

// Global reference to the TradingView widget
window.tradingViewWidget = null;

// Maximum number of retry attempts for Datafeeds
const MAX_RETRIES = 10;
let retryCount = 0;

/**
 * Initialize the TradingView widget with the specified symbol, interval, and studies
 * @param {string} targetSymbol - The trading pair symbol (e.g., 'KRAKEN:BTCUSD')
 * @param {string} targetInterval - The chart interval (e.g., '60' for 1h)
 * @param {Array<string>} studies - Array of study IDs to be displayed on the chart
 * @param {Array} logMessages - Reference to the log messages array
 * @param {Function} updateLogger - Function to update the logger display
 */
function initTradingViewWidget(targetSymbol, targetInterval, studies = [], logMessages = [], updateLogger = console.log) {
  // Check if TradingView is available
  if (typeof TradingView === 'undefined') {
    logMessages.push(`[${new Date().toISOString()}] TradingView library not loaded yet, retrying... (${retryCount + 1}/${MAX_RETRIES})`);
    if (updateLogger) updateLogger();
    
    if (retryCount < MAX_RETRIES) {
      retryCount++;
      setTimeout(() => initTradingViewWidget(targetSymbol, targetInterval, studies, logMessages, updateLogger), 1000);
    } else {
      logMessages.push(`[${new Date().toISOString()}] Failed to load TradingView library after ${MAX_RETRIES} attempts`);
      if (updateLogger) updateLogger();
    }
    return;
  }

  // Check if Datafeeds is available
  if (typeof Datafeeds === 'undefined') {
    logMessages.push(`[${new Date().toISOString()}] Datafeeds library not loaded yet, retrying... (${retryCount + 1}/${MAX_RETRIES})`);
    if (updateLogger) updateLogger();
    
    if (retryCount < MAX_RETRIES) {
      retryCount++;
      setTimeout(() => initTradingViewWidget(targetSymbol, targetInterval, studies, logMessages, updateLogger), 1000);
      return;
    } else {
      logMessages.push(`[${new Date().toISOString()}] Failed to load Datafeeds after ${MAX_RETRIES} attempts`);
      if (updateLogger) updateLogger();
      return;
    }
  }

  try {
    logMessages.push(`[${new Date().toISOString()}] Initializing TradingView widget for ${targetSymbol} on ${targetInterval}`);
    if (updateLogger) updateLogger();

    // Create widget options
    const widgetOptions = {
      symbol: targetSymbol,
      interval: targetInterval,
      container_id: 'tradingview_candle',
      width: '100%',
      height: '100%',
      locale: 'en',
      theme: document.body.classList.contains('dark-mode') ? 'dark' : 'light',
      style: '1',
      toolbar_bg: '#1a1a2a',
      enable_publishing: false,
      allow_symbol_change: true,
      hide_side_toolbar: false,
      studies: studies,
      autosize: true,
      datafeed: new Datafeeds.UDFCompatibleDatafeed("https://demo-feed-data.tradingview.com"),
      library_path: 'https://s3.tradingview.com/tv.js',
      disabled_features: [
        'header_widget',
        'left_toolbar',
        'header_chart_properties',
        'header_indicators',
        'header_compare',
        'header_screenshot',
        'header_undo_redo',
        'header_saveload',
        'header_fullscreen_button',
        'timeframes_toolbar',
        'edit_buttons_in_legend',
        'context_menus',
        'control_bar'
      ],
      enabled_features: [
        'hide_left_toolbar_by_default',
        'side_toolbar_in_fullscreen_mode',
        'header_in_fullscreen_mode'
      ]
    };

    // Initialize or update the tradingViewManager if it doesn't exist
    if (!window.StarCrypt) window.StarCrypt = {};
    if (!window.StarCrypt.tradingViewManager) {
      window.StarCrypt.tradingViewManager = {
        chart: null,
        currentSymbol: targetSymbol,
        currentInterval: targetInterval,
        setSymbol: function(symbol, interval) {
          if (this.chart) {
            this.chart.setSymbol(symbol, interval);
            this.currentSymbol = symbol;
            this.currentInterval = interval;
          }
        }
      };
    }

    // Remove existing chart if it exists
    if (window.StarCrypt.tradingViewManager.chart && typeof window.StarCrypt.tradingViewManager.chart.remove === 'function') {
      window.StarCrypt.tradingViewManager.chart.remove();
    }

    // Create new chart instance
    window.StarCrypt.tradingViewManager.chart = new TradingView.widget(widgetOptions);
    const chart = window.StarCrypt.tradingViewManager.chart;
    console.log('[TradingViewInit] TradingView widget created:', chart);
    if (window.tradingViewWidget) {
      console.log('[TradingViewInit] window.tradingViewWidget exists:', window.tradingViewWidget);
    }
    if (window.StarCrypt && window.StarCrypt.tradingViewManager && window.StarCrypt.tradingViewManager.chart) {
      console.log('[TradingViewInit] window.StarCrypt.tradingViewManager.chart assigned:', window.StarCrypt.tradingViewManager.chart);
    }
    // Robustly link the new TradingView widget to the SignalSystem for reliable timeframe sync
    function linkTradingViewToSignalSystem(chart, attempt = 1, maxAttempts = 100) {
      if (
        window.StarCrypt &&
        window.StarCrypt.signalSystem &&
        typeof window.StarCrypt.signalSystem.setTradingViewChart === 'function'
      ) {
        console.log(`[TradingViewInit] Linking TradingView widget to SignalSystem on attempt ${attempt}.`);
        window.StarCrypt.signalSystem.setTradingViewChart(chart);
        if (
          window.StarCrypt.signalSystem.config &&
          window.StarCrypt.signalSystem.config.debug
        ) {
          console.log('[TradingViewInit] Linked TradingView widget to SignalSystem.');
        }
      } else if (attempt < maxAttempts) {
        if (attempt % 10 === 0) {
          console.warn(`[TradingViewInit] SignalSystem.setTradingViewChart not available (attempt ${attempt}/${maxAttempts})...`);
        }
        setTimeout(() => linkTradingViewToSignalSystem(chart, attempt + 1, maxAttempts), 100);
      } else {
        console.error('[TradingViewInit] SignalSystem.setTradingViewChart not available after 10 seconds of retrying.');
      }
    }
    linkTradingViewToSignalSystem(chart);
    
    // Set up chart ready callback
    chart.onChartReady(() => {
      logMessages.push(`[${new Date().toLocaleString()}] New TradingView chart ready for ${targetSymbol} on ${targetInterval}.`);
      
      // Store reference to the chart in window.StarCrypt.tradingViewManager
      if (!window.StarCrypt) window.StarCrypt = {};
      
      // Initialize tradingViewManager if it doesn't exist
      if (!window.StarCrypt.tradingViewManager) {
        window.StarCrypt.tradingViewManager = {
          chart: null,
          currentSymbol: targetSymbol,
          currentInterval: targetInterval,
          setSymbol: function(symbol, interval) {
            if (this.chart) {
              this.chart.setSymbol(symbol, interval);
              this.currentSymbol = symbol;
              this.currentInterval = interval;
            }
          }
        };
      }
      
      // Update the chart reference
      window.StarCrypt.tradingViewManager.chart = chart;
      
      // Connect to SignalSystem if available
      if (window.StarCrypt.signalSystem) {
        // Initialize SignalSystem with the chart
        if (typeof window.StarCrypt.signalSystem.setTradingViewChart === 'function') {
          window.StarCrypt.signalSystem.setTradingViewChart(chart);
          
          // Set initial timeframe
          if (targetInterval) {
            const intervalMap = {
              '1': '1m', '5': '5m', '15': '15m',
              '60': '1h', '240': '4h', '1D': '1d', '1W': '1w'
            };
            const tf = intervalMap[targetInterval] || '1h';
            window.StarCrypt.signalSystem.updateSelectedTimeframeGlow(tf);
          }
        } else {
          console.warn('[TradingViewInit] SignalSystem.setTradingViewChart is not a function');
        }
      } else {
        console.warn('[TradingViewInit] SignalSystem not available on window.StarCrypt');
      }
      
      updateLogger();
    });
    
    // Store reference to the widget
    window.tradingViewWidget = chart;
    
    // Also store in window.StarCrypt for easier access
    if (!window.StarCrypt) window.StarCrypt = {};
    if (!window.StarCrypt.tradingViewManager) {
      window.StarCrypt.tradingViewManager = {
        chart: chart,
        currentSymbol: targetSymbol,
        currentInterval: targetInterval
      };
    }
    
  } catch (error) {
    console.error('Error initializing TradingView widget:', error);
    logMessages.push(`[${new Date().toLocaleString()}] Error initializing TradingView: ${error.message}`);
    updateLogger();
  }
}

// Make the function available globally
window.initTradingViewWidget = initTradingViewWidget;
