// Enhanced Strategy Animations & Indicator Handler
// This script ensures all strategy animations work properly and indicators display correctly

// INDICATOR_DISPLAY_NAMES is now defined globally in global-variables.js; do not redefine here.

// Define 5-color logic for all indicators
// Each indicator has colors for: strong buy, mild buy, neutral, mild sell, strong sell
const INDICATOR_COLOR_SCHEMES = {
  rsi: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  stochRsi: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  macd: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  bollingerBands: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  adx: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  williams<PERSON>: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  ultimateOscillator: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  mfi: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  volume: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  vwap: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  atr: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  fractal: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  ml: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  sentiment: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  entropy: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  correlation: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
  time_anomaly: ['#00FF00', '#0000FF', '#808080', '#FFA500', '#FF0000'],
}

// Initialize our enhanced indicator display system
function initializeEnhancedIndicators() {
  console.log('[EnhancedIndicators] Initializing enhanced indicator system')

  // Ensure all necessary indicators have rows in the signal matrix
  document.addEventListener('DOMContentLoaded', () => {
    // Replace the existing showStrategyAnimation function with our enhanced version
    window.originalShowStrategyAnimation = window.showStrategyAnimation || function () {}
    window.showStrategyAnimation = enhancedShowStrategyAnimation

    // Enhance the switchStrategy function to force update all indicators
    const originalSwitchStrategy = window.switchStrategy
    window.switchStrategy = function (strategy) {
      console.log('[EnhancedIndicators] Enhanced strategy switch to:', strategy)

      try {
        // Call the original function
        originalSwitchStrategy.apply(this, arguments)

        // Get the strategy details
        const strategyDetails = window.TRADING_STRATEGIES[strategy]
        if (!strategyDetails) {
          console.error('[EnhancedIndicators] Strategy not found:', strategy)
          return
        }

        // Create and display all required indicator rows
        createRequiredIndicatorRows(strategyDetails.indicators)

        // Show the enhanced strategy animation
        enhancedShowStrategyAnimation(strategy)

        // Apply indicator coloring based on strategy
        colorIndicatorNames(strategyDetails.indicators, strategy)
      } catch (error) {
        console.error('[EnhancedIndicators] Error in enhanced switchStrategy:', error.message)
      }
    }

    // Initialize for current strategy after a short delay
    setTimeout(() => {
      if (window.currentStrategy && window.TRADING_STRATEGIES[window.currentStrategy]) {
        const indicators = window.TRADING_STRATEGIES[window.currentStrategy].indicators || []
        createRequiredIndicatorRows(indicators)
        colorIndicatorNames(indicators, window.currentStrategy)
      }
    }, 1000)
  })
}

// Enhanced strategy animation function that works for all strategies
function enhancedShowStrategyAnimation(strategy) {
  console.log('[EnhancedIndicators] Showing animation for strategy:', strategy)

  try {
    // Get strategy details
    const strategyDetails = window.TRADING_STRATEGIES[strategy]
    if (!strategyDetails) {
      console.error('[EnhancedIndicators] Cannot show animation, strategy not found:', strategy)
      return
    }

    // Remove any existing animation containers
    const existingContainers = document.querySelectorAll('.strategy-switch-animation')
    existingContainers.forEach(container => {
      container.remove()
    })

    // Create animation container
    const animationContainer = document.createElement('div')
    animationContainer.className = 'strategy-switch-animation'
    animationContainer.style.position = 'fixed'
    animationContainer.style.top = '0'
    animationContainer.style.left = '0'
    animationContainer.style.width = '100vw'
    animationContainer.style.height = '100vh'
    animationContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)'
    animationContainer.style.zIndex = '9999'
    animationContainer.style.display = 'flex'
    animationContainer.style.flexDirection = 'column'
    animationContainer.style.justifyContent = 'center'
    animationContainer.style.alignItems = 'center'
    animationContainer.style.color = 'white'
    animationContainer.style.textAlign = 'center'
    animationContainer.style.padding = '20px'
    animationContainer.style.boxSizing = 'border-box'
    animationContainer.style.opacity = '0'
    animationContainer.style.transition = 'opacity 0.5s ease-in-out'

    document.body.appendChild(animationContainer)

    // Create content container
    const contentContainer = document.createElement('div')
    contentContainer.className = 'strategy-animation-content'
    contentContainer.style.maxWidth = '800px'
    contentContainer.style.width = '90%'
    contentContainer.style.backgroundColor = 'rgba(20, 20, 30, 0.9)'
    contentContainer.style.borderRadius = '10px'
    contentContainer.style.padding = '30px'
    contentContainer.style.boxShadow = '0 0 40px rgba(0, 100, 255, 0.5)'
    contentContainer.style.border = `2px solid ${strategyDetails.color || '#FFFFFF'}`

    // Create strategy title
    const strategyTitle = document.createElement('h2')
    strategyTitle.textContent = strategyDetails.name
    strategyTitle.style.color = strategyDetails.color || '#FFFFFF'
    strategyTitle.style.fontSize = '28px'
    strategyTitle.style.marginBottom = '15px'
    strategyTitle.style.textShadow = '0 0 10px rgba(255, 255, 255, 0.4)'

    // Create strategy description
    const strategyDescription = document.createElement('p')
    strategyDescription.textContent = strategyDetails.description
    strategyDescription.style.color = '#FFFFFF'
    strategyDescription.style.fontSize = '16px'
    strategyDescription.style.marginBottom = '25px'
    strategyDescription.style.lineHeight = '1.5'

    // Create indicators section
    const indicatorsTitle = document.createElement('h3')
    indicatorsTitle.textContent = 'Key Indicators'
    indicatorsTitle.style.color = strategyDetails.color || '#FFFFFF'
    indicatorsTitle.style.fontSize = '20px'
    indicatorsTitle.style.marginBottom = '15px'

    // Create indicators container
    const indicatorsContainer = document.createElement('div')
    indicatorsContainer.style.display = 'flex'
    indicatorsContainer.style.flexWrap = 'wrap'
    indicatorsContainer.style.justifyContent = 'center'
    indicatorsContainer.style.gap = '10px'
    indicatorsContainer.style.marginBottom = '20px'

    // Add indicators
    strategyDetails.indicators.forEach(indicator => {
      const indicatorBadge = document.createElement('div')
      indicatorBadge.className = 'indicator-badge'
      indicatorBadge.textContent = INDICATOR_DISPLAY_NAMES[indicator] || (typeof indicator === 'string' ? indicator.toUpperCase() : String(indicator))
      indicatorBadge.style.backgroundColor = 'rgba(30, 30, 40, 0.8)'
      indicatorBadge.style.color = getIndicatorColor(indicator)
      indicatorBadge.style.padding = '8px 12px'
      indicatorBadge.style.borderRadius = '5px'
      indicatorBadge.style.fontSize = '14px'
      indicatorBadge.style.fontWeight = 'bold'
      indicatorBadge.style.border = `1px solid ${getIndicatorColor(indicator)}`
      indicatorBadge.style.boxShadow = `0 0 10px ${getIndicatorColor(indicator, 0.3)}`
      indicatorBadge.style.opacity = '0'
      indicatorBadge.style.transform = 'translateY(20px)'

      indicatorsContainer.appendChild(indicatorBadge)
    })

    // Create dismiss button
    const dismissButton = document.createElement('button')
    dismissButton.textContent = 'Start Trading'
    dismissButton.style.backgroundColor = strategyDetails.color || '#FFFFFF'
    dismissButton.style.color = '#000000'
    dismissButton.style.border = 'none'
    dismissButton.style.borderRadius = '5px'
    dismissButton.style.padding = '10px 20px'
    dismissButton.style.fontSize = '16px'
    dismissButton.style.fontWeight = 'bold'
    dismissButton.style.cursor = 'pointer'
    dismissButton.style.marginTop = '20px'
    dismissButton.style.opacity = '0'
    dismissButton.style.transform = 'translateY(10px)'
    dismissButton.style.transition = 'all 0.3s ease-in-out'

    dismissButton.addEventListener('mouseover', () => {
      dismissButton.style.backgroundColor = '#FFFFFF'
    })

    dismissButton.addEventListener('mouseout', () => {
      dismissButton.style.backgroundColor = strategyDetails.color || '#FFFFFF'
    })

    dismissButton.addEventListener('click', () => {
      dismissStrategyAnimation(animationContainer)
    })

    // Build the DOM
    contentContainer.appendChild(strategyTitle)
    contentContainer.appendChild(strategyDescription)
    contentContainer.appendChild(indicatorsTitle)
    contentContainer.appendChild(indicatorsContainer)
    contentContainer.appendChild(dismissButton)
    animationContainer.appendChild(contentContainer)

    // Add click event to dismiss animation when clicking outside the content
    animationContainer.addEventListener('click', event => {
      if (event.target === animationContainer) {
        dismissStrategyAnimation(animationContainer)
      }
    })

    // Animate in
    setTimeout(() => {
      animationContainer.style.opacity = '1'

      // Animate indicators sequentially
      const indicatorBadges = indicatorsContainer.querySelectorAll('.indicator-badge')
      indicatorBadges.forEach((badge, index) => {
        setTimeout(() => {
          badge.style.transition = 'all 0.5s ease-out'
          badge.style.opacity = '1'
          badge.style.transform = 'translateY(0)'
        }, 100 + index * 100)
      })

      // Animate dismiss button
      setTimeout(() => {
        dismissButton.style.opacity = '1'
        dismissButton.style.transform = 'translateY(0)'
      }, 100 + indicatorBadges.length * 100 + 200)
    }, 50)

    // Auto-dismiss after 10 seconds
    setTimeout(() => {
      if (document.body.contains(animationContainer)) {
        dismissStrategyAnimation(animationContainer)
      }
    }, 10000)
  } catch (error) {
    console.error('[EnhancedIndicators] Error showing strategy animation:', error.message)
  }
}

// Function to dismiss strategy animation
function dismissStrategyAnimation(container) {
  container.style.opacity = '0'
  setTimeout(() => {
    if (document.body.contains(container)) {
      document.body.removeChild(container)
    }
  }, 500)
}

// Create indicator rows for all required indicators
function createRequiredIndicatorRows(requiredIndicators) {
  console.log('[EnhancedIndicators] Creating indicator rows for:', requiredIndicators)

  // Safety check
  if (!Array.isArray(requiredIndicators)) {
    console.error('[EnhancedIndicators] Invalid indicators array:', requiredIndicators)
    return
  }

  // Get the momentum table (Oracle Matrix)
  const momentumTable = document.getElementById('momentum-table')
  if (!momentumTable) {
    console.error('[EnhancedIndicators] Momentum table not found')
    return
  }

  // Get existing indicators
  const existingRows = Array.from(momentumTable.querySelectorAll('tr[data-indicator]'))
  const existingIndicators = existingRows.map(row => row.getAttribute('data-indicator'))

  // Handle visibility of existing rows
  existingRows.forEach(row => {
    const indicator = row.getAttribute('data-indicator')
    if (requiredIndicators.includes(indicator)) {
      row.style.display = '' // Show the row
    } else {
      row.style.display = 'none' // Hide the row
    }
  })

  // Find missing indicators
  const missingIndicators = requiredIndicators.filter(ind => !existingIndicators.includes(ind))

  // Create rows for missing indicators
  missingIndicators.forEach(indicator => {
    const newRow = createIndicatorRow(indicator)
    momentumTable.appendChild(newRow)
  })
}

// Create an indicator row
function createIndicatorRow(indicator) {
  try {
    console.log(`[EnhancedIndicators] Creating row for indicator: ${indicator}`)
    // Create row
    const row = document.createElement('tr')
    row.setAttribute('data-indicator', indicator)
    row.className = 'indicator-row signal-row' // Ensure both classes for compatibility

    // Create indicator name cell
    const nameCell = document.createElement('td')
    nameCell.className = 'signal-name'
    nameCell.id = `${indicator}-name`
    nameCell.textContent = INDICATOR_DISPLAY_NAMES[indicator] || (typeof indicator === 'string' ? indicator.toUpperCase() : String(indicator))
    row.appendChild(nameCell)

    // Create signal cells for each timeframe
    if (window.TIMEFRAMES) {
      window.TIMEFRAMES.forEach(timeframe => {
        const signalCell = document.createElement('td')
        signalCell.className = 'signal-light-cell'
        signalCell.setAttribute('data-timeframe', timeframe)

        // Create signal circle with the class and data attributes that update-signal-lights.js expects
        const signalCircle = document.createElement('div')
        signalCircle.className = 'signal-circle grey-light' // CRITICAL: use signal-circle class instead of signal-light
        signalCircle.id = `${indicator}-${timeframe}-signal`
        signalCircle.setAttribute('data-ind', indicator) // CRITICAL: this attribute is required for updates
        signalCircle.setAttribute('data-tf', timeframe) // CRITICAL: this attribute is required for updates
        signalCircle.setAttribute('data-indicator', indicator) // For consistency
        signalCircle.setAttribute('data-timeframe', timeframe) // For consistency
        signalCircle.setAttribute('data-tooltip', `${INDICATOR_DISPLAY_NAMES[indicator] || indicator.toUpperCase()} (${timeframe}): No data`)

        signalCell.appendChild(signalCircle)
        row.appendChild(signalCell)
      })
    }

    return row
  } catch (err) {
    if (window.DEBUG_MODE) console.error(`Error in createIndicatorRow for indicator ${indicator}: ${err.message}`)
    return null // Return null to indicate failure, can be handled by caller
  }
}

// Apply indicator coloring to match strategy
function colorIndicatorNames(indicators, strategy) {
  console.log(`[EnhancedIndicators] Coloring indicator names for strategy: ${strategy}`)

  try {
    // Get strategy details
    const strategyDetails = window.TRADING_STRATEGIES[strategy]
    if (!strategyDetails) {
      console.error('[EnhancedIndicators] Strategy not found:', strategy)
      return
    }

    // Apply color to each indicator
    indicators.forEach(indicator => {
      const nameElement = document.getElementById(`${indicator}-name`)
      if (nameElement) {
        nameElement.style.color = getIndicatorColor(indicator)
        nameElement.style.fontWeight = 'bold'
      }
    })
  } catch (error) {
    console.error('[EnhancedIndicators] Error coloring indicator names:', error.message)
  }
}

// Get indicator color based on indicator type
function getIndicatorColor(indicator, alpha = 1) {
  // Get colors that represent each indicator type
  const colors = {
    rsi: '#FF5722', // Orange-red for RSI
    stochRsi: '#FF9800', // Amber for Stoch RSI
    macd: '#FFEB3B', // Yellow for MACD
    bollingerBands: '#8BC34A', // Light green for Bollinger Bands
    adx: '#4CAF50', // Green for ADX
    williamsR: '#009688', // Teal for Williams %R
    ultimateOscillator: '#00BCD4', // Cyan for Ultimate Oscillator
    mfi: '#03A9F4', // Light blue for MFI
    volume: '#2196F3', // Blue for Volume
    vwap: '#3F51B5', // Indigo for VWAP
    atr: '#673AB7', // Deep Purple for ATR
    fractal: '#9C27B0', // Purple for Fractal
    ml: '#E91E63', // Pink for ML
    sentiment: '#F44336', // Red for Sentiment
    entropy: '#9E9E9E', // Grey for Entropy
    correlation: '#607D8B', // Blue Grey for Correlation
    time_anomaly: '#795548', // Brown for Time Anomaly
  }

  const color = colors[indicator] || '#FFFFFF'

  if (alpha < 1) {
    // Convert hex to rgba with alpha
    const r = parseInt(color.slice(1, 3), 16)
    const g = parseInt(color.slice(3, 5), 16)
    const b = parseInt(color.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
  }

  return color
}

// Initialize on script load
initializeEnhancedIndicators()

// Export functions for external use
window.enhancedShowStrategyAnimation = enhancedShowStrategyAnimation
window.createRequiredIndicatorRows = createRequiredIndicatorRows
window.colorIndicatorNames = colorIndicatorNames
