// js/ui/thresholds-menu.js

(function() {
    window.isUpdatingSlidersProgrammatically = false; // Flag to prevent recursion
    window.isUpdatingSignalLightsFromThresholds = false; // Flag for signal light updates
    // Ensure global objects and helper functions are available
    window.TRADING_STRATEGIES = window.TRADING_STRATEGIES || {};
    window.INDICATORS = window.INDICATORS || { momentum: [], trend: [], volume: [], ml: [] };
    window.logMessages = window.logMessages || [];
    
    window.updateLogger = window.updateLogger || function() { /* console.log('[Stub] updateLogger called'); */ };
    window.showToast = window.showToast || function(message, type) { /* console.log(`[Stub] Toast: ${message} (${type})`); */ };
    window.updateAllSignalLights = window.updateAllSignalLights || function() { /* console.log('[Stub] updateAllSignalLights called'); */ };

    window.defaultThresholds = {
        rsi: { red: 70, orange: 60, blue: 40, green: 30 },
        stochRsi: { red: 80, orange: 65, blue: 35, green: 20 },
        williamsR: { red: 80, orange: 65, blue: 35, green: 20 },
        ultimateOscillator: { red: 70, orange: 60, blue: 40, green: 30 },
        mfi: { red: 80, orange: 65, blue: 35, green: 20 },
        adx: { red: 80, orange: 65, blue: 35, green: 20 },
        bollingerBands: { red: 98, orange: 96, blue: 4, green: 2 },
        atr: { high: 2, moderate: 1, low: 0.5 },
        macd: { red: 70, orange: 60, blue: 40, green: 30 },
        volume: { red: 80, orange: 65, blue: 35, green: 20 },
        sentiment: { red: 80, orange: 65, blue: 35, green: 20 },
        entropy: { red: 80, orange: 65, blue: 35, green: 20 },
        correlation: { red: 80, orange: 65, blue: 35, green: 20 },
        time_anomaly: { red: 80, orange: 65, blue: 35, green: 20 }
    };

    if (typeof window.thresholds === 'undefined') {
        const savedThresholds = localStorage.getItem('userThresholds');
        if (savedThresholds) {
            try {
                window.thresholds = JSON.parse(savedThresholds);
                for (const key in window.defaultThresholds) {
                    if (!window.thresholds[key]) {
                        window.thresholds[key] = JSON.parse(JSON.stringify(window.defaultThresholds[key]));
                    }
                    if (window.defaultThresholds[key].red !== undefined && window.thresholds[key].red === undefined && key !== 'atr') {
                         window.thresholds[key] = JSON.parse(JSON.stringify(window.defaultThresholds[key]));
                    }
                }
            } catch (e) {
                window.logMessages.push(`[${new Date().toLocaleString()}] Error loading/parsing saved thresholds: ${e.message}`);
                window.updateLogger();
                window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
            }
        } else {
            window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
        }
    }

    function renderThresholdSliders(strategy) {
        try {
            const slidersContainerElement = document.getElementById('threshold-sliders');
            if (!slidersContainerElement) {
                console.error('Threshold sliders container (#threshold-sliders) not found');
                return;
            }
            slidersContainerElement.innerHTML = ''; // Clear previous content

            const header = document.createElement('h3');
            header.style.cssText = 'font-size: 14px; margin: 5px 0px; padding: 0px;';
            header.textContent = 'Thresholds: Admiral T.O.A. Convergence';
            slidersContainerElement.appendChild(header);

            const currentStrategyName = strategy || window.currentStrategy || 'admiral_toa'; // Ensure currentStrategy is defined
            const strategyIndicators = window.TRADING_STRATEGIES[currentStrategyName] ?
                window.TRADING_STRATEGIES[currentStrategyName].indicators :
                Object.keys(window.thresholds).filter(ind => ind !== 'atr');

            const makeThumbDraggable = (thumb, indicatorName, sliderBar) => {
                let active = false;
                let lastPointerX = 0;
                let currentVal = 0; // Stores the latest calculated percentage after constraints
                let updateRAFId = null;
                let sliderBarRect = null;

                const dragStart = (e) => {
                    sliderBar.classList.add('active-slider-bar');
                    thumb.classList.add('dragging');
                    sliderBarRect = sliderBar.getBoundingClientRect();

                    if (e.type === 'touchstart') {
                        lastPointerX = e.touches[0].clientX;
                    } else {
                        lastPointerX = e.clientX;
                        document.addEventListener('mouseup', dragEnd, { passive: false });
                        document.addEventListener('mousemove', drag, { passive: false });
                    }
                    active = true;
                };

                const dragEnd = (e) => {
                    sliderBar.classList.remove('active-slider-bar');
                    thumb.classList.remove('dragging');
                    active = false;
                    document.removeEventListener('mouseup', dragEnd);
                    document.removeEventListener('mousemove', drag);
                    
                    localStorage.setItem('userThresholds', JSON.stringify(window.thresholds));
                    if (typeof window.updateAllSignalLights === 'function') {
                        window.updateAllSignalLights();
                    }
                    if (typeof window.logMessages !== 'undefined' && typeof window.updateLogger === 'function') {
                        const thumbType = thumb.dataset.type;
                        // Use the value from the model which is the source of truth
                        window.logMessages.push(`[${new Date().toLocaleString()}] Threshold updated for ${indicatorName} - ${thumbType}: ${window.thresholds[indicatorName][thumbType]}%`);
                        window.updateLogger();
                    }
                    sliderBarRect = null; 
                };

                const drag = (e) => {
                    if (!active) return;
                    e.preventDefault();

                    let currentPointerX;
                    if (e.type === 'touchmove') {
                        currentPointerX = e.touches[0].clientX;
                    } else {
                        currentPointerX = e.clientX;
                    }

                    const thumbType = thumb.dataset.type;
                    console.clear(); // Uncomment for cleaner logs during rapid testing, but can be annoying
                    console.log(`%cDRAG: ${indicatorName}-${thumbType}`, 'color: yellow; font-weight: bold;');

                    const pointerDeltaPx = currentPointerX - lastPointerX;
                    console.log(`  Pointer: currentX=${currentPointerX.toFixed(2)}, lastX=${lastPointerX.toFixed(2)}, deltaPx=${pointerDeltaPx.toFixed(2)}`);
                    
                    let pointerDeltaPct = 0;
                    if (sliderBarRect && sliderBarRect.width > 0) { // Ensure sliderBarRect is available
                        pointerDeltaPct = (pointerDeltaPx / sliderBarRect.width) * 100;
                    } else {
                        console.warn('sliderBarRect not available or width is zero.');
                        lastPointerX = currentPointerX; // Prevent large jumps if rect is momentarily unavailable
                        return;
                    }
                    console.log(`  DeltaPct: ${pointerDeltaPct.toFixed(3)}%`);
                    
                    const thValues = window.thresholds[indicatorName];
                    let currentThumbStoredPct = parseFloat(thValues[thumbType]);
                    console.log(`  StoredPct (model): ${currentThumbStoredPct.toFixed(1)}%`);

                    let newPosPercent = currentThumbStoredPct + pointerDeltaPct;
                    console.log(`  NewPos (model + delta, pre-clamp): ${newPosPercent.toFixed(3)}%`);
                    
                    newPosPercent = Math.max(0, Math.min(100, newPosPercent)); // Clamp to 0-100
                    console.log(`  NewPos (clamped): ${newPosPercent.toFixed(3)}%`);
                    
                    currentVal = parseFloat(newPosPercent.toFixed(1)); // This is the potential new value before constraints
                    console.log(`  CurrentVal (toFixed(1), pre-constraint): ${currentVal}%`);

                    // Apply constraints
                    const minGap = 0.1;
                    let constrainedVal = currentVal;
                    if (thumbType === 'green') {
                        constrainedVal = Math.min(currentVal, thValues.blue - minGap);
                    } else if (thumbType === 'blue') {
                        constrainedVal = Math.max(currentVal, thValues.green + minGap);
                        constrainedVal = Math.min(constrainedVal, thValues.orange - minGap);
                    } else if (thumbType === 'orange') {
                        constrainedVal = Math.max(currentVal, thValues.blue + minGap);
                        constrainedVal = Math.min(constrainedVal, thValues.red - minGap);
                    } else if (thumbType === 'red') {
                        constrainedVal = Math.max(currentVal, thValues.orange + minGap);
                    }
                    // Re-clamp after constraints and ensure precision
                    currentVal = parseFloat(Math.max(0, Math.min(100, constrainedVal)).toFixed(1));
                    // if (constrainedVal !== currentValBeforeConstraints) console.log(`  Value after constraints: ${currentVal}% (was ${currentValBeforeConstraints}%)`);
                    // console.log(`  Final currentVal (after all constraints & clamping): ${currentVal}%`);

                    if (thValues[thumbType] !== currentVal) {
                    //    console.log(`  %cCHANGING MODEL for ${thumbType}: from ${thValues[thumbType]}% to ${currentVal}%`, 'color: lightgreen;');
                       thValues[thumbType] = currentVal; // Update the data model
                       
                       // Update thumb's visual style
                       thumb.style.left = currentVal + '%';
                       thumb.title = `${thumbType.charAt(0).toUpperCase() + thumbType.slice(1)}: ${currentVal}%`;

                       // Schedule other visual updates (segments, labels, etc.)
                       if (updateRAFId) {
                           cancelAnimationFrame(updateRAFId);
                       }
                       updateRAFId = requestAnimationFrame(() => {
                        //    console.log(`rAF: Updating display for ${indicatorName}`);
                           updateThresholdDisplay(indicatorName);
                           updateRAFId = null;
                       });
                    } else {
                        // console.log(`  Model for ${thumbType} already ${currentVal}%, no change.`);
                    }
                    lastPointerX = currentPointerX; // Update lastPointerX for the next delta calculation
                };

                thumb.addEventListener('mousedown', dragStart);
                thumb.addEventListener('touchstart', dragStart, { passive: false });
                thumb.addEventListener('touchend', dragEnd);
                // ... (rest of the backup file content for thresholds-menu.js)
            }
            // ... (rest of the backup file content for thresholds-menu.js)
        } catch (error) {
            console.error("Error in renderThresholdSliders:", error);
            window.logMessages.push(`[${new Date().toLocaleString()}] Error rendering threshold sliders: ${error.message}`);
            window.updateLogger();
        }
    }

    function updateThresholdDisplay(indicatorName) {
        const sliderBar = document.getElementById(`slider-${indicatorName}`);
        if (!sliderBar) return;

        const thValues = window.thresholds[indicatorName];
        const segments = sliderBar.querySelectorAll('.slider-segment');
        const thumbs = sliderBar.querySelectorAll('.slider-thumb');
        const labels = sliderBar.querySelectorAll('.slider-label');

        const positions = {
            green: parseFloat(thValues.green),
            blue: parseFloat(thValues.blue),
            orange: parseFloat(thValues.orange),
            red: parseFloat(thValues.red)
        };

        segments.forEach(segment => {
            if (segment.classList.contains('segment-green')) {
                segment.style.width = positions.green + '%';
            } else if (segment.classList.contains('segment-blue')) {
                segment.style.left = positions.green + '%';
                segment.style.width = (positions.blue - positions.green) + '%';
            } else if (segment.classList.contains('segment-yellow')) { // Assuming yellow is the neutral zone
                segment.style.left = positions.blue + '%';
                segment.style.width = (positions.orange - positions.blue) + '%';
            } else if (segment.classList.contains('segment-orange')) {
                segment.style.left = positions.orange + '%';
                segment.style.width = (positions.red - positions.orange) + '%';
            } else if (segment.classList.contains('segment-red')) {
                segment.style.left = positions.red + '%';
                segment.style.width = (100 - positions.red) + '%';
            }
        });

        thumbs.forEach(thumb => {
            const type = thumb.dataset.type;
            thumb.style.left = positions[type] + '%';
            thumb.title = `${type.charAt(0).toUpperCase() + type.slice(1)}: ${positions[type]}%`;
        });

        labels.forEach(label => {
            const type = label.dataset.type;
            label.textContent = positions[type] + '%';
            // Basic positioning, might need refinement for overlap
            if (type === 'green') label.style.left = Math.max(0, positions.green - 5) + '%';
            else if (type === 'blue') label.style.left = positions.blue + '%';
            else if (type === 'orange') label.style.left = positions.orange + '%';
            else if (type === 'red') label.style.left = Math.min(95, positions.red) + '%'; // Adjust to keep label in bounds
        });
    }

    function createSliderDOM(indicatorName) {
        const indicatorData = window.thresholds[indicatorName];
        if (!indicatorData) return null;

        const container = document.createElement('div');
        container.className = 'slider-container-new';
        container.id = `slider-container-${indicatorName}`;

        const label = document.createElement('label');
        label.className = 'slider-main-label';
        label.textContent = indicatorName.toUpperCase();
        container.appendChild(label);

        const sliderBar = document.createElement('div');
        sliderBar.className = 'slider-bar-new';
        sliderBar.id = `slider-${indicatorName}`;

        // Segments
        ['green', 'blue', 'yellow', 'orange', 'red'].forEach(color => {
            const segment = document.createElement('div');
            segment.className = `slider-segment segment-${color}`;
            sliderBar.appendChild(segment);
        });

        // Thumbs
        ['green', 'blue', 'orange', 'red'].forEach(type => {
            const thumb = document.createElement('div');
            thumb.className = `slider-thumb thumb-${type}`;
            thumb.dataset.type = type;
            makeThumbDraggable(thumb, indicatorName, sliderBar);
            sliderBar.appendChild(thumb);
        });
        
        // Value Labels (optional, can be part of thumb title or separate)
        ['green', 'blue', 'orange', 'red'].forEach(type => {
            const valueLabel = document.createElement('span');
            valueLabel.className = `slider-label label-${type}`;
            valueLabel.dataset.type = type;
            sliderBar.appendChild(valueLabel); // Append to sliderBar or container as preferred
        });

        container.appendChild(sliderBar);
        updateThresholdDisplay(indicatorName); // Initial display update
        return container;
    }

    strategyIndicators.forEach(indicatorName => {
        if (indicatorName === 'atr') return; // Skip ATR for standard slider
        const sliderElement = createSliderDOM(indicatorName);
        if (sliderElement) {
            slidersContainerElement.appendChild(sliderElement);
        }
    });

    // Add Save and Reset buttons
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'threshold-button-container';

    const saveButton = document.createElement('button');
    saveButton.id = 'saveThresholdsButton';
    saveButton.className = 'threshold-button save-button';
    saveButton.textContent = 'Save Thresholds';
    saveButton.addEventListener('click', () => {
        localStorage.setItem('userThresholds', JSON.stringify(window.thresholds));
        if (typeof window.showToast === 'function') {
            window.showToast('Thresholds saved successfully!', 'success');
        }
        if (typeof window.logMessages !== 'undefined' && typeof window.updateLogger === 'function') {
            window.logMessages.push(`[${new Date().toLocaleString()}] User thresholds saved.`);
            window.updateLogger();
        }
        if (typeof window.updateAllSignalLights === 'function') {
            window.updateAllSignalLights(); 
        }
    });

    const resetButton = document.createElement('button');
    resetButton.id = 'resetThresholdsButton';
    resetButton.className = 'threshold-button reset-button';
    resetButton.textContent = 'Reset to Defaults';
    resetButton.addEventListener('click', () => {
        if (confirm('Are you sure you want to reset all thresholds to their default values?')) {
            window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
            localStorage.setItem('userThresholds', JSON.stringify(window.thresholds));
            renderThresholdSliders(window.currentStrategy);
            if (typeof window.showToast === 'function') {
                window.showToast('Thresholds reset to defaults.', 'info');
            }
            if (typeof window.logMessages !== 'undefined' && typeof window.updateLogger === 'function') {
                window.logMessages.push(`[${new Date().toLocaleString()}] User thresholds reset to defaults.`);
                window.updateLogger();
            }
            if (typeof window.updateAllSignalLights === 'function') {
                window.updateAllSignalLights();
            }
        }
    });

    buttonContainer.appendChild(saveButton);
    buttonContainer.appendChild(resetButton);
    slidersContainerElement.appendChild(buttonContainer);

    // Initial rendering based on the current strategy or default
    renderThresholdSliders(window.currentStrategy);

    // Listen for strategy changes to re-render sliders if necessary
    if (window.strategyState && typeof window.strategyState.on === 'function') {
        window.strategyState.on('strategyChanged', (event) => {
            if (event.newStrategy && event.newStrategy.id) {
                renderThresholdSliders(event.newStrategy.id);
            }
        });
    }

    // Expose the render function if needed elsewhere
    window.renderThresholdSliders = renderThresholdSliders;
    window.updateThresholdDisplay = updateThresholdDisplay; // Expose for external updates if needed

})();
