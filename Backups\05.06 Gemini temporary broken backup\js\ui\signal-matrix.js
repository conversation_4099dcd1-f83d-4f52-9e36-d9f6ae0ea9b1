(function () {
/**
 * StarCrypt Signal Matrix Module
 *
 * Handles rendering and updating the signal matrix/grid, including the dedicated volume signal light row.
 * This module is responsible for displaying the current state of all indicators across different timeframes.
 * It works in conjunction with the strategy selector to show relevant indicators for the selected strategy.
 */

  // Track initialization state
  const signalMatrixState = {
    isInitialized: false,
    container: null,
    indicators: [],
    timeframes: [],
    strategy: null,
    lastUpdateTime: 0,
  }

  // Global reference
  window.signalMatrixState = signalMatrixState

  // Default configuration uses window.TIMEFRAMES and 'admiral_toa' (or window.currentStrategy).

  const UPDATE_THROTTLE_MS = 1000 // Throttle updates to once per second

  // Performance optimization: Cache DOM elements
  const signalElementsCache = new Map()
  const indicatorUpdateTimes = new Map()

  // Error handling
  class SignalMatrixError extends Error {
    constructor(message, code = 'SIGNAL_MATRIX_ERROR') {
      super(message)
      this.name = 'SignalMatrixError'
      this.code = code
    }
  }

  /**
 * Creates and initializes the signal matrix in the specified container.
 * @param {string} containerId - The ID of the container element
 * @param {string[]} [timeframes] - Array of timeframe strings (e.g., ['1m', '5m', '1h'])
 * @param {string[]} [enabledIndicators] - Array of enabled indicator names
 * @returns {boolean} True if successful, false otherwise
 */
  function createSignalMatrix(containerId, timeframes = DEFAULT_TIMEFRAMES, enabledIndicators = []) {
    try {
      console.log('[SignalMatrix] Creating signal matrix...')

      // Validate inputs
      if (!containerId) {
        throw new SignalMatrixError('Container ID is required', 'INVALID_CONTAINER')
      }

      // Ensure MATRIX_INDICATORS is available first
      if (typeof window.MATRIX_INDICATORS === 'undefined') {
        console.warn('[SignalMatrix] MATRIX_INDICATORS not found, using default indicators')
        window.MATRIX_INDICATORS = [...DEFAULT_INDICATORS]
      }

      // Get default timeframes from MATRIX_INDICATORS if not provided
      const defaultTimeframes = window.MATRIX_INDICATORS[0]?.timeframes || DEFAULT_TIMEFRAMES

      // Store current timeframes
      signalMatrixState.timeframes = Array.isArray(timeframes) ? [...timeframes] : [...defaultTimeframes]

      // Validate timeframes
      if (!signalMatrixState.timeframes.length) {
        console.error('[SignalMatrix] No valid timeframes found, using default')
        signalMatrixState.timeframes = [...DEFAULT_TIMEFRAMES]
      }

      // Get or create container
      signalMatrixState.container = document.getElementById(containerId)
      if (!signalMatrixState.container) {
        console.warn(`[SignalMatrix] Container not found: ${containerId}, creating one...`)
        signalMatrixState.container = document.createElement('div')
        signalMatrixState.container.id = containerId
        document.body.appendChild(signalMatrixState.container)
      }

      // Log signal matrix creation attempt
      console.log('Attempting to create signal matrix. Initialization state:', signalMatrixState)

      // Only initialize once
      if (signalMatrixState.isInitialized) {
        console.log('[SignalMatrix] Already initialized, skipping creation')
        return true
      }

      signalMatrixState.isInitialized = true

      // DO NOT Clear existing content or set className for the main container
      // signalMatrixState.container.innerHTML = '';
      // signalMatrixState.container.className = 'signal-matrix'; // Assuming class is already set in HTML
      // signalMatrixState.container.setAttribute('data-initialized', 'true'); // This might still be useful

      // DO NOT Add loading state this way if it clears content
      // signalMatrixState.container.innerHTML = '<div class="loading">Initializing signal matrix...</div>';

      // DO NOT Create the matrix structure - it should exist in HTML
      /*
    const matrixWrapper = document.createElement('div');
    matrixWrapper.className = 'signal-matrix-wrapper';

    // Create header row with timeframes
    const headerRow = document.createElement('div');
    headerRow.className = 'signal-row header';

    // Add a title for the matrix
    const titleRow = document.createElement('div');
    titleRow.className = 'signal-matrix-title';
    titleRow.textContent = 'Signal Matrix';
    titleRow.title = 'Shows the current state of indicators across different timeframes';

    // Add refresh button
    const refreshButton = document.createElement('button');
    refreshButton.className = 'refresh-button';
    refreshButton.innerHTML = '🔄';
    refreshButton.title = 'Refresh signal matrix';
    refreshButton.onclick = () => updateSignalMatrix();

    titleRow.appendChild(refreshButton);
    matrixWrapper.appendChild(titleRow);

    // Add indicator label cell with tooltip
    const labelCell = document.createElement('div');
    labelCell.className = 'signal-label';
    labelCell.textContent = 'Indicators';
    labelCell.title = 'Indicator names';
    headerRow.appendChild(labelCell);

    // Add help text
    const helpText = document.createElement('div');
    helpText.className = 'help-text';
    helpText.textContent = 'Hover over cells for details. Colors indicate signal strength.';
    helpText.style.gridColumn = `1 / ${timeframes.length + 1}`; // Span all columns

    // Add timeframe cells with tooltips
    timeframes.forEach((tf, index) => {
      const cell = document.createElement('div');
      cell.className = 'signal-cell timeframe';
      cell.textContent = tf;
      cell.dataset.timeframe = tf;
      cell.title = `Timeframe: ${tf}`;
      cell.style.gridColumn = index + 2; // +1 for the label column
      headerRow.appendChild(cell);
    });

    // Add header row to matrix
    matrixWrapper.appendChild(headerRow);
    matrixWrapper.appendChild(helpText);

    // Add the matrix to the container
    signalMatrixContainer.appendChild(matrixWrapper);

    // Create a container for indicator rows
    const rowsContainer = document.createElement('div');
    rowsContainer.className = 'signal-rows-container';
    matrixWrapper.appendChild(rowsContainer);

    // Store reference to rows container
    // signalMatrixContainer.rowsContainer = rowsContainer; // This needs to be signalMatrixState.container if it's the direct parent of rows
    */
      // Create rows for each indicator
      updateIndicatorRows()

      // Initial update of the matrix
      updateSignalMatrix()

      // Set up auto-refresh
      setupAutoRefresh()

      console.log('[SignalMatrix] Initialized successfully')

      // Mark as initialized
      isInitialized = true
      return true
    } catch (error) {
      console.error('[SignalMatrix] Initialization error:', error)
      // Don't show error UI, just log to console
      if (signalMatrixContainer) {
        signalMatrixContainer.innerHTML = '' // Clear any existing content
        signalMatrixContainer.style.display = 'none' // Hide the container
      }
      return false
    }
  }

  /**
 * Creates a row for the signal matrix
 * @param {string} indicator - The indicator name
 * @param {string[]} timeframes - Array of timeframes
 * @param {boolean} [isVolumeRow=false] - Whether this is a volume row
 * @returns {HTMLElement|null} The created row element or null if invalid
 */
  function createSignalRow(indicator, timeframes, isVolumeRow = false) {
    try {
      if (!indicator || !timeframes?.length) {
        console.error('Invalid arguments for createSignalRow:', { indicator, timeframes })
        return null
      }

      const row = document.createElement('div')
      row.className = `signal-row ${isVolumeRow ? 'volume-row' : ''}`
      row.dataset.indicator = indicator

      try {
      // Add indicator label
        const labelCell = document.createElement('div')
        labelCell.className = 'signal-label'
        labelCell.textContent = typeof indicator === 'string' ?
          indicator.charAt(0).toUpperCase() + indicator.slice(1) :
          String(indicator)
        row.appendChild(labelCell)

        // Add signal cells for each timeframe
        timeframes.forEach(tf => {
          try {
            const cell = document.createElement('div')
            cell.className = 'signal-cell'
            cell.dataset.timeframe = tf
            cell.id = `cell-${indicator}-${tf}`.toLowerCase() // Assign ID to cell

            const signal = document.createElement('div')
            signal.className = 'signal-circle'
            signal.dataset.indicator = indicator
            signal.dataset.timeframe = tf
            signal.dataset.state = 'neutral'
            signal.dataset.id = `signal-${indicator}-${tf}`.toLowerCase()

            // Set a unique ID for the signal element
            signal.id = signal.dataset.id

            // Add tooltip
            signal.title = `${indicator} (${tf}) - No signal`

            // Add ARIA attributes for accessibility
            signal.setAttribute('role', 'status')
            signal.setAttribute('aria-live', 'polite')
            signal.setAttribute('aria-atomic', 'true')

            cell.appendChild(signal)
            row.appendChild(cell)

            // Register the signal element immediately if signalInitializer is available
            if (window.signalInitializer) {
              window.signalInitializer.registerSignalElement(signal)
            }
          } catch (cellError) {
            console.error(`Error creating signal cell for ${indicator} (${tf}):`, cellError)
          }
        })

        return row
      } catch (rowError) {
        console.error(`Error creating signal row for ${indicator}:`, rowError)
        return null
      }
    } catch (error) {
      console.error('Unexpected error in createSignalRow:', error)
      return null
    }
  }

  /**
 * --- SIGNAL MATRIX UPDATE ---
 *
 * This section handles updating the signal matrix based on the current state of indicators
 * and the selected strategy. It ensures that only relevant indicators are shown and that
 * their states are accurately reflected in the UI.
 */

  /**
 * Updates the signal matrix for a specific strategy
 * @param {string} strategyId - The ID of the strategy to update for
 * @param {boolean} [force=false] - Whether to force an update even if the strategy hasn't changed
 */
  function updateForStrategy(strategyId, force = false) {
    try {
      if (!strategyId) {
        console.warn('[SignalMatrix] No strategy ID provided, using default')
        strategyId = DEFAULT_STRATEGY
      }

      // Store last update time
      signalMatrixState.lastUpdateTime = Date.now()
      const now = Date.now()
      if (!force && signalMatrixState.currentStrategy === strategyId && (now - signalMatrixState.lastUpdateTime) < UPDATE_THROTTLE_MS) {
        console.log(`[SignalMatrix] Throttling update for ${strategyId} (last update: ${now - signalMatrixState.lastUpdateTime}ms ago)`)
        return
      }

      console.log(`[SignalMatrix] Updating for strategy: ${strategyId}${force ? ' (forced)' : ''}`)

      // Only update if the strategy has changed or if forced
      if (!force && currentStrategy === strategyId) {
        console.log('[SignalMatrix] Strategy unchanged, skipping update')
        return
      }

      // Update the current strategy
      const previousStrategy = currentStrategy
      currentStrategy = strategyId
      lastUpdateTime = now

      // Update the indicator rows if the strategy changed
      if (previousStrategy !== strategyId) {
        updateIndicatorRows()
      }

      // Get indicators for the current strategy
      const indicators = getStrategySpecificIndicators(strategyId)

      // Update the matrix with the new indicators
      updateMatrix(indicators)

      console.log(`[SignalMatrix] Updated for strategy: ${strategyId} with ${indicators.length} indicators`)
    } catch (error) {
      console.error('[SignalMatrix] Error updating for strategy:', error)
      // Try to recover by falling back to default strategy
      if (strategyId !== DEFAULT_STRATEGY) {
        console.log('[SignalMatrix] Falling back to default strategy')
        updateForStrategy(DEFAULT_STRATEGY, true)
      } else {
      // If we're already on the default strategy, show error
        showError('Failed to update signal matrix. Please refresh the page.')
      }
    }
  }

  /**
 * Updates the signal matrix implementation
 * @private
 */
  function updateSignalMatrixImpl() {
  // Ensure the main container and essential state are available
    if (!signalMatrixState.container) {
      console.warn('[SignalMatrix] Main container (momentum-indicators) not initialized or found.')
      return
    }
    if (!signalMatrixState.indicators || !signalMatrixState.indicators.length) {
    // This state is normal if a strategy has no indicators or before first updateIndicatorRows call.
    // console.warn('[SignalMatrix] No active indicators in state to update. Call updateIndicatorRows first or check strategy.');
      return
    }
    if (!signalMatrixState.timeframes || !signalMatrixState.timeframes.length) {
      console.warn('[SignalMatrix] No timeframes defined in state.')
      return
    }

    try {
    // console.log('[SignalMatrix] Updating signal displays for indicators:', signalMatrixState.indicators.join(', '));

      signalMatrixState.indicators.forEach(indicatorName => {
      // Find the existing row for this indicator
      // Assuming row ID is like 'indicator-row-mfi', 'indicator-row-rsi'
        const indicatorRowId = `indicator-row-${indicatorName.toLowerCase()}`
        const indicatorRowElement = document.getElementById(indicatorRowId)

        if (!indicatorRowElement) {
        // console.warn(`[SignalMatrix] Row element not found for indicator ${indicatorName} (ID: ${indicatorRowId})`);
          return // Skip to next indicator if its row doesn't exist
        }

        // TODO: Logic to show/hide rows based on whether indicatorName is in current strategy's active indicators
        // For now, assume all rows for signalMatrixState.indicators should be visible.
        // indicatorRowElement.style.display = '';

        signalMatrixState.timeframes.forEach(timeframe => {
        // Construct the ID of the signal circle element
        // e.g., "signal-mfi-1m", "signal-rsi-5m"
        // Ensure IDs match exactly what's in the HTML (case, separators)
          const signalId = `signal-${indicatorName.toLowerCase()}-${timeframe.toLowerCase().replace(' ', '')}`
          const signalElement = document.getElementById(signalId)

          if (signalElement) {
          // TODO: Get the actual signal data for this indicator and timeframe
          // This data would come from your WebSocket feed and be stored somewhere accessible.
          // For example, let's assume a function getSignalData(indicatorName, timeframe) exists.
          // const signalData = getSignalData(indicatorName, timeframe);

            // For now, let's simulate some states for testing
            // Replace this with actual data retrieval and state determination logic
            const newState = 'neutral' // Default state
            // const states = ['neutral', 'weak-buy', 'buy', 'strong-buy', 'weak-sell', 'sell', 'strong-sell'];
            // newState = states[Math.floor(Math.random() * states.length)]; // Random for testing

            // Update the signal element's class or data attribute
            // This depends on how your CSS applies the 5-color logic
            signalElement.className = 'signal-circle' // Reset classes to base
            // Add new state class, e.g., 'signal-strong-buy', 'signal-neutral'
            signalElement.classList.add(`signal-${newState.toLowerCase().replace(/\s+/g, '-')}`)
            signalElement.dataset.state = newState
            signalElement.title = `${indicatorName} (${timeframe}) - ${newState}`
          } else {
          // console.warn(`[SignalMatrix] Signal element not found for ID: ${signalId} (Indicator: ${indicatorName}, Timeframe: ${timeframe})`);
          }
        })
      })
    } catch (error) {
      console.error('[SignalMatrix] Error updating signal matrix display:', error)
    }
  }

  // Debounced version of updateSignalMatrix
  const debouncedUpdateSignalMatrix = debounce(updateSignalMatrixImpl, 100)

  // Public function to update the signal matrix
  function updateSignalMatrix() {
    debouncedUpdateSignalMatrix()
  }

  /**
 * Updates the signal matrix with the given indicators
 * @param {string[]} indicators - Array of indicator names to display in the matrix
 */
  function updateMatrix(indicators) {
    try {
      if (!signalMatrixContainer) {
        console.warn('[SignalMatrix] Container not initialized')
        return
      }

      // Clear existing rows
      if (signalMatrixContainer.rowsContainer) {
        signalMatrixContainer.rowsContainer.innerHTML = ''
      } else {
        console.warn('[SignalMatrix] Rows container not found')
        return
      }

      // Create rows for each indicator
      indicators.forEach(indicator => {
        try {
          const row = createSignalRow(indicator, currentTimeframes, indicator === 'volume')
          if (row && signalMatrixContainer.rowsContainer) {
            signalMatrixContainer.rowsContainer.appendChild(row)
          }
        } catch (e) {
          console.error(`[SignalMatrix] Error creating row for ${indicator}:`, e)
        }
      })

      console.log(`[SignalMatrix] Matrix updated with ${indicators.length} indicators`)
    } catch (error) {
      console.error('[SignalMatrix] Error in updateMatrix:', error)
      showError(`Failed to update matrix: ${error.message}`)
    }
  }

  /**
 * Simple debounce implementation
 * @param {Function} func - Function to debounce
 * @param {number} wait - Debounce delay in ms
 * @returns {Function} Debounced function
 */
  function debounce(func, wait) {
    let timeout
    return function () {
      const context = this
      const args = arguments
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(context, args), wait)
    }
  }

  /**
 * Logs error messages to console
 * @param {string} message - Error message to log
 */
  function showError(message) {
    console.error('[SignalMatrix]', message)
  // No UI updates, just log to console
  }

  /**
 * Gets indicators specific to a strategy
 * @param {string} strategyId - Strategy ID
 * @returns {string[]} Array of indicator names
 */
  function getStrategySpecificIndicators(strategyId) {
    const strategy = window.TRADING_STRATEGIES?.[strategyId]
    if (!strategy || !strategy.indicators) {
      return []
    }

    // Return a copy of the indicators array
    return [...(strategy.indicators || [])]
  }

  /**
 * Initialize the signal matrix when the DOM is ready
 */
  function initializeSignalMatrix() {
    if (window.signalMatrixState.isInitialized) {
      console.log('[SignalMatrix] Already initialized')
      return
    }

    console.log('[SignalMatrix] Initializing signal matrix...')

    // Default timeframes if not provided
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']

    // Create the signal matrix
    createSignalMatrix('momentum-indicators', timeframes, []) // Target momentum-indicators

    // Update for the current strategy
    const strategyId = window.currentStrategy || 'admiral_toa'
    updateForStrategy(strategyId)

    console.log('[SignalMatrix] Signal matrix initialized')
    window.signalMatrixState.isInitialized = true
  }

  /**
 * Initialize the signal matrix module
 */
  function initializeModule() {
    try {
    // Only initialize once
      if (window.signalMatrixInitialized) {
        console.log('[SignalMatrix] Module already initialized')
        return
      }

      console.log('[SignalMatrix] Initializing module...')

      // Create debounced versions of update functions
      const updateSignalMatrixDebounced = debounce(updateSignalMatrix, 500)

      // Make functions globally available
      window.updateSignalMatrixDebounced = updateSignalMatrixDebounced
      window.updateSignalMatrix = updateSignalMatrix
      window.initializeSignalMatrix = initializeSignalMatrix
      window.updateForStrategy = updateForStrategy

      // Mark as initialized
      window.signalMatrixInitialized = true

      console.log('[SignalMatrix] Module initialized')

      // Initialize when the DOM is ready
      const init = () => {
        try {
          console.log('[SignalMatrix] DOM ready, initializing UI...')
          initializeSignalMatrix()

          // Listen for strategy changes
          window.addEventListener('strategyChanged', (event) => {
            if (event.detail?.strategyId) {
              console.log('[SignalMatrix] Detected strategy change to:', event.detail.strategyId)
              updateForStrategy(event.detail.strategyId)
            }
          })

          console.log('[SignalMatrix] UI initialization complete')
        } catch (error) {
          console.error('[SignalMatrix] Error during UI initialization:', error)
        }
      }

      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init)
      } else {
      // DOM already loaded, initialize with a small delay
        setTimeout(init, 100)
      }
    } catch (error) {
      console.error('[SignalMatrix] Failed to initialize module:', error)
    }
  }

  /**
 * Set up automatic refresh of the signal matrix
 */
  /**
 * Updates the indicator rows in the matrix based on the current strategy
 */
  function updateIndicatorRows() {
    if (!signalMatrixState.container) { // Changed from signalMatrixContainer?.rowsContainer
      console.warn('[SignalMatrix] Main container (momentum-indicators) not available') // Updated log
      return
    }

    try {
    // DO NOT Clear existing rows from the container:
    // signalMatrixState.container.innerHTML = ''; // Was signalMatrixContainer.rowsContainer.innerHTML = '';

      // Get indicators for the current strategy
      const strategyId = window.currentStrategy || DEFAULT_STRATEGY
      const indicators = getStrategySpecificIndicators(strategyId)

      if (!indicators?.length) {
        console.warn('[SignalMatrix] No indicators found for strategy:', strategyId)
        // const noIndicators = document.createElement('div');
        // noIndicators.className = 'no-indicators';
        // noIndicators.textContent = 'No indicators available for the current strategy';
        // signalMatrixState.container.appendChild(noIndicators); // Was signalMatrixContainer.rowsContainer
        // For now, just log and return. UI should reflect this (e.g. all lights off or a dedicated message area).
        signalMatrixState.indicators = [] // Clear indicators
        signalMatrixState.strategy = strategyId
        return
      }

      // Store current indicators
      signalMatrixState.indicators = Array.isArray(indicators) ? [...indicators] : [...window.MATRIX_INDICATORS]
      signalMatrixState.strategy = strategyId

      // Rows are NOT created here anymore. Assumed to exist in HTML.
      // The updateSignalMatrix / updateSignalMatrixImpl function will use signalMatrixState.indicators
      // and signalMatrixState.timeframes to find and update the existing signal circle elements.
      // indicators.forEach(indicator => {
      //   try {
      //     // const row = createSignalRow(indicator, signalMatrixState.timeframes, indicator === 'volume'); // NO LONGER CALLED
      //     // if (row) {
      //     //   signalMatrixState.container.appendChild(row); // Was signalMatrixContainer.rowsContainer
      //     // }
      //   } catch (e) {
      //     console.error(`[SignalMatrix] Error processing indicator ${indicator} for row creation (which is now removed):`, e);
      //   }
      // });

      console.log(`[SignalMatrix] Active indicators for strategy ${strategyId}: ${signalMatrixState.indicators.join(', ')}`) // Updated log
    } catch (error) {
      console.error('[SignalMatrix] Error updating indicator rows:', error)
    // Consider not re-throwing: throw error;
    }
  }

  /**
 * Set up automatic refresh of the signal matrix
 */
  function setupAutoRefresh() {
    try {
    // Clear any existing interval
      if (window.signalMatrixRefreshInterval) {
        clearInterval(window.signalMatrixRefreshInterval)
      }

      // Instead of auto-refresh, listen for SignalManager updates
      if (window.StarCrypt && window.StarCrypt.SignalManager) {
        const signalManager = window.StarCrypt.SignalManager
        if (typeof signalManager.updateSignal === 'function') {
        // Listen for signal updates
          signalManager.addEventListener('update', () => {
            try {
              updateSignalMatrix()
            } catch (error) {
              console.error('[SignalMatrix] Error updating from SignalManager:', error)
            }
          })
        }
      }
    } catch (error) {
      console.error('[SignalMatrix] Error setting up auto-refresh:', error)
    }
  }

  // Auto-initialize the module when the script loads
  if (typeof window !== 'undefined') {
    initializeModule()
  }
})()
