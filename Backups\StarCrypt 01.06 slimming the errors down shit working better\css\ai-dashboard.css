/* AI Dashboard Styles */

/* Loading State */
.ai-dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.ai-dashboard-loading .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 162, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00a2ff;
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error State */
.ai-dashboard-error {
  padding: 1.5rem;
  background: rgba(255, 50, 50, 0.1);
  border: 1px solid #ff3232;
  border-radius: 8px;
  color: #ff6b6b;
  margin: 1rem 0;
}

.ai-dashboard-error h3 {
  color: #ff3232;
  margin-top: 0;
}

.ai-dashboard-error button {
  background: #ff3232;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.ai-dashboard-error button:hover {
  background: #ff0000;
}

/* Base styles */
.ai-dashboard {
  --primary-color: #00f2fe;
  --secondary-color: #4facfe;
  --success-color: #00e676;
  --danger-color: #ff5252;
  --warning-color: #ffab00;
  --text-primary: #e0e0e0;
  --text-secondary: #a0a0a0;
  --bg-primary: rgba(10, 14, 23, 0.95);
  --bg-secondary: rgba(20, 25, 40, 0.8);
  --border-color: rgba(255, 255, 255, 0.1);
  --card-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  --transition-speed: 0.3s;
}

/* Dashboard container */
#ai-dashboard-container {
  font-family: 'Segoe UI', Roboto, -apple-system, sans-serif;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 20px;
  max-width: 1200px;
  margin: 20px auto;
  overflow: hidden;
}

/* Dashboard header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

/* Dashboard grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Cards */
.dashboard-card {
  background: var(--bg-secondary);
  border-radius: 10px;
  padding: 20px;
  box-shadow: var(--card-shadow);
  transition: transform var(--transition-speed) ease, 
              box-shadow var(--transition-speed) ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Card header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  margin: 10px 0;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Confidence meter */
.confidence-meter {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin: 15px 0;
  overflow: hidden;
}

.confidence-level {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

.confidence-high { background: var(--success-color); }
.confidence-medium { background: var(--warning-color); }
.confidence-low { background: var(--danger-color); }

/* Tags */
.tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tag-buy { 
  background: rgba(0, 230, 118, 0.2); 
  color: var(--success-color);
  border: 1px solid rgba(0, 230, 118, 0.3);
}

.tag-sell { 
  background: rgba(255, 82, 82, 0.2); 
  color: var(--danger-color);
  border: 1px solid rgba(255, 82, 82, 0.3);
}

.tag-hold { 
  background: rgba(255, 171, 0, 0.2); 
  color: var(--warning-color);
  border: 1px solid rgba(255, 171, 0, 0.3);
}

/* Key levels */
.key-levels {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  background: rgba(0, 0, 0, 0.2);
  padding: 15px;
  border-radius: 8px;
}

.level {
  text-align: center;
  flex: 1;
  padding: 0 10px;
  position: relative;
}

.level:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 60%;
  width: 1px;
  background: var(--border-color);
}

.level-value {
  font-weight: 700;
  font-size: 18px;
  margin: 5px 0;
  color: var(--primary-color);
}

.level-label {
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Loading state */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .key-levels {
    flex-direction: column;
    gap: 15px;
  }
  
  .level {
    padding: 10px 0;
  }
  
  .level:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
  }
  
  .level:not(:last-child)::after {
    display: none;
  }
}

/* AI Status Indicator */
.ai-status {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
}

.ai-status.strong_buy { background-color: var(--success-color); box-shadow: 0 0 10px var(--success-color); }
.ai-status.buy { background-color: var(--success-color); }
.ai-status.hold { background-color: var(--warning-color); }
.ai-status.sell { background-color: var(--danger-color); }
.ai-status.strong_sell { background-color: var(--danger-color); box-shadow: 0 0 10px var(--danger-color); }
