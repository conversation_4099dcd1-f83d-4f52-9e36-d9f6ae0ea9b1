/**
 * Timeframe UI Manager
 * Handles timeframe selection and updates across the application
 */

// Default timeframes
const DEFAULT_TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

class TimeframeUIManager {
  constructor() {
    this.timeframes = [...DEFAULT_TIMEFRAMES]
    this.activeTimeframes = [...DEFAULT_TIMEFRAMES]
    this.currentTimeframe = '1h' // Default timeframe
    this.signalManager = window.StarCrypt?.SignalManager
    this.webSocketProcessor = window.StarCrypt?.WebSocketProcessor
    this.initialize()
  }

  initialize() {
    if (!this.cacheElements()) {
      console.error('[TimeframeUI] Failed to initialize: Required elements not found')
      return
    }

    this.setupEventListeners()
    this.renderTimeframeControls()
    this.initializeWebSocketHandlers()

    console.log('[TimeframeUI] Initialized with timeframes:', this.timeframes)

    // Initial update
    this.updateSignalManagerTimeframe()
  }

  cacheElements() {
    this.timeframeContainer = document.getElementById('timeframeControls')
    if (!this.timeframeContainer) {
      console.warn('[TimeframeUI] Timeframe container not found')
      return false
    }
    return true
  }

  setupEventListeners() {
    // Delegate events for timeframe buttons
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('timeframe-btn')) {
        const timeframe = e.target.dataset.timeframe
        if (timeframe) {
          this.setTimeframe(timeframe)
        }
      }
    })

    // Listen for strategy changes to update timeframes if needed
    document.addEventListener('strategyChanged', (e) => {
      this.handleStrategyChange(e.detail.strategyId)
    })
  }

  initializeWebSocketHandlers() {
    if (!this.webSocketProcessor) {
      console.warn('[TimeframeUI] WebSocketProcessor not available')
      return
    }

    // Listen for WebSocket connection status changes
    document.addEventListener('websocketStatus', (e) => {
      if (e.detail.connected) {
        console.log('[TimeframeUI] WebSocket connected, updating timeframe')
        this.updateSignalManagerTimeframe()
      }
    })
  }

  handleStrategyChange(strategyId) {
    console.log(`[TimeframeUI] Strategy changed to ${strategyId}, updating timeframes if needed`)
    // Re-render in case the strategy affects available timeframes
    this.renderTimeframeControls()
  }

  renderTimeframeControls() {
    if (!this.timeframeContainer) return

    const buttons = this.timeframes.map(timeframe => `
      <button class="timeframe-btn ${this.currentTimeframe === timeframe ? 'active' : ''}"
              data-timeframe="${timeframe}"
              title="Switch to ${timeframe.toUpperCase()} timeframe">
        ${timeframe.toUpperCase()}
      </button>
    `).join('')

    this.timeframeContainer.innerHTML = `
      <div class="timeframe-selector">
        <div class="timeframe-buttons">${buttons}</div>
      </div>
    `
  }

  setTimeframe(timeframe) {
    if (!this.timeframes.includes(timeframe)) {
      console.warn(`[TimeframeUI] Invalid timeframe: ${timeframe}`)
      return
    }

    if (this.currentTimeframe === timeframe) {
      console.log(`[TimeframeUI] Timeframe ${timeframe} already active`)
      return
    }

    console.log(`[TimeframeUI] Changing timeframe to: ${timeframe}`)
    this.currentTimeframe = timeframe
    this.renderTimeframeControls()

    // Update SignalManager with new timeframe
    this.updateSignalManagerTimeframe()

    // Dispatch custom event for other components to listen to
    const event = new CustomEvent('timeframeChanged', {
      detail: {
        timeframe,
        previousTimeframe: this.previousTimeframe,
      },
    })
    document.dispatchEvent(event)

    // Store the previous timeframe for potential use
    this.previousTimeframe = timeframe

    // If WebSocket is available, request data for the new timeframe
    this.requestTimeframeData(timeframe)
  }

  updateSignalManagerTimeframe() {
    if (!this.signalManager) {
      console.warn('[TimeframeUI] SignalManager not available')
      return
    }

    try {
      // Update SignalManager with current timeframe
      this.signalManager.setTimeframe(this.currentTimeframe)
      console.log(`[TimeframeUI] Updated SignalManager with timeframe: ${this.currentTimeframe}`)
    } catch (error) {
      console.error('[TimeframeUI] Error updating SignalManager:', error)
    }
  }

  requestTimeframeData(timeframe) {
    if (!this.webSocketProcessor) {
      console.warn('[TimeframeUI] WebSocketProcessor not available for data request')
      return
    }

    try {
      // Request data for the new timeframe
      this.webSocketProcessor.requestTimeframeData(timeframe)
      console.log(`[TimeframeUI] Requested data for timeframe: ${timeframe}`)
    } catch (error) {
      console.error(`[TimeframeUI] Error requesting data for timeframe ${timeframe}:`, error)
    }
  }

  getCurrentTimeframe() {
    return this.currentTimeframe
  }

  getActiveTimeframes() {
    return [...this.activeTimeframes]
  }

  // Public method to update timeframes from external sources
  updateTimeframes(timeframes) {
    if (!Array.isArray(timeframes) || timeframes.length === 0) {
      console.warn('[TimeframeUI] Invalid timeframes array provided')
      return
    }

    this.timeframes = [...timeframes]

    // If current timeframe is not in the new list, default to the first one
    if (!this.timeframes.includes(this.currentTimeframe)) {
      this.currentTimeframe = this.timeframes[0]
    }

    this.renderTimeframeControls()
    console.log('[TimeframeUI] Updated timeframes:', this.timeframes)
  }
}

// Initialize Timeframe UI Manager when DOM is ready
function initTimeframeUI() {
  // Wait for StarCrypt global to be available
  if (window.StarCrypt) {
    window.TimeframeUI = new TimeframeUIManager()
  } else {
    console.log('[TimeframeUI] Waiting for StarCrypt to be available...')
    setTimeout(initTimeframeUI, 100)
  }
}

// Start initialization when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initTimeframeUI)
} else {
  initTimeframeUI()
}
