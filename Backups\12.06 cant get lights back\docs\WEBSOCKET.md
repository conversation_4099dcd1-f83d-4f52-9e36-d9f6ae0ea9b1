# StarCrypt WebSocket System

**Last Updated:** 2025-06-10

## 1. Overview

The WebSocket system in StarCrypt provides real-time communication between the client and server, handling market data, user actions, and system events with reliability and efficiency.

## 2. Core Implementation

The main WebSocket implementation is in `js/websocket-core.js`, which provides a robust connection management system.

### 2.1 Key Features

- **Automatic Reconnection**: Handles connection drops and automatically reconnects
- **Message Batching**: Efficiently processes messages in batches
- **Error Handling**: Comprehensive error handling and recovery
- **Connection Monitoring**: Tracks connection health and performance
- **Event-Based Architecture**: Uses a pub/sub pattern for message handling

### 2.2 Initialization

```javascript
// Basic initialization
const wsCore = new WebSocketCore({
    url: 'wss://your-websocket-endpoint',
    maxReconnectAttempts: 10,
    reconnectInterval: 1000,
    maxReconnectInterval: 30000,
    pingInterval: 30000,
    pongTimeout: 5000
});

// Subscribe to messages
wsCore.subscribe('market.data', handleMarketData);
wsCore.subscribe('system.status', handleSystemStatus);

// Send messages
wsCore.send({
    type: 'subscribe',
    channel: 'market.data',
    pair: 'BTC/USD'
});
```

## 3. Message Format

### 3.1 Outgoing Messages

```typescript
interface OutgoingMessage {
    type: string;           // Message type (e.g., 'subscribe', 'unsubscribe', 'command')
    channel: string;        // Channel name
    [key: string]: any;      // Additional payload
}
```

### 3.2 Incoming Messages

```typescript
interface IncomingMessage {
    type: string;           // Message type
    channel: string;        // Source channel
    timestamp: number;      // Unix timestamp
    data: any;              // Message payload
    error?: string;         // Error details (if any)
}
```

## 4. Error Handling

The WebSocket system implements several layers of error handling:

1. **Connection Errors**: Automatic reconnection with exponential backoff
2. **Message Errors**: Invalid messages are logged and discarded
3. **Rate Limiting**: Message queuing and throttling
4. **State Recovery**: Automatic state synchronization on reconnection

## 5. Best Practices

### 5.1 Message Handling

- Keep message handlers small and focused
- Always validate incoming messages
- Use TypeScript interfaces for type safety
- Handle errors gracefully and provide user feedback

### 5.2 Performance

- Batch small messages when possible
- Use binary protocols for high-frequency data
- Implement message compression for large payloads
- Monitor connection quality and adjust settings accordingly

## 6. Debugging

### 6.1 Common Issues

1. **Connection Drops**
   - Check network connectivity
   - Verify WebSocket endpoint URL
   - Review server logs for errors

2. **Message Loss**
   - Check message queue size limits
   - Verify message handlers are properly registered
   - Monitor for error events

### 6.2 Logging

The WebSocket system provides detailed logging:

```javascript
// Enable debug logging
wsCore.setLogLevel('debug');

// Handle log messages
wsCore.on('log', (level, message, data) => {
    console[level](`[WS:${level.toUpperCase()}]`, message, data);
});
```

## 7. Security Considerations

1. **Authentication**: All connections must be authenticated
2. **Authorization**: Validate user permissions for each operation
3. **Data Validation**: Sanitize all incoming data
4. **Rate Limiting**: Prevent abuse with rate limits
5. **Encryption**: Use WSS (WebSocket Secure)

## 8. API Reference

### 8.1 WebSocketCore

#### Constructor

```typescript
new WebSocketCore(options: {
    url: string;
    protocols?: string[];
    maxReconnectAttempts?: number;
    reconnectInterval?: number;
    maxReconnectInterval?: number;
    pingInterval?: number;
    pongTimeout?: number;
    maxBatchSize?: number;
    maxQueueSize?: number;
}): WebSocketCore
```

#### Methods

- `connect(): Promise<void>` - Establish WebSocket connection
- `disconnect(): void` - Close the connection
- `send(message: object): void` - Send a message
- `subscribe(channel: string, handler: Function): void` - Subscribe to messages
- `unsubscribe(channel: string, handler?: Function): void` - Unsubscribe from messages
- `setLogLevel(level: 'error' | 'warn' | 'info' | 'debug'): void` - Set logging level

#### Events

- `open` - Connection established
- `close` - Connection closed
- `error` - Error occurred
- `message` - Message received
- `reconnect` - Reconnection attempt started
- `reconnect_failed` - All reconnection attempts failed

## 9. Examples

### 9.1 Market Data Subscription

```javascript
// Subscribe to market data
wsCore.subscribe('market.data', (data) => {
    updateChart(data.candles);
    updateIndicators(data.indicators);
});

// Request historical data
wsCore.send({
    type: 'request',
    channel: 'market.history',
    pair: 'BTC/USD',
    timeframe: '1h',
    limit: 1000
});
```

### 9.2 Order Management

```javascript
// Place an order
function placeOrder(side, pair, amount, price) {
    wsCore.send({
        type: 'order.place',
        side,
        pair,
        amount,
        price,
        clientId: generateClientId()
    });
}

// Handle order updates
wsCore.subscribe('order.update', (update) => {
    updateOrderStatus(update);
});
```

## 10. Testing

### 10.1 Unit Tests

```javascript
describe('WebSocketCore', () => {
    let wsCore;
    
    beforeEach(() => {
        wsCore = new WebSocketCore({ url: 'ws://test' });
    });
    
    afterEach(() => {
        wsCore.disconnect();
    });
    
    test('should connect successfully', async () => {
        await wsCore.connect();
        expect(wsCore.isConnected()).toBe(true);
    });
    
    // More tests...
});
```

### 10.2 Integration Tests

```javascript
describe('Market Data Integration', () => {
    test('should receive market data updates', (done) => {
        const wsCore = new WebSocketCore({ url: 'ws://test' });
        
        wsCore.subscribe('market.data', (data) => {
            expect(data).toHaveProperty('candles');
            expect(data).toHaveProperty('indicators');
            wsCore.disconnect();
            done();
        });
        
        wsCore.connect();
    });
});
```

## 11. Performance Considerations

1. **Message Size**: Keep messages as small as possible
2. **Batch Updates**: Group frequent updates
3. **Throttling**: Limit update frequency for high-volume data
4. **Compression**: Enable WebSocket compression
5. **Buffering**: Implement client-side buffering for high-frequency data

## 12. Troubleshooting Guide

### 12.1 Common Issues

#### Connection Issues

- **Symptom**: Frequent disconnections
  - Check network stability
  - Verify WebSocket URL and protocols
  - Check server logs for errors

#### Performance Issues

- **Symptom**: High CPU/memory usage
  - Check for message flooding
  - Review message handlers for performance bottlenecks
  - Monitor WebSocket frame size and frequency

### 12.2 Debugging Tools

1. Browser Developer Tools
   - Network tab for WebSocket frames
   - Performance tab for CPU/memory usage
   - Console for error messages

2. Server-Side Logs
   - Connection events
   - Message processing times
   - Error conditions

## 13. Future Enhancements

1. **Protocol Versioning**: Add support for multiple protocol versions
2. **Compression**: Implement message compression
3. **Load Balancing**: Support for multiple WebSocket servers
4. **Metrics**: Detailed performance metrics collection
5. **Quality of Service**: Priority-based message handling

## 14. Support

For issues and support, please contact the development team.

---
**Documentation generated on:** 2025-06-10
