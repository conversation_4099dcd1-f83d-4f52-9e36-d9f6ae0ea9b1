/**
 * Unified Strategy Manager - Handles strategy selection and timeframe management
 */
class StrategyManager {
    constructor() {
        // Strategy state
        if (!window.TRADING_STRATEGIES) {
            console.error('TRADING_STRATEGIES is not defined. Make sure global-variables.js is loaded first.');
            window.TRADING_STRATEGIES = {}; // Initialize as empty object to prevent further errors
        }
        this.strategies = window.TRADING_STRATEGIES;
        this.currentStrategy = null;
        this.strategyContainer = null;
        this.strategyMenu = null;
        
        // Timeframe state
        this.timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
        this.activeTimeframes = [...this.timeframes];
        this.currentTimeframe = '1h';
        
        // UI elements
        this.timeframeContainer = null;
        this.timeframeButtons = null;
        
        // WebSocket and Signal references
        this.signalManager = window.StarCrypt?.SignalManager;
        this.webSocketProcessor = window.StarCrypt?.WebSocketProcessor;
        
        // Initialize
        this.initialize();
    }

    initialize() {
        // Cache elements
        this.strategyContainer = document.getElementById('strategyControls');
        this.strategyMenu = document.getElementById('strategyMenu');
        // this.timeframeContainer = document.getElementById('timeframeControls'); // Obsolete
        
        if (!this.strategyContainer || !this.strategyMenu) {
            console.error('[StrategyManager] Required elements not found');
            return;
        }
        
        // Load saved state
        this.loadSavedState();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Render UI
        this.renderStrategyMenu();
        // this.renderTimeframeControls(); // Obsolete
        
        // Initialize WebSocket handlers
        this.initializeWebSocketHandlers();
        
        console.log('[StrategyManager] Initialized');
    }

    setupEventListeners() {
        // Strategy menu toggle
        document.addEventListener('click', (e) => {
            if (e.target.id === 'strategyButton') {
                this.toggleStrategyMenu();
            }
        });
        
        // Timeframe selection
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('timeframe-btn')) {
                const timeframe = e.target.dataset.timeframe;
                if (timeframe) {
                    this.setTimeframe(timeframe);
                }
            }
        });
        
        // Strategy change handler
        document.addEventListener('strategyChanged', (e) => {
            this.handleStrategyChange(e.detail.strategyId);
        });
        
        // WebSocket connection handler
        document.addEventListener('websocketStatus', (e) => {
            if (e.detail.connected) {
                console.log('[StrategyManager] WebSocket connected, updating state');
                this.updateSignalManagerTimeframe();
            }
        });
    }

    initializeWebSocketHandlers() {
        if (!this.webSocketProcessor) {
            console.warn('[StrategyManager] WebSocketProcessor not available');
            return;
        }
        
        // Listen for connection status
        this.webSocketProcessor.addEventListener('status', (status) => {
            if (status === 'connected') {
                this.updateSignalManagerTimeframe();
            }
        });
    }

    loadSavedState() {
        // Load saved strategy
        const savedStrategy = localStorage.getItem('currentStrategy') || 'admiral_toa';
        if (this.strategies[savedStrategy]) {
            this.currentStrategy = savedStrategy;
        } else {
            console.warn(`Saved strategy ${savedStrategy} not found, using default`);
            this.currentStrategy = 'admiral_toa';
        }
        
        // Load saved timeframe
        const savedTimeframe = localStorage.getItem('currentTimeframe') || '1h';
        if (this.timeframes.includes(savedTimeframe)) {
            this.currentTimeframe = savedTimeframe;
        }
    }

    renderStrategyMenu() {
        if (!this.strategyMenu) return;
        
        const strategyOptions = Object.entries(this.strategies).map(([id, strategy]) => `
            <option value="${id}" ${id === this.currentStrategy ? 'selected' : ''}>
                ${strategy.name}
            </option>
        `).join('');
        
        this.strategyMenu.innerHTML = `
            <div class="strategy-controls">
                <h3>Strategy Selector</h3>
                <select id="strategySelect" onchange="window.StrategyManager.handleStrategyChange(this.value)">
                    ${strategyOptions}
                </select>
                <div class="strategy-description">
                    ${this.getStrategyDescription(this.currentStrategy)}
                </div>
            </div>
        `;
    }

    renderTimeframeControls() {
        if (!this.timeframeContainer) return;
        
        const buttons = this.timeframes.map(timeframe => `
            <button class="timeframe-btn ${this.currentTimeframe === timeframe ? 'active' : ''}"
                    data-timeframe="${timeframe}">
                ${timeframe.toUpperCase()}
            </button>
        `).join('');
        
        this.timeframeContainer.innerHTML = `
            <div class="timeframe-selector">
                <div class="timeframe-buttons">${buttons}</div>
            </div>
        `;
    }

    toggleStrategyMenu() {
        if (this.strategyMenu) {
            this.strategyMenu.classList.toggle('open');
        }
    }

    handleStrategyChange(strategyId) {
        if (!this.strategies[strategyId]) return;
        
        this.currentStrategy = strategyId;
        localStorage.setItem('currentStrategy', strategyId);
        
        // Update UI
        this.renderStrategyMenu();
        this.updateStrategyDescription();
        
        // Update indicators
        this.updateIndicatorsForStrategy(strategyId);
        
        // Notify other components
        this.notifyStrategyChange(strategyId);
    }

    updateStrategyDescription() {
        const description = this.getStrategyDescription(this.currentStrategy);
        const descElement = document.querySelector('.strategy-description');
        if (descElement) {
            descElement.innerHTML = description;
        }
    }

    getStrategyDescription(strategyId) {
        const strategy = this.strategies[strategyId];
        if (!strategy) return '';
        
        return `
            <h4>${strategy.name}</h4>
            <p>${strategy.description}</p>
            ${strategy.helperText || ''}
        `;
    }

    setTimeframe(timeframe) {
        if (!this.timeframes.includes(timeframe)) return;

        this.currentTimeframe = timeframe;
        localStorage.setItem('currentTimeframe', timeframe);
        
        // Update UI
        this.renderTimeframeControls();
        
        // Update signal manager (old system)
        this.updateSignalManagerTimeframe();
        
        // Dispatch event (local DOM)
        this.dispatchEvent('timeframeChanged', { timeframe });

        // Notify backend server via WebSocket
        if (typeof ensureWebSocketAndSend === 'function') {
            ensureWebSocketAndSend({ type: 'timeframe_change', timeframe: timeframe });
            console.log(`[StrategyManager] Sent timeframe_change event to server for ${timeframe}`);
        } else {
            console.warn('[StrategyManager] ensureWebSocketAndSend function not found. Cannot send timeframe_change to server.');
        }

        console.log(`[StrategyManager] Local timeframe changed to ${timeframe}`); // Adjusted log
    }

    updateSignalManagerTimeframe() {
        // This method interacts with an older SignalManager instance if available.
        // It might also need to trigger a data refresh via WebSocket if SignalManager doesn't handle it.
        if (this.signalManager && typeof this.signalManager.setCurrentTimeframe === 'function') {
            this.signalManager.setCurrentTimeframe(this.currentTimeframe);
            // Optionally, trigger a refresh if needed, e.g., this.signalManager.updateAllSignalLights();
        } else {
            // console.warn('[StrategyManager] SignalManager not available or setCurrentTimeframe is not a function.');
        }
    }
    
    updateIndicatorsForStrategy(strategyId) {
        const strategy = this.strategies[strategyId];
        if (!strategy || !strategy.indicators) return;

        // Example: Send a message to the backend to update indicators based on the new strategy
        // This assumes your WebSocket connection and message handling are set up.
        if (window.wsCore && window.wsCore.isConnected()) {
            window.wsCore.send({
                type: 'update_indicators_for_strategy',
                strategyId: strategyId,
                indicators: strategy.indicators
            });
        } else {
            // console.warn('[StrategyManager] WebSocket not connected. Cannot update indicators for strategy.');
        }
        // Placeholder for actual indicator update logic on the frontend if needed
        console.log(`[StrategyManager] Indicators for strategy ${strategyId} should be updated. Indicators: ${strategy.indicators.join(', ')}`);
    }

    notifyStrategyChange(strategyId) {
        this.dispatchEvent('strategyChanged', { strategyId });
    }

    // Helper to dispatch events with a prefix
    dispatchEvent(type, detail) {
        const event = new CustomEvent(`strategyManager:${type}`, {
            detail
        });
        document.dispatchEvent(event);
    }
}

// Create and expose singleton instance
let strategyManager = null;

// Initialize the StrategyManager only after the DOM is fully loaded and TRADING_STRATEGIES is available
function initializeStrategyManager() {
    if (!window.TRADING_STRATEGIES || Object.keys(window.TRADING_STRATEGIES).length === 0) {
        console.warn('TRADING_STRATEGIES not yet available, retrying...');
        setTimeout(initializeStrategyManager, 100);
        return;
    }
    
    console.log('Initializing StrategyManager with strategies:', Object.keys(window.TRADING_STRATEGIES));
    strategyManager = new StrategyManager();
    window.StrategyManager = strategyManager;
    
    // Dispatch event that StrategyManager is ready
    document.dispatchEvent(new CustomEvent('strategymanager:initialized', { 
        detail: { strategyManager } 
    }));
}

// Start initialization when DOM is ready
document.addEventListener('DOMContentLoaded', initializeStrategyManager);

