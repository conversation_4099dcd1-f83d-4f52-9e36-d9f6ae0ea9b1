// Strategy Persistence Module for StarCrypt
// Handles strategy persistence and prevents reverting to default

// Save current strategy to localStorage
function saveCurrentStrategy(strategy) {
  try {
    if (!strategy) return false
    console.log('[StrategyPersistence] Saving strategy:', strategy)
    localStorage.setItem('currentStrategy', strategy)
    return true
  } catch (err) {
    console.error('[StrategyPersistence] Error saving strategy:', err)
    return false
  }
}

// Load strategy from localStorage
function loadSavedStrategy() {
  try {
    const savedStrategy = localStorage.getItem('currentStrategy')
    console.log('[StrategyPersistence] Loaded saved strategy:', savedStrategy)

    // Validate the strategy exists in our strategy definitions
    if (savedStrategy && window.TRADING_STRATEGIES && window.TRADING_STRATEGIES[savedStrategy]) {
      return savedStrategy
    }
    return null
  } catch (err) {
    console.error('[StrategyPersistence] Error loading strategy:', err)
    return null
  }
}

// Initialize with proper strategy
function initializeWithCorrectStrategy() {
  try {
    // Wait a short time to ensure TRADING_STRATEGIES is loaded
    setTimeout(() => {
      const savedStrategy = loadSavedStrategy()

      if (savedStrategy) {
        // Set the current strategy
        window.currentStrategy = savedStrategy
        console.log('[StrategyPersistence] Set current strategy to saved value:', savedStrategy)

        // Update the UI
        const mainStrategySelector = document.getElementById('mainStrategySelector')
        if (mainStrategySelector) {
          mainStrategySelector.value = savedStrategy
          console.log('[StrategyPersistence] Updated strategy selector value')
        }
      }
    }, 500)
  } catch (err) {
    console.error('[StrategyPersistence] Error initializing strategy:', err)
  }
}

// Attach to Apply button click
function enhanceApplyButton() {
  try {
    const applyButton = document.getElementById('applyStrategyButton')
    if (applyButton) {
      // Store original handler
      const originalClickHandler = applyButton.onclick

      // Add our persistence logic
      applyButton.onclick = function (event) {
        // Get current strategy from selector
        const strategySelector = document.getElementById('mainStrategySelector')
        if (strategySelector) {
          const selectedStrategy = strategySelector.value

          // Save to localStorage
          saveCurrentStrategy(selectedStrategy)

          // Update global variable
          window.currentStrategy = selectedStrategy

          console.log('[StrategyPersistence] Strategy applied and saved:', selectedStrategy)
        }

        // Call original handler if it exists
        if (typeof originalClickHandler === 'function') {
          return originalClickHandler.call(this, event)
        }
      }

      console.log('[StrategyPersistence] Enhanced Apply button with persistence')
    }
  } catch (err) {
    console.error('[StrategyPersistence] Error enhancing Apply button:', err)
  }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
  console.log('[StrategyPersistence] Initializing strategy persistence')
  initializeWithCorrectStrategy()
  enhanceApplyButton()
})

// Export functions
window.saveCurrentStrategy = saveCurrentStrategy
window.loadSavedStrategy = loadSavedStrategy
