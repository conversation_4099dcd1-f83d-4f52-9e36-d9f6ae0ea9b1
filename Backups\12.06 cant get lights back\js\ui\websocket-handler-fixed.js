function ensureStatusElement() {
    let statusContainer = document.getElementById('statusContainer');
    if (!statusContainer) {
        statusContainer = document.createElement('div');
        statusContainer.id = 'statusContainer';

        const statusElement = document.createElement('span');
        statusElement.id = 'status';

        const latencyElement = document.createElement('span');
        latencyElement.className = 'latency';

        statusContainer.appendChild(statusElement);
        statusContainer.appendChild(latencyElement);
        document.body.appendChild(statusContainer);
    }
    return statusContainer;
}

// Initialize status element when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', ensureStatusElement);
} else {
    ensureStatusElement();
}
