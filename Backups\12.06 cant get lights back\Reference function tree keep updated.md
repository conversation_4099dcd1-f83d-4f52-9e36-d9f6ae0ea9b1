# StarCrypt Project - Comprehensive Reference

This document serves as a comprehensive reference for the StarCrypt trading application, detailing its architecture, components, and functionality to facilitate maintenance and development.

## Table of Contents

1. [Application Overview](#1-application-overview)  
2. [Backend Components](#2-backend-components)  
   - [2.1 Server.js](#21-serverjs)  
   - [2.2 Data Processing Pipeline](#22-data-processing-pipeline)  
3. [Frontend Architecture](#3-frontend-architecture)  
   - [3.1 WebSocket Client](#31-websocket-client)  
   - [3.2 Signal Processing](#32-signal-processing)  
   - [3.3 Strategy Management](#33-strategy-management)  
4. [Core Modules](#4-core-modules)  
5. [UI Components](#5-ui-components)  
6. [Data Flow](#6-data-flow)  
7. [Configuration](#7-configuration)  
8. [Known Issues & TODOs](#8-known-issues--todos)  

## 1. Application Overview

StarCrypt is an advanced cryptocurrency trading platform that provides real-time market analysis, technical indicators, and trading signals. The application features a modular architecture with a Node.js backend and a responsive web-based frontend.

### Key Features

- Real-time market data processing  
- Multiple trading strategies with customizable parameters  
- Technical indicator analysis  
- Signal generation and visualization  
- WebSocket-based real-time updates  
- Responsive UI with interactive charts  

## 2. Backend Components {#2-backend-components}

### 2.1 Server.js - Main Backend Server

#### Purpose  
Serves as the central hub for the StarCrypt application, handling HTTP requests, WebSocket connections, and data processing.

#### Key Responsibilities  
- HTTP server for serving the frontend  
- WebSocket server for real-time communication  
- Market data aggregation and processing  
- Technical indicator calculations  
- Trading strategy execution  
- Client connection management  

#### Port Configuration  
- HTTP Server: 3000 (default), falls back to 3001 if in use  
- WebSocket Server: 8080 (default), falls back to 8081 if in use  

### 2.2 Data Processing Pipeline

#### 1. Data Ingestion  
- Real-time WebSocket feeds from Kraken exchange  
- Historical data via REST API  
- Custom data sources (sentiment, ML models)  

#### 2. Processing  
- Normalization of market data  
- Technical indicator calculations  
- Signal generation based on active strategy  
- Risk assessment and position sizing  

#### 2. Processing

- Normalization of market data
- Technical indicator calculations
- Signal generation based on active strategy
- Risk assessment and position sizing

#### 3. Distribution

- Real-time updates to connected clients
- Historical data on demand
- System status and alerts

## 2. Backend Components

### 2.1 Server.js

#### Purpose

Acts as the backend for the StarCrypt application. It serves the application, manages WebSocket connections with clients, fetches live and historical trading data from the Kraken exchange (via REST API and a dedicated WebSocket client), calculates a wide array of technical indicators, applies trading strategies, and distributes processed data and signals to connected clients.

#### Key Components & Functionality

##### HTTP Server (Express.js)

- Serves the frontend application
- Listens on port 3000 by default (fallback to 3001)

##### WebSocket Server (ws library)
- Fetches historical OHLC data via Kraken's REST API with rate limiting  
- Maps StarCrypt pair names (e.g., `xbtusdt`) to Kraken API pair names (e.g., `BTC/USDT`)  

##### Data Processing & Storage  
- **Configuration**: Manages pairs, timeframes, and API parameters  
- **Indicator Calculation**: Implements technical indicators including:  
  - RSI, MACD, Bollinger Bands, ATR, MFI, Williams %R, ADX  
  - Ultimate Oscillator, VWAP, Volume Spike, Fractal  
  - Sentiment, Entropy, Correlation, Time Anomaly  
  - ML-based indicators and EMAs/SMAs  
- **Key Functions**:  
  - `calculateAllIndicators(pair, timeframe)`: Orchestrates calculations  
  - `calculateIndicatorsAllTimeframes(pair)`: Processes indicators across timeframes  
- **Trading Strategies**: Defines `TRADING_STRATEGIES` with indicators and logic  
- **Storage**: Uses in-memory objects for data persistence  

##### Dependencies  
- `express`: HTTP server  
- `ws`: WebSocket server  
- `node-fetch`: HTTP requests to Kraken's API  
- `./krakenWsClient.js`: Custom WebSocket client  

#### Notable Operations  
- Sends initial data on client connection  
- Handles data updates on symbol/timeframe changes  
- Processes live market data through WebSocket client  

---

## 3. Frontend Architecture {#3-frontend-architecture}

### 3.1 WebSocket Client (`websocket-core.js`)

#### Purpose  
Manages the WebSocket connection between the frontend and backend, handling connection lifecycle, message queuing, and error recovery.

#### Key Features  
- Automatic reconnection with exponential backoff  
- Message queuing during connection drops  
- Batch processing of messages  
- Connection state management  
- Error handling and recovery  
- Ping/pong heartbeat mechanism  

#### Main Methods  
- `connect()`: Establishes WebSocket connection  
- `sendMessage(type, data)`: Sends a message to the server  
- `addMessageHandler(type, handler)`: Registers a message handler  
- `removeMessageHandler(type, handler)`: Removes a message handler  
- `disconnect()`: Closes the WebSocket connection  

### 3.2 Signal Processing (`signal-system.js`)

#### Purpose  
Processes and manages trading signals, updating the UI to reflect current market conditions and trading opportunities.

#### Key Features  
- Signal state management  
- Signal visualization (lights, indicators)  
- Signal history tracking  
- Threshold-based signal triggering  
- Batched and debounced updates for performance  

#### Main Methods  
- `updateSignal(signalData)`: Updates signal state  
- `getCurrentSignal()`: Returns current signal state  
- `resetSignal()`: Resets signal to neutral state  
- `addSignalListener(callback)`: Registers for signal updates  

### 3.3 Strategy Management (`strategy-manager.js`)

#### Purpose  
Manages trading strategies, including loading, selecting, and applying different trading strategies and timeframes.

#### Key Features  
- Strategy definition and configuration  
- Timeframe management  
- Strategy persistence  
- UI integration for strategy selection  
- Strategy-specific indicator configuration  

#### Main Methods  
- `loadStrategies()`: Loads available strategies  
- `setStrategy(strategyId)`: Sets the active strategy  
- `getCurrentStrategy()`: Returns the current strategy  
- `getAvailableTimeframes()`: Returns available timeframes  
- `setTimeframe(timeframe)`: Sets the active timeframe  

---

## 4. WebSocket Core Module (`js/websocket-core.js`)

### 4.1 Overview

A robust WebSocket client management system that handles all real-time communication between the StarCrypt frontend and backend server. This module provides a reliable, event-driven interface for managing WebSocket connections, message routing, and connection health monitoring.

### 4.2 Core Functionality

#### 4.2.1 Connection Management
- **Automatic Connection Handling**
  - Establishes WebSocket connection to `ws://<host>/ws`
  - Implements exponential backoff for reconnection attempts
  - Maintains connection state (`isConnected`, `isConnecting`)
  - Provides explicit control via `connect()` and `disconnect()` methods

#### 4.2.2 Message System
- **Message Format**
  ```json
  {
    "type": "message_type",
    "data": {}
  }
  ```
- **Message Routing**
  - Type-based handler registration with `addMessageHandler(type, callback)`
  - Batch processing of messages for performance
  - Global fallback handler for unhandled message types

#### 4.2.3 Connection Health
- **Ping/Pong Protocol**
  - Client sends ping every 30s (configurable)
  - Expects pong response within 10s (configurable)
  - Automatic reconnection on timeout
  - Handles server-initiated pings

### 4.3 API Reference

#### Constructor
```javascript
new WebSocketCore({
  url: string,
  pingInterval: number = 30000,
  pingTimeout: number = 10000,
  maxReconnectAttempts: number = 5,
  reconnectDelay: number = 1000
})
```

#### Core Methods
- **Connection Management**
  - `connect()`: Establish WebSocket connection
  - `disconnect(code, reason)`: Close connection
  - `reconnect()`: Force reconnection

- **Message Handling**
  - `send(message)`: Send message to server
  - `addMessageHandler(type, handler)`: Register message handler
  - `removeMessageHandler(type, handler)`: Unregister handler
  - `queueMessage(message)`: Add message to send queue

- **Connection State**
  - `isConnected`: Boolean connection state
  - `isConnecting`: Boolean connecting state
  - `getConnectionState()`: Get detailed connection state

#### Events
- `on('open', callback)`: Connection established
- `on('close', callback)`: Connection closed
- `on('error', callback)`: Connection error
- `on('message', callback)`: Message received
- `on('reconnect', callback)`: Reconnection attempt

### 4.4 Integration with Backend

#### Message Types
| Type | Direction | Description |
|------|-----------|-------------|
| `ping` | Bidirectional | Connection health check |
| `pong` | Bidirectional | Response to ping |
| `ohlc_data` | Server → Client | OHLC candle data |
| `indicator_update` | Server → Client | Technical indicator updates |
| `live_trade` | Server → Client | Real-time trade data |
| `strategy_change` | Client → Server | Update trading strategy |

#### Error Handling
- Automatic reconnection on connection loss
- Exponential backoff for reconnection attempts
- Error event emission for application-level handling
- Connection state validation before message sending

### 4.5 Best Practices
1. Always check `isConnected` before sending messages
2. Register message handlers during component initialization
3. Clean up handlers when components unmount
4. Use specific message types for better organization
5. Handle connection state changes in the UI

### 4.6 Example Usage
```javascript
// Initialize WebSocket
const ws = new WebSocketCore({
  url: 'ws://localhost:8080/ws',
  pingInterval: 30000,
  pingTimeout: 10000
});

// Handle connection events
ws.on('open', () => {
  console.log('WebSocket connected');
  ws.send({ type: 'subscribe', pair: 'BTCUSDT' });
});

// Handle incoming messages
ws.addMessageHandler('ohlc_data', (data) => {
  // Process OHLC data
  updateChart(data);
});

// Handle errors
ws.on('error', (error) => {
  console.error('WebSocket error:', error);
});
```

### 4.7 Performance Considerations
- Message batching for high-frequency updates
- Debounced processing for rapid message sequences
- Efficient cleanup of old messages and handlers
- Memory-optimized message queue implementation
* `cleanup()`

**Callbacks (passed in options):**

* `onOpenCallback(event)`
* `onCloseCallback(event)`
* `onErrorCallback(error)`
* `globalMessageHandler(message)`

**Export:** The class is exported for CommonJS and also attached to the `window` object (`window.WebSocketCore` for the class, `window.wsCore` for an auto-instantiated instance if not present).

---

### `js/strategy-manager.js` - StrategyManager Class

**Purpose:** The `StrategyManager` class is responsible for managing trading strategies and timeframes. It handles user selections, updates the UI, and communicates relevant changes (like timeframe adjustments) to the backend via WebSocket messages. The file structure has been significantly cleaned up by removing a large duplicated and corrupted code block, and associated syntax errors have been resolved.

**Key Methods & Properties:**

* `constructor()`: Initializes strategy and timeframe states, loads saved preferences, and sets up UI elements and event listeners.
* `initialize()`: Caches DOM elements, loads saved state, sets up event listeners, renders UI components, and initializes WebSocket handlers.
* `setupEventListeners()`: Sets up listeners for strategy menu toggles, timeframe selections, custom `strategyChanged` events, and `websocketStatus` events.
* `initializeWebSocketHandlers()`: Sets up listeners for WebSocket connection status, currently using `window.StarCrypt?.WebSocketProcessor`. **Note:** This might need updating to use `window.wsCore` if `WebSocketProcessor` is a legacy component.
* `loadSavedState()`: Loads the current strategy and timeframe from `localStorage` or defaults if not found.
* `renderStrategyMenu()`: Renders the strategy selection dropdown and description in the UI.
* `renderTimeframeControls()`: Renders the timeframe selection buttons.
* `toggleStrategyMenu()`: Toggles the visibility of the strategy selection menu.
* `handleStrategyChange(strategyId)`: Updates the current strategy, saves it, updates the UI, modifies indicators, and notifies other components.
* `updateStrategyDescription()`: Updates the displayed description for the current strategy.
* `getStrategyDescription(strategyId)`: Retrieves the name, description, and helper text for a given strategy ID.
* `setTimeframe(timeframe)`: Sets the current trading timeframe, saves it to `localStorage`, updates UI. It dispatches a local DOM event `strategyManager:timeframeChanged` and **also sends a WebSocket message `{ type: 'timeframe_change', timeframe: timeframe }` to the backend using the `ensureWebSocketAndSend` helper function (which relies on `window.wsCore`).**
* `updateIndicatorsForStrategy(strategyId)`: Updates the active indicators based on the selected strategy (details depend on `SignalManager` or similar component interaction).
* `notifyStrategyChange(strategyId)`: Dispatches a custom `strategyChanged` event.
* `notifyTimeframeChange(timeframe)`: Dispatches a custom `timeframeChanged` event.
* `updateSignalManagerTimeframe()`: Updates the timeframe in the `SignalManager` (if available).

**Dependencies:**

* `window.TRADING_STRATEGIES`: Expected to be a global object (likely from `js/global-variables.js`) containing strategy definitions.
* `window.StarCrypt?.SignalManager`: Used to update signals based on strategy and timeframe.
* `window.StarCrypt?.WebSocketProcessor`: Used for WebSocket status updates. (Potential refactor point for `window.wsCore`)

**Initialization:**
* A singleton instance is created as `window.StrategyManager` by the `initializeStrategyManager` function, which runs on `DOMContentLoaded`.

---

### `js/ui/event-handlers.js` - UI Event Handlers

**Purpose:** Contains various event handlers for UI interactions, often acting as a bridge between user actions and backend communication or other frontend module updates.

**Key Event Handlers & Functionality:**

*   **Strategy Selection (`strategySelector` change event):**
    *   Triggered when the user selects a new strategy from the dropdown (`#strategySelector`).
    *   Uses the `ensureWebSocketAndSend` helper function (which relies on `window.wsCore`) to send messages to the backend:
        *   `{ type: 'strategy_change', strategy: newStrategyId }`: Notifies the backend of the new strategy.
        *   `{ type: 'request_active_indicator_list', strategy: newStrategyId }`: Requests the list of active indicators for the new strategy.
        *   `{ type: 'request_all_indicator_data_for_strategy', strategy: newStrategyId }`: Requests all indicator data for the new strategy.
    *   Updates UI elements related to the strategy description (e.g., via `StrategyManagerInstance.updateStrategyDescription()`).

*   **Coin Selection (`coinSelector` change event):**
    *   Triggered when the user selects a new coin/pair from the dropdown (`#coinSelector`).
    *   Uses `ensureWebSocketAndSend` to send a `{ type: 'coin_change', coin: selectedCoin }` message to the backend.

*   **Other Potential Handlers:** This file may contain handlers for other UI elements like chart interactions, buttons for manual actions, etc., generally centralizing WebSocket message dispatch for UI-triggered events.

### `js/websocket-core.js` - WebSocket Core Implementation

**Purpose:**
The WebSocketCore class provides a robust WebSocket client implementation with advanced features for connection management, message processing, and error handling. It serves as the communication backbone between the frontend and backend.

**Key Features:**
1. **Connection Management:**
   - Automatic reconnection with exponential backoff
   - Connection state tracking (connected, connecting, disconnected)
   - Configurable connection timeouts and retry logic

2. **Message Processing:**
   - Batch processing of messages with configurable batch sizes
   - Message queuing with priority handling
   - Deduplication of messages
   - Throttling and debouncing of rapid updates

3. **Error Handling:**
   - Comprehensive error tracking and recovery
   - Configurable error thresholds and backoff strategies
   - Automatic reconnection on connection loss

4. **Ping/Pong Mechanism:**
   - Heartbeat system to detect stale connections
   - Configurable ping intervals and timeouts
   - Automatic reconnection on ping timeout

**Key Methods:**
- `connect()`: Establishes WebSocket connection
- `disconnect()`: Gracefully closes the connection
- `send(message)`: Sends messages to the server
- `addMessageHandler(type, handler)`: Registers message handlers
- `processQueue()`: Processes queued messages
- `handlePing()/handlePong()`: Implements ping/pong heartbeat

### `js/signal-system.js` - Signal Processing Engine

**Purpose:**
The SignalSystem class manages the processing and visualization of trading signals across multiple timeframes and indicators.

**Key Features:**
1. **Signal Processing:**
   - Processes raw indicator data into trading signals
   - Handles signal strength calculations
   - Manages signal state and history

2. **UI Integration:**
   - Updates signal visualization elements
   - Manages tooltips and hover interactions
   - Handles signal element creation and updates

3. **Performance Optimization:**
   - Batched updates for signal processing
   - Caching of signal elements and tooltips
   - Debounced updates to prevent UI jank

**Key Methods:**
- `init()`: Initializes the signal system
- `updateSignal(indicator, timeframe, data)`: Updates a specific signal
- `updateAllSignalLights(force)`: Refreshes all signal visualizations
- `processUpdates()`: Processes batched signal updates

### `js/strategy-manager.js` - Strategy Management System

**Purpose:**
The StrategyManager class handles the selection, configuration, and application of trading strategies across the application.

**Key Features:**
1. **Strategy Management:**
   - Loads and manages available trading strategies
   - Handles strategy selection and persistence
   - Manages strategy-specific configurations

2. **Timeframe Management:**
   - Handles timeframe selection
   - Manages active timeframes for analysis
   - Coordinates timeframe changes across components

3. **UI Integration:**
   - Renders strategy selection UI
   - Updates strategy descriptions and information
   - Handles user interactions with strategy controls

**Key Methods:**
- `initialize()`: Sets up the strategy manager
- `loadSavedState()`: Loads saved strategy and timeframe preferences
- `handleStrategyChange(strategyId)`: Handles strategy selection changes
- `setTimeframe(timeframe)`: Updates the active timeframe
- `updateIndicatorsForStrategy(strategyId)`: Updates indicators based on strategy

### `index.html` - Main Application Page & Key Helper Functions

**Purpose:** Serves as the main entry point for the StarCrypt frontend application, loading all necessary HTML, CSS, and JavaScript components. It also hosts globally accessible helper functions.

**Key Helper Functions:**

*   **`ensureWebSocketAndSend(message)`:**
    *   **Location:** Defined as a global function within a `<script>` tag in `index.html`.
    *   **Purpose:** Provides a robust way to send WebSocket messages to the backend via the global `window.wsCore` instance.
    *   **Mechanism:** It checks if `window.wsCore` and its underlying WebSocket connection (`window.wsCore.ws`) are available and if the connection's `readyState` is `WebSocket.OPEN`.
        *   If the connection is open, it directly calls `window.wsCore.send(message)`.
        *   If the connection is *not* open (or `wsCore` is not fully initialized), it *still* calls `window.wsCore.send(message)`. This relies on the `WebSocketCore.send()` method itself to handle message queuing if the WebSocket is not currently connected or ready (e.g., during initial connection attempts or reconnections by `WebSocketCore`).
    *   **Usage:** Used by various frontend components (e.g., `js/ui/event-handlers.js`, `js/strategy-manager.js`) to ensure consistent and reliable message sending to the backend, leveraging the queuing and reconnection capabilities of `window.wsCore`.
