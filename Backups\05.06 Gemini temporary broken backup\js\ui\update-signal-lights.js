(function () {
// update-signal-lights.js - Optimized for performance and responsiveness
// This file handles updating the signal lights based on indicator data

  // Track states to prevent recursive calls
  let isUpdating = false
  let lastUpdateTime = 0
  let updatePending = false // Track if an update is pending
  const UPDATE_THROTTLE_MS = 100 // Throttle updates to max once every 100ms
  const pendingUpdates = new Map() // Track pending updates by indicator+timeframe
  let updateInProgress = false // Track if an update is currently in progress

  // Add debug mode (disabled by default)
  window.DEBUG_SIGNALS = false

  // Performance optimization: Cache DOM elements
  const signalElementsCache = new Map()

  // Debounce function to prevent rapid calls
  function debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  // Debounced version of the update function
  const debouncedUpdate = debounce(updateSignalLightsNonRecursive, UPDATE_THROTTLE_MS)

  /**
 * Public function to update all signal lights - optimized for performance
 */
  function updateAllSignalLights() {
    const now = Date.now()

    // If we're already in the middle of an update, just return
    // The debounced function will handle scheduling the next update
    if (updateInProgress) {
      return
    }

    // Throttle updates to prevent excessive rendering
    if (now - lastUpdateTime < UPDATE_THROTTLE_MS) {
      return
    }

    // Update the last update time
    lastUpdateTime = now

    try {
      if (window.DEBUG_SIGNALS) console.log('Scheduling signal light update...')

      // Check if we have indicator data from the server
      if (!window.indicatorsData || Object.keys(window.indicatorsData).length === 0) {
        if (window.DEBUG_SIGNALS) console.log('No indicator data available')
        return
      }

      // Set the flag to prevent multiple updates
      updateInProgress = true

      // Use requestAnimationFrame for the next frame
      requestAnimationFrame(() => {
        try {
        // Perform the actual update using the debounced function
          debouncedUpdate()
        } catch (err) {
          console.error('Error in signal light update:', err)
        } finally {
        // Clear the in-progress flag after a small delay
        // to ensure we don't get into a tight loop
          setTimeout(() => {
            updateInProgress = false
          }, 10)
        }
      })
    } catch (err) {
      console.error('Unexpected error in updateAllSignalLights:', err)
      updateInProgress = false
    }
  }

  /**
 * Non-recursive implementation of signal light updates
 */
  function updateSignalLightsNonRecursive() {
  // Skip if no data available
    if (!window.indicatorsData) {
      console.warn('No indicator data available')
      return
    }

    // Define indicator groups (moved from global scope to local)
    const INDICATORS = {
      momentum: ['rsi', 'stochRsi', 'macd', 'williamsR', 'mfi', 'ultimateOscillator'],
      trend: ['adx', 'bollingerBands', 'atr', 'vwap', 'fractal'],
      volume: ['volume', 'obv', 'cmf'],
      advanced: ['ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    }

    // Define timeframes (moved from global scope to local)
    const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

    // Batching to improve performance
    batchUpdateSignalLights(INDICATORS, TIMEFRAMES)
  }

  /**
 * Batch updates all signal lights with optimized rendering
 */
  function batchUpdateSignalLights(INDICATORS, TIMEFRAMES) {
    if (!INDICATORS || !TIMEFRAMES) {
      console.error('Invalid indicators or timeframes provided')
      return
    }

    // Process updates in chunks to avoid blocking the main thread
    const CHUNK_SIZE = 10
    const updates = []

    // Prepare all updates
    for (const ind of INDICATORS) {
      for (const tf of TIMEFRAMES) {
        updates.push({ ind, tf })
      }
    }

    // Process chunks with requestIdleCallback for better performance
    const processChunk = (startIdx) => {
      const endIdx = Math.min(startIdx + CHUNK_SIZE, updates.length)

      // Process current chunk
      for (let i = startIdx; i < endIdx; i++) {
        const { ind, tf } = updates[i]
        updateSingleSignalLight(ind, tf)
      }

      // Schedule next chunk if there are more updates
      if (endIdx < updates.length) {
        requestIdleCallback(() => processChunk(endIdx), { timeout: 50 })
      } else {
      // All updates complete
        if (window.DEBUG_SIGNALS) console.log('All signal lights updated')
        isUpdating = false

        // Check if there are pending updates
        if (updatePending) {
          updatePending = false
          updateAllSignalLights()
        }
      }
    }

    // Start processing chunks
    processChunk(0)
  }

  // Track reported missing indicators to avoid console spam
  const reportedMissingIndicators = new Set()

  /**
 * Creates a new signal light element
 */
  function createSignalLight(ind, tf, container) {
    const circle = document.createElement('div')
    circle.className = 'signal-circle'
    circle.setAttribute('data-ind', ind)
    circle.setAttribute('data-tf', tf)
    circle.setAttribute('title', `${ind} (${tf})`)
    circle.id = `signal-${ind}-${tf}` // Assign the ID

    // Add animation class for visual feedback
    circle.classList.add('signal-pulse')

    // Remove animation after it completes
    setTimeout(() => {
      circle.classList.remove('signal-pulse')
    }, 1000)

    if (container) {
      container.appendChild(circle)
    }

    return circle
  }

  /**
 * Updates a single signal light with performance optimizations
 */
  function updateSingleSignalLight(ind, tf) {
    try {
    // Get the signal element or create it if it doesn't exist
      const signalId = `signal-${ind}-${tf}`
      let signalElement = document.getElementById(signalId)

      if (!signalElement) {
        signalElement = createSignalLight(ind, tf)
        if (!signalElement) return // If creation failed, exit

        // If the element was newly created, it needs to be appended to the DOM.
        // Assume a cell structure like id='cell-${ind}-${tf}' within signalGrid.
        if (!signalElement.parentNode) {
          const cellId = `cell-${ind}-${tf}`
          const cell = document.getElementById(cellId)
          if (cell) {
            cell.appendChild(signalElement)
          } else {
          // Log a warning if the specific cell isn't found.
          // This indicates the table structure (expected to be created by an init function) is missing or uses different IDs.
            console.warn(`Cell ${cellId} not found for signal ${signalId}. Signal circle created but not appended to a specific cell.`)
          // Avoid appending directly to signalGrid as it would likely break the layout.
          // const grid = document.getElementById('signalGrid');
          // if (grid) grid.appendChild(signalElement);
          }
        }
      }

      // Get indicator data for this timeframe
      const indicatorData = window.indicatorsData?.[tf]?.[ind]
      if (!indicatorData) {
        if (window.DEBUG_SIGNALS) console.log(`No data for ${ind} on ${tf}`)
        signalElement.className = 'signal neutral'
        signalElement.title = `${ind.toUpperCase()} (${tf}): No data`
        signalElement.style.backgroundColor = '#808080' // Neutral gray
        return
      }

      // Determine signal state based on indicator data
      let signalState = 'neutral'
      let tooltip = `${ind.toUpperCase()} (${tf}): `
      let color = '#808080' // Default neutral color (gray)

      // Custom logic for each indicator type
      try {
        switch (ind) {
          case 'rsi':
            if (indicatorData.value > 70) {
              signalState = 'bearish'
              color = '#FF0000' // Red for overbought
            } else if (indicatorData.value < 30) {
              signalState = 'bullish'
              color = '#00FF00' // Green for oversold
            }
            tooltip += `RSI: ${indicatorData.value.toFixed(2)}`
            break

          case 'macd':
            if (indicatorData.histogram > 0) {
              signalState = 'bullish'
              color = '#00FF00' // Green for bullish
            } else if (indicatorData.histogram < 0) {
              signalState = 'bearish'
              color = '#FF0000' // Red for bearish
            }
            tooltip += `MACD: ${indicatorData.histogram.toFixed(4)}`
            break

          case 'bollingerBands':
            if (indicatorData.upper && indicatorData.middle && indicatorData.lower) {
              const price = indicatorData.price || 0
              const upper = indicatorData.upper
              const lower = indicatorData.lower
              const middle = indicatorData.middle

              if (price > upper) {
                signalState = 'overbought'
                color = '#FF0000' // Red for above upper band
              } else if (price < lower) {
                signalState = 'oversold'
                color = '#00FF00' // Green for below lower band
              } else if (price > middle) {
                signalState = 'bullish'
                color = '#90EE90' // Light green for upper half
              } else {
                signalState = 'bearish'
                color = '#FFA07A' // Light red for lower half
              }
              tooltip += `BB: ${price.toFixed(2)} (${lower.toFixed(2)}-${upper.toFixed(2)})`
            }
            break

            // Add more indicator cases as needed
          default:
          // Default logic for other indicators
            if (indicatorData.value !== undefined) {
              if (indicatorData.value > 0.5) {
                signalState = 'bullish'
                color = '#00FF00'
              } else if (indicatorData.value < -0.5) {
                signalState = 'bearish'
                color = '#FF0000'
              }
              tooltip += `${ind}: ${indicatorData.value.toFixed(2)}`
            } else {
              tooltip += `${ind}: N/A`
            }
        }
      } catch (e) {
        console.error(`Error processing ${ind} indicator:`, e)
        signalState = 'error'
        color = '#FF00FF' // Magenta for errors
        tooltip += 'Error processing indicator'
      }

      // Get the current color and tooltip for comparison
      const prevColor = signalElement.dataset.currentColor || ''
      const prevTooltip = signalElement.dataset.currentTooltip || ''

      // Only update if the color has changed
      if (prevColor !== color || prevTooltip !== tooltip) {
      // First remove all possible color classes
        signalElement.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light')

        // Remove all possible signal classes
        signalElement.classList.remove('degen-buy', 'mild-buy', 'neutral', 'mild-sell', 'degen-sell')

        // Add the appropriate color and signal classes
        signalElement.classList.add(signalState)
        signalElement.style.backgroundColor = color

        // Set data attributes for reference
        signalElement.dataset.currentColor = color
        signalElement.dataset.signalClass = signalState

        // Update tooltip
        signalElement.title = tooltip
        signalElement.dataset.currentTooltip = tooltip

        // Log for debugging
        if (window.DEBUG_SIGNALS) {
          console.log(`Updated signal light ${ind}:${tf} to ${signalState} (${color})`)
        }
      }

      return true
    } catch (error) {
      console.error(`Error updating signal light for ${ind} (${tf}):`, error)
      const signalId = `signal-${ind}-${tf}`
      const signalElement = document.getElementById(signalId)
      if (signalElement) {
        signalElement.className = 'signal error'
        signalElement.style.backgroundColor = '#FF00FF' // Magenta for errors
        signalElement.title = `Error: ${error.message || 'Unknown error'}`
      }
      return false
    }
  }

  /**
 * Updates the convergence indicators (like overall strategy strength)
 * Completely separate from the main signal light update to avoid recursion
 */
  function updateConvergenceIndicators() {
    try {
    // Update the strategy strength display if it exists
      const strengthElement = document.getElementById('strategyStrength')
      if (strengthElement) {
      // Calculate strength based on signal convergence
        const strength = calculateStrategyStrength()
        strengthElement.textContent = `${strength}%`

        // Update color based on strength
        if (strength >= 80) {
          strengthElement.className = 'green-value'
        } else if (strength >= 60) {
          strengthElement.className = 'blue-value'
        } else if (strength >= 40) {
          strengthElement.className = 'neutral-value'
        } else if (strength >= 20) {
          strengthElement.className = 'orange-value'
        } else {
          strengthElement.className = 'red-value'
        }
      }

    // Other convergence indicators can be updated here
    } catch (err) {
      console.error('Error updating convergence indicators:', err)
    }
  }

  /**
 * Calculate the strategy strength based on signal convergence
 * Returns a percentage from 0-100
 */
  function calculateStrategyStrength() {
  // Default to 50% if we can't calculate
    let strength = 50

    try {
    // Count green vs red signals as a very simple metric
      let green = 0
      let red = 0
      let total = 0

      // Get all signal lights
      const signals = document.querySelectorAll('.signal-circle')

      signals.forEach(signal => {
        if (signal.classList.contains('green-light') || signal.classList.contains('degen-buy')) {
          green++
        } else if (signal.classList.contains('blue-light') || signal.classList.contains('mild-buy')) {
          green += 0.5
        } else if (signal.classList.contains('red-light') || signal.classList.contains('degen-sell')) {
          red++
        } else if (signal.classList.contains('orange-light') || signal.classList.contains('mild-sell')) {
          red += 0.5
        }
        total++
      })

      if (total > 0) {
      // Balance between buy and sell signals
        const buyRatio = green / total
        const sellRatio = red / total

        if (buyRatio > sellRatio) {
          strength = Math.round(50 + (buyRatio * 50))
        } else {
          strength = Math.round(50 - (sellRatio * 50))
        }

        // Cap at 0-100
        strength = Math.max(0, Math.min(100, strength))
      }
    } catch (err) {
      console.error('Error calculating strategy strength:', err)
    }

    return strength
  }

  // Track API call statistics
  const apiStats = {
    callsToday: 0,
    lastReset: new Date().setHours(0, 0, 0, 0),
    totalCalls: 0,
    callsByIndicator: {},
  }

  /**
 * Track an API call
 * @param {string} indicator - The indicator that was called
 */
  function trackApiCall(indicator) {
    const now = new Date()

    // Reset counter if it's a new day
    if (now.getTime() - apiStats.lastReset > 24 * 60 * 60 * 1000) {
      apiStats.callsToday = 0
      apiStats.lastReset = now.setHours(0, 0, 0, 0)
    }

    // Update counters
    apiStats.callsToday++
    apiStats.totalCalls++

    if (!apiStats.callsByIndicator[indicator]) {
      apiStats.callsByIndicator[indicator] = 0
    }
    apiStats.callsByIndicator[indicator]++

    // Update the UI if the stats element exists
    updateApiStatsDisplay()
  }

  /**
 * Update the API stats display in the UI
 */
  function updateApiStatsDisplay() {
    const statsElement = document.getElementById('apiStats')
    if (!statsElement) return

    const now = new Date()
    const nextReset = new Date(apiStats.lastReset)
    nextReset.setDate(nextReset.getDate() + 1)
    const timeUntilReset = Math.floor((nextReset - now) / 1000 / 60 / 60) // hours until reset

    statsElement.innerHTML = `
    <div>API Calls Today: ${apiStats.callsToday}</div>
    <div>Total API Calls: ${apiStats.totalCalls}</div>
    <div>Next reset in: ~${timeUntilReset} hours</div>
    <div>Last updated: ${now.toLocaleString()}</div>
  `

    // Update every minute
    setTimeout(updateApiStatsDisplay, 60000)
  }

  // Initialize signal lights and API stats when the page loads
  document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM fully loaded, initializing signal lights and API stats...')

    // Initialize signal lights with a small delay to ensure all elements are ready
    setTimeout(() => {
      try {
      // Initialize signal lights
        if (window.initializeSignalLights) {
          window.initializeSignalLights()
        }

        // Force update all signal lights after a short delay
        setTimeout(() => {
          if (window.updateAllSignalLights) {
            console.log('Forcing initial update of all signal lights...')
            window.updateAllSignalLights()
          }

          // Update signal matrix to ensure it reflects the current strategy
          if (window.updateSignalMatrix) {
            console.log('Updating signal matrix...')
            window.updateSignalMatrix()
          }
        }, 500)
      } catch (error) {
        console.error('Error initializing signal lights:', error)
      }
    }, 100)

    // Create stats element if it doesn't exist
    if (!document.getElementById('apiStats')) {
      const statsDiv = document.createElement('div')
      statsDiv.id = 'apiStats'
      statsDiv.style.position = 'fixed'
      statsDiv.style.bottom = '10px'
      statsDiv.style.right = '10px'
      statsDiv.style.background = 'rgba(0, 0, 0, 0.7)'
      statsDiv.style.color = 'white'
      statsDiv.style.padding = '10px'
      statsDiv.style.borderRadius = '5px'
      statsDiv.style.fontSize = '12px'
      statsDiv.style.zIndex = '10000'
      document.body.appendChild(statsDiv)

      // Initial update
      updateApiStatsDisplay()
    }
  })

  // Make the functions available globally
  window.updateAllSignalLights = updateAllSignalLights
  window.trackApiCall = trackApiCall
  window.apiStats = apiStats
})()
