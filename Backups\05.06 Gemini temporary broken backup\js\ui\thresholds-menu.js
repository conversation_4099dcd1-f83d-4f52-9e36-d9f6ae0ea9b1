// js/ui/thresholds-menu.js

(function () {
  window.isUpdatingSlidersProgrammatically = false // Flag to prevent recursion
  window.isUpdatingSignalLightsFromThresholds = false // Flag for signal light updates
  // Ensure global objects and helper functions are available
  window.TRADING_STRATEGIES = window.TRADING_STRATEGIES || {}
  window.INDICATORS = window.INDICATORS || { momentum: [], trend: [], volume: [], ml: [] }
  window.logMessages = window.logMessages || []

  window.updateLogger = window.updateLogger || function () { /* console.log('[Stub] updateLogger called'); */ }
  window.showToast = window.showToast || function (message, type) { /* console.log(`[Stub] Toast: ${message} (${type})`); */ }
  window.updateAllSignalLights = window.updateAllSignalLights || function () { /* console.log('[Stub] updateAllSignalLights called'); */ }

  window.defaultThresholds = {
    rsi: { red: 70, orange: 60, blue: 40, green: 30 },
    stochRsi: { red: 80, orange: 65, blue: 35, green: 20 },
    williamsR: { red: 80, orange: 65, blue: 35, green: 20 },
    ultimateOscillator: { red: 70, orange: 60, blue: 40, green: 30 },
    mfi: { red: 80, orange: 65, blue: 35, green: 20 },
    adx: { red: 80, orange: 65, blue: 35, green: 20 },
    bollingerBands: { red: 98, orange: 96, blue: 4, green: 2 },
    atr: { high: 2, moderate: 1, low: 0.5 },
    macd: { red: 70, orange: 60, blue: 40, green: 30 },
    volume: { red: 80, orange: 65, blue: 35, green: 20 },
    sentiment: { red: 80, orange: 65, blue: 35, green: 20 },
    entropy: { red: 80, orange: 65, blue: 35, green: 20 },
    correlation: { red: 80, orange: 65, blue: 35, green: 20 },
    time_anomaly: { red: 80, orange: 65, blue: 35, green: 20 },
  }

  if (typeof window.thresholds === 'undefined') {
    const savedThresholds = localStorage.getItem('userThresholds')
    if (savedThresholds) {
      try {
        window.thresholds = JSON.parse(savedThresholds)
        for (const key in window.defaultThresholds) {
          if (!window.thresholds[key]) {
            window.thresholds[key] = JSON.parse(JSON.stringify(window.defaultThresholds[key]))
          }
          if (window.defaultThresholds[key].red !== undefined && window.thresholds[key].red === undefined && key !== 'atr') {
            window.thresholds[key] = JSON.parse(JSON.stringify(window.defaultThresholds[key]))
          }
        }
      } catch (e) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Error loading/parsing saved thresholds: ${e.message}`)
        window.updateLogger()
        window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds))
      }
    } else {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds))
    }
  }

  function renderThresholdSliders(strategy) {
    try {
      const slidersContainerElement = document.getElementById('threshold-sliders')
      if (!slidersContainerElement) {
        console.error('Threshold sliders container (#threshold-sliders) not found')
        return
      }
      slidersContainerElement.innerHTML = '' // Clear previous content

      const header = document.createElement('h3')
      header.style.cssText = 'font-size: 14px; margin: 5px 0px; padding: 0px;'
      header.textContent = 'Thresholds: Admiral T.O.A. Convergence'
      slidersContainerElement.appendChild(header)

      const currentStrategyName = strategy || window.currentStrategy || 'admiral_toa' // Ensure currentStrategy is defined
      const strategyIndicators = window.TRADING_STRATEGIES[currentStrategyName] ?
        window.TRADING_STRATEGIES[currentStrategyName].indicators :
        Object.keys(window.thresholds).filter(ind => ind !== 'atr')

      const makeThumbDraggable = (thumb, indicatorName, sliderBar) => {
        let active = false
        let lastPointerX = 0
        let currentVal = 0 // Stores the latest calculated percentage after constraints
        let updateRAFId = null
        let sliderBarRect = null

        const dragStart = (e) => {
          sliderBar.classList.add('active-slider-bar')
          thumb.classList.add('dragging')
          sliderBarRect = sliderBar.getBoundingClientRect()

          if (e.type === 'touchstart') {
            lastPointerX = e.touches[0].clientX
          } else {
            lastPointerX = e.clientX
            document.addEventListener('mouseup', dragEnd, { passive: false })
            document.addEventListener('mousemove', drag, { passive: false })
          }
          active = true
        }

        const dragEnd = (e) => {
          sliderBar.classList.remove('active-slider-bar')
          thumb.classList.remove('dragging')
          active = false
          document.removeEventListener('mouseup', dragEnd)
          document.removeEventListener('mousemove', drag)

          localStorage.setItem('userThresholds', JSON.stringify(window.thresholds))
          if (typeof window.updateAllSignalLights === 'function') {
            window.updateAllSignalLights()
          }
          if (typeof window.logMessages !== 'undefined' && typeof window.updateLogger === 'function') {
            const thumbType = thumb.dataset.type
            // Use the value from the model which is the source of truth
            window.logMessages.push(`[${new Date().toLocaleString()}] Threshold updated for ${indicatorName} - ${thumbType}: ${window.thresholds[indicatorName][thumbType]}%`)
            window.updateLogger()
          }
          sliderBarRect = null
        }

        const drag = (e) => {
          if (!active) return
          e.preventDefault()

          let currentPointerX
          if (e.type === 'touchmove') {
            currentPointerX = e.touches[0].clientX
          } else {
            currentPointerX = e.clientX
          }

          const thumbType = thumb.dataset.type
          console.clear() // Uncomment for cleaner logs during rapid testing, but can be annoying
          console.log(`%cDRAG: ${indicatorName}-${thumbType}`, 'color: yellow; font-weight: bold;')

          const pointerDeltaPx = currentPointerX - lastPointerX
          console.log(`  Pointer: currentX=${currentPointerX.toFixed(2)}, lastX=${lastPointerX.toFixed(2)}, deltaPx=${pointerDeltaPx.toFixed(2)}`)

          let pointerDeltaPct = 0
          if (sliderBarRect && sliderBarRect.width > 0) { // Ensure sliderBarRect is available
            pointerDeltaPct = (pointerDeltaPx / sliderBarRect.width) * 100
          } else {
            console.warn('sliderBarRect not available or width is zero.')
            lastPointerX = currentPointerX // Prevent large jumps if rect is momentarily unavailable
            return
          }
          console.log(`  DeltaPct: ${pointerDeltaPct.toFixed(3)}%`)

          const thValues = window.thresholds[indicatorName]
          const currentThumbStoredPct = parseFloat(thValues[thumbType])
          console.log(`  StoredPct (model): ${currentThumbStoredPct.toFixed(1)}%`)

          let newPosPercent = currentThumbStoredPct + pointerDeltaPct
          console.log(`  NewPos (model + delta, pre-clamp): ${newPosPercent.toFixed(3)}%`)

          newPosPercent = Math.max(0, Math.min(100, newPosPercent)) // Clamp to 0-100
          console.log(`  NewPos (clamped): ${newPosPercent.toFixed(3)}%`)

          currentVal = parseFloat(newPosPercent.toFixed(1)) // This is the potential new value before constraints
          console.log(`  CurrentVal (toFixed(1), pre-constraint): ${currentVal}%`)

          // Apply constraints
          const minGap = 0.1
          let constrainedVal = currentVal
          if (thumbType === 'green') {
            constrainedVal = Math.min(currentVal, thValues.blue - minGap)
          } else if (thumbType === 'blue') {
            constrainedVal = Math.max(currentVal, thValues.green + minGap)
            constrainedVal = Math.min(constrainedVal, thValues.orange - minGap)
          } else if (thumbType === 'orange') {
            constrainedVal = Math.max(currentVal, thValues.blue + minGap)
            constrainedVal = Math.min(constrainedVal, thValues.red - minGap)
          } else if (thumbType === 'red') {
            constrainedVal = Math.max(currentVal, thValues.orange + minGap)
          }
          // Re-clamp after constraints and ensure precision
          currentVal = parseFloat(Math.max(0, Math.min(100, constrainedVal)).toFixed(1))
          // if (constrainedVal !== currentValBeforeConstraints) console.log(`  Value after constraints: ${currentVal}% (was ${currentValBeforeConstraints}%)`);
          // console.log(`  Final currentVal (after all constraints & clamping): ${currentVal}%`);

          if (thValues[thumbType] !== currentVal) {
            //    console.log(`  %cCHANGING MODEL for ${thumbType}: from ${thValues[thumbType]}% to ${currentVal}%`, 'color: lightgreen;');
            thValues[thumbType] = currentVal // Update the data model

            // Update thumb's visual style
            thumb.style.left = `${currentVal}%`
            thumb.title = `${thumbType.charAt(0).toUpperCase() + thumbType.slice(1)}: ${currentVal}%`

            // Schedule other visual updates (segments, labels, etc.)
            if (updateRAFId) {
              cancelAnimationFrame(updateRAFId)
            }
            updateRAFId = requestAnimationFrame(() => {
              //    console.log(`rAF: Updating display for ${indicatorName}`);
              updateThresholdDisplay(indicatorName)
              updateRAFId = null
            })
          } else {
            // console.log(`  Model for ${thumbType} already ${currentVal}%, no change.`);
          }
          lastPointerX = currentPointerX // Update lastPointerX for the next delta calculation
        }

        thumb.addEventListener('mousedown', dragStart)
        thumb.addEventListener('touchstart', dragStart, { passive: false })
        thumb.addEventListener('touchend', dragEnd)
        thumb.addEventListener('touchmove', drag, { passive: false })
      }

      strategyIndicators.forEach(ind => {
        if (ind === 'atr') return // Skip ATR if it's not a standard threshold type
        if (!window.thresholds[ind] || typeof window.thresholds[ind].green === 'undefined') { // Ensure full structure exists
          // Initialize with default RSI or a base default if specific indicator threshold is missing/incomplete
          window.thresholds[ind] = JSON.parse(JSON.stringify(window.defaultThresholds[ind] || window.defaultThresholds.rsi || { red: 70, orange: 60, blue: 40, green: 30 }))
        }
        const th = window.thresholds[ind]

        const indicatorContainer = document.createElement('div')
        indicatorContainer.className = 'threshold-container'
        indicatorContainer.id = `${ind}Container` // Add ID for easier targeting by updateThresholdDisplay

        const indicatorHeader = document.createElement('h4')
        indicatorHeader.textContent = ind.toUpperCase()
        indicatorContainer.appendChild(indicatorHeader)

        const sliderContainer = document.createElement('div')
        sliderContainer.className = 'slider-container'

        const sliderBar = document.createElement('div')
        sliderBar.className = 'slider-bar'
        sliderBar.id = `${ind}SliderBar`

        // Calculate segment widths, ensuring they are not negative
        const greenWidth = th.green
        const blueWidth = Math.max(0, th.blue - th.green)
        const greyWidth = Math.max(0, th.orange - th.blue)
        const orangeWidth = Math.max(0, th.red - th.orange)
        const redWidth = Math.max(0, 100 - th.red)

        sliderBar.innerHTML = `
                    <div class="segment green" style="width: ${greenWidth}%;"></div>
                    <div class="segment blue" style="width: ${blueWidth}%;"></div>
                    <div class="segment grey" style="width: ${greyWidth}%;"></div>
                    <div class="segment orange" style="width: ${orangeWidth}%;"></div>
                    <div class="segment red" style="width: ${redWidth}%;"></div>
                `
        sliderContainer.appendChild(sliderBar)

        // Create Thumbs
        const createThumb = (type, value) => {
          const thumb = document.createElement('div')
          thumb.className = `slider-thumb ${type}-thumb`
          thumb.id = `${ind}${type.charAt(0).toUpperCase() + type.slice(1)}Thumb`
          thumb.dataset.type = type
          thumb.dataset.indicator = ind
          thumb.style.left = `${value}%`
          thumb.title = `${type.charAt(0).toUpperCase() + type.slice(1)}: ${value}%`
          return thumb
        }

        const greenThumb = createThumb('green', th.green)
        const blueThumb = createThumb('blue', th.blue)
        const orangeThumb = createThumb('orange', th.orange)
        const redThumb = createThumb('red', th.red)

        sliderContainer.appendChild(greenThumb)
        sliderContainer.appendChild(blueThumb)
        sliderContainer.appendChild(orangeThumb)
        sliderContainer.appendChild(redThumb)

        makeThumbDraggable(greenThumb, ind, sliderBar)
        makeThumbDraggable(blueThumb, ind, sliderBar)
        makeThumbDraggable(orangeThumb, ind, sliderBar)
        makeThumbDraggable(redThumb, ind, sliderBar)

        indicatorContainer.appendChild(sliderContainer)

        // Add Dynamic Zone Labels
        const labelsContainer = document.createElement('div')
        labelsContainer.className = 'threshold-labels'
        labelsContainer.id = `${ind}LabelsContainer`

        const createLabel = (className, textFunction) => {
          const label = document.createElement('div')
          label.className = `threshold-label ${className}`
          label.innerHTML = textFunction(th)
          return label
        }

        labelsContainer.appendChild(createLabel('green-zone-label', t => `<span class="color-dot green"></span> Green Zone (0% - <span class="value-g1">${t.green}</span>%)`))
        labelsContainer.appendChild(createLabel('blue-zone-label', t => `<span class="color-dot blue"></span> Blue Zone (<span class="value-g1">${t.green}</span>% - <span class="value-b1">${t.blue}</span>%)`))
        labelsContainer.appendChild(createLabel('grey-zone-label', t => `<span class="color-dot grey"></span> Grey Zone (<span class="value-b1">${t.blue}</span>% - <span class="value-o1">${t.orange}</span>%)`)) // Assuming grey is between blue and orange
        labelsContainer.appendChild(createLabel('orange-zone-label', t => `<span class="color-dot orange"></span> Orange Zone (<span class="value-o1">${t.orange}</span>% - <span class="value-r1">${t.red}</span>%)`))
        labelsContainer.appendChild(createLabel('red-zone-label', t => `<span class="color-dot red"></span> Red Zone (<span class="value-r1">${t.red}</span>% - 100%)`))

        indicatorContainer.appendChild(labelsContainer)
        slidersContainerElement.appendChild(indicatorContainer)
      })

      // Add Reset Button (ensure it's appended to slidersContainerElement)
      const resetButton = document.createElement('button')
      resetButton.className = 'reset-thresholds-button'
      resetButton.textContent = 'Reset to Default Values' // Corrected text from backup
      resetButton.addEventListener('click', () => {
        if (confirm('Are you sure you want to reset all thresholds to their default values?')) {
          // Deep clone defaultThresholds to avoid modifying the original
          window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds))
          // Ensure all indicators in defaultThresholds are present in window.thresholds
          for (const key in window.defaultThresholds) {
            if (!window.thresholds[key] && key !== 'atr') {
              window.thresholds[key] = JSON.parse(JSON.stringify(window.defaultThresholds[key]))
            }
          }
          localStorage.setItem('userThresholds', JSON.stringify(window.thresholds))
          renderThresholdSliders(currentStrategyName) // Re-render this menu
          if (typeof window.updateAllSignalLights === 'function') {
            window.updateAllSignalLights() // Update signal lights across the app
          }
          if (typeof window.showToast === 'function') {
            window.showToast('Thresholds have been reset to defaults.', 'success')
          }
          if (typeof window.logMessages !== 'undefined' && typeof window.updateLogger === 'function') {
            window.logMessages.push(`[${new Date().toLocaleString()}] All thresholds reset to default values.`)
            window.updateLogger()
          }
        }
      })
      slidersContainerElement.appendChild(resetButton)

      if (typeof window.logMessages !== 'undefined' && typeof window.updateLogger === 'function') {
        window.logMessages.push(`[${new Date().toLocaleString()}] Threshold sliders rendered for strategy: ${currentStrategyName}`)
        window.updateLogger()
      }
    } catch (e) {
      console.error('Error rendering threshold sliders:', e)
      window.logMessages.push(`[${new Date().toLocaleString()}] Error rendering threshold sliders: ${e.message}`)
      window.updateLogger()
    }
  }

  function updateThresholdDisplay(indicatorName) {
    try {
      if (!window.thresholds[indicatorName]) {
        console.warn(`No threshold data for ${indicatorName} in updateThresholdDisplay`)
        return
      }
      const th = window.thresholds[indicatorName]

      // Update Slider Bar Segments
      const sliderBar = document.getElementById(`${indicatorName}SliderBar`)
      if (sliderBar) {
        const greenSegment = sliderBar.querySelector('.segment.green')
        const blueSegment = sliderBar.querySelector('.segment.blue')
        const greySegment = sliderBar.querySelector('.segment.grey')
        const orangeSegment = sliderBar.querySelector('.segment.orange')
        const redSegment = sliderBar.querySelector('.segment.red') // Corrected variable name

        const greenWidth = th.green
        const blueWidth = Math.max(0, th.blue - th.green)
        const greyWidth = Math.max(0, th.orange - th.blue)
        const orangeWidth = Math.max(0, th.red - th.orange)
        const redWidth = Math.max(0, 100 - th.red)

        if (greenSegment) greenSegment.style.width = `${greenWidth}%`
        if (blueSegment) { blueSegment.style.width = `${blueWidth}%` /* blueSegment.style.left = `${greenWidth}%`; // Segments are block, left not needed */ }
        if (greySegment) { greySegment.style.width = `${greyWidth}%` /* greySegment.style.left = `${th.blue}%`; */ }
        if (orangeSegment) { orangeSegment.style.width = `${orangeWidth}%` /* orangeSegment.style.left = `${th.orange}%`; */ }
        if (redSegment) { redSegment.style.width = `${redWidth}%` /* redSegment.style.left = `${th.red}%`; */ }
      }

      // Update Thumb Positions and Tooltips (primarily for non-dragged thumbs if constraints moved them)
      const sliderContainer = document.getElementById(`${indicatorName}Container`).querySelector('.slider-container')
      if (sliderContainer) {
        const greenThumb = sliderContainer.querySelector('.green-thumb')
        const blueThumb = sliderContainer.querySelector('.blue-thumb')
        const orangeThumb = sliderContainer.querySelector('.orange-thumb')
        const redThumb = sliderContainer.querySelector('.red-thumb')

        if (greenThumb) { greenThumb.style.left = `${th.green}%`; greenThumb.title = `Green: ${th.green}%` }
        if (blueThumb) { blueThumb.style.left = `${th.blue}%`; blueThumb.title = `Blue: ${th.blue}%` }
        if (orangeThumb) { orangeThumb.style.left = `${th.orange}%`; orangeThumb.title = `Orange: ${th.orange}%` }
        if (redThumb) { redThumb.style.left = `${th.red}%`; redThumb.title = `Red: ${th.red}%` }
      }

      // Update Zone Labels
      const labelsContainer = document.getElementById(`${indicatorName}LabelsContainer`)
      if (labelsContainer) {
        const greenZoneLabel = labelsContainer.querySelector('.green-zone-label')
        const blueZoneLabel = labelsContainer.querySelector('.blue-zone-label')
        const greyZoneLabel = labelsContainer.querySelector('.grey-zone-label')
        const orangeZoneLabel = labelsContainer.querySelector('.orange-zone-label') // Corrected: use labelsContainer
        const redZoneLabel = labelsContainer.querySelector('.red-zone-label') // Corrected: use labelsContainer

        if (greenZoneLabel) greenZoneLabel.innerHTML = `<span class="color-dot green"></span> Green Zone (0% - <span class="dynamic-value-span value-g1">${th.green.toFixed(1)}</span>%)`
        if (blueZoneLabel) blueZoneLabel.innerHTML = `<span class="color-dot blue"></span> Blue Zone (<span class="dynamic-value-span value-g1">${th.green.toFixed(1)}</span>% - <span class="dynamic-value-span value-b1">${th.blue.toFixed(1)}</span>%)`
        if (greyZoneLabel) greyZoneLabel.innerHTML = `<span class="color-dot grey"></span> Grey Zone (<span class="dynamic-value-span value-b1">${th.blue.toFixed(1)}</span>% - <span class="dynamic-value-span value-o1">${th.orange.toFixed(1)}</span>%)`
        if (orangeZoneLabel) orangeZoneLabel.innerHTML = `<span class="color-dot orange"></span> Orange Zone (<span class="dynamic-value-span value-o1">${th.orange.toFixed(1)}</span>% - <span class="dynamic-value-span value-r1">${th.red.toFixed(1)}</span>%)`
        if (redZoneLabel) redZoneLabel.innerHTML = `<span class="color-dot red"></span> Red Zone (<span class="dynamic-value-span value-r1">${th.red.toFixed(1)}</span>% - 100%)`
      }
      // Removed redundant localStorage.setItem and updateAllSignalLights calls from here.
      // They are correctly handled in dragEnd.
    } catch (e) {
      console.error(`Error updating threshold display for ${indicatorName}: ${e.message}`) // Corrected: use indicatorName
      window.logMessages.push(`[${new Date().toLocaleString()}] Error updating threshold display for ${indicatorName}: ${e.message}`) // Corrected: use indicatorName
      window.updateLogger()
    }
  }

  function updateThresholdValues(indicator, green, blue, orange, red) {
    try {
      const validatedGreen = Math.max(0, Math.min(green, blue, 100))
      const validatedBlue = Math.max(validatedGreen, Math.min(blue, orange, 100))
      const validatedOrange = Math.max(validatedBlue, Math.min(orange, red, 100))
      const validatedRed = Math.max(validatedOrange, Math.min(red, 100))

      window.thresholds[indicator] = { green: validatedGreen, blue: validatedBlue, orange: validatedOrange, red: validatedRed }

      const greenInput = document.getElementById(`${indicator}Green`)
      if (greenInput) {
        greenInput.value = validatedGreen
        const blueInput = document.getElementById(`${indicator}Blue`)
        const orangeInput = document.getElementById(`${indicator}Orange`)
        const redInput = document.getElementById(`${indicator}Red`)
        if (blueInput) blueInput.value = validatedBlue
        if (orangeInput) orangeInput.value = validatedOrange
        if (redInput) redInput.value = validatedRed
        updateThresholdDisplay(indicator)
      } else {
        window.updateAllSignalLights()
      }
      window.logMessages.push(`[${new Date().toLocaleString()}] Programmatically updated thresholds for ${indicator}`)
      window.updateLogger()
    } catch (e) {
      console.error(`Error updating thresholds for ${indicator}: ${e.message}`)
      window.logMessages.push(`[${new Date().toLocaleString()}] Error updating thresholds for ${indicator}: ${e.message}`)
      window.updateLogger()
    }
  }

  function initializeThresholdsMenu(currentStrategy) {
    const menuContainer = document.getElementById('thresholdsMenu')
    const slidersContainer = document.getElementById('threshold-sliders')
    if (!menuContainer || !slidersContainer) {
      console.warn('Thresholds menu containers not found. Skipping initialization.')
      return
    }
    renderThresholdSliders(currentStrategy || window.currentStrategy || 'admiral_toa')
    document.addEventListener('strategyChanged', (event) => {
      if (event.detail && event.detail.newStrategy) {
        renderThresholdSliders(event.detail.newStrategy)
      }
    })
    window.logMessages.push(`[${new Date().toLocaleString()}] Thresholds Menu Initialized.`)
    window.updateLogger()
  }

  window.initializeThresholdsMenu = initializeThresholdsMenu
  window.renderThresholdSliders = renderThresholdSliders
  window.updateThresholdValues = updateThresholdValues
})()
