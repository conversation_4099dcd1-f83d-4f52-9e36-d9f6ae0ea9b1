/**
 * WebSocket Service
 * Centralized WebSocket connection management and message handling
 */

class WebSocketService {
  constructor() {
    this.connections = new Map()
    this.nextConnectionId = 0
    this.cleanupInterval = null

    // Initialize cleanup
    this.initializeCleanup()
  }

  // Create a new WebSocket connection
  createConnection(url, options = {}) {
    const connectionId = this.nextConnectionId++
    const ws = new WebSocket(url)

    // Store connection with metadata
    const connection = {
      ws,
      url,
      options,
      lastActivity: Date.now(),
      handlers: new Map(),
    }

    // Apply pending handlers if any
    if (this.pendingHandlers && this.pendingHandlers.has(connectionId)) {
      const pendingHandlers = this.pendingHandlers.get(connectionId)
      pendingHandlers.forEach((handler, type) => {
        connection.handlers.set(type, handler)
      })
      this.pendingHandlers.delete(connectionId)
    }

    this.connections.set(connectionId, connection)

    // Set up event handlers
    ws.onopen = this.handleOpen.bind(this, connectionId)
    ws.onclose = this.handleClose.bind(this, connectionId)
    ws.onerror = this.handleError.bind(this, connectionId)
    ws.onmessage = this.handleMessage.bind(this, connectionId)

    return connectionId
  }

  // Add message handler
  addHandler(connectionId, type, handler) {
    if (!this.connections.has(connectionId)) {
      // Store pending handlers for future connection
      if (!this.pendingHandlers) {
        this.pendingHandlers = new Map()
      }
      if (!this.pendingHandlers.has(connectionId)) {
        this.pendingHandlers.set(connectionId, new Map())
      }
      this.pendingHandlers.get(connectionId).set(type, handler)
      return
    }

    const connection = this.connections.get(connectionId)
    connection.handlers.set(type, handler)
  }

  // Remove message handler
  removeHandler(connectionId, type) {
    const connection = this.connections.get(connectionId)
    if (connection) {
      connection.handlers.delete(type)
    }
  }

  // Send message
  sendMessage(connectionId, message) {
    const connection = this.connections.get(connectionId)
    if (!connection || connection.ws.readyState !== WebSocket.OPEN) {
      return false
    }

    try {
      connection.ws.send(JSON.stringify(message))
      connection.lastActivity = Date.now()
      return true
    } catch (error) {
      console.error('[WebSocket] Send error:', error)
      return false
    }
  }

  // Close connection
  closeConnection(connectionId) {
    const connection = this.connections.get(connectionId)
    if (connection) {
      connection.ws.close()
      this.connections.delete(connectionId)
    }
  }

  // Event handlers
  handleOpen(connectionId) {
    const connection = this.connections.get(connectionId)
    if (connection) {
      connection.lastActivity = Date.now()
      console.log(`[WebSocket] Connection ${connectionId} opened`)
    }
  }

  handleClose(connectionId, event) {
    const connection = this.connections.get(connectionId)
    if (connection) {
      console.log(`[WebSocket] Connection ${connectionId} closed:`, event.code, event.reason)
      this.connections.delete(connectionId)
    }
  }

  handleError(connectionId, error) {
    console.error(`[WebSocket] Connection ${connectionId} error:`, error)
    const connection = this.connections.get(connectionId)
    if (connection) {
      this.connections.delete(connectionId)
    }
  }

  async handleMessage(connectionId, event) {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    try {
      const message = JSON.parse(event.data)
      const handler = connection.handlers.get(message.type)

      if (handler) {
        await handler(message)
      }

      connection.lastActivity = Date.now()
    } catch (error) {
      console.error(`[WebSocket] Connection ${connectionId} message error:`, error)
    }
  }

  // Cleanup inactive connections
  initializeCleanup() {
    this.cleanupInterval = setInterval(() => {
      const now = Date.now()

      for (const [id, connection] of this.connections) {
        if (now - connection.lastActivity > 30000) { // 30 seconds
          console.log(`[WebSocket] Cleaning up inactive connection ${id}`)
          this.closeConnection(id)
        }
      }
    }, 5000) // Check every 5 seconds
  }

  // Cleanup all connections
  cleanup() {
    clearInterval(this.cleanupInterval)
    for (const [id, connection] of this.connections) {
      this.closeConnection(id)
    }
    this.connections.clear()
  }
}

// Initialize WebSocket service
window.WebSocketService = new WebSocketService()
