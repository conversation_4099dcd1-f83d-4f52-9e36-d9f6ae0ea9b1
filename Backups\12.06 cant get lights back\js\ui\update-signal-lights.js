window.hasInitializedUpdateSignalLightsScript = true; // Cascade: Effectively disable this legacy script

// update-signal-lights.js - Optimized for performance and responsiveness
// This file handles updating the signal lights based on indicator data

// Enable debug mode for signal lights
window.DEBUG_SIGNALS = true; // Set to false in production

if (!window.hasInitializedUpdateSignalLightsScript) {
  (function() {
    'use strict';
    
    // Debug mode for signal lights
    window.DEBUG_SIGNALS = true; // Set to false in production

    // Ensure signalLightsState is declared only once on the window object
    if (typeof window.signalLightsState === 'undefined') {
      window.signalLightsState = {
        isUpdating: false,
        lastUpdateTime: 0,
        updatePending: false // Track if an update is pending
      };
    }
    // NO top-level let aliases for isUpdating, lastUpdateTime, updatePending
const UPDATE_THROTTLE_MS = 25; // Throttle updates to max once every 25ms
let pendingUpdates = new Map(); // Track pending updates by indicator+timeframe
let updateInProgress = false; // Track if an update is currently in progress

// Add debug mode (disabled by default)
window.DEBUG_SIGNALS = false;

// Performance optimization: Cache DOM elements
const signalElementsCache = new Map();

// Debounce function to prevent rapid calls
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Debounced version of the update function
const debouncedUpdate = debounce(updateSignalLightsNonRecursive, UPDATE_THROTTLE_MS);

// Export the debounced update function for WebSocket handlers
window.updateSignalLights = debouncedUpdate;

/**
 * Public function to update all signal lights - optimized for performance
 */
function updateAllSignalLights() {
  const now = Date.now();
  
  // If we're already in the middle of an update, just return
  // The debounced function will handle scheduling the next update
  if (window.signalLightsState.isUpdating || updateInProgress) { // updateInProgress is a separate local guard for the debounced function
    return;
  }
  
  // Throttle updates to prevent excessive rendering
  if (now - window.signalLightsState.lastUpdateTime < UPDATE_THROTTLE_MS) {
    return;
  }
  
  // Update the last update time on the global state object
  window.signalLightsState.lastUpdateTime = now;
  
  try {
    if (window.DEBUG_SIGNALS) console.log('Scheduling signal light update...');
    
    // Check if we have indicator data from the server
    if (!window.indicatorsData || Object.keys(window.indicatorsData).length === 0) {
      if (window.DEBUG_SIGNALS) console.log('No indicator data available');
      return;
    }
    
    // Set the flag to prevent multiple updates
    updateInProgress = true;
    
    // Use requestAnimationFrame for the next frame
    requestAnimationFrame(() => {
      try {
        // Perform the actual update using the debounced function
        debouncedUpdate();
      } catch (err) {
        console.error('Error in signal light update:', err);
      } finally {
        // Clear the in-progress flag after a small delay
        // to ensure we don't get into a tight loop
        setTimeout(() => {
          updateInProgress = false;
        }, 10);
      }
    });
  } catch (err) {
    console.error('Unexpected error in updateAllSignalLights:', err);
    updateInProgress = false;
  }
}

/**
 * Non-recursive implementation of signal light updates
 */
function updateSignalLightsNonRecursive() {
  // Skip if no data available
  if (!window.indicatorsData) {
    console.warn('[SignalLights] No indicator data available');
    return;
  }
  
  // Debug: Log the structure of indicatorsData
  if (window.DEBUG_SIGNALS) {
    console.group('[SignalLights] Current indicatorsData structure');
    Object.entries(window.indicatorsData).forEach(([timeframe, indicators]) => {
      console.group(`Timeframe: ${timeframe}`);
      console.log('Available indicators:', Object.keys(indicators));
      console.groupEnd();
    });
    console.groupEnd();
  }
  
  try {
    // Define all possible indicators we might have (excluding 'stoch' as per requirements)
    const ALL_INDICATORS = [
      // Momentum indicators
      'rsi', 'macd', 'stochRsi', 'williamsR', 'mfi', 'ultimateOscillator',
      // Trend indicators
      'adx', 'bollingerBands', 'atr', 'vwap', 'fractal',
      // Volume indicators
      'volume', 'obv', 'cmf',
      // Advanced indicators
      'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly',
      // Additional indicators
      'ema', 'sma', 'ichimoku', 'pivot', 'fib', 'sr', 'vwma', 'alma', 'bb'
    ];
    
    const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
    
    // Log available timeframes and indicators for debugging
    if (window.DEBUG_SIGNALS) {
      console.log('Available timeframes in indicatorsData:', Object.keys(window.indicatorsData));
      Object.entries(window.indicatorsData).forEach(([tf, indicators]) => {
        console.log(`Indicators for ${tf}:`, Object.keys(indicators));
      });
    }
    
    // Process all indicators and timeframes
    ALL_INDICATORS.forEach(indicator => {
      TIMEFRAMES.forEach(timeframe => {
        try {
          const elementId = `signal-${indicator}-${timeframe}`;
          let element = document.getElementById(elementId);
          
          // Skip if this is a stoch indicator (removed as per requirements)
          if (indicator === 'stoch') {
            if (window.DEBUG_SIGNALS) {
              console.log(`[SignalLights] Skipping stoch indicator as it's been removed`);
            }
            return;
          }
          
          // If element doesn't exist, create it
          if (!element && typeof window.createSignalElement === 'function') {
            // Skip if we don't have data for this indicator/timeframe
            const hasData = window.indicatorsData?.[timeframe]?.[indicator] !== undefined;
            
            if (!hasData) {
              if (window.DEBUG_SIGNALS) {
                console.log(`[SignalLights] No data for ${indicator}-${timeframe}, skipping element creation`);
              }
              return;
            }
            
            if (window.DEBUG_SIGNALS) {
              console.log(`[SignalLights] Creating missing signal element for ${indicator}-${timeframe}`);
            }
            element = window.createSignalElement(indicator, timeframe);
            
            // If we still don't have an element after trying to create it, log a warning
            if (!element && window.DEBUG_SIGNALS) {
              console.warn(`[SignalLights] Failed to create signal element for ${indicator}-${timeframe}`);
            }
          }
          
          // Skip if we don't have an element to update
          if (!element) return;
          
          // Try to find the signal data with flexible matching
          let signalData = null;
          
          // Try exact match first
          signalData = window.indicatorsData?.[timeframe]?.[indicator];
          
          // If no exact match, try case-insensitive match
          if (!signalData && window.indicatorsData?.[timeframe]) {
            const indicatorLower = indicator.toLowerCase();
            const matchingKey = Object.keys(window.indicatorsData[timeframe]).find(
              k => k && typeof k === 'string' && k.toLowerCase() === indicatorLower
            );
            if (matchingKey) {
              signalData = window.indicatorsData[timeframe][matchingKey];
            }
          }
          
          // If we found data, update the signal
          if (signalData) {
            if (window.DEBUG_SIGNALS) {
              console.log(`[SignalLights] Updating ${indicator}-${timeframe} with data:`, signalData);
            }
            updateSingleSignalLight(indicator, timeframe, signalData, true);
          } else if (window.DEBUG_SIGNALS) {
            console.log(`[SignalLights] No data found for ${indicator}-${timeframe}`);
          }
        } catch (err) {
          console.error(`[SignalLights] Error processing ${indicator}-${timeframe}:`, err);
        }
      });
    });
    
    // Update the last update time
    window.signalLightsState = window.signalLightsState || {};
    window.signalLightsState.lastUpdateTime = Date.now();
    
    // Debug: Log completion
    if (window.DEBUG_SIGNALS) {
      console.log('[SignalLights] Update completed at', new Date().toISOString());
    }
    
  } catch (error) {
    console.error('[SignalLights] Error in updateSignalLightsNonRecursive:', error);
  }
  
  // Define indicator groups (moved from global scope to local)
  const INDICATORS = {
    momentum: ['rsi', 'stochRsi', 'macd', 'williamsR', 'mfi', 'ultimateOscillator'],
    trend: ['adx', 'bollingerBands', 'atr', 'vwap', 'fractal'],
    volume: ['volume', 'obv', 'cmf'],
    advanced: ['ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly']
  };
  
  // Define timeframes (moved from global scope to local) 
  const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
  
  // Batching to improve performance
  batchUpdateSignalLights(INDICATORS, TIMEFRAMES);
}

/**
 * Batch updates all signal lights with optimized rendering
 */
function batchUpdateSignalLights(INDICATORS, TIMEFRAMES) {
  if (!INDICATORS || !TIMEFRAMES) {
    console.error('Invalid indicators or timeframes provided');
    return;
  }
  
  // Process updates in chunks to avoid blocking the main thread
  const CHUNK_SIZE = 10;
  const updates = [];
  
  // Prepare all updates
  for (const ind of INDICATORS) {
    for (const tf of TIMEFRAMES) {
      updates.push({ ind, tf });
    }
  }
  
  // Process chunks with requestIdleCallback for better performance
  const processChunk = (startIdx) => {
    const endIdx = Math.min(startIdx + CHUNK_SIZE, updates.length);
    
    // Process current chunk
    for (let i = startIdx; i < endIdx; i++) {
      const { ind, tf } = updates[i];
      updateSingleSignalLight(ind, tf);
    }
    
    // Schedule next chunk if there are more updates
    if (endIdx < updates.length) {
      requestIdleCallback(() => processChunk(endIdx), { timeout: 50 });
    } else {
      // All updates complete
      if (window.DEBUG_SIGNALS) console.log('All signal lights updated');
      window.signalLightsState.isUpdating = false;
      
      // Check if there are pending updates
      if (window.signalLightsState.updatePending) {
        window.signalLightsState.updatePending = false;
        updateAllSignalLights(); // This function will use window.signalLightsState internally
      }
    }
  };
  
  // Start processing chunks
  processChunk(0);
}

// Track reported missing indicators to avoid console spam
const reportedMissingIndicators = new Set();

/**
 * Creates a new signal light element
 */
function createSignalLight(ind, tf, container) {
  const circle = document.createElement('div');
  circle.className = 'signal-circle';
  circle.setAttribute('data-ind', ind);
  circle.setAttribute('data-tf', tf);
  circle.setAttribute('title', `${ind} (${tf})`);
  
  // Add animation class for visual feedback
  circle.classList.add('signal-pulse');
  
  // Remove animation after it completes
  setTimeout(() => {
    circle.classList.remove('signal-pulse');
  }, 1000);
  
  if (container) {
    container.appendChild(circle);
  }
  
  return circle;
}

/**
 * Updates a single signal light with performance optimizations
 */
function getSignalColorClass(color) {
  if (!color) return 'neutral';
  
  // Convert to lowercase for case-insensitive comparison
  const colorStr = String(color).toLowerCase().trim();
  
  if (colorStr === '#00ff00' || colorStr === 'green') {
    return 'degen-buy';
  } else if (colorStr === '#0000ff' || colorStr === 'blue') {
    return 'mild-buy';
  } else if (colorStr === '#ff0000' || colorStr === 'red') {
    return 'degen-sell';
  } else if (colorStr === '#ffa500' || colorStr === 'orange') {
    return 'mild-sell';
  }
  return 'neutral';
}

/**
 * Updates a single signal light with the latest indicator data
 * @param {string} ind - The indicator name
 * @param {string} tf - The timeframe
 * @param {boolean} [force=false] - Force update even if data hasn't changed
 * @returns {boolean} True if the signal was updated, false otherwise
 */
function updateSingleSignalLight(ind, tf, force = false) {
  let signalElement;
  
  try {
    // Skip if we don't have the necessary data
    if (!window.indicatorsData || !window.indicatorsData[tf] || !window.indicatorsData[tf][ind]) {
      if (window.DEBUG_SIGNALS) console.log(`[SignalDebug] No data for ${ind} (${tf})`);
      return false;
    }
    
    // Get the signal element or create it if it doesn't exist
    const signalId = `signal-${ind}-${tf}`;
    signalElement = document.getElementById(signalId);
    
    // Create the signal element if it doesn't exist
    if (!signalElement) {
      signalElement = document.createElement('div');
      signalElement.id = signalId;
      signalElement.className = 'signal-circle';
      signalElement.setAttribute('data-ind', ind);
      signalElement.setAttribute('data-tf', tf);
      signalElement.setAttribute('data-indicator', ind);
      signalElement.setAttribute('data-timeframe', tf);
      
      // Find the appropriate container and append the signal element
      const container = document.querySelector(`.indicator-row[data-indicator="${ind}"] .signal-cell`);
      if (container) {
        container.appendChild(signalElement);
      } else {
        // If no container found, try to find or create one
        const row = document.querySelector(`.indicator-row[data-indicator="${ind}"]`);
        if (row) {
          let cell = row.querySelector('.signal-cell');
          if (!cell) {
            cell = document.createElement('td');
            cell.className = 'signal-cell';
            row.appendChild(cell);
          }
          cell.appendChild(signalElement);
        } else {
          console.warn(`[SignalDebug] Could not find or create container for indicator: ${ind}`);
          return false;
        }
      }
    }
    
    // Get the indicator data
    const indicatorData = window.indicatorsData[tf][ind];
    
    // Skip if no data and not forcing an update
    if (!indicatorData && !force) {
      return false;
    }
    
    // Determine the signal class based on the indicator data
    let signalClass = 'neutral';
    let tooltipText = `${ind} (${tf})`;
    let color = '#808080'; // Default neutral color
    
    if (indicatorData) {
      // Use the color from the indicator data if available, otherwise use the signal
      if (indicatorData.color) {
        signalClass = getSignalColorClass(indicatorData.color);
        color = indicatorData.color;
      } else if (indicatorData.signal) {
        signalClass = indicatorData.signal;
        // Map signal to color if not provided
        if (signalClass === 'buy') {
          color = '#00FF00';
        } else if (signalClass === 'sell') {
          color = '#FF0000';
        } else if (signalClass === 'neutral') {
          color = '#808080';
        }
      }
      
      // Update the tooltip with the indicator value if available
      if (indicatorData.value !== undefined) {
        tooltipText = `${ind} (${tf}): ${indicatorData.value}`;
      }
      
      // Add additional metadata to the tooltip if available
      if (indicatorData.metadata) {
        tooltipText += `\n${JSON.stringify(indicatorData.metadata, null, 2)}`;
      }
    }
    
    // Update the signal element
    if (signalElement) {
      // Remove all existing signal classes
      const classesToRemove = [
        'degen-buy', 'mild-buy', 'neutral', 'mild-sell', 'degen-sell',
        'green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light',
        'signal-pulse', 'error'
      ];
      signalElement.classList.remove(...classesToRemove);
      
      // Add the appropriate signal class
      signalElement.classList.add(signalClass);
      
      // Set the background color
      signalElement.style.backgroundColor = color;
      
      // Update the tooltip
      signalElement.setAttribute('title', tooltipText);
      signalElement.setAttribute('data-tooltip', tooltipText);
      
      // Ensure the signal is visible
      signalElement.style.display = 'inline-block';
      
      // Add animation class for visual feedback
      signalElement.classList.add('signal-pulse');
      
      // Remove animation after it completes
      setTimeout(() => {
        if (signalElement) {
          signalElement.classList.remove('signal-pulse');
        }
      }, 1000);
      
      if (window.DEBUG_SIGNALS) {
        console.log(`[SignalDebug] Updated signal ${ind} (${tf}) with class: ${signalClass}`);
      }
      
      return true;
    }
    
    return false;
    
  } catch (error) {
    console.error(`Error updating signal light for ${ind} (${tf}):`, error);
    if (signalElement) {
      signalElement.className = 'signal-circle error';
      signalElement.style.backgroundColor = '#FF00FF'; // Magenta for errors
      signalElement.title = `Error: ${error.message || 'Unknown error'}`;
    }
    return false;
  }
}

/**
 * Updates the convergence indicators (like overall strategy strength)
 * Completely separate from the main signal light update to avoid recursion
 */
function updateConvergenceIndicators() {
  try {
    // Update the strategy strength display if it exists
    const strengthElement = document.getElementById('strategyStrength');
    if (strengthElement) {
      // Calculate strength based on signal convergence
      let strength = calculateStrategyStrength();
      strengthElement.textContent = `${strength}%`;
      
      // Update color based on strength
      if (strength >= 80) {
        strengthElement.className = 'green-value';
      } else if (strength >= 60) {
        strengthElement.className = 'blue-value';
      } else if (strength >= 40) {
        strengthElement.className = 'neutral-value';
      } else if (strength >= 20) {
        strengthElement.className = 'orange-value';
      } else {
        strengthElement.className = 'red-value';
      }
    }
    
    // Other convergence indicators can be updated here
  } catch (err) {
    console.error('Error updating convergence indicators:', err);
  }
}

/**
 * Calculate the strategy strength based on signal convergence
 * Returns a percentage from 0-100
 */
function calculateStrategyStrength() {
  // Default to 50% if we can't calculate
  let strength = 50;
  
  try {
    // Count green vs red signals as a very simple metric
    let green = 0;
    let red = 0;
    let total = 0;
    
    // Get all signal lights
    const signals = document.querySelectorAll('.signal-circle');
    
    signals.forEach(signal => {
      if (signal.classList.contains('green-light') || signal.classList.contains('degen-buy')) {
        green++;
      } else if (signal.classList.contains('blue-light') || signal.classList.contains('mild-buy')) {
        green += 0.5;
      } else if (signal.classList.contains('red-light') || signal.classList.contains('degen-sell')) {
        red++;
      } else if (signal.classList.contains('orange-light') || signal.classList.contains('mild-sell')) {
        red += 0.5;
      }
      total++;
    });
    
    if (total > 0) {
      // Balance between buy and sell signals
      const buyRatio = green / total;
      const sellRatio = red / total;
      
      if (buyRatio > sellRatio) {
        strength = Math.round(50 + (buyRatio * 50));
      } else {
        strength = Math.round(50 - (sellRatio * 50));
      }
      
      // Cap at 0-100
      strength = Math.max(0, Math.min(100, strength));
    }
  } catch (err) {
    console.error('Error calculating strategy strength:', err);
  }
  
  return strength;
}

// Track API call statistics
const apiStats = {
  callsToday: 0,
  lastReset: new Date().setHours(0, 0, 0, 0),
  totalCalls: 0,
  callsByIndicator: {}
};

/**
 * Track an API call
 * @param {string} indicator - The indicator that was called
 */
function trackApiCall(indicator) {
  const now = new Date();
  
  // Reset counter if it's a new day
  if (now.getTime() - apiStats.lastReset > 24 * 60 * 60 * 1000) {
    apiStats.callsToday = 0;
    apiStats.lastReset = now.setHours(0, 0, 0, 0);
  }
  
  // Update counters
  apiStats.callsToday++;
  apiStats.totalCalls++;
  
  if (!apiStats.callsByIndicator[indicator]) {
    apiStats.callsByIndicator[indicator] = 0;
  }
  apiStats.callsByIndicator[indicator]++;
  
  // Update the UI if the stats element exists
  updateApiStatsDisplay();
}

/**
 * Update the API stats display in the UI
 */
function updateApiStatsDisplay() {
  const statsElement = document.getElementById('apiStats');
  if (!statsElement) return;
  
  const now = new Date();
  const nextReset = new Date(apiStats.lastReset);
  nextReset.setDate(nextReset.getDate() + 1);
  const timeUntilReset = Math.floor((nextReset - now) / 1000 / 60 / 60); // hours until reset
  
  statsElement.innerHTML = `
    <div>API Calls Today: ${apiStats.callsToday}</div>
    <div>Total API Calls: ${apiStats.totalCalls}</div>
    <div>Next reset in: ~${timeUntilReset} hours</div>
    <div>Last updated: ${now.toLocaleString()}</div>
  `;
  
  // Update every minute
  setTimeout(updateApiStatsDisplay, 60000);
}

// Initialize signal lights and API stats when the page loads
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM fully loaded, initializing signal lights and API stats...');
  
  // Initialize signal lights with a small delay to ensure all elements are ready
  setTimeout(() => {
    try {
      // Initialize signal lights
      if (window.initializeSignalLights) {
        window.initializeSignalLights();
      }
      
      // Force update all signal lights after a short delay
      setTimeout(() => {
        if (window.updateAllSignalLights) {
          console.log('Forcing initial update of all signal lights...');
          window.updateAllSignalLights();
        }
        
        // Update signal matrix to ensure it reflects the current strategy
        if (window.updateSignalMatrix) {
          console.log('Updating signal matrix...');
          window.updateSignalMatrix();
        }
      }, 500);
      
      // Add API stats display if it doesn't exist
      if (!document.getElementById('apiStats')) {
        const statsDiv = document.createElement('div');
        statsDiv.id = 'apiStats';
        statsDiv.style.position = 'fixed';
        statsDiv.style.bottom = '10px';
        statsDiv.style.right = '10px';
        statsDiv.style.background = 'rgba(0, 0, 0, 0.7)';
        statsDiv.style.color = 'white';
        statsDiv.style.padding = '10px';
        statsDiv.style.borderRadius = '5px';
        statsDiv.style.fontSize = '12px';
        statsDiv.style.zIndex = '10000';
        document.body.appendChild(statsDiv);
        
        // Initial update
        updateApiStatsDisplay();
      }
    } catch (error) {
      console.error('Error during initialization:', error);
    }
  }, 100);
});

/**
 * Initializes all signal lights for the first time.
 * This function directly calls the non-recursive update to bypass throttling and update flags
 * meant for subsequent updates, ensuring an immediate render on page load.
 */
function initializeSignalLights() {
  if (window.DEBUG_SIGNALS) {
    console.log('[initializeSignalLights] Initializing signal lights for the first time...');
  }
  
  try {
    // Directly call the core update logic that creates/updates all lights
    updateSignalLightsNonRecursive(); 
  } catch (error) {
    console.error('[initializeSignalLights] Error during initial signal light setup:', error);
  }
}

// Make the functions available globally
window.initializeSignalLights = initializeSignalLights;
window.updateAllSignalLights = updateAllSignalLights;
window.trackApiCall = trackApiCall;
window.apiStats = apiStats;

// Initialize signal lights when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM fully loaded, initializing signal lights...');
    initializeSignalLights();
});

// End of IIFE and script initialization
})();

window.hasInitializedUpdateSignalLightsScript = true;
} // Add missing closing brace
