// Theme Manager for StarCrypt
class ThemeManager {
  constructor() {
    this.themes = {
      dark: {
        '--bg-color': '#0a0a1a',
        '--text-color': '#e0e0e0',
        '--primary-color': '#00a8ff',
        '--secondary-color': '#6c5ce7',
        '--accent-color': '#00ffaa',
        '--success-color': '#00ff00',
        '--warning-color': '#ffa500',
        '--danger-color': '#ff3860',
        '--border-color': '#2a2a4a',
        '--card-bg': '#111133',
        '--card-shadow': '0 0.5rem 1rem rgba(0, 0, 0, 0.6)',
        '--signal-buy': '#00ff00',
        '--signal-sell': '#ff3860',
        '--signal-neutral': '#808080',
        '--signal-warning': '#ffa500',
        '--chart-bg': '#0a0a1a',
        '--chart-grid': '#1a1a3a',
        '--chart-text': '#e0e0e0',
        '--chart-up': '#00ff00',
        '--chart-down': '#ff3860',
        '--chart-volume': 'rgba(0, 168, 255, 0.2)',
      },
      light: {
        '--bg-color': '#f0f2f5',
        '--text-color': '#2c3e50',
        '--primary-color': '#3498db',
        '--secondary-color': '#9b59b6',
        '--accent-color': '#2ecc71',
        '--success-color': '#27ae60',
        '--warning-color': '#f39c12',
        '--danger-color': '#e74c3c',
        '--border-color': '#dcdde1',
        '--card-bg': '#ffffff',
        '--card-shadow': '0 0.5rem 1rem rgba(0, 0, 0, 0.1)',
        '--signal-buy': '#27ae60',
        '--signal-sell': '#e74c3c',
        '--signal-neutral': '#95a5a6',
        '--signal-warning': '#f39c12',
        '--chart-bg': '#ffffff',
        '--chart-grid': '#ecf0f1',
        '--chart-text': '#2c3e50',
        '--chart-up': '#27ae60',
        '--chart-down': '#e74c3c',
        '--chart-volume': 'rgba(52, 152, 219, 0.2)',
      },
      cosmic: {
        '--bg-color': '#0f0c29',
        '--text-color': '#e0e0e0',
        '--primary-color': '#8a2be2',
        '--secondary-color': '#6a5acd',
        '--accent-color': '#9370db',
        '--success-color': '#00ff9d',
        '--warning-color': '#ff9d00',
        '--danger-color': '#ff3c5f',
        '--border-color': '#4b0082',
        '--card-bg': '#1a1642',
        '--card-shadow': '0 0.5rem 1rem rgba(0, 0, 0, 0.7)',
        '--signal-buy': '#00ff9d',
        '--signal-sell': '#ff3c5f',
        '--signal-neutral': '#8e8e9e',
        '--signal-warning': '#ff9d00',
        '--chart-bg': '#0f0c29',
        '--chart-grid': '#1a1642',
        '--chart-text': '#e0e0e0',
        '--chart-up': '#00ff9d',
        '--chart-down': '#ff3c5f',
        '--chart-volume': 'rgba(154, 89, 255, 0.2)',
      },
    }

    this.currentTheme = 'dark'
    this.themeChangeCallbacks = []
    this.initialized = false
  }

  // Apply a theme by name
  setTheme(themeName, savePreference = true) {
    if (!this.themes[themeName]) {
      console.warn(`Theme '${themeName}' not found`)
      return
    }

    this.currentTheme = themeName
    const theme = this.themes[themeName]

    // Apply theme variables to root element
    const root = document.documentElement
    Object.entries(theme).forEach(([key, value]) => {
      root.style.setProperty(key, value)
    })

    // Update theme meta tag
    document.documentElement.setAttribute('data-theme', themeName)

    // Save preference if needed
    if (savePreference) {
      this.saveThemePreference(themeName)
    }

    // Notify listeners
    this.notifyThemeChange(themeName)
  }

  init() {
    if (this.initialized) return

    // Load saved theme or use default
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme && this.themes[savedTheme]) {
      this.setTheme(savedTheme, false)
    } else {
      // Check system preference
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
      this.setTheme(prefersDark ? 'dark' : 'light', false)
    }

    // Listen for system theme changes
    if (window.matchMedia) {
      const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      darkModeMediaQuery.addListener((e) => {
        if (!localStorage.getItem('theme')) { // Only auto-change if no theme is set
          this.setTheme(e.matches ? 'dark' : 'light', false)
        }
      })
    }

    this.initialized = true
  }

  // Add theme selector dropdown
  addThemeSelector() {
    // Check if selector already exists
    if (document.getElementById('theme-selector')) return

    const container = document.createElement('div')
    container.className = 'theme-selector'
    container.innerHTML = `
      <select id="theme-selector" aria-label="Select theme">
        ${Object.keys(this.themes).map(theme =>
    `<option value="${theme}">${this.formatThemeName(theme)}</option>`,
  ).join('')}
      </select>
    `

    // Add to DOM
    const toolbar = document.querySelector('.toolbar') || document.body
    toolbar.appendChild(container)

    // Set current theme
    const select = document.getElementById('theme-selector')
    select.value = this.currentTheme

    // Add event listener
    select.addEventListener('change', (e) => {
      this.applyTheme(e.target.value)
    })

    // Add styles
    this.addThemeSelectorStyles()
  }

  // Format theme name for display
  formatThemeName(name) {
    return name
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  // Setup system theme listener
  setupSystemThemeListener() {
    if (window.matchMedia) {
      const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

      const handleSystemThemeChange = (e) => {
        // Only auto-apply if user hasn't manually selected a theme
        if (!localStorage.getItem('themePreference')) {
          const theme = e.matches ? 'dark' : 'light'
          this.applyTheme(theme)
        }
      }

      // Initial check
      handleSystemThemeChange({ matches: darkModeMediaQuery.matches })

      // Listen for changes
      darkModeMediaQuery.addListener(handleSystemThemeChange)
    }
  }

  // Save theme preference
  saveThemePreference(themeName) {
    try {
      localStorage.setItem('themePreference', themeName)
    } catch (e) {
      console.warn('Could not save theme preference:', e)
    }
  }

  // Load saved theme preference
  loadThemePreference() {
    try {
      const savedTheme = localStorage.getItem('themePreference')
      if (savedTheme && this.themes[savedTheme]) {
        this.currentTheme = savedTheme
        return savedTheme
      }
      return null
    } catch (e) {
      console.warn('Could not load theme preference:', e)
      return null
    }
  }

  // Apply a theme by name
  applyTheme(themeName) {
    if (!this.themes[themeName]) {
      console.warn(`Theme '${themeName}' not found. Using 'dark'`)
      themeName = 'dark'
    }

    if (this.currentTheme === themeName) return

    this.currentTheme = themeName
    const theme = this.themes[themeName]

    // Apply CSS variables to root element
    const root = document.documentElement
    Object.entries(theme).forEach(([key, value]) => {
      root.style.setProperty(key, value)
    })

    // Update body class
    document.body.classList.remove('light-theme', 'dark-theme', 'cosmic-theme')
    document.body.classList.add(`${themeName}-theme`)
    document.body.setAttribute('data-theme', themeName)

    // Update meta theme color
    document.querySelector('meta[name="theme-color"]')?.setAttribute('content', theme['--bg-color'] || '#0a0a1a')

    // Save preference
    this.saveThemePreference(themeName)

    // Notify listeners
    this.notifyThemeChange(themeName)

    // Dispatch custom event
    document.dispatchEvent(new CustomEvent('themeChange', {
      detail: {
        theme: themeName,
        themeData: theme,
      },
    }))
  }

  // Subscribe to theme changes
  onThemeChange = (callback) => {
    if (typeof callback === 'function') {
      this.themeChangeCallbacks.push(callback)
    }
  }

  // Notify all subscribers of theme change
  notifyThemeChange = (themeName) => {
    this.themeChangeCallbacks.forEach(callback => {
      try {
        callback(themeName)
      } catch (error) {
        console.error('Error in theme change callback:', error)
      }
    })
  }

  // Add styles for theme toggle button
  addThemeToggleStyles() {
    const style = document.createElement('style')
    style.textContent = `
      .theme-toggle {
        background: none;
        border: none;
        font-size: 1.5em;
        cursor: pointer;
        padding: 0.5em;
        margin: 0 0.5em;
        border-radius: 50%;
        width: 2em;
        height: 2em;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s, background-color 0.2s;
        background-color: var(--bg-tertiary);
        color: var(--text-primary);
      }
      
      .theme-toggle:hover {
        transform: scale(1.1);
        background-color: var(--accent-primary);
        color: var(--bg-primary);
      }
      
      .theme-toggle:active {
        transform: scale(0.95);
      }
      
      .theme-toggle:focus {
        outline: 2px solid var(--accent-primary);
        outline-offset: 2px;
      }
    `
    document.head.appendChild(style)
  }

  // Add styles for theme selector
  addThemeSelectorStyles() {
    const style = document.createElement('style')
    style.textContent = `
      .theme-selector {
        margin: 0 1em;
        position: relative;
      }
      
      .theme-selector select {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background-color: var(--bg-tertiary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 0.5em 2em 0.5em 1em;
        font-size: 0.9em;
        cursor: pointer;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23e0e0e0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 0.7em top 50%;
        background-size: 0.65em auto;
      }
      
      .theme-selector select:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 2px rgba(0, 180, 216, 0.2);
      }
      
      .theme-selector select option {
        background-color: var(--bg-secondary);
        color: var(--text-primary);
      }
    `
    document.head.appendChild(style)
  }
}

// Create and export singleton instance
window.ThemeManager = new ThemeManager()

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.ThemeManager.init()

    // Apply the loaded theme after initialization
    const savedTheme = window.ThemeManager.loadThemePreference()
    if (savedTheme) {
      window.ThemeManager.applyTheme(savedTheme)
    } else {
      // Default to dark theme if no preference is set
      window.ThemeManager.applyTheme('dark')
    }
  })
} else {
  window.ThemeManager.init()

  // Apply the loaded theme after initialization
  const savedTheme = window.ThemeManager.loadThemePreference()
  if (savedTheme) {
    window.ThemeManager.applyTheme(savedTheme)
  } else {
    // Default to dark theme if no preference is set
    window.ThemeManager.applyTheme('dark')
  }
}
