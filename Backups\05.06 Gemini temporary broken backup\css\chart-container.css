/* Layout Styles */
body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', 'Roboto', 'Segoe UI', sans-serif;
  font-size: 14px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Chart Container Styles */
.chart-container {
  position: relative;
  flex: 1;
  min-height: 500px;
  width: 100%;
  height: 100%;
  background-color: var(--chart-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin: 1rem 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

#mainChart {
  width: 100%;
  height: 100%;
}

/* Chart Controls */
.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.timeframe-selector {
  display: flex;
  gap: 0.5rem;
}

.timeframe-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.timeframe-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.timeframe-btn.active {
  background-color: var(--accent-primary);
  color: var(--bg-primary);
  border-color: var(--accent-primary);
}

.chart-toolbar {
  display: flex;
  gap: 0.5rem;
}

.chart-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.chart-btn.active {
  background-color: var(--accent-primary);
  color: var(--bg-primary);
  border-color: var(--accent-primary);
}

/* Chart Canvas */
.chart-canvas-container {
  position: relative;
  width: 100%;
  height: calc(100% - 42px); /* Account for controls height */
}

#mainChart {
  width: 100%;
  height: 100%;
}

/* Chart Legend */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  font-size: 0.85rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .chart-container {
    flex: 1;
    min-height: 500px;
    width: 100%;
    height: 60vh;
    position: relative;
    background: #0a0e17;
    border: 1px solid #1a1f2e;
    border-radius: 4px;
    overflow: hidden;
  }
  
  #mainChart {
    width: 100% !important;
    height: 100% !important;
    display: block;
  }
  
  .timeframe-selector {
    overflow-x: auto;
    padding-bottom: 0.5rem;
    -webkit-overflow-scrolling: touch;
  }
  
  .timeframe-btn {
    flex-shrink: 0;
  }
  
  .chart-legend {
    font-size: 0.8rem;
    gap: 0.5rem;
  }
}
