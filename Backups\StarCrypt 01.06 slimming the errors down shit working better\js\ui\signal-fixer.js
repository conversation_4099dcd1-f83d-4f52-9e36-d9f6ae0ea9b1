// signal-fixer.js - Ultimate fix for indicator signal display issues
// This script runs last and fixes all recursion and indicator row problems

// Define required display constants
const INDICATOR_DISPLAY_NAMES = {
  rsi: 'RSI',
  stochRsi: 'STOCH RSI',
  williamsR: 'WILLIAMS %R',
  ultimateOscillator: 'ULT OSC',
  mfi: 'MFI',
  macd: 'MACD',
  bollingerBands: 'BOLL BANDS',
  adx: 'ADX',
  atr: 'ATR',
  vwap: 'VWAP',
  fractal: 'FRACTAL',
  volume: 'VOLUME',
  ml: 'ML',
  sentiment: 'SENTIMENT',
  entropy: 'ENTROPY',
  correlation: 'CORRELATION',
  time_anomaly: 'TIME ANOMALY',
}

// Global flags to prevent recursion
let isUpdatingSignals = false
const isRebuilding = false

// Prevent execution until DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('[SignalFixer] Installing universal signal fix...')

  // Wait to ensure all other scripts have had time to initialize
  setTimeout(installFixes, 1500)
})

// Install all fixes
function installFixes() {
  console.log('[SignalFixer] Installing all fixes...')

  // Fix updateAllSignalLights function
  fixUpdateAllSignalLights()

  // Setup consistent indicator row creation
  setupIndicatorRowCreation()

  // Setup mutation observer to monitor DOM changes
  setupMutationObserver()

  // Schedule initial rebuild
  setTimeout(() => {
    if (typeof window.rebuildAllIndicatorRows === 'function') {
      window.rebuildAllIndicatorRows()
    }
  }, 1000)

  console.log('[SignalFixer] All fixes applied successfully')
}

// Fix updateAllSignalLights function
function fixUpdateAllSignalLights() {
  console.log('[SignalFixer] Fixing updateAllSignalLights function...')

  // Completely override the problematic updateAllSignalLights function
  window.updateAllSignalLights = function () {
    // Check if we're already in an update cycle to prevent recursion
    if (isUpdatingSignals) {
      console.log('[SignalFixer] Prevented recursive call to updateAllSignalLights')
      return
    }

    try {
      isUpdatingSignals = true
      console.log('[SignalFixer] Safely updating signal lights...')

      // Get all signal circles
      const circles = document.querySelectorAll('.signal-circle')
      if (!circles || circles.length === 0) {
        console.log('[SignalFixer] No signal circles found to update')
        isUpdatingSignals = false
        return
      }

      console.log(`[SignalFixer] Found ${circles.length} signal circles to update`)

      // Update each signal circle based on available data
      let updatedCount = 0

      circles.forEach(circle => {
        try {
          // Get indicator and timeframe from data attributes
          const indicator = circle.getAttribute('data-ind') || circle.getAttribute('data-indicator')
          const timeframe = circle.getAttribute('data-tf') || circle.getAttribute('data-timeframe')

          if (!indicator || !timeframe) {
            console.log('[SignalFixer] Skipping circle with missing data attributes')
            return
          }

          // Get indicator data if available
          const hasData = window.indicatorsData &&
                         window.indicatorsData[timeframe] &&
                         window.indicatorsData[timeframe][indicator]

          // Remove all signal classes first
          circle.className = 'signal-circle'

          // Apply appropriate class based on data
          if (hasData) {
            const data = window.indicatorsData[timeframe][indicator]
            const color = data.color || '#808080'

            if (color === '#00FF00') {
              circle.classList.add('green-light')
              circle.classList.add('degen-buy')
            } else if (color === '#0000FF') {
              circle.classList.add('blue-light')
              circle.classList.add('mild-buy')
            } else if (color === '#FF0000') {
              circle.classList.add('red-light')
              circle.classList.add('degen-sell')
            } else if (color === '#FFA500') {
              circle.classList.add('orange-light')
              circle.classList.add('mild-sell')
            } else {
              circle.classList.add('grey-light')
              circle.classList.add('neutral')
            }

            // Set tooltip with value if available
            if (data.value !== undefined) {
              circle.setAttribute('data-tooltip', `${indicator.toUpperCase()} (${timeframe}): ${data.value}`)
            }

            updatedCount++
          } else {
            // No data, set to neutral/waiting state
            circle.classList.add('grey-light')
            circle.classList.add('neutral')
            circle.setAttribute('data-tooltip', `${indicator.toUpperCase()} (${timeframe}): No data`)
          }

          // Ensure timeframe text is visible
          if (!circle.innerText || circle.innerText.trim() === '') {
            circle.innerText = timeframe
          }
        } catch (err) {
          console.error('[SignalFixer] Error updating circle:', err)
        }
      })

      console.log(`[SignalFixer] Successfully updated ${updatedCount} signal circles`)
    } catch (err) {
      console.error('[SignalFixer] Error in updateAllSignalLights:', err)
    } finally {
      // Always clear the updating flag when done
      isUpdatingSignals = false
    }
  }
}

// Setup consistent indicator row creation
function setupIndicatorRowCreation() {
  console.log('[SignalFixer] Setting up consistent indicator row creation...')

  // Create a consistent indicator row function
  window.createConsistentIndicatorRow = function (indicator) {
    try {
      if (!indicator) {
        console.error('[SignalFixer] Cannot create row for undefined indicator')
        return document.createElement('tr')
      }

      console.log(`[SignalFixer] Creating consistent row for ${indicator}`)

      // Create row with proper data attributes
      const row = document.createElement('tr')
      row.className = 'indicator-row signal-row'
      row.setAttribute('data-indicator', indicator)
      row.setAttribute('data-ind', indicator)
      row.id = `indicator-row-${indicator}`

      // Create name cell
      const nameCell = document.createElement('td')
      nameCell.className = 'signal-name indicator-name'
      nameCell.setAttribute('data-indicator', indicator)

      // Use proper display name
      const displayName = INDICATOR_DISPLAY_NAMES[indicator] || indicator.toUpperCase()
      nameCell.textContent = displayName

      row.appendChild(nameCell)

      // Create mini-chart cell if needed
      const chartCell = document.createElement('td')
      chartCell.className = 'mini-chart-cell'
      const chartContainer = document.createElement('div')
      chartContainer.className = 'mini-chart-container'
      chartContainer.id = `${indicator}-chart-container`
      chartCell.appendChild(chartContainer)
      row.appendChild(chartCell)

      // Create signal cells for each timeframe
      if (window.TIMEFRAMES && Array.isArray(window.TIMEFRAMES)) {
        window.TIMEFRAMES.forEach(tf => {
          const signalCell = document.createElement('td')
          signalCell.className = 'signal-light-cell'
          signalCell.setAttribute('data-timeframe', tf)

          // Create signal circle
          const signalCircle = document.createElement('div')
          signalCircle.className = 'signal-circle grey-light'
          signalCircle.id = `${indicator}-${tf}-signal`
          signalCircle.setAttribute('data-ind', indicator)
          signalCircle.setAttribute('data-tf', tf)
          signalCircle.setAttribute('data-indicator', indicator)
          signalCircle.setAttribute('data-timeframe', tf)
          signalCircle.setAttribute('data-tooltip', `${displayName} (${tf}): No data`)
          signalCircle.innerText = tf

          signalCell.appendChild(signalCircle)
          row.appendChild(signalCell)
        })
      }

      return row
    } catch (err) {
      console.error(`[SignalFixer] Error creating row for ${indicator}:`, err)
      return document.createElement('tr') // Return empty row to prevent errors
    }
  }

  // Override createIndicatorRow if it exists
  if (typeof window.createIndicatorRow !== 'undefined') {
    console.log('[SignalFixer] Overriding existing createIndicatorRow function')
    window.createIndicatorRow = window.createConsistentIndicatorRow
  }
}

// Setup mutation observer to monitor DOM changes
function setupMutationObserver() {
  console.log('[SignalFixer] Setting up mutation observer...')

  const observer = new MutationObserver(mutations => {
    let needsRebuild = false

    for (const mutation of mutations) {
      if (mutation.type === 'childList' &&
          (mutation.target.classList.contains('oracle-matrix') ||
           mutation.target.classList.contains('momentum-table'))) {
        needsRebuild = true
        break
      }
    }

    if (needsRebuild && !isRebuilding) {
      console.log('[SignalFixer] Detected changes to indicator table, scheduling rebuild...')
      setTimeout(() => {
        if (typeof window.rebuildAllIndicatorRows === 'function') {
          window.rebuildAllIndicatorRows()
        }
      }, 100)
    }
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })
}

console.log('[SignalFixer] Script loaded successfully')
