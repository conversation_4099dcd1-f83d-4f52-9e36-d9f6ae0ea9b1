/**
 * Signal Manager
 * Centralized signal update and WebSocket handling
 */

class SignalManager {
  constructor() {
    this.isUpdating = false
    this.updateQueue = []
    this.errorCount = 0
    this.maxErrors = 10
    this.maxQueueSize = 100
    this.throttleTimeout = 100
    this.lastUpdate = 0

    // Initialize WebSocket handlers
    this.initializeWebSocketHandlers()
  }

  initializeWebSocketHandlers() {
    // Create WebSocket connection
    this.connectionId = window.WebSocketService.createConnection('ws://localhost:8080')

    // Register handlers with WebSocket service using connection ID
    window.WebSocketService.addHandler(this.connectionId, 'open', () => {
      console.log('[SignalManager] WebSocket connected')
      this.sendSubscription()
    })

    window.WebSocketService.addHandler(this.connectionId, 'close', () => {
      console.log('[SignalManager] WebSocket disconnected')
      this.clearPendingUpdates()
    })

    window.WebSocketService.addHandler(this.connectionId, 'error', (error) => {
      console.error('[SignalManager] WebSocket error:', error)
      this.handleError(error)
    })

    window.WebSocketService.addHandler(this.connectionId, 'message', (message) => {
      this.queueUpdate(JSON.parse(message.data))
    })
  }

  sendSubscription() {
    const message = {
      type: 'subscribe',
      data: {
        pairs: ['xbtusdt'],
        timeframes: window.TIMEFRAMES,
        indicators: Object.keys(window.MATRIX_INDICATORS),
      },
    }

    window.WebSocketService.sendMessage(message)
  }

  queueUpdate(message) {
    if (this.updateQueue.length >= this.maxQueueSize) {
      console.warn('[SignalManager] Update queue full, dropping message')
      return
    }

    this.updateQueue.push(message)

    // Process queue if not already processing
    if (!this.isUpdating) {
      this.processQueue()
    }
  }

  async processQueue() {
    if (this.isUpdating) return

    this.isUpdating = true
    try {
      while (this.updateQueue.length > 0) {
        const message = this.updateQueue.shift()
        await this.processMessage(message)

        // Add small delay between updates
        await new Promise(resolve => setTimeout(resolve, this.throttleTimeout))
      }
    } catch (error) {
      console.error('[SignalManager] Queue processing error:', error)
      this.handleError(error)
    } finally {
      this.isUpdating = false
    }
  }

  async processMessage(message) {
    try {
      // Break call stack
      await new Promise(resolve => setTimeout(resolve, 0))

      // Process message with error recovery
      await this.updateSignalLights(message)

      // Add small delay between messages
      await new Promise(resolve => setTimeout(resolve, this.throttleTimeout))
    } catch (error) {
      console.error('[SignalManager] Message processing error:', error)
      throw error
    }
  }

  async updateSignalLights(message) {
    if (this.errorCount >= this.maxErrors) {
      console.error('[SignalManager] Too many errors, disabling updates')
      return
    }

    try {
      // Break call stack
      await new Promise(resolve => setTimeout(resolve, 0))

      // Get all indicators for current strategy
      const indicators = window.INDICATORS[window.currentStrategy] || []

      // Update each indicator for each timeframe
      for (const indicator of indicators) {
        // Skip momentum indicators to preserve Oracle matrix
        if (window.MATRIX_INDICATORS.includes(indicator)) {
          continue
        }

        for (const timeframe of window.TIMEFRAMES) {
          try {
            await this.updateSignalLight(indicator, timeframe, message)

            // Add small delay between updates
            await new Promise(resolve => setTimeout(resolve, this.throttleTimeout))
          } catch (updateError) {
            console.error(`[SignalManager] Error updating ${indicator} ${timeframe}:`, updateError)
            // Don't throw here to prevent stopping other updates
          }
        }
      }
    } catch (error) {
      console.error('[SignalManager] Signal lights update error:', error)
      this.handleError(error)
    }
  }

  async updateSignalLight(indicator, timeframe, data) {
    try {
      // Break call stack
      await new Promise(resolve => setTimeout(resolve, 0))

      // Get the signal circle element
      const signalCircle = document.querySelector(`.signal-circle[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`)
      if (!signalCircle) {
        console.warn(`[SignalManager] Signal circle not found for ${indicator} ${timeframe}`)
        return
      }

      // Get signal calculator instance
      const calculator = SignalCalculator.getInstance()

      // Calculate signal with recursion prevention
      const signal = await calculator.calculateSignal(data, indicator, timeframe)

      // Apply signal with error recovery
      try {
        this.applySignal(signalCircle, signal)
        this.updateRelatedUI(indicator, timeframe, signal)
      } catch (applyError) {
        console.error(`[SignalManager] Error applying signal:`, applyError)
        throw applyError
      }
    } catch (error) {
      console.error(`[SignalManager] Signal update error for ${indicator} ${timeframe}:`, error)
      throw error
    }
  }

  applySignal(signalCircle, signal) {
    // Apply signal to the UI element
    signalCircle.style.backgroundColor = signal.color
    signalCircle.style.opacity = signal.intensity

    // Update any tooltips or labels
    this.updateSignalTooltip(signalCircle, signal)
  }

  updateSignalTooltip(signalCircle, signal) {
    const tooltip = signalCircle.querySelector('.signal-tooltip')
    if (tooltip) {
      tooltip.textContent = `Value: ${signal.value}\nIntensity: ${signal.intensity}`
    }
  }

  updateRelatedUI(indicator, timeframe, signal) {
    // Update any related UI elements (charts, tables, etc.)
    const chart = document.querySelector(`.mini-chart[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`)
    if (chart) {
      this.updateChart(chart, signal)
    }
  }

  updateChart(chart, signal) {
    // Update chart data and appearance
    const ctx = chart.getContext('2d')
    const chartInstance = Chart.getChart(ctx)
    if (chartInstance) {
      chartInstance.data.datasets[0].data = signal.data
      chartInstance.update()
    }
  }

  handleError(error) {
    this.errorCount++
    if (this.errorCount > this.maxErrors) {
      console.error('[SignalManager] Too many errors, disabling updates')
      this.disableUpdates()
    }
  }

  clearPendingUpdates() {
    this.updateQueue = []
    this.isUpdating = false
    this.errorCount = 0
  }

  disableUpdates() {
    this.clearPendingUpdates()
    this.isUpdating = true // Prevent further updates
  }

  static getInstance() {
    if (!window.signalManager) {
      window.signalManager = new SignalManager()
    }
    return window.signalManager
  }
}

// Initialize SignalManager when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    SignalManager.getInstance()
  })
} else {
  SignalManager.getInstance()
}

// Export for global access
window.SignalManager = SignalManager
