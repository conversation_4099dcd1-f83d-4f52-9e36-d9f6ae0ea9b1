if (window.hasInitializedSignalLightsFile) {
    console.warn('[signal-lights.js] Already initialized or intentionally skipped by Cascade to prevent conflicts.');
    // return; // Intentionally not returning to allow other utility functions if needed, but global assignments will be stopped.
}
window.hasInitializedSignalLightsFile = true;

/**
 * Signal Lights - UI Component for Displaying Signal Indicators
 * Integrates with SignalManager and SignalInitializer to provide visual feedback for trading signals
 */
function handleMouseOver(event) {
  // Placeholder for future mouseover logic
  // console.log('Mouse over signal light:', event.target);
}

function handleMouseOut(event) {
    // Placeholder: Implement mouseout logic for signal lights if needed
    // Example: Hide tooltip or remove highlight
    // console.log('Mouse out signal:', event.target);
}

function handleClick(event) {
    // Placeholder: Implement click logic if needed for document-level clicks
    // console.log('Document clicked:', event.target);
    // Check if the click was on a signal circle and delegate if necessary
    const signalCircle = event.target.closest('.signal-circle');
    if (signalCircle) {
        const indicator = signalCircle.dataset.ind || signalCircle.dataset.indicator;
        const timeframe = signalCircle.dataset.tf || signalCircle.dataset.timeframe;
        if (indicator && timeframe && typeof window.handleSignalClick === 'function') {
            // console.log(`Delegating click on signal ${indicator}-${timeframe} to window.handleSignalClick`);
            // window.handleSignalClick(indicator, timeframe); // Call specific handler if needed, but individual circles might already do this.
        }
    }
}

(function() {
'use strict';

// Global state
const state = {
    signalLightsInitialized: false,
    existingTooltips: new Map()
};

// Define all signal indicators we expect to see
const SIGNAL_INDICATORS = [
  // Momentum Indicators
  'rsi', 'macd', 'cci', 'mfi', 'roc', 'williams', 'mom', 'ppo', 'apz',
  
  // Trend Indicators
  'ema', 'sma', 'vwap', 'alma', 'tema', 'dema', 'kama', 't3', 'zlema', 'hull',
  'supertrend', 'psar', 'keltner', 'donchian', 'vwma', 'trix', 'qstick', 'kst',
  
  // Volatility Indicators
  'bb', 'atr', 'kc', 'bbw', 'rvi', 'ui', 'efi', 'mass', 'thermo', 'chop',
  
  // Volume Indicators
  'obv', 'volume', 'vwap', 'cmf', 'ad', 'adosc', 'nvi', 'pvi', 'pvo', 'mfi',
  
  // Support/Resistance & Patterns
  'pivot', 'fib', 'sr', 'pivotr', 'pivotf', 'pivotc', 'pivotw', 'pivotm',
  
  // Advanced Indicators
  'ichimoku', 'hull', 'keltner', 'donchian', 'supertrend', 'vwma', 'alma',
  'keltneratr', 'keltnerrma', 'keltrenv', 'keltatr', 'keltatr2', 'keltatr3',
  
  // Experimental Indicators
  'squeeze', 'ttm', 'vp', 'vwapb', 'vwapw', 'vwapm', 'vwapq', 'vwapy', 'vwapd',
  'vwapde', 'vwapdb', 'vwapdw', 'vwapdm', 'vwapdq', 'vwapdy', 'vwapdd', 'vwapde',
  'vwapdf', 'vwapdg', 'vwapdh', 'vwapdi', 'vwapdj', 'vwapdk', 'vwapdl', 'vwapdm',
  'vwapdn', 'vwapdo', 'vwapdp', 'vwapdq', 'vwapdr', 'vwapds', 'vwapdt', 'vwapdu',
  'vwapdv', 'vwapdw', 'vwapdx', 'vwapdy', 'vwapdz'
];

// Define all timeframes we support
const SIGNAL_TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];

/**
 * Get the full display name for an indicator
 * @param {string} indicator - The indicator code
 * @returns {string} The full display name
 */
function getIndicatorFullName(indicator) {
    const names = {
        // Momentum Indicators
        'rsi': 'Relative Strength Index',
        'macd': 'Moving Average Convergence Divergence',
        'stoch': 'Stochastic Oscillator',
        'cci': 'Commodity Channel Index',
        'mfi': 'Money Flow Index',
        'roc': 'Rate of Change',
        'williams': 'Williams %R',
        'mom': 'Momentum',
        'ppo': 'Percentage Price Oscillator',
        'apz': 'Adaptive Price Zone',
        
        // Trend Indicators
        'ema': 'Exponential Moving Average',
        'sma': 'Simple Moving Average',
        'vwap': 'Volume Weighted Average Price',
        'alma': 'Arnaud Legoux Moving Average',
        'tema': 'Triple Exponential Moving Average',
        'dema': 'Double Exponential Moving Average',
        'kama': 'Kaufman Adaptive Moving Average',
        't3': 'Triple Exponential Moving Average T3',
        'zlema': 'Zero Lag Exponential Moving Average',
        'hull': 'Hull Moving Average',
        'supertrend': 'SuperTrend',
        'psar': 'Parabolic SAR',
        'keltner': 'Keltner Channel',
        'donchian': 'Donchian Channel',
        'vwma': 'Volume Weighted Moving Average',
        'trix': 'Triple Exponential Average',
        'qstick': 'QStick',
        'kst': 'Know Sure Thing',
        
        // Volatility Indicators
        'bb': 'Bollinger Bands',
        'atr': 'Average True Range',
        'kc': 'Keltner Channel',
        'bbw': 'Bollinger Band Width',
        'rvi': 'Relative Volatility Index',
        'ui': 'Ulcer Index',
        'efi': 'Elder Force Index',
        'mass': 'Mass Index',
        'thermo': 'Thermo',
        'chop': 'Choppiness Index',
        
        // Volume Indicators
        'obv': 'On Balance Volume',
        'volume': 'Volume',
        'cmf': 'Chaikin Money Flow',
        'ad': 'Accumulation/Distribution',
        'adosc': 'Accumulation/Distribution Oscillator',
        'nvi': 'Negative Volume Index',
        'pvi': 'Positive Volume Index',
        'pvo': 'Percentage Volume Oscillator',
        
        // Support/Resistance & Patterns
        'pivot': 'Pivot Points',
        'fib': 'Fibonacci Retracement',
        'sr': 'Support & Resistance',
        'pivotr': 'Pivot Range',
        'pivotf': 'Pivot Fibonacci',
        'pivotc': 'Pivot Camarilla',
        'pivotw': 'Pivot Woodie',
        'pivotm': 'Pivot Monthly',
        
        // Advanced Indicators
        'ichimoku': 'Ichimoku Cloud',
        'keltneratr': 'Keltner Channel (ATR)',
        'keltnerrma': 'Keltner Channel (RMA)',
        'keltrenv': 'Keltner Envelope',
        'keltatr': 'Keltner ATR',
        'keltatr2': 'Keltner ATR 2',
        'keltatr3': 'Keltner ATR 3',
        
        // Experimental Indicators
        'squeeze': 'TTM Squeeze',
        'ttm': 'Trend Trader Momentum',
        'vp': 'Volume Profile',
        'vwapb': 'VWAP Bands',
        'vwapw': 'VWAP Weekly',
        'vwapm': 'VWAP Monthly',
        'vwapq': 'VWAP Quarterly',
        'vwapy': 'VWAP Yearly',
        'vwapd': 'VWAP Daily',
        'vwapde': 'VWAP Daily Extended'
    };
    
    // For any indicator not in the map, format it nicely
    const indicatorLower = indicator.toLowerCase();
    if (!names[indicatorLower]) {
        // Convert 'someIndicator' to 'Some Indicator'
        return indicator
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .trim();
    }
    
    return names[indicatorLower];
}

/**
 * Create a signal row for an indicator
 * @param {HTMLElement} grid - The grid container element
 * @param {string} indicator - The indicator name
 * @returns {HTMLElement} The created row element
 */
function createSignalRow(grid, indicator) {
    const row = document.createElement('div');
    row.className = 'signal-row';
    row.dataset.indicator = indicator.toLowerCase();
    
    // Add indicator label cell
    const labelCell = document.createElement('div');
    labelCell.className = 'indicator-cell';
    labelCell.textContent = indicator.toUpperCase();
    labelCell.title = getIndicatorFullName(indicator);
    row.appendChild(labelCell);
    
    // Add signal cells for each timeframe
    SIGNAL_TIMEFRAMES.forEach(timeframe => {
        const cell = document.createElement('div');
        cell.className = 'signal-cell';
        cell.dataset.timeframe = timeframe;
        row.appendChild(cell);
    });
    
    // Add the row to the grid
    grid.appendChild(row);
    return row;
}

function checkSignalElements() {
    const signals = document.querySelectorAll('.signal-circle');
    console.log(`Found ${signals.length} signal elements`);
    signals.forEach((signal, index) => {
        console.log(`Signal ${index + 1}:`, {
            id: signal.id,
            indicator: signal.dataset.indicator || signal.dataset.ind,
            timeframe: signal.dataset.timeframe || signal.dataset.tf,
            classes: signal.className
        });
    });
}
// Initialize signal lights system
function initializeSignalLightsSystem() {
    console.log('[SignalLights] Initializing signal lights system');
    
    // Use requestAnimationFrame to avoid layout thrashing
    requestAnimationFrame(() => {
        try {
            // Use existing momentum-indicators container
            const container = document.getElementById('momentum-indicators');
            if (!container) {
                console.error('[SignalLights] momentum-indicators container not found');
                return;
            }
            
            // Ensure we have a table for signals
            let table = container.querySelector('table#momentum-table');
            if (!table) {
                table = document.createElement('table');
                table.id = 'momentum-table';
                table.className = 'signal-matrix';
                container.appendChild(table);
            }
            
            // Clear existing content
            while (table.firstChild) {
                table.removeChild(table.firstChild);
            }
            
            // Create table body for signal rows (no thead)
            const tbody = document.createElement('tbody');
            table.appendChild(tbody);
            
            // Add table to container if it's new
            if (!container.contains(table)) {
                container.appendChild(table);
            }
            
            // Define all indicators to be displayed (excluding 'stoch' as per user request)
            const INDICATORS = [
                // Momentum Indicators
                'rsi', 'macd', 'cci', 'mfi', 'roc', 'williams', 'mom', 'ppo', 'apz',
                
                // Trend Indicators
                'ema', 'sma', 'vwap', 'alma', 'tema', 'dema', 'kama', 't3', 'zlema', 'hull',
                'supertrend', 'psar', 'keltner', 'donchian', 'vwma', 'trix', 'qstick', 'kst',
                
                // Volatility Indicators
                'bb', 'atr', 'kc', 'bbw', 'rvi', 'ui', 'efi', 'mass', 'thermo', 'chop',
                
                // Volume Indicators
                'obv', 'volume', 'cmf', 'ad', 'adosc', 'nvi', 'pvi', 'pvo',
                
                // Support/Resistance & Patterns
                'pivot', 'fib', 'sr', 'pivotr', 'pivotf', 'pivotc', 'pivotw', 'pivotm',
                
                // Advanced Indicators
                'ichimoku', 'keltneratr', 'keltnerrma', 'keltrenv', 'keltatr', 'keltatr2', 'keltatr3',
                
                // Experimental Indicators
                'squeeze', 'ttm', 'vp', 'vwapb', 'vwapw', 'vwapm', 'vwapq', 'vwapy', 'vwapd', 'vwapde'
            ];
            
            // Create signal elements for all timeframes
            const TIMEFRAMES = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M'];
            
            INDICATORS.forEach(indicator => {
                TIMEFRAMES.forEach(timeframe => {
                    const elementId = `signal-${indicator}-${timeframe}`;
                    if (!document.getElementById(elementId)) {
                        if (window.DEBUG_SIGNALS) {
                            console.log(`[SignalLights] Creating signal element for ${indicator}-${timeframe}...`);
                        }
                        createSignalElement(indicator, timeframe);
                    }
                });
            });
            
            // Initialize UI components
            initSignalLights();
            
            // Set up signal update listener
            document.addEventListener('signalUpdate', handleSignalUpdate);
            
            // Set up signal system ready listener
            document.addEventListener('signalSystem:ready', (event) => {
                console.log('[SignalLights] SignalSystem ready');
                // Trigger initial update of all signal lights
                if (window.signalSystem) {
                    setTimeout(() => {
                        console.log('[SignalLights] Triggering initial signal light update...');
                        window.signalSystem.updateAllSignalLights(true);
                    }, 1000);
                }
            });
            
            // Fallback initialization in case SignalManager isn't immediately available
            let attempts = 0;
            const maxAttempts = 10;
            
            const checkSignalManager = setInterval(() => {
                if (window.SignalManager) {
                    clearInterval(checkSignalManager);
                    console.log('[SignalLights] SignalManager found after', attempts * 100, 'ms');
                    initSignalLights();
                } else if (attempts >= maxAttempts) {
                    clearInterval(checkSignalManager);
                    console.warn('[SignalLights] SignalManager not found after max attempts, using fallback');
                    
                    // Create a minimal SignalManager fallback
                    window.SignalManager = {
                        updateSignal: (ind, tf, data) => {
                            if (!ind || !tf || !data) return;
                            
                            const key = `${ind}_${tf}`;
                            if (window.DEBUG_SIGNALS) {
                                console.log(`[SignalLights] Fallback update: ${key}`, data);
                            }
                            
                            // Use the signal system if available
                            if (window.signalSystem) {
                                window.signalSystem.updateSignal(ind, tf, data);
                                return;
                            }
                            
                            // Fallback to direct DOM update
                            const element = document.querySelector(`[data-indicator="${ind}"][data-timeframe="${tf}"]`) || 
                                            document.querySelector(`#signal-${ind}-${tf}`);
                            
                            if (element) {
                                updateSignalElement(element, ind, tf, data);
                            } else if (window.DEBUG_SIGNALS) {
                                console.warn(`[SignalLights] Signal element not found: ${ind}-${tf}`);
                            }
                        }
                    };
                }
                attempts++;
            }, 100);
            
            // Log current signal elements for debugging
            if (window.DEBUG_SIGNALS) {
                checkSignalElements();
            }
        } catch (error) {
            console.error('[SignalLights] Error initializing signal lights:', error);
        }
    });
}

// Track if we've initialized
let signalLightsInitialized = false;

/**
 * Initialize signal lights when everything is ready
 */
function initializeWhenReady() {
    try {
        // Check if we've already initialized
        if (signalLightsInitialized) {
            console.log('[SignalLights] Already initialized');
            return;
        }
        
        // Check if TradingView is ready
        const tradingViewReady = window.tvWidget && 
            typeof window.tvWidget.chart === 'function';

        if (!tradingViewReady) {
            // Try again after a short delay
            console.log('[SignalLights] Waiting for TradingView...');
            setTimeout(initializeWhenReady, 500);
            return;
        }

        // Track initialization state
        const initState = {
            isInitialized: false,
            attempts: 0,
            MAX_ATTEMPTS: 5
        };
        
        // Mark as initialized
        signalLightsInitialized = true;
        
        // Set up event listeners first
        setupSignalEventListeners();
        
        // Initialize the signal lights system
        initializeSignalLightsSystem();
        
        // Set initial active timeframe (default to 1h)
        // Get timeframe from URL hash if available
        let initialTimeframe = '1h';
        try {
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            const tfFromUrl = hashParams.get('timeframe');
            if (tfFromUrl) {
                initialTimeframe = tfFromUrl;
                console.log(`[SignalLights] Using timeframe from URL: ${initialTimeframe}`);
            }
        } catch (e) {
            console.warn('[SignalLights] Could not parse URL hash:', e);
        }
        
        // Small delay to ensure DOM is fully ready
        setTimeout(() => {
            updateActiveTimeframe(initialTimeframe);
            
            // Listen for URL hash changes to update timeframe
            window.addEventListener('popstate', handlePopState);
            
            console.log('[SignalLights] Initialization complete');
        }, 300);
        
        console.log('[SignalLights] Signal lights initialized successfully');
    } catch (error) {
        console.error('[SignalLights] Error during initialization:', error);
        signalLightsInitialized = false;
        // Try to recover after a delay
        setTimeout(initializeWhenReady, 1000);
    }
}

/**
 * Handle browser back/forward navigation
 */
function handlePopState() {
    try {
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const tfFromUrl = hashParams.get('timeframe');
        if (tfFromUrl) {
            console.log(`[SignalLights] Timeframe changed via URL: ${tfFromUrl}`);
            updateActiveTimeframe(tfFromUrl);
        }
    } catch (e) {
        console.warn('[SignalLights] Error handling popstate:', e);
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('[SignalLights] DOM Content Loaded');
        // Wait a bit to ensure momentum-indicators container is available
        setTimeout(initializeWhenReady, 100);
    });
} else {
    console.log('[SignalLights] DOM already loaded');
    // Small delay to ensure container is available
    setTimeout(initializeWhenReady, 100);
}

// Also handle browser back/forward navigation
window.addEventListener('popstate', handlePopState);

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initSignalLights,
        updateSignalLight,
        getIndicatorFullName,
        getIndicatorDescription
    };
}

window.StarCrypt = window.StarCrypt || {};
window.StarCrypt.SignalLights = {
    initialize: initializeWhenReady,
    updateActiveTimeframe
};

/**
 * Get signal class based on signal type and strength
 */
function getSignalClass(signal, strength = 0.5) {
    const baseSignal = (signal || '').toLowerCase();
    
    if (baseSignal.includes('buy')) {
        return strength > 0.6 ? 'strong-buy' : 'mild-buy';
    } else if (baseSignal.includes('sell')) {
        return strength > 0.6 ? 'strong-sell' : 'mild-sell';
    } else if (baseSignal.includes('neutral')) {
        return 'neutral';
    }

    return 'neutral';
}

/**
 * Initialize signal lights with requestAnimationFrame for better performance
 */
function initializeSignalLights() {
    if (window.signalLightsInitialized) {
        if (window.DEBUG_SIGNALS) {
            console.log('[SignalLights] Already initialized');
        }
        return;
    }

    // Use requestAnimationFrame to avoid layout thrashing
    requestAnimationFrame(() => {
        try {
            initializeSignalLightsInternal();
            window.signalLightsInitialized = true;

            if (window.DEBUG_SIGNALS) {
                console.log('[SignalLights] Initialization complete');
            }
        } catch (error) {
            console.error('[SignalLights] Error initializing signal lights:', error);
            // Reset initialization flag on error to allow retries
            window.signalLightsInitialized = false;
        }
    });
}

function initializeSignalLightsInternal() {
    const startTime = performance.now();
    console.log('[SignalLights] Initializing signal lights...');

    // Get or create signal grid container
    const container = document.getElementById('momentum-indicators');
    if (!container) {
        console.error('[SignalLights] momentum-indicators container not found');
        return;
    }

    // Define available indicators and timeframes
    const indicators = [
        'RSI', 'MACD', 'BB', 'Stoch', 'ADX', 
        'Ichimoku', 'ATR', 'OBV', 'VWAP', 'SMA',
        'EMA', 'Bollinger', 'Keltner', 'Donchian', 'SuperTrend',
        'Pivot', 'Volume', 'CCI', 'MFI', 'ROC', 'Williams', 'Momentum', 'PPO', 'APZ',
        'TEMA', 'DEMA', 'KAMA', 'T3', 'ZLEMA', 'Hull', 'PSAR', 'TRIX', 'QStick', 'KST',
        'KC', 'BBW', 'RVI', 'UI', 'EFI', 'MASS', 'Thermo', 'Chop', 'CMF', 'AD', 'ADOSC',
        'NVI', 'PVI', 'PVO', 'PivotR', 'PivotF', 'PivotC', 'PivotW', 'PivotM', 'Squeeze',
        'TTM', 'VP', 'VWAPB', 'VWAPW', 'VWAPM', 'VWAPQ', 'VWAPY', 'VWAPD', 'VWAPDE'
    ];

    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
    
    // Batch DOM updates
    const fragment = document.createDocumentFragment();
    
    // Ensure we have a table for signals
    let table = container.querySelector('table#momentum-table');
    if (!table) {
        table = document.createElement('table');
        table.id = 'momentum-table';
        table.className = 'signal-matrix';
        container.appendChild(table);
    }

    // Get or create table body
    const tbody = table.querySelector('tbody#signal-tbody') || (() => {
        const newTbody = document.createElement('tbody');
        newTbody.id = 'signal-tbody';
        table.appendChild(newTbody);
        return newTbody;
    })();

    // Clear existing rows while preserving the table structure
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }

    // Create header row with timeframes
    const headerRow = document.createElement('tr');
    const indicatorHeader = document.createElement('th');
    indicatorHeader.textContent = 'Indicator';
    indicatorHeader.className = 'indicator-header';
    headerRow.appendChild(indicatorHeader);

    // Add timeframe headers
    timeframes.forEach(tf => {
        const th = document.createElement('th');
        th.textContent = tf;
        th.className = 'timeframe-header';
        headerRow.appendChild(th);
    });

    tbody.appendChild(headerRow);

    // Create rows for each indicator
    indicators.forEach(indicator => {
        const row = document.createElement('tr');
        row.className = 'signal-row';
        
        // Add indicator name cell
        const indicatorCell = document.createElement('td');
        indicatorCell.className = 'indicator-name';
        indicatorCell.textContent = getIndicatorFullName(indicator);
        row.appendChild(indicatorCell);
        
        // Add signal cells for each timeframe
        timeframes.forEach(timeframe => {
            const cell = document.createElement('td');
            cell.className = 'signal-cell';
            
            const signalDiv = document.createElement('div');
            signalDiv.className = 'signal';
            signalDiv.id = `signal-${indicator.toLowerCase()}-${timeframe}`;
            signalDiv.setAttribute('data-indicator', indicator.toLowerCase());
            signalDiv.setAttribute('data-timeframe', timeframe);
            
            const circle = document.createElement('div');
            circle.className = 'signal-circle';
            circle.setAttribute('data-tooltip', `${indicator} (${timeframe}) - Loading...`);
            
            signalDiv.appendChild(circle);
            cell.appendChild(signalDiv);
            row.appendChild(cell);
        });
        
        tbody.appendChild(row);
    });
    
    // Initialize tooltips
    initializeTooltips();
    
    console.log(`[SignalLights] Initialized ${indicators.length} indicators with ${timeframes.length} timeframes each`);
    
    // Mark initialization as complete
    signalLightsInitialized = true;
    
    // Trigger initial update
    updateSignalLightsNonRecursive();
    return true;
}

function initializeSignalLightEvents() {
    // Add event listeners when DOM is ready
    const init = () => {
        document.addEventListener('mouseover', handleMouseOver);
        document.addEventListener('mouseout', handleMouseOut);
        document.addEventListener('click', handleClick);
    };

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
}

/**
 * Clean up signal light event listeners
 */
function cleanupSignalLights() {
    document.removeEventListener('mouseover', handleMouseOver);
    document.removeEventListener('mouseout', handleMouseOut);
    document.removeEventListener('click', handleClick);
    
    // Clean up tooltips if they exist
    if (window.signalExistingTooltips && typeof window.signalExistingTooltips.clear === 'function') {
        window.signalExistingTooltips.clear();
    }
    
    // Clean up signalTooltips if it exists
    if (window.signalTooltips && typeof window.signalTooltips.clear === 'function') {
        window.signalTooltips.clear();
    }
}

// Initialize signal light events when this module loads
initializeSignalLightEvents();

// Initialize signal lights when the DOM is ready
const init = function() {
    // Add styles for signal lights if they don't exist
    if (!document.getElementById('signal-lights-styles')) {
        const style = document.createElement('style');
        style.id = 'signal-lights-styles';
        style.textContent = `
            /* Signal Lights Animations */
            @keyframes pulse-buy {
                0% { box-shadow: 0 0 0 0 rgba(0, 200, 83, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(0, 200, 83, 0); }
                100% { box-shadow: 0 0 0 0 rgba(0, 200, 83, 0); }
            }
            
            @keyframes pulse-sell {
                0% { box-shadow: 0 0 0 0 rgba(255, 82, 82, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(255, 82, 82, 0); }
                100% { box-shadow: 0 0 0 0 rgba(255, 82, 82, 0); }
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* Signal Lights Base Styles */
            #momentum-indicators {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 20px auto;
                max-width: 1200px;
        document.head.appendChild(style);
    }
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSignalLights);
    } else {
        // DOM already loaded, initialize immediately
        initSignalLights();
    }
};

// Export public API
const SignalLights = {
    init: init,
    updateSignal: updateSignalElement,
    updateActiveTimeframe: updateActiveTimeframe
};

// Expose to window
window.SignalLights = SignalLights;

// Auto-initialize
init();
            
            .signal-matrix th,
            .signal-matrix td {
                padding: 8px;
                text-align: center;
                border: 1px solid rgba(0, 255, 255, 0.1);
            }
            
            .signal-matrix th {
                background: rgba(0, 180, 255, 0.1);
                color: #00ffff;
                font-weight: 500;
                text-transform: uppercase;
                font-size: 0.8em;
                letter-spacing: 0.5px;
            }
            
            .signal-container {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
                padding: 8px;
                background: rgba(20, 22, 29, 0.8);
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(5px);
                margin: 10px;
                max-width: 100%;
                overflow-x: auto;
            }
            
            .signal-circle {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 8px;
                margin: 0;
                transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
                position: relative;
                cursor: pointer;
                border: 1px solid rgba(255, 255, 255, 0.1);
                box-sizing: border-box;
                font-size: 11px;
                font-weight: 600;
                color: rgba(255, 255, 255, 0.9);
                text-transform: uppercase;
                user-select: none;
            }
            
            .signal-circle:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                z-index: 10;
            }
            
            .signal-circle.active {
                border: 2px solid #fff;
                box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.1);
            }
            
            /* Signal states */
            .strong-buy { 
                background: linear-gradient(135deg, #00c853, #5efc82);
                color: #003915;
            }
            .mild-buy { 
                background: linear-gradient(135deg, #00b0ff, #40c4ff);
                color: #002f6c;
            }
            .neutral { 
                background: linear-gradient(135deg, #9e9e9e, #e0e0e0);
                color: #212121;
            }
            .mild-sell { 
                background: linear-gradient(135deg, #ff9100, #ffd180);
                color: #5d4037;
            }
            .strong-sell { 
                background: linear-gradient(135deg, #ff3d00, #ff9e80);
                color: #3e2723;
            }
            .error { 
                background: linear-gradient(135deg, #f50057, #ff80ab);
                color: #4a148c;
            }
            
            /* Pulse animation for strong signals */
            .pulse {
                animation: pulse 2s infinite;
                box-shadow: 0 0 0 rgba(255, 255, 255, 0.5);
            }
            
            @keyframes pulse {
                0% { 
                    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
                    transform: scale(1);
                }
                50% { 
                    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
                    transform: scale(1.05);
                }
                100% { 
                    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
                    transform: scale(1);
                }
            }
            
            /* Tooltip styles */
            .custom-tooltip {
                position: fixed;
                background: rgba(0, 0, 0, 0.8);
                color: #fff;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                white-space: nowrap;
                z-index: 1000;
                pointer-events: none;
                margin-bottom: 5px;
            }
            .custom-tooltip .signal-content {
                font-size: 11px;
                font-weight: 700;
                text-align: center;
                pointer-events: none;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
                padding: 2px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 100%;
                transition: all 0.2s ease;
            }
            
            .signal-circle:hover .signal-content {
                transform: scale(1.1);
            }
            
            .signal-circle.active .signal-content {
                font-weight: 800;
                transform: scale(1.15);
            }
        `;
        document.head.appendChild(style);
    }
    
    // Initialize tooltips
    initTooltips();
}

/**
 * Initialize tooltips for signal circles
 */
function initTooltips() {
    // Track existing tooltips
    const existingTooltips = new Map();
    
    // Remove any existing tooltip event listeners
    document.querySelectorAll('.signal-light').forEach(el => {
        el.removeEventListener('mouseenter', handleTooltipShow);
        el.removeEventListener('mouseleave', handleTooltipHide);
    });
    
    // Clean up any existing tooltips
    document.querySelectorAll('.custom-tooltip').forEach(el => el.remove());
    
    // Initialize tooltips map if it doesn't exist
    if (!window.signalTooltips) {
        window.signalTooltips = new Map();
    }
    
    // Add event listeners to all signal circles
    document.querySelectorAll('.signal-circle').forEach(el => {
        el.removeEventListener('mouseenter', handleTooltipShow);
        el.removeEventListener('mouseleave', handleTooltipHide);
        el.addEventListener('mouseenter', handleTooltipShow);
        el.addEventListener('mouseleave', handleTooltipHide);
    });
    
    console.log('[SignalLights] Tooltip handlers initialized');
    
    // Store existingTooltips in window for cleanup
    window.signalExistingTooltips = existingTooltips;
}

/**
 * Handle tooltip display with improved positioning and content
 * @param {MouseEvent} event - The mouse event
 */
function handleTooltipShow(event) {
    try {
        const element = event.currentTarget;
        if (!element || !element.matches('.signal-circle')) return;
        
        // Don't show tooltip if element is in error or loading state
        if (element.dataset.state === 'error' || element.dataset.state === 'loading') {
            return;
        }
        
        const indicator = element.dataset.indicator || element.dataset.ind || 'unknown';
        const timeframe = element.dataset.timeframe || element.dataset.tf || 'N/A';
        const state = element.dataset.state || 'neutral';
        let strength = parseFloat(element.dataset.strength || '0');
        
        // Cap strength between 0 and 1
        strength = Math.min(1, Math.max(0, strength));
        
        // Create tooltip if it doesn't exist
        let tooltip = document.getElementById('signal-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'signal-tooltip';
            tooltip.className = 'signal-tooltip';
            document.body.appendChild(tooltip);
        }
        
        // Format strength as percentage with 1 decimal place
        const strengthPercent = (strength * 100).toFixed(1);
        
        // Get signal interpretation based on state and strength
        let interpretation = '';
        if (state === 'bullish') {
            if (strength > 0.7) interpretation = 'Strong Buy';
            else if (strength > 0.4) interpretation = 'Buy';
            else interpretation = 'Weak Buy';
        } else if (state === 'bearish') {
            if (strength > 0.7) interpretation = 'Strong Sell';
            else if (strength > 0.4) interpretation = 'Sell';
            else interpretation = 'Weak Sell';
        } else {
            interpretation = 'Neutral';
        }
        
        // Get indicator information
        const indicatorName = getIndicatorFullName(indicator);
        const indicatorDesc = getIndicatorDescription(indicator);
        
        // Set tooltip content with improved formatting
        const tooltipContent = [
            '<div class="tooltip-header">',
            `    <strong>${String(indicatorName || '')}</strong>`,
            `    <span class="timeframe">${String(timeframe || '')}</span>`,
            '</div>',
            '<div class="tooltip-body">',
            `    <div class="signal-state ${String(state || '')}">`,
            '        <span class="label">Signal:</span>',
            `        <span class="value">${String(interpretation || '')}</span>`,
            '    </div>',
            '    <div class="signal-strength">',
            '        <span class="label">Strength:</span>',
            '        <div class="strength-bar">',
            `            <div class="strength-fill" style="width: ${String(strengthPercent || '0')}%;"></div>`,
            `            <span class="strength-value">${String(strengthPercent || '0')}%</span>`,
            '        </div>',
            '    </div>',
            indicatorDesc ? `    <div class="indicator-desc">${String(indicatorDesc || '').replace(/</g, '&lt;').replace(/>/g, '&gt;')}</div>` : '',
            '</div>',
            '<div class="tooltip-footer">',
            `    Last updated: ${new Date().toLocaleTimeString()}`,
            '</div>'
        ];
        
        tooltip.innerHTML = tooltipContent.filter(Boolean).join('\n');
        
        // Position tooltip relative to viewport
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // Default position: above the element
        let left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2);
        let top = rect.top - tooltip.offsetHeight - 10;
        
        // Adjust if tooltip goes off-screen
        if (left < 10) left = 10;
        if (left + tooltip.offsetWidth > viewportWidth - 10) {
            left = viewportWidth - tooltip.offsetWidth - 10;
        }
        
        // If not enough space above, show below
        if (top < 10) {
            top = rect.bottom + 10;
        }
        
        // If still off-screen, adjust to be within viewport
        if (top + tooltip.offsetHeight > viewportHeight - 10) {
            top = viewportHeight - tooltip.offsetHeight - 10;
        }
        
        tooltip.style.left = `${Math.max(10, left)}px`;
        tooltip.style.top = `${Math.max(10, top)}px`;
        tooltip.style.display = 'block';
        
        // Add a class to the element being hovered
        element.classList.add('tooltip-active');
        
    } catch (error) {
        console.error('[SignalLights] Error in handleTooltipShow:', error);
    }
}

/**
 * Handle tooltip hide event
 * @param {MouseEvent} event - The mouse event
 */
function handleTooltipHide(event) {
    try {
        const element = event.currentTarget;
        if (!element) return;
        
        // Remove the tooltip-active class
        element.classList.remove('tooltip-active');
        
        // Remove the tooltip element if it exists
        const tooltip = document.getElementById('signal-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
        
        // Clean up any lingering tooltips
        document.querySelectorAll('.signal-tooltip').forEach(el => {
            if (el !== tooltip) el.remove();
        });
        
    } catch (error) {
        console.error('[SignalLights] Error in handleTooltipHide:', error);
    }
}

/**
 * Get a description for an indicator
 * @param {string} indicator - The indicator code
 * @returns {string} The indicator description
 */
function getIndicatorDescription(indicator) {
    if (!indicator) return '';
    
    const descriptions = {
        'rsi': 'Measures the magnitude of recent price changes to evaluate overbought or oversold conditions.',
        'macd': 'Shows the relationship between two moving averages of a security\'s price.',
        'stoch': 'Compares a particular closing price to a range of its prices over a certain period.',
        'bb': 'A volatility indicator consisting of a middle band with two outer bands.',
        'ema': 'A type of moving average that places a greater weight on recent prices.',
        'sma': 'The average price over a specific period of time.',
        'vwap': 'The average price a security has traded at throughout the day, based on both volume and price.',
        'adx': 'Measures the strength of a trend but not its direction.',
        'atr': 'Measures market volatility by decomposing the entire range of an asset price for that period.',
        'obv': 'Uses volume flow to predict changes in stock price.',
        'volume': 'The number of shares or contracts traded in a security or market during a given period.',
        'ichimoku': 'A comprehensive indicator that defines support and resistance, identifies trend direction, gauges momentum.',
        'pivot': 'A technical analysis indicator used to determine the overall trend of the market over different time frames.',
        'fib': 'A technical analysis tool that uses horizontal lines to indicate areas of support or resistance.',
        'sr': 'Key levels where the price of an asset has difficulty moving beyond.',
        'vwma': 'A moving average that incorporates the volume for each period in the moving average calculation.',
        'alma': 'A moving average that reduces lag while maintaining smoothness.'
    };
    
    const normalizedIndicator = indicator.toLowerCase();
    return descriptions[normalizedIndicator] || `No description available for ${indicator}.`;
}

// Generate tooltip text
function generateTooltipText(indicator, timeframe, data) {
    if (!data) return `${indicator} (${timeframe}): No data`;
    
    let text = `${indicator.toUpperCase()} (${timeframe})\n-------------------\n`;
    
    // Add indicator-specific data
    switch (indicator) {
        case 'rsi':
            text += `Value: ${data.value?.toFixed(2) || 'N/A'}\n`;
            text += `Signal: ${data.value > 70 ? 'Overbought' : data.value < 30 ? 'Oversold' : 'Neutral'}`;
            break;
        case 'macd':
            text += `Histogram: ${data.histogram?.toFixed(4) || 'N/A'}\n`;
            text += `Signal: ${data.histogram > 0 ? 'Bullish' : 'Bearish'}`;
            break;
        // Add more cases as needed
        default:
            text += JSON.stringify(data, null, 2);
    }
    
    return text;
}

// Update signal light with new data
function updateSignalLight(indicator, timeframe, data) {
    const signalId = `${indicator.toLowerCase()}-${timeframe}`;
    let signalElement = document.querySelector(`[data-indicator="${indicator.toLowerCase()}"][data-timeframe="${timeframe}"] .signal-circle`);
    
    if (!signalElement) {
        // Try to find the cell and create the signal if it doesn't exist
        const cell = document.querySelector(`[data-indicator="${indicator.toLowerCase()}"][data-timeframe="${timeframe}"]`);
        if (cell) {
            signalElement = document.createElement('div');
            signalElement.className = 'signal-circle';
            signalElement.setAttribute('data-indicator', indicator.toLowerCase());
            signalElement.setAttribute('data-timeframe', timeframe);
            cell.appendChild(signalElement);
        } else {
            console.warn(`[SignalLights] Could not find or create signal element for ${indicator}-${timeframe}`);
            return;
        }
    }
    
    // Update signal appearance based on data
    updateSignalElement(signalElement, indicator, timeframe, data);
    
    // Update tooltip content
    const tooltipText = generateTooltipText(indicator, timeframe, data);
    signalElement.setAttribute('data-original-title', tooltipText);
    signalElement.setAttribute('title', tooltipText);
    
    // If tooltip is currently showing, update it
    const existingTooltip = existingTooltips.get(signalId);
    if (existingTooltip) {
        existingTooltip.textContent = tooltipText;
    }
}

// Create a new signal light element
function createSignalLightElement(indicator, timeframe) {
    // Find the cell for this indicator and timeframe
    const cell = document.querySelector(`[data-indicator="${indicator.toLowerCase()}"][data-timeframe="${timeframe}"]`);
    if (!cell) {
        console.warn(`[SignalLights] Could not find cell for ${indicator}-${timeframe}`);
        return null;
    }
    
    // Create the signal circle
    const signalElement = document.createElement('div');
    signalElement.className = 'signal-circle';
    signalElement.setAttribute('data-indicator', indicator.toLowerCase());
    signalElement.setAttribute('data-timeframe', timeframe);
    
    // Add to cell
    cell.appendChild(signalElement);
    
    // Add event listeners
    signalElement.addEventListener('mouseenter', handleTooltipShow);
    signalElement.addEventListener('mouseleave', handleTooltipHide);
    
    return signalElement;
}



/**
 * Handle signal update events
 */
function handleSignalUpdate(event) {
    const { indicator, timeframe, data } = event.detail;
    
    // Find or create the signal element
    let element = document.querySelector(`.signal-light[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`);
    
    if (!element) {
        element = createSignalLightElement(indicator, timeframe);
    }
    
    if (element) {
        updateSignalElement(element, indicator, timeframe, data);
    }
}

/**
 * Create a new signal element
 * @param {string} indicator - The indicator name (e.g., 'rsi', 'macd')
 * @param {string} timeframe - The timeframe (e.g., '1h', '4h')
 * @returns {HTMLElement|null} The created element or null if creation failed
 */
function createSignalElement(indicator, timeframe) {
    console.log(`[SignalLights] Creating signal element for ${indicator}-${timeframe}`);
    
    try {
        // Ensure indicator and timeframe are valid
        if (!indicator || !timeframe) {
            console.error('[SignalLights] Invalid indicator or timeframe:', { indicator, timeframe });
            return null;
        }
        
        // Normalize values
        const indicatorLower = indicator.toLowerCase();
        const timeframeLower = timeframe.toLowerCase();
        const elementId = `signal-${indicatorLower}-${timeframeLower}`;
        
        // Check if element already exists
        const existingElement = document.getElementById(elementId);
        if (existingElement) {
            console.log(`[SignalLights] Element ${elementId} already exists`);
            return existingElement;
        }
        
        // Get or create the signal grid container
        let container = document.getElementById('momentum-indicators');
        if (!container) {
            console.error('[SignalLights] momentum-indicators container not found');
            return null;
        }
        
        // Create table if it doesn't exist
        let table = container.querySelector('table');
        if (!table) {
            table = document.createElement('table');
            table.className = 'signal-table';
            container.appendChild(table);
        }
        
        // Get or create table body
        let tbody = table.querySelector('tbody');
        if (!tbody) {
            tbody = document.createElement('tbody');
            table.appendChild(tbody);
        }
        
        // Find or create row for this indicator
        let row = tbody.querySelector(`tr[data-indicator="${indicatorLower}"]`);
        if (!row) {
            row = document.createElement('tr');
            row.className = 'signal-row';
            row.dataset.indicator = indicatorLower;
            
            // Add indicator label cell
            const labelCell = document.createElement('td');
            labelCell.className = 'indicator-label';
            labelCell.textContent = indicator.toUpperCase();
            row.appendChild(labelCell);
            
            // Add cells for each timeframe
            SIGNAL_TIMEFRAMES.forEach(tf => {
                const cell = document.createElement('td');
                cell.className = 'signal-cell';
                cell.dataset.indicator = indicatorLower;
                cell.dataset.timeframe = tf;
                
                // Create signal element
                const signal = document.createElement('div');
                signal.className = 'signal-circle';
                signal.dataset.indicator = indicatorLower;
                signal.dataset.timeframe = tf;
                signal.id = `signal-${indicatorLower}-${tf}`;
                
                // Add loading spinner
                const spinner = document.createElement('div');
                spinner.className = 'signal-spinner';
                signal.appendChild(spinner);
                
                // Add signal label
                const label = document.createElement('div');
                label.className = 'signal-label';
                label.textContent = indicatorLower.toUpperCase();
                signal.appendChild(label);
                
                // Add signal value
                const value = document.createElement('div');
                value.className = 'signal-value';
                value.textContent = '--';
                signal.appendChild(value);
                
                // Add to cell
                cell.appendChild(signal);
                row.appendChild(cell);
            });
            
            // Add row to table body
            tbody.appendChild(row);
        }
        
        // Add table to container if it's new
        if (!container.contains(table)) {
            container.appendChild(table);
        }
        
        // Initialize signal lights
        updateSignalLightsNonRecursive();
        return true;
    } catch (error) {
        console.error('[SignalLights] Error creating signal element:', error);
        return null;
    }
}

// Define functions inside IIFE
function updateSignalAppearance(element, indicator, data) {
    try {
        if (!element) return;
        
        // Add convergence class if multiple indicators agree
        if (data.convergence && data.convergence.count > 2) {
            element.classList.add('convergence');
        } else {
            element.classList.remove('convergence');
        }
        
        // Update element state and attributes
        element.dataset.state = data.state || 'neutral';
        element.dataset.strength = data.strength || '0';
        element.dataset.lastUpdate = Date.now();
        
        // Update tooltip
        element.title = generateTooltipText(indicator, element.dataset.timeframe, data);
        
        // Update visual state
        updateSignalVisualFeedback(element, data);
        
        // Show/hide spinner based on loading state
        const spinner = element.querySelector('.signal-spinner');
        if (spinner) {
            spinner.style.opacity = data.isLoading ? '1' : '0';
        }
        
        // Update signal label if needed
        const signalLabel = element.querySelector('.signal-label');
        if (signalLabel && !signalLabel.textContent) {
            signalLabel.textContent = indicator.charAt(0).toUpperCase();
        }
        
        // Update timeframe label if needed
        const timeframeLabel = element.querySelector('.timeframe-label');
        if (timeframeLabel && !timeframeLabel.textContent) {
            timeframeLabel.textContent = element.dataset.timeframe || '';
        }
    } catch (error) {
        console.error(`[SignalLights] Error updating signal light ${indicator}:`, error);
    }
}

function updateSignalVisualFeedback(element, data) {
    if (!element) return;
    
    try {
        // Get current state and strength
        const state = element.dataset.state || 'neutral';
        const strength = parseFloat(element.dataset.strength) || 0;
        
        // Remove all signal classes
        element.classList.remove('strong-buy', 'mild-buy', 'neutral', 'mild-sell', 'strong-sell');
        
        // Add appropriate signal class based on state and strength
        if (state === 'buy') {
            element.classList.add(strength >= 0.7 ? 'strong-buy' : 'mild-buy');
        } else if (state === 'sell') {
            element.classList.add(strength >= 0.7 ? 'strong-sell' : 'mild-sell');
        } else {
            element.classList.add('neutral');
        }
        
        // Update signal strength indicator
        const strengthIndicator = element.querySelector('.signal-strength');
        if (strengthIndicator) {
            strengthIndicator.style.width = `${strength * 100}%`;
        }
        
    } catch (error) {
        console.error('[SignalLights] Error updating visual feedback:', error);
    }
}

// Update visual feedback for signal element
function updateSignalVisualFeedback(element, data) {
    if (!element) return;
    
    // Reset all visual states
    element.style.borderColor = '';
    element.style.boxShadow = '';
    element.style.opacity = '1';
    
    const state = element.dataset.state || 'neutral';
    const strength = parseFloat(element.dataset.strength) || 0;
    
    // Apply visual feedback based on state and strength
    switch (state) {
        case 'buy':
            element.style.borderColor = 'rgba(0, 200, 83, 0.7)';
            element.style.boxShadow = `0 0 ${5 + (strength * 10)}px rgba(0, 200, 83, ${0.3 + (strength * 0.5)})`;
            break;
        case 'sell':
            element.style.borderColor = 'rgba(255, 82, 82, 0.7)';
            element.style.boxShadow = `0 0 ${5 + (strength * 10)}px rgba(255, 82, 82, ${0.3 + (strength * 0.5)})`;
            break;
        case 'neutral':
            element.style.borderColor = 'rgba(158, 158, 158, 0.5)';
            element.style.opacity = '0.7';
            break;
    }
    
    // Pulsing effect for strong signals
    if (strength > 0.7) {
        element.style.animation = `pulse-${state} 2s infinite`;
    } else {
        element.style.animation = '';
    }
    
    // Add visual indicator for convergence
    if (data.convergence && data.convergence.count > 2) {
        element.style.borderWidth = '2px';
        element.style.borderStyle = 'double';
    }
}

// Update signal element with new data
function updateSignalElement(element, indicator, timeframe, data = {}) {
    const startTime = performance.now();
    const elementId = element && element.id ? element.id : `${indicator}-${timeframe}`;
    
    // Validate inputs
    if (!element || !indicator || !timeframe) {
        console.warn('[SignalLights] Invalid arguments for updateSignalElement:', { 
            element: !!element, 
            indicator, 
            timeframe,
            elementId
        });
        return null;
    }

    try {
        // Log the update for debugging
        if (window.DEBUG_SIGNALS) {
            console.log(`[SignalLights] Updating ${elementId} with data:`, {
                indicator,
                timeframe,
                data,
                elementExists: !!element,
                elementState: element.dataset.state,
                currentTime: new Date().toISOString()
            });
        }
        
        // Normalize input data with validation
        const signal = String(data.signal || 'neutral').toLowerCase().trim();
        const strength = Math.min(1, Math.max(0, parseFloat(data.strength) || 0));
        const timestamp = data.timestamp || Date.now();
        const lastUpdate = parseInt(element.dataset.lastUpdate) || 0;
        const direction = parseInt(data.direction) || 0;
        
        // Validate signal value
        const validSignals = ['strong-buy', 'buy', 'neutral', 'sell', 'strong-sell', 'loading', 'error'];
        if (!validSignals.includes(signal)) {
            console.warn(`[SignalLights] Invalid signal value '${signal}' for ${elementId}, defaulting to 'neutral'`);
            signal = 'neutral';
        }
        
        // Skip if this update is older than the current data (unless forced)
        if (timestamp < lastUpdate && !data.force) {
            if (window.DEBUG_SIGNALS) {
                console.log(`[SignalLights] Skipping stale update for ${elementId}:`, { 
                    updateTime: new Date(timestamp).toISOString(),
                    lastUpdateTime: new Date(lastUpdate).toISOString()
                });
            }
            return element;
        }
        
        // Update element attributes with validation
        element.dataset.lastUpdate = timestamp.toString();
        element.dataset.state = signal;
        element.dataset.strength = strength.toFixed(2);
        element.dataset.direction = direction.toString();
        
        // Store the raw value if provided
        if (data.value !== undefined) {
            element.dataset.value = String(data.value);
        }
        
        // Update tooltip with detailed information
        const updatedTooltipText = generateTooltipText(indicator, timeframe, {
            ...data,
            signal,
            strength,
            direction,
            lastUpdate: new Date(timestamp).toLocaleString(),
            elementId
        });
        
        // Only update title if it's different to avoid unnecessary reflows
        if (element.title !== updatedTooltipText) {
            element.title = updatedTooltipText;
        }
        
        // Update visual classes
        const signalClass = getSignalClass(signal, strength);
        const allSignalClasses = ['strong-buy', 'buy', 'neutral', 'sell', 'strong-sell', 'loading', 'error'];
        
        // Remove all signal classes first
        element.classList.remove(...allSignalClasses);
        
        // Add the current signal class
        if (signalClass) {
            element.classList.add(signalClass);
        }
        
        // Toggle active state based on strength
        element.classList.toggle('active', strength > 0);
        
        // Add direction class if applicable
        element.classList.toggle('direction-up', direction > 0);
        element.classList.toggle('direction-down', direction < 0);
        
        // Log performance
        const duration = performance.now() - startTime;
        if (window.DEBUG_SIGNALS || duration > 10) {
            console.log(`[SignalLights] Updated ${elementId} in ${duration.toFixed(2)}ms`, {
                signal,
                strength,
                direction,
                timestamp: new Date(timestamp).toISOString(),
                previousUpdate: lastUpdate ? new Date(lastUpdate).toISOString() : 'never'
            });
        }
        
        // Update element attributes if they don't exist or are different
        const updateAttribute = (attr, value) => {
            if (element.getAttribute(attr) !== String(value)) {
                element.setAttribute(attr, value);
            }
        };
        
        // Set attributes
        updateAttribute('data-indicator', indicator);
        updateAttribute('data-timeframe', timeframe);
        updateAttribute('data-signal', signal);
        updateAttribute('data-strength', strength.toFixed(2));
        updateAttribute('title', data.tooltip || `${indicator} (${timeframe}): ${signal.replace(/-/g, ' ')}`);
        updateAttribute('aria-label', `${indicator} signal for ${timeframe} is ${signal} with ${(strength * 100).toFixed(0)}% strength`);
        
        // Update signal class
        const signalClasses = ['strong-buy', 'mild-buy', 'neutral', 'mild-sell', 'strong-sell', 'error', 'loading'];
        element.classList.remove(...signalClasses);
        
        if (data.loading) {
            element.classList.add('loading');
        } else if (signalClass) {
            element.classList.add(signalClass);
        } else {
            element.classList.add('neutral');
        }
        
        // Add pulse animation for strong signals
        if (strength > 0.8) {
            element.classList.add('pulse');
        } else {
            element.classList.remove('pulse');
        }
        
        // Add updated class for visual feedback
        element.classList.add('updated');
        setTimeout(() => {
            element.classList.remove('updated');
        }, 500);
        
        // Update tooltip with more detailed information
        const tooltipText = data.tooltip || `${indicator.toUpperCase()} (${timeframe})
Signal: ${signalClass || 'neutral'}
Strength: ${(strength * 100).toFixed(0)}%
Updated: ${new Date(timestamp).toLocaleTimeString()}`;
        
        element.title = tooltipText;
        
        // Update loading spinner
        const spinner = element.querySelector('.signal-spinner');
        if (spinner) {
            if (data.loading) {
                spinner.style.display = 'block';
            } else {
                spinner.style.display = 'none';
            }
        }
        
        // Dispatch custom event for any additional handling
        const updateEvent = new CustomEvent('signalElementUpdated', {
            detail: { 
                element, 
                indicator, 
                timeframe, 
                data: { 
                    ...signalData, 
                    signalClass, 
                    strength 
                } 
            },
            bubbles: true
        });
        document.dispatchEvent(updateEvent);
        
        if (window.DEBUG_SIGNALS) {
            console.log(`[SignalLights] Updated signal: ${indicator}-${timeframe}`, {
                signalClass,
                strength,
                element,
                data: signalData
            });
        }
        
        return element;
        
    } catch (error) {
        const errorMessage = `[SignalLights] Error updating signal element ${elementId}: ${error.message || 'Unknown error'}`;
        console.error(errorMessage, error);
        
        // Try to mark element as errored if possible
        if (element && typeof element.setAttribute === 'function') {
            try {
                // Set error state
                element.dataset.state = 'error';
                element.title = `Error: ${error.message || 'Failed to update signal'}`;
                
                // Update classes
                element.className = 'signal-circle error';
                
                // Add error class to parent for better visibility
                if (element.parentElement) {
                    element.parentElement.classList.add('has-error');
                }
                
                // Dispatch error event
                const errorEvent = new CustomEvent('signalElementError', {
                    detail: { 
                        element,
                        elementId,
                        indicator, 
                        timeframe, 
                        error: error.message || 'Unknown error',
                        timestamp: Date.now(),
                        stack: error.stack
                    },
                    bubbles: true,
                    cancelable: true
                });
                
                // Only dispatch if no one prevents it
                if (document.dispatchEvent(errorEvent)) {
                    console.warn(`[SignalLights] Error event was not handled for ${elementId}`);
                }
                
            } catch (domError) {
                console.error('[SignalLights] Critical error during error handling:', domError);
            }
        }
        
        return null;
    }
}

// Add styles for signal lights if they don't exist
if (!document.getElementById('signal-lights-styles')) {
    const style = document.createElement('style');
    style.id = 'signal-lights-styles';
    style.textContent = `
        /* Signal Grid Container */
        #momentum-indicators {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px auto;
            max-width: 1200px;
            overflow-x: auto;
            background: #1e1e2d;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #2b2b40;
            color: #fff;
            padding: 12px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #2c2e3e;
        }
        
        .section-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }
        
        /* Signal Grid */
        .signal-matrix {
            display: table;
            width: 100%;
            border-collapse: collapse;
            background: #1e1e2d;
            color: #fff;
        }
        
        /* Header Row */
        .signal-row.header {
            background: #2b2b40;
            font-weight: bold;
        }
        
        .signal-row {
            display: flex;
            border-bottom: 1px solid #2c2e3e;
        }
        
        .signal-row:last-child {
            border-bottom: none;
        }
        
        .signal-row:nth-child(even) {
            background: rgba(255, 255, 255, 0.02);
        }
        
        .signal-row:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        /* Cells */
        .indicator-cell, 
        .timeframe-header,
        .signal-cell {
            padding: 12px 8px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            box-sizing: border-box;
        }
        
        .indicator-cell {
            font-weight: 500;
            color: #a1a5b7;
            justify-content: flex-start;
            padding-left: 16px;
            width: 150px;
            border-right: 1px solid #2c2e3e;
        }
        
        .timeframe-header {
            font-weight: 500;
            color: #a1a5b7;
            border-right: 1px solid #2c2e3e;
        }
        
        .timeframe-header:last-child,
        .signal-cell:last-child {
            border-right: none;
        }
        
        /* Signal Circle */
        .signal-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
            margin: 0 auto;
        }
        
        .signal-circle:hover {
            transform: scale(1.1);
            z-index: 10;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        /* Signal States */
        .signal-circle[data-state="bullish"] {
            border-color: #0abb87;
            background: rgba(10, 187, 135, 0.1);
        }
        
        .signal-circle[data-state="bearish"] {
            border-color: #f4516c;
            background: rgba(244, 81, 108, 0.1);
        }
        
        .signal-circle[data-state="neutral"] {
            border-color: #ffb822;
            background: rgba(255, 184, 34, 0.1);
        }
        
        .signal-circle[data-state="loading"] {
            border-color: #6c7293;
            background: rgba(108, 114, 147, 0.1);
        }
        
        .signal-circle[data-state="error"] {
            border-color: #ff0000;
            background: rgba(255, 0, 0, 0.1);
            animation: pulse 2s infinite;
        }
        
        /* Labels */
        .signal-label {
            font-size: 12px;
            font-weight: bold;
            color: white;
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.7);
            pointer-events: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
        }
        
        .timeframe-label {
            font-size: 9px;
            color: #a1a5b7;
            line-height: 1;
            opacity: 0.8;
        }
        
        /* Loading Spinner */
        .signal-spinner {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 2px solid rgba(0, 180, 255, 0.2);
            border-top-color: #00b4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }
        
        /* Tooltip */
        .signal-tooltip {
            position: absolute;
            background: #2b2b40;
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            pointer-events: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            border: 1px solid #2c2e3e;
            max-width: 250px;
            line-height: 1.4;
        }
        
        .signal-tooltip strong {
            color: #6c7293;
            display: block;
            margin-bottom: 4px;
            border-bottom: 1px solid #2c2e3e;
            padding-bottom: 4px;
        }
        
        .signal-loader {
            width: 16px;
            height: 16px;
            border: 2px solid #fff;
            border-bottom-color: transparent;
            border-radius: 50%;
            display: inline-block;
            box-sizing: border-box;
            animation: rotation 1s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            margin: -8px 0 0 -8px;
        }
        
        @keyframes rotation {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .signal-circle {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .signal-circle.updated {
            transform: scale(1.1);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        .signal-circle.pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 255, 0, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 255, 0, 0); }
        }
        
        /* Signal color classes */
        .signal-circle.strong-buy { background-color: #00c853 !important; }
        .signal-circle.mild-buy { background-color: #69f0ae !important; }
        .signal-circle.neutral { background-color: #9e9e9e !important; }
        .signal-circle.mild-sell { background-color: #ff8a80 !important; }
        .signal-circle.strong-sell { background-color: #ff3d00 !important; }
        .signal-circle.error { background-color: #d50000 !important; }
        
        /* Loading state */
        .signal-circle.loading {
            opacity: 0.7;
            position: relative;
        }
    `;
    document.head.appendChild(style);
}

// SignalLights object to expose public methods
const SignalLights = {
    init: function() {
        initializeSignalLights();
    },
    updateSignalLight: updateSignalLight,
    updateActiveTimeframe: updateActiveTimeframe,
    initializeSignalLightsSystem: initializeSignalLightsSystem
};

/**
 * Update the active timeframe and notify other components
 * @param {string} timeframe - The new timeframe to activate
 */
function updateActiveTimeframe(timeframe) {
    if (!timeframe) {
        console.warn('[SignalLights] No timeframe provided to updateActiveTimeframe');
        return;
    }
    
    console.log(`[SignalLights] Changing to timeframe: ${timeframe}`);
    
    // Update active state in signal lights
    const signalElements = document.querySelectorAll('.signal-circle');
    let foundActive = false;
    
    signalElements.forEach(el => {
        const elTimeframe = el.dataset.timeframe || el.dataset.tf;
        if (elTimeframe === timeframe) {
            el.classList.add('active');
            el.setAttribute('data-active', 'true');
            foundActive = true;
        } else {
            el.classList.remove('active');
            el.removeAttribute('data-active');
        }
    });
    
    if (!foundActive && signalElements.length > 0) {
        console.warn(`[SignalLights] No signal element found for timeframe: ${timeframe}`);
    }
    
    // Update TradingView chart timeframe if available
    if (window.tvWidget) {
        try {
            const chart = window.tvWidget.chart();
            if (chart && chart.activeChart) {
                // Convert our timeframe format to TradingView's format
                let tvTimeframe = timeframe;
                if (timeframe.endsWith('m')) {
                    tvTimeframe = timeframe.replace('m', '');
                } else if (timeframe.endsWith('h')) {
                    tvTimeframe = timeframe.replace('h', '') + '|60';
                } else if (timeframe.endsWith('d')) {
                    tvTimeframe = timeframe.replace('d', 'D');
                } else if (timeframe.endsWith('w')) {
                    tvTimeframe = timeframe.replace('w', 'W');
                }
                
                // Use executeActionById for better compatibility
                try {
                    chart.executeActionById('timeScaleReset');
                    chart.setSymbol('BINANCE:BTCUSDT', tvTimeframe, () => {
                        console.log(`[SignalLights] TradingView chart updated to ${timeframe} (${tvTimeframe})`);
                    });
                } catch (e) {
                    console.warn('[SignalLights] Error using setSymbol, falling back to setResolution');
                    chart.setResolution(tvTimeframe, () => {
                        console.log(`[SignalLights] TradingView chart resolution updated to ${tvTimeframe}`);
                    });
                }
            } else {
                console.warn('[SignalLights] TradingView chart not ready, will retry...');
                // Retry after a delay if chart isn't ready
                setTimeout(() => updateActiveTimeframe(timeframe), 500);
            }
        } catch (error) {
            console.error('[SignalLights] Error updating TradingView timeframe:', error);
            // Retry with exponential backoff
            const retryDelay = Math.min(1000 * Math.pow(2, (window.tvRetryCount || 0)), 10000);
            window.tvRetryCount = (window.tvRetryCount || 0) + 1;
            console.log(`[SignalLights] Will retry in ${retryDelay}ms (attempt ${window.tvRetryCount})`);
            setTimeout(() => updateActiveTimeframe(timeframe), retryDelay);
        }
    } else {
        console.log('[SignalLights] TradingView widget not available');
    }
    
    // Dispatch custom event for other components
    const event = new CustomEvent('timeframeChanged', {
        detail: { 
            timeframe,
            timestamp: Date.now()
        }
    });
    document.dispatchEvent(event);
    
    // Update URL hash for deep linking
    try {
        const url = new URL(window.location);
        url.hash = `timeframe=${timeframe}`;
        window.history.replaceState({}, '', url);
    } catch (e) {
        console.warn('[SignalLights] Could not update URL:', e);
    }
}

    // ... (rest of the code remains the same)

    // Export functions
    if (typeof window !== 'undefined') {
        window.updateSignalAppearance = updateSignalAppearance;
        window.updateSignalVisualFeedback = updateSignalVisualFeedback;
        // window.initializeSignalLights = initializeSignalLights; // Cascade: Prevented global assignment
        // window.initializeSignalLightsSystem = initializeSignalLightsSystem; // Cascade: Prevented global assignment
        window.SignalLights = SignalLights; // This object itself might be okay if its methods are called explicitly
        // window.initSignalLights = init; // Cascade: Prevented global assignment (init calls initializeSignalLightsSystem)
        window.handleSignalClick = handleClick; // Ensure this is globally available if needed
        window.handleMouseOver = handleMouseOver;
        window.handleMouseOut = handleMouseOut;
        window.getIndicatorFullName = getIndicatorFullName; // Expose for potential external use
    }
})();