'use strict';

import js from '@eslint/js';
import globals from 'globals';

/** @type {import('eslint').Linter.FlatConfig[]} */
export default [
  // Global ignores
  {
    ignores: ['**/node_modules/**', '**/dist/**', '**/build/**'],
  },
  
  // Base configuration
  {
    files: ['**/*.js'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        ...globals.browser,
        ...globals.es2021,
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    rules: {
      // Basic rules
      'no-var': 'error',
      'prefer-const': 'error',
      'no-unused-vars': ['warn', { args: 'none' }],
      'no-console': ['warn', { allow: ['warn', 'error', 'info', 'debug'] }],
      'no-debugger': 'warn',
      'no-extra-semi': 'error',
      'no-mixed-spaces-and-tabs': 'error',
      'no-multi-spaces': 'error',
      'no-multiple-empty-lines': ['error', { max: 1 }],
      'no-trailing-spaces': 'error',
      'no-undef': 'error',
      'no-unreachable': 'error',
      'no-unsafe-negation': 'error',
      'valid-typeof': 'error',
      'no-duplicate-imports': 'error',
      'no-const-assign': 'error',
      'no-this-before-super': 'error',
      'constructor-super': 'error',
      'use-isnan': 'error',
      'no-nested-ternary': 'warn',
      'no-prototype-builtins': 'warn',
      'no-else-return': 'warn',
      'no-useless-return': 'warn',
      'no-useless-constructor': 'warn',
      'no-dupe-args': 'error',
      'no-dupe-keys': 'error',
      'no-dupe-class-members': 'error',
      'no-duplicate-case': 'error',
      'no-extra-boolean-cast': 'error',
      'no-func-assign': 'error',
      'no-obj-calls': 'error',
      'no-redeclare': 'error',
      'no-sparse-arrays': 'error',
      'no-unexpected-multiline': 'error',
      'no-unused-expressions': 'warn',
      'no-useless-catch': 'warn',
      'no-useless-escape': 'warn',
      'no-with': 'error',
      'require-atomic-updates': 'warn',
      'no-empty': ['error', { allowEmptyCatch: true }],
      'no-constant-condition': ['error', { checkLoops: false }],
      'no-unused-vars': ['warn', { vars: 'all', args: 'after-used', ignoreRestSiblings: true }],
      'no-var': 'error',
      'prefer-const': 'warn',
      'prefer-rest-params': 'warn',
      'prefer-spread': 'warn',
      'prefer-template': 'warn',
      'prefer-object-spread': 'warn',
      'prefer-arrow-callback': 'warn',
      'object-shorthand': ['warn', 'always'],
      'dot-notation': 'warn',
      'one-var': ['warn', 'never'],
      'spaced-comment': ['warn', 'always'],
      'arrow-spacing': 'warn',
      'block-spacing': 'warn',
      'brace-style': ['warn', '1tbs', { allowSingleLine: true }],
      'comma-dangle': ['warn', 'always-multiline'],
      'comma-spacing': 'warn',
      'comma-style': 'warn',
      'eol-last': 'warn',
      'func-call-spacing': 'warn',
      'indent': ['warn', 2, { SwitchCase: 1, ignoredNodes: ['PropertyDefinition'] }],
      'key-spacing': 'warn',
      'keyword-spacing': 'warn',
      'linebreak-style': ['warn', 'unix'],
      'new-parens': 'warn',
      'no-multi-assign': 'warn',
      'no-multiple-empty-lines': ['warn', { max: 1 }],
      'no-trailing-spaces': 'warn',
      'no-whitespace-before-property': 'warn',
      'object-curly-spacing': ['warn', 'always'],
      'operator-linebreak': ['warn', 'after'],
      'padded-blocks': ['warn', 'never'],
      'quote-props': ['warn', 'as-needed'],
      'quotes': ['warn', 'single', { avoidEscape: true, allowTemplateLiterals: true }],
      'semi': ['warn', 'always'],
      'semi-spacing': 'warn',
      'semi-style': 'warn',
      'space-before-blocks': 'warn',
      'space-before-function-paren': ['warn', { anonymous: 'always', named: 'never', asyncArrow: 'always' }],
      'space-in-parens': 'warn',
      'space-infix-ops': 'warn',
      'space-unary-ops': 'warn',
      'switch-colon-spacing': 'warn',
      'template-curly-spacing': 'warn',
      'yoda': 'warn',
    },
  },
  
  // Test files configuration
  {
    files: ['**/*.test.js', '**/test/**/*.js'],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.mocha,
      },
    },
  },
  
  // Config files configuration
  {
    files: ['*.config.js', '.*.js'],
    languageOptions: {
      globals: {
        ...globals.node,
      },
    },
  },
];
