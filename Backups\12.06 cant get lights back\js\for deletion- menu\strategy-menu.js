/**
 * @module StrategyMenu
 * @description Menu system for trading strategies
 */

import { MenuController } from './menu-controller.js'
import { MenuUtils } from './menu-utils.js'
import { StrategyManager } from '../strategy-manager.js'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/error-handler.js'
import { PerformanceMonitor } from '../utils/performance-monitor.js'

export class StrategyMenu extends MenuController {
  constructor() {
    super()

    // Strategy-specific configuration
    this.config = {
      menuId: 'strategy-menu',
      buttonId: 'strategy-button',
      containerId: 'strategy-container',
      helperId: 'strategy-helper',
      animationDuration: 300,
      maxStrategies: 10,
      showHelp: true,
    }

    // Strategy manager
    this.strategyManager = new StrategyManager()

    // Error handling
    this.errorHandler = new ErrorHandler({
      maxErrors: 5,
      recoveryDelay: 2000,
      errorThreshold: 0.1,
    })

    // Performance monitoring
    this.performance = new PerformanceMonitor({
      targetFPS: 60,
      maxLatency: 100,
      batchInterval: 100,
    })

    // Menu state
    this.state = {
      currentStrategy: null,
      strategies: [],
      isInitialized: false,
      isLoading: false,
      error: null,
    }
  }

  /**
     * Initialize strategy menu
     */
  async init() {
    try {
      // Initialize base menu
      await super.init()

      // Initialize strategy manager
      await this.strategyManager.init()

      // Get strategies
      this.state.strategies = await this.getStrategies()

      // Create menu structure
      this.createMenuStructure()

      // Add event listeners
      this.setupEventListeners()

      // Set initial state
      this.state.isInitialized = true
      this.state.currentStrategy = this.strategyManager.currentStrategy

      // Dispatch initialized event
      this.dispatch('initialized', {
        strategies: this.state.strategies,
        currentStrategy: this.state.currentStrategy,
      })
    } catch (error) {
      this.handleError(error, 'init')
    }
  }

  /**
     * Get available strategies
     * @returns {Promise<Array>} List of strategies
     * @private
     */
  async getStrategies() {
    try {
      // Get strategies from manager
      const strategies = await this.strategyManager.getStrategies()

      // Validate strategies
      if (!Array.isArray(strategies)) {
        throw new Error('Strategies must be an array')
      }

      // Limit strategies if needed
      return strategies.slice(0, this.config.maxStrategies)
    } catch (error) {
      this.handleError(error, 'get-strategies')
      return []
    }
  }

  /**
     * Create menu structure
     * @private
     */
  createMenuStructure() {
    try {
      // Get menu element
      const menu = MenuUtils.getMenuElement(this.config.menuId)
      if (!menu) return

      // Clear existing items
      menu.innerHTML = ''

      // Add strategy items
      this.state.strategies.forEach(strategy => {
        const item = MenuUtils.addMenuItem(this.config.menuId, {
          id: `strategy-${strategy.id}`,
          text: strategy.name,
          className: 'strategy-item',
          style: {
            cursor: 'pointer',
            padding: '8px 16px',
            borderRadius: '4px',
            transition: 'background-color 0.2s',
          },
          events: {
            click: () => this.handleStrategyClick(strategy),
            mouseenter: () => this.handleStrategyHover(strategy),
            mouseleave: () => this.handleStrategyHoverEnd(),
          },
        })

        // Add ARIA attributes
        item.setAttribute('role', 'menuitem')
        item.setAttribute('aria-label', strategy.description)
      })

      // Add helper content if enabled
      if (this.config.showHelp) {
        this.addHelperContent()
      }
    } catch (error) {
      this.handleError(error, 'create-menu-structure')
    }
  }

  /**
     * Add helper content
     * @private
     */
  addHelperContent() {
    try {
      // Get helper element
      const helper = document.getElementById(this.config.helperId)
      if (!helper) return

      // Add helper content
      helper.innerHTML = `
                <h3>Strategy Selection Guide</h3>
                <p>Select a trading strategy to view its indicators and settings.</p>
                <ul>
                    <li>Click to select a strategy</li>
                    <li>Hover for strategy description</li>
                    <li>Use arrow keys for navigation</li>
                </ul>
            `

      // Add ARIA attributes
      helper.setAttribute('role', 'region')
      helper.setAttribute('aria-label', 'Strategy selection guide')
    } catch (error) {
      this.handleError(error, 'add-helper-content')
    }
  }

  /**
     * Handle strategy click
     * @param {Object} strategy - Selected strategy
     * @private
     */
  async handleStrategyClick(strategy) {
    try {
      // Set loading state
      this.state.isLoading = true

      // Update strategy
      await this.strategyManager.switchStrategy(strategy.id)

      // Update menu state
      this.state.currentStrategy = strategy

      // Update UI
      this.updateUI()

      // Dispatch change event
      this.dispatch('strategy-change', {
        strategy,
        timestamp: Date.now(),
      })

      // Close menu
      this.close()
    } catch (error) {
      this.handleError(error, 'handle-strategy-click')
    } finally {
      this.state.isLoading = false
    }
  }

  /**
     * Handle strategy hover
     * @param {Object} strategy - Hovered strategy
     * @private
     */
  handleStrategyHover(strategy) {
    try {
      // Update helper content
      this.updateHelperContent(strategy)

      // Add hover class
      const item = document.getElementById(`strategy-${strategy.id}`)
      if (item) {
        item.classList.add('hover')
      }
    } catch (error) {
      this.handleError(error, 'handle-strategy-hover')
    }
  }

  /**
     * Handle strategy hover end
     * @private
     */
  handleStrategyHoverEnd() {
    try {
      // Remove hover classes
      document.querySelectorAll('.strategy-item.hover')
        .forEach(item => item.classList.remove('hover'))

      // Reset helper content
      this.resetHelperContent()
    } catch (error) {
      this.handleError(error, 'handle-strategy-hover-end')
    }
  }

  /**
     * Update helper content
     * @param {Object} strategy - Strategy to display
     * @private
     */
  updateHelperContent(strategy) {
    try {
      // Get helper element
      const helper = document.getElementById(this.config.helperId)
      if (!helper) return

      // Update content
      helper.innerHTML = `
                <h3>${strategy.name}</h3>
                <p>${strategy.description}</p>
                <ul>
                    ${strategy.indicators.map(indicator =>
    `<li>${indicator.name} (${indicator.type})</li>`,
  ).join('')}
                </ul>
            `
    } catch (error) {
      this.handleError(error, 'update-helper-content')
    }
  }

  /**
     * Reset helper content
     * @private
     */
  resetHelperContent() {
    try {
      // Get helper element
      const helper = document.getElementById(this.config.helperId)
      if (!helper) return

      // Reset content
      helper.innerHTML = ''
    } catch (error) {
      this.handleError(error, 'reset-helper-content')
    }
  }

  /**
     * Update UI based on current state
     * @private
     */
  updateUI() {
    try {
      // Get menu element
      const menu = MenuUtils.getMenuElement(this.config.menuId)
      if (!menu) return

      // Update strategy items
      this.state.strategies.forEach(strategy => {
        const item = document.getElementById(`strategy-${strategy.id}`)
        if (item) {
          // Add active class if current strategy
          if (strategy.id === this.state.currentStrategy?.id) {
            item.classList.add('active')
          } else {
            item.classList.remove('active')
          }
        }
      })

      // Update button text
      const button = document.getElementById(this.config.buttonId)
      if (button) {
        button.textContent = this.state.currentStrategy?.name || 'Select Strategy'
      }
    } catch (error) {
      this.handleError(error, 'update-ui')
    }
  }

  /**
     * Setup event listeners
     * @private
     */
  setupEventListeners() {
    try {
      // Listen for strategy changes
      this.strategyManager.on('strategy-change', (data) => {
        this.handleStrategyChange(data)
      })

      // Listen for menu events
      this.on('menu-close', () => this.handleMenuClose())
      this.on('error', (error) => this.handleError(error))
    } catch (error) {
      this.handleError(error, 'setup-event-listeners')
    }
  }

  /**
     * Handle strategy change
     * @param {Object} data - Strategy change data
     * @private
     */
  handleStrategyChange(data) {
    try {
      // Update state
      this.state.currentStrategy = data.strategy

      // Update UI
      this.updateUI()
    } catch (error) {
      this.handleError(error, 'handle-strategy-change')
    }
  }

  /**
     * Handle menu close
     * @private
     */
  handleMenuClose() {
    try {
      // Reset helper content
      this.resetHelperContent()

      // Remove hover classes
      this.handleStrategyHoverEnd()
    } catch (error) {
      this.handleError(error, 'handle-menu-close')
    }
  }

  /**
     * Handle errors
     * @param {Error} error - Error object
     * @param {string} context - Context where error occurred
     * @private
     */
  handleError(error, context) {
    try {
      // Log error
      console.error(`[StrategyMenu] Error in ${context}:`, error)

      // Track error
      this.errorHandler.track(error, context)

      // Set error state
      this.state.error = error

      // Dispatch error event
      this.dispatch('error', {
        error,
        context,
        timestamp: Date.now(),
      })

      // Try to recover
      if (this.errorHandler.shouldRecover()) {
        this.recoverFromError()
      }
    } catch (error) {
      console.error('[StrategyMenu] Error handling failed:', error)
    }
  }

  /**
     * Attempt to recover from error
     * @private
     */
  recoverFromError() {
    try {
      // Reset state
      this.state = {
        currentStrategy: null,
        strategies: [],
        isInitialized: false,
        isLoading: false,
        error: null,
      }

      // Reinitialize
      this.init()
    } catch (error) {
      console.error('[StrategyMenu] Recovery failed:', error)
    }
  }

  /**
     * Clean up resources
     */
  cleanup() {
    try {
      // Cleanup base menu
      super.cleanup()

      // Cleanup strategy manager
      this.strategyManager.cleanup()

      // Reset state
      this.state = {
        currentStrategy: null,
        strategies: [],
        isInitialized: false,
        isLoading: false,
        error: null,
      }

      // Reset error handler
      this.errorHandler.reset()

      // Reset performance monitor
      this.performance.reset()
    } catch (error) {
      console.error('[StrategyMenu] Cleanup failed:', error)
    }
  }

  /**
     * Destroy menu
     */
  destroy() {
    try {
      // Cleanup
      this.cleanup()

      // Reset references
      this.strategyManager = null
      this.errorHandler = null
      this.performance = null
    } catch (error) {
      console.error('[StrategyMenu] Destruction failed:', error)
    }
  }
}

// Export singleton instance
const strategyMenu = new StrategyMenu()
export default strategyMenu
