/* Styles for Threshold Sliders, Labels, and Color Dots */
/* Extracted from index.html */

.slider-container {
  position: relative; /* Establish stacking context */
  /* width: 100%; /* Or specific width */ 
  /* height: 20px; /* Or enough to contain sliders and bar */
}

.slider-bar {
  position: relative; /* Needed for z-index */
  z-index: 1;         /* Visually behind interactive sliders */
  width: 100%;
  height: 20px;       /* Match the slider height for visual alignment */
  display: flex;      /* To layout segments horizontally */
  border-radius: 5px; /* Optional: for rounded corners on the bar itself */
  overflow: hidden;   /* Optional: if segments might poke out */
}

.slider-bar .segment.green {
  left: 0;
  background: linear-gradient(to right, #006600, #00FF00);
  box-shadow: inset 0 0 10px rgba(0, 255, 0, 0.5);
}

.slider-bar .segment.blue {
  background: linear-gradient(to right, #0044AA, #0088FF);
  box-shadow: inset 0 0 10px rgba(0, 100, 255, 0.5);
}

.slider-bar .segment.grey {
  background: linear-gradient(to right, #555555, #808080);
  box-shadow: inset 0 0 10px rgba(128, 128, 128, 0.5);
}

.slider-bar .segment.orange {
  background: linear-gradient(to right, #DD7500, #FFA500);
  box-shadow: inset 0 0 10px rgba(255, 165, 0, 0.5);
}

.slider-bar .segment.red {
  background: linear-gradient(to right, #CC0000, #FF0000);
  box-shadow: inset 0 0 10px rgba(255, 0, 0, 0.5);
}

/* Range input styling */
.threshold-slider {
  position: absolute;
  width: 100%;
  height: 20px;
  top: 0;
  left: 0;
  margin: 0;
  opacity: 0.01; /* Nearly invisible but still interactive */
  /* z-index: 10; /* Base z-index removed, specific ones are enough */
  cursor: pointer;
}

/* Corrected z-index for proper interaction */
div.slider-container input.threshold-slider.green  { z-index: 11; }
div.slider-container input.threshold-slider.blue   { z-index: 12; }
div.slider-container input.threshold-slider.orange { z-index: 13; }
div.slider-container input.threshold-slider.red    { z-index: 14; } /* Red on top */

.threshold-labels {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 10px;
}

.threshold-label {
  font-size: 11px;
  margin: 2px 5px;
  color: #CCCCCC;
  display: flex;
  align-items: center;
}

.dynamic-value-span {
  display: inline-block; /* Allows min-width and text-align to work */
  min-width: 3.5em;      /* Adjust as needed, enough for "100.0" */
  text-align: right;     /* Aligns numbers to the right, looks neat */
  /* padding-right: 0.2em; /* Optional: if it feels too close to the '%' sign */
}

.color-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.color-dot.green { background-color: #00FF00; box-shadow: 0 0 5px #00FF00; }
.color-dot.blue { background-color: #0088FF; box-shadow: 0 0 5px #0088FF; }
.color-dot.grey { background-color: #808080; box-shadow: 0 0 5px #808080; }
.color-dot.orange { background-color: #FFA500; box-shadow: 0 0 5px #FFA500; }
.color-dot.red { background-color: #FF0000; box-shadow: 0 0 5px #FF0000; }
