// init-app.js
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing StarCrypt...');
    
    // Initialize WebSocket connection
    function initWebSocket() {
        const ws = new WebSocket('ws://localhost:8080');
        
        ws.onopen = () => {
            console.log('WebSocket connected');
            // Request initial data
            ws.send(JSON.stringify({ type: 'subscribe', pair: 'xbtusdt' }));
        };
        
        ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                window.WebSocketProcessor.processMessage(message);
            } catch (error) {
                console.error('Error processing WebSocket message:', error);
            }
        };
        
        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
        
        ws.onclose = () => {
            console.log('WebSocket disconnected, reconnecting...');
            setTimeout(initWebSocket, 5000);
        };
        
        window.appWebSocket = ws;
    }
    
    // Initialize components
    function initComponents() {
        // Initialize any other components here
        console.log('Components initialized');
    }
    
    // Start the app
    initWebSocket();
    initComponents();
    
    console.log('StarCrypt initialized');
});