/**
 * Logger utility for StarCrypt
 * Provides centralized logging functionality with different log levels
 * and automatic console integration
 */

// Initialize logger state
let isInitialized = false
const MAX_LOG_ENTRIES = 1000

// Default log levels
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
  trace: 4,
}

// Current log level (default to 'info' in production)
let currentLogLevel = LOG_LEVELS.info

// Log storage
let logMessages = []

// Format a log message with timestamp and level
function formatMessage(level, message) {
  const timestamp = new Date().toISOString()
  return `[${timestamp}] [${level.toUpperCase()}] ${message}`
}

// Add a message to the log with safety checks
function addToLog(level, message) {
  try {
    // Validate log level
    if (typeof level !== 'string' || LOG_LEVELS[level] === undefined) {
      level = 'info' // Default to info for invalid levels
    }

    // Check if message should be logged based on current log level
    if (LOG_LEVELS[level] > currentLogLevel) {
      return false
    }

    // Ensure message is a string
    const safeMessage = typeof message === 'string' ? message : String(message)

    // Format the message
    const formattedMessage = formatMessage(level, safeMessage)

    // Add to log messages array
    if (Array.isArray(logMessages)) {
      logMessages.push(formattedMessage)

      // Keep log size under control
      if (logMessages.length > MAX_LOG_ENTRIES * 1.5) {
        logMessages = logMessages.slice(-MAX_LOG_ENTRIES)
      }
    }

    // Safely update the UI if the function exists
    try {
      if (typeof updateLoggerDisplay === 'function') {
        updateLoggerDisplay()
      }
    } catch (uiError) {
      // Silently fail UI updates to prevent breaking logging
    }

    return true
  } catch (error) {
    // If logging fails, at least log to console
    console.error('Logging failed:', error)
    return false
  }
}

// Set the log level
function setLogLevel(level) {
  if (LOG_LEVELS[level] !== undefined) {
    currentLogLevel = LOG_LEVELS[level]
    log('info', `Log level set to: ${level}`)
    return true
  }
  return false
}

// Main logging function
function log(level, ...args) {
  if (!isInitialized) {
    initializeLogger()
  }

  // Convert all arguments to strings
  const message = args.map(arg => {
    if (typeof arg === 'object' && arg !== null) {
      try {
        return JSON.stringify(arg, null, 2)
      } catch (e) {
        return String(arg)
      }
    }
    return String(arg)
  }).join(' ')

  // Add to our log
  const logged = addToLog(level, message)

  // Also log to console if available
  if (console && console[level]) {
    console[level](`[${level.toUpperCase()}]`, ...args)
  } else if (console && console.log) {
    console.log(`[${level.toUpperCase()}]`, ...args)
  }

  return logged
}

// Update the logger display in the UI with safety checks
function updateLoggerDisplay() {
  try {
    // Safely get the logger window element
    let loggerWindow
    try {
      loggerWindow = document.getElementById('loggerWindow')
    } catch (e) {
      // If we can't access document, just bail
      return
    }

    if (!loggerWindow) {
      return // No logger window found
    }

    try {
      // Safely get messages to display
      const messagesToShow = Array.isArray(logMessages) ?
        logMessages.slice(-50) :
        []

      // Join messages with line breaks
      const messagesHtml = messagesToShow.join('<br>')

      // Update the content if it has changed
      if (loggerWindow.innerHTML !== messagesHtml) {
        loggerWindow.innerHTML = messagesHtml
      }

      // Auto-scroll to bottom if needed
      if (loggerWindow.scrollHeight > loggerWindow.clientHeight) {
        loggerWindow.scrollTop = loggerWindow.scrollHeight
      }
    } catch (e) {
      // If updating the content fails, try a more basic approach
      try {
        loggerWindow.textContent = 'Log display error'
      } catch (e) {
        // If even that fails, give up silently
      }
    }
  } catch (e) {
    // Catch any unhandled errors to prevent breaking the application
    try {
      console.error('Error in updateLoggerDisplay:', e)
    } catch (consoleError) {
      // If console is not available, there's nothing we can do
    }
  }
}

// Initialize the logger with safety checks
function initializeLogger() {
  try {
    // Prevent re-initialization
    if (isInitialized) {
      return
    }

    // Mark as initialized early to prevent re-entry
    isInitialized = true

    // Handle any existing log messages
    if (window.logMessages && Array.isArray(window.logMessages)) {
      const legacyLogs = [...window.logMessages]
      window.logMessages = []

      // Process legacy logs with error handling
      legacyLogs.forEach(logMessage => {
        try {
          if (typeof logMessage === 'string') {
            let level = 'info'
            const message = logMessage.replace(
              /^\[.*?\]\s*\[?(WARN|ERROR|INFO|DEBUG|TRACE)\]?\s*/i,
              (match, p1) => {
                if (p1) level = p1.toLowerCase()
                return ''
              },
            )

            addToLog(level, message)
          }
        } catch (e) {
          // Silently handle any errors during legacy log processing
        }
      })
    }

    // Save original console methods
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug,
    }

    // Safely override console methods
    Object.keys(originalConsole).forEach(level => {
      try {
        const originalMethod = originalConsole[level]

        console[level] = function (...args) {
          try {
            // Call original console method first
            originalMethod.apply(console, args)

            // Safely log to our system
            if (level in LOG_LEVELS) {
              const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg),
              ).join(' ')

              addToLog(level, message)
            }
          } catch (e) {
            // If our logging fails, still ensure the original console works
            originalMethod.apply(console, args)
          }
        }
      } catch (e) {
        // If we can't override a console method, continue with others
      }
    })

    // Log successful initialization
    originalConsole.log('[INFO] Logger initialized successfully')
  } catch (error) {
    // If initialization fails completely, at least ensure console works
    console.error('Logger initialization failed:', error)
  }
}

// Export public API
window.Logger = {
  log: log.bind(null, 'info'),
  info: log.bind(null, 'info'),
  warn: log.bind(null, 'warn'),
  error: log.bind(null, 'error'),
  debug: log.bind(null, 'debug'),
  trace: log.bind(null, 'trace'),
  setLevel: setLogLevel,
  getLogs: () => [...logMessages],
  clear: () => { logMessages = [] },
  initialize: initializeLogger,
}

// Initialize the logger with a small delay to ensure the DOM is ready
// and to prevent any potential race conditions
const initLogger = () => {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(initializeLogger, 100)
    })
  } else {
    setTimeout(initializeLogger, 100)
  }
}

// Start the initialization
initLogger()
