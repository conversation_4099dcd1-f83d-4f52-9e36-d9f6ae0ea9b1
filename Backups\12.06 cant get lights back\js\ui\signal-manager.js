/**
 * Signal Manager - Centralized Signal Management
 * Handles all signal-related functionality including updates, rendering, and state management
 */

const SignalManager = (function() {
    // Private state
    let isInitialized = false;
    let isUpdating = false;
    let lastUpdateTime = 0;
    let updateTimer = null; // Track the update timer
    const signalState = new Map();
    const pendingUpdates = new Map();
    const elementCache = new Map();
    const tooltipCache = new Map();
    
    // Configuration
    const config = {
        updateDebounce: 50, // Reduced from 100ms for more responsive updates
        maxUpdateTime: 500, // Reduced from 1000ms to prevent UI freezing
        maxBatchSize: 5,    // Process signals in smaller batches
        signalColors: {
            'strong-buy': '#00FF00',
            'mild-buy': '#00AAFF',
            'neutral': '#808080',
            'mild-sell': '#FFA500',
            'strong-sell': '#FF0000',
            'error': '#FF00FF'
        },
        defaultTimeframes: ['1m', '5m', '15m', '1h', '4h', '1d', '1w'],
        batchSizes: {
            '1m': 3,   // More frequent updates for lower timeframes
            '5m': 3,
            '15m': 2,
            '1h': 2,
            '4h': 1,
            '1d': 1,
            '1w': 1
        }
    };

    /**
     * Initialize the signal manager
     */
    function init() {
        if (isInitialized) return;
        console.log('[SignalManager] Initializing...');
        
        // Initialize all signal elements with default values
        const signals = document.querySelectorAll('.signal-circle');
        signals.forEach(signal => {
            const indicator = signal.dataset.indicator || signal.dataset.ind;
            const timeframe = signal.dataset.timeframe || signal.dataset.tf;
            if (indicator && timeframe) {
                const key = `${indicator}:${timeframe}`;
                signalState.set(key, {
                    value: 0,
                    strength: 0.5,
                    timestamp: Date.now(),
                    status: 'neutral'
                });
                updateSignalElement(key, signalState.get(key));
            }
        });
        
        // Setup event listeners
        setupEventListeners();
        
        isInitialized = true;
        console.log('[SignalManager] Initialized');
        
        // Dispatch ready event
        const event = new CustomEvent('signalManager:ready', {
            detail: { signalManager: SignalManager }
        });
        document.dispatchEvent(event);
    }

    /**
     * Set up event listeners for signal interactions
     */
    function setupEventListeners() {
        // Handle signal clicks for more details
        document.addEventListener('click', handleSignalClick);
        
        // Handle indicator toggles
        document.addEventListener('change', handleIndicatorToggle);
        
        // Handle window resize for responsive updates
        window.addEventListener('resize', debounce(handleWindowResize, 100));
        
        // Handle tooltips
        const signals = document.querySelectorAll('.signal-circle');
        signals.forEach(signal => {
            // Remove existing event listeners to prevent duplicates
            signal.removeEventListener('mouseenter', handleSignalHover);
            signal.removeEventListener('mouseleave', handleSignalLeave);
            
            const tooltip = signal.querySelector('.signal-tooltip');
            if (tooltip) {
                // Remove existing tooltip from cache if it exists
                const existingTooltip = tooltipCache.get(signal);
                if (existingTooltip) {
                    existingTooltip.remove();
                }
                
                tooltipCache.set(signal, tooltip);
                signal.addEventListener('mouseenter', handleSignalHover);
                signal.addEventListener('mouseleave', handleSignalLeave);
            }
        });
    }

    /**
     * Handle signal click events
     */
    function handleSignalClick(event) {
        const signal = event.target.closest('.signal-circle');
        if (!signal) return;

        const indicator = signal.dataset.indicator || signal.dataset.ind;
        const timeframe = signal.dataset.timeframe || signal.dataset.tf;
        
        if (indicator && timeframe) {
            console.log(`[SignalManager] Signal clicked: ${indicator} (${timeframe})`);
            // Emit custom event for other components to handle
            const signalEvent = new CustomEvent('signalClick', {
                detail: { indicator, timeframe, element: signal }
            });
            document.dispatchEvent(signalEvent);
        }
    }

    function handleSignalHover(event) {
        const signal = event.target.closest('.signal-circle');
        const tooltip = tooltipCache.get(signal);
        if (tooltip) {
            // Ensure only one tooltip is visible at a time
            tooltipCache.forEach((t, s) => {
                if (s !== signal && t.style.display === 'block') {
                    t.style.display = 'none';
                }
            });
            
            tooltip.style.display = 'block';
            positionTooltip(signal, tooltip);
        }
    }

    function handleSignalLeave(event) {
        const signal = event.target.closest('.signal-circle');
        const tooltip = tooltipCache.get(signal);
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }

    function positionTooltip(signal, tooltip) {
        const rect = signal.getBoundingClientRect();
        tooltip.style.left = `${rect.left + rect.width / 2}px`;
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 10}px`;
    }

    /**
     * Handle indicator toggle events
     */
    function handleIndicatorToggle(event) {
        const toggle = event.target;
        if (!toggle.matches('.indicator-toggle, .menu-indicator-toggle')) return;
        
        const name = toggle.getAttribute('data-name') || toggle.getAttribute('data-indicator');
        const enabled = toggle.checked;
        
        if (name) {
            console.log(`[SignalManager] Toggling indicator: ${name} (${enabled ? 'enabled' : 'disabled'})`);
            // Emit custom event for other components to handle
            const toggleEvent = new CustomEvent('indicatorToggle', {
                detail: { name, enabled, element: toggle }
            });
            document.dispatchEvent(toggleEvent);
        }
    }

    /**
     * Handle window resize events
     */
    function handleWindowResize() {
        console.log('[SignalManager] Window resized, updating signal display');
        requestUpdate();
    }

    /**
     * Request an update of signal displays
     */
    function requestUpdate(indicator, timeframe) {
        try {
            const now = Date.now();
            const timeSinceLastUpdate = now - lastUpdateTime;
            
            // If we have specific indicator and timeframe, add to pending updates
            if (indicator && timeframe) {
                const key = `${indicator}:${timeframe}`;
                pendingUpdates.set(key, { indicator, timeframe });
            }
            
            // Don't update too frequently
            if (isUpdating || (timeSinceLastUpdate < config.updateDebounce && pendingUpdates.size < 5)) {
                return;
            }
            
            // Clear any pending update
            if (updateTimer) {
                clearTimeout(updateTimer);
            }
            
            // Process updates after debounce period
            updateTimer = setTimeout(() => {
                processUpdates();
            }, Math.min(100, config.updateDebounce));
        } catch (error) {
            console.error('Error in requestUpdate:', error);
        }
    }

    /**
     * Process pending signal updates
     */
    function processUpdates() {
        if (isUpdating || pendingUpdates.size === 0) {
            return;
        }
        
        isUpdating = true;
        const startTime = Date.now();
        let processedCount = 0;
        const batchSize = Math.min(config.maxBatchSize, pendingUpdates.size);
        
        try {
            // Clear any pending timer
            if (updateTimer) {
                clearTimeout(updateTimer);
                updateTimer = null;
            }
            // Process updates in batches
            const batch = [];
            const entries = Array.from(pendingUpdates.entries());
            
            // Sort updates by priority (timeframe)
            entries.sort((a, b) => {
                const tfA = a[1].timeframe;
                const tfB = b[1].timeframe;
                const idxA = config.defaultTimeframes.indexOf(tfA);
                const idxB = config.defaultTimeframes.indexOf(tfB);
                return idxA - idxB;
            });
            
            // Process highest priority updates first
            for (let i = 0; i < Math.min(batchSize, entries.length); i++) {
                const [key, { indicator, timeframe }] = entries[i];
                const data = signalState.get(key);
                
                if (data) {
                    updateSignalElement(key, data);
                    pendingUpdates.delete(key);
                    processedCount++;
                }
                
                // Don't block the UI for too long
                if (Date.now() - startTime > config.maxUpdateTime) {
                    break;
                }
            }
            
            console.log(`[SignalManager] Processed ${processedCount} signal updates (${pendingUpdates.size} remaining)`);
            
        } catch (error) {
            console.error('[SignalManager] Error processing updates:', error);
        } finally {
            isUpdating = false;
            lastUpdateTime = Date.now();
            
            // Schedule next batch if we have more updates
            if (pendingUpdates.size > 0) {
                setTimeout(processUpdates, 10);
            }
        }
    }

    /**
     * Update a single signal element
     */
    function updateSignalElement(key, data) {
        try {
            // Skip if no data
            if (!data) {
                console.warn(`[SignalManager] No data provided for signal update:`, key);
                return;
            }

            // Get or create the signal element
            let element = elementCache.get(key);
            if (!element) {
                const [indicator, timeframe] = key.split('_');
                const selector = `#${indicator}-${timeframe}`;
                element = document.querySelector(selector);
                
                if (!element) {
                    console.warn(`[SignalManager] Signal element not found:`, selector);
                    return;
                }
                
                // Cache the element
                elementCache.set(key, element);
                
                // Ensure element has the signal-circle class
                if (!element.classList.contains('signal-circle')) {
                    element.classList.add('signal-circle');
                }
            }
            
            // Determine signal class and strength
            const signalClass = getSignalClass(data.signal, data.strength || 0.5);
            
            // Use the global signal initializer if available
            if (window.signalInitializer) {
                window.signalInitializer.queueUpdate(element, signalClass, {
                    tooltip: data.tooltip || `${key}: ${signalClass}`,
                    strength: data.strength || 0.5,
                    timestamp: Date.now()
                });
            } else {
                // Fallback to direct update if signalInitializer is not available
                element.className = `signal-circle ${signalClass}`;
                element.style.backgroundColor = config.signalColors[signalClass] || config.signalColors.neutral;
                
                if (data.tooltip) {
                    element.title = data.tooltip;
                }
            }
            
            // Store the current state
            signalState.set(key, {
                ...data,
                lastUpdated: Date.now(),
                element: element,
                signalClass: signalClass
            });
            
        } catch (error) {
            console.error(`[SignalManager] Error updating signal element ${key}:`, error);
            
            // Try to recover by resetting the element
            try {
                const element = elementCache.get(key);
                if (element && window.signalInitializer) {
                    window.signalInitializer.resetSignal(element);
                }
            } catch (recoveryError) {
                console.error('[SignalManager] Error during recovery:', recoveryError);
            }
        }
    }

    /**
     * Get the CSS class for a signal based on its type and strength
     */
    function getSignalClass(signal, strength = 0.5) {
        const baseSignal = signal.toLowerCase();
        
        if (baseSignal.includes('buy')) {
            return strength > 0.6 ? 'strong-buy' : 'mild-buy';
        } else if (baseSignal.includes('sell')) {
            return strength > 0.6 ? 'strong-sell' : 'mild-sell';
        }
        
        return 'neutral';
    }

    /**
     * Update signal data
     */
    function updateSignal(indicator, timeframe, data) {
        if (!indicator || !timeframe) {
            console.warn('[SignalManager] Cannot update signal: missing indicator or timeframe');
            return false;
        }
        
        const key = `${indicator}::${timeframe}`;
        
        try {
            // Update signal state
            signalState.set(key, {
                ...data,
                updatedAt: Date.now()
            });
            
            // Request UI update
            requestUpdate(indicator, timeframe);
            return true;
            
        } catch (error) {
            console.error(`[SignalManager] Error updating signal ${key}:`, error);
            return false;
        }
    }

    /**
     * Batch update multiple signals
     */
    function updateSignals(signals) {
        let successCount = 0;
        
        for (const { indicator, timeframe, data } of signals) {
            if (updateSignal(indicator, timeframe, data)) {
                successCount++;
            }
        }
        
        console.log(`[SignalManager] Updated ${successCount}/${signals.length} signals`);
        return successCount;
    }

    /**
     * Get current signal state
     */
    function getSignalState(indicator, timeframe) {
        const key = `${indicator}::${timeframe}`;
        return signalState.get(key) || null;
    }

    /**
     * Helper function to debounce function calls
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Public API
    return {
        init,
        updateSignal,
        updateSignals,
        getSignalState,
        getSignalClass,
        requestUpdate,
        config: Object.freeze(config)
    };
})();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => SignalManager.init());
} else {
    SignalManager.init();
}

// Export for CommonJS/Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SignalManager;
}

// Export for browser global
window.SignalManager = SignalManager;