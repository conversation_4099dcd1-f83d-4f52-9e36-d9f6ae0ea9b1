(function () {
/**
 * Strategy Selector Module
 * Handles strategy selection and management for StarCrypt
 *
 * This module provides a clean, event-driven API for managing trading strategies
 * with proper state management and UI integration.
 */

  // Use the global TRADING_STRATEGIES defined in global-variables.js
  if (!window.TRADING_STRATEGIES) {
    console.error('Error: TRADING_STRATEGIES not defined in global-variables.js')
    window.TRADING_STRATEGIES = {} // Prevent further errors
  }

  // Default strategy is 'admiral_toa', defined globally or used as literal.

  /**
 * Strategy State Management
 * Handles the current strategy and notifies listeners of changes
 */
  const strategyState = {
    currentStrategy: null,
    isInitialized: false,
    eventListeners: new Map(),

    /**
   * Initialize the strategy state
   */
    init() {
      if (this.isInitialized) return

      // Load saved strategy or use default
      this.currentStrategy = localStorage.getItem('currentStrategy') || 'admiral_toa'

      // Validate the loaded strategy exists
      if (!TRADING_STRATEGIES[this.currentStrategy]) {
        console.warn(`Saved strategy ${this.currentStrategy} not found, using default`)
        this.currentStrategy = 'admiral_toa'
      }

      this.isInitialized = true
      console.log('[Strategy] Initialized with strategy:', this.currentStrategy)

      // Notify listeners that strategy is ready
      this.notifyStrategyChange(this.currentStrategy)
    },

    /**
   * Get the current strategy
   * @returns {string} The current strategy ID
   */
    getCurrentStrategy() {
      return this.currentStrategy
    },

    /**
   * Set the current strategy
   * @param {string} strategyId - The ID of the strategy to set
   * @returns {boolean} True if the strategy was changed, false otherwise
   */
    setStrategy(strategyId) {
      if (!TRADING_STRATEGIES[strategyId]) {
        console.error(`[Strategy] Invalid strategy: ${strategyId}`)
        return false
      }

      // Only update if different
      if (this.currentStrategy !== strategyId) {
        const previousStrategy = this.currentStrategy
        this.currentStrategy = strategyId

        // Save to localStorage
        localStorage.setItem('currentStrategy', strategyId)

        console.log(`[Strategy] Changed from ${previousStrategy} to ${strategyId}`)

        // Notify listeners
        this.notifyStrategyChange(strategyId, previousStrategy)
      }

      return true
    },

    /**
   * Add an event listener for strategy changes
   * @param {string} event - The event name
   * @param {Function} callback - The callback function
   */
    on(event, callback) {
      if (!this.eventListeners.has(event)) {
        this.eventListeners.set(event, new Set())
      }
      this.eventListeners.get(event).add(callback)
    },

    /**
   * Remove an event listener
   * @param {string} event - The event name
   * @param {Function} callback - The callback function to remove
   */
    off(event, callback) {
      if (this.eventListeners.has(event)) {
        this.eventListeners.get(event).delete(callback)
      }
    },

    /**
   * Notify all listeners of a strategy change
   * @param {string} newStrategy - The new strategy ID
   * @param {string} [oldStrategy] - The previous strategy ID
   * @private
   */
    notifyStrategyChange(newStrategy, oldStrategy) {
      if (!this.eventListeners.has('change')) return

      const event = {
        type: 'change',
        strategy: newStrategy,
        previousStrategy: oldStrategy,
        strategyInfo: TRADING_STRATEGIES[newStrategy] || {},
      }

      this.eventListeners.get('change').forEach(callback => {
        try {
          callback(event)
        } catch (error) {
          console.error('[Strategy] Error in strategy change listener:', error)
        }
      })
    },
  }

  // Initialize strategy state when the module loads
  strategyState.init()

  /**
 * Strategy Selector UI Component
 * Handles rendering and interaction for the strategy selection UI
 */
  class StrategySelectorUI {
    constructor() {
    // DOM Elements
      this.strategyMenu = document.getElementById('strategyMenu')
      this.strategyButton = document.getElementById('strategyButton')
      this.strategyList = this.strategyMenu?.querySelector('.strategy-list')
      this.strategyTitle = this.strategyButton?.querySelector('.strategy-title')
      this.strategyDesc = this.strategyMenu?.querySelector('.strategy-description')
      this.strategyHelper = this.strategyMenu?.querySelector('.strategy-helper')
      this.applyButton = this.strategyMenu?.querySelector('.apply-strategy')

      // State
      this.selectedStrategy = null
      this.isInitialized = false

      // Bind methods
      this.handleDocumentClick = this.handleDocumentClick.bind(this)
      this.handleStrategySelect = this.handleStrategySelect.bind(this)
      this.handleApplyClick = this.handleApplyClick.bind(this)
      this.onStrategyChanged = this.onStrategyChanged.bind(this)

      // Initialize
      this.initialize()
    }

    /**
   * Initialize the strategy selector UI
   */
    initialize() {
      if (this.isInitialized) return

      if (!this.strategyMenu || !this.strategyList) {
        console.warn('[StrategySelector] Required elements not found')
        return
      }

      try {
      // Render strategy list
        this.renderStrategyList()

        // Set initial strategy from state
        this.selectedStrategy = strategyState.getCurrentStrategy()
        this.updateSelectedStrategyUI()

        // Add event listeners
        this.strategyList.addEventListener('click', this.handleStrategySelect)

        if (this.applyButton) {
          this.applyButton.addEventListener('click', this.handleApplyClick)
        }

        // Listen for strategy changes from other components
        strategyState.on('change', this.onStrategyChanged)

        // Close menu when clicking outside
        document.addEventListener('click', this.handleDocumentClick, true)

        this.isInitialized = true
        console.log('[StrategySelector] Initialized')
      } catch (error) {
        console.error('[StrategySelector] Initialization error:', error)
      }
    }

    /**
   * Render the list of available strategies
   * @private
   */
    renderStrategyList() {
      if (!this.strategyList) return

      // Clear existing items
      this.strategyList.innerHTML = ''

      // Add strategy items
      Object.entries(TRADING_STRATEGIES).forEach(([id, strategy]) => {
        if (!strategy || !strategy.name) {
          console.warn(`[StrategySelector] Invalid strategy: ${id}`)
          return
        }

        const item = document.createElement('div')
        item.className = 'strategy-item'
        item.dataset.strategyId = id
        item.innerHTML = `
        <div class="strategy-name">${strategy.name}</div>
        <div class="strategy-short-desc">${strategy.shortDesc || ''}</div>
      `

        this.strategyList.appendChild(item)
      })
    }

    /**
   * Handle strategy selection from the UI
   * @param {Event} event - The click event
   * @private
   */
    handleStrategySelect(event) {
      const strategyItem = event.target.closest('.strategy-item')
      if (!strategyItem) return

      const strategyId = strategyItem.dataset.strategyId
      if (!strategyId || !TRADING_STRATEGIES[strategyId]) {
        console.warn(`[StrategySelector] Invalid strategy selected: ${strategyId}`)
        return
      }

      this.selectedStrategy = strategyId
      this.updateSelectedStrategyUI()
    }

    /**
   * Handle apply button click
   * @private
   */
    handleApplyClick() {
      if (!this.selectedStrategy) return

      // Set the selected strategy
      const success = strategyState.setStrategy(this.selectedStrategy)

      if (success) {
      // Close the menu
        this.strategyMenu?.classList.remove('active')

        // Show success feedback
        this.showFeedback('Strategy applied successfully!')
      } else {
        this.showFeedback('Failed to apply strategy', 'error')
      }
    }

    /**
   * Update the UI to reflect the selected strategy
   * @private
   */
    updateSelectedStrategyUI() {
      if (!this.selectedStrategy) return

      const strategy = TRADING_STRATEGIES[this.selectedStrategy]
      if (!strategy) return

      // Update button text
      if (this.strategyTitle) {
        this.strategyTitle.textContent = strategy.name
      }

      // Update description
      if (this.strategyDesc) {
        this.strategyDesc.textContent = strategy.description || 'No description available'
      }

      // Update helper text
      if (this.strategyHelper) {
        this.strategyHelper.textContent = strategy.helperText || 'Click to select a different strategy'
      }

      // Update selected state in the list
      const items = this.strategyList?.querySelectorAll('.strategy-item')
      items?.forEach(item => {
        item.classList.toggle('selected', item.dataset.strategyId === this.selectedStrategy)
      })
    }

    /**
   * Handle strategy changes from other components
   * @param {Object} event - The strategy change event
   * @private
   */
    onStrategyChanged(event) {
      if (!event || !event.strategy) return

      // Update UI if the strategy was changed externally
      if (this.selectedStrategy !== event.strategy) {
        this.selectedStrategy = event.strategy
        this.updateSelectedStrategyUI()
      }
    }

    /**
   * Show feedback message
   * @param {string} message - The message to show
   * @param {string} [type='success'] - The type of feedback (success, error, warning)
   * @private
   */
    showFeedback(message, type = 'success') {
      console.log(`[${type.toUpperCase()}] ${message}`)

      // Create feedback element
      const feedback = document.createElement('div')
      feedback.className = `strategy-feedback ${type}`
      feedback.textContent = message

      // Add to menu
      this.strategyMenu?.appendChild(feedback)

      // Remove after delay
      setTimeout(() => {
        feedback.remove()
      }, 3000)
    }

    /**
   * Handle clicks outside the strategy menu to close it
   * @param {MouseEvent} event - The click event
   * @private
   */
    handleDocumentClick(event) {
      if (!this.strategyMenu || !this.strategyButton) return

      const isClickInside = this.strategyMenu.contains(event.target) ||
                         this.strategyButton.contains(event.target)

      if (!isClickInside) {
        this.strategyMenu.classList.remove('active')
      }
    }

    /**
   * Clean up event listeners and resources
   */
    cleanup() {
      if (this.strategyList) {
        this.strategyList.removeEventListener('click', this.handleStrategySelect)
      }

      if (this.applyButton) {
        this.applyButton.removeEventListener('click', this.handleApplyClick)
      }

      document.removeEventListener('click', this.handleDocumentClick, true)
      strategyState.off('change', this.onStrategyChanged)

      this.isInitialized = false
    }
  }

  // Clean up any existing global functions to prevent conflicts
  const cleanupGlobals = () => {
    const oldFunctions = [
      'initializeStrategySelector',
      'toggleStrategyMenu',
      'updateStrategyDescription',
      'handleStrategyChange',
      'updateStrategyUI',
      'updateIndicatorsForStrategy',
      'getStrategySpecificIndicators',
      'notifyStrategyChange',
      'applySelectedStrategy',
      'initializeStrategySelectorModule',
      'showStatusMessage',
      'getCurrentStrategy',
      'updateIndicatorMenu',
      'createIndicatorCheckboxes',
      'createIndicatorMenu',
      'attachIndicatorMenuHandler',
      'initializeAll',
    ]

    oldFunctions.forEach(fn => {
      if (window[fn]) {
        delete window[fn]
      }
    })
  }

  // Clean up old globals when the script loads
  cleanupGlobals()

  // Export the new API
  window.StarCrypt = window.StarCrypt || {}
  window.StarCrypt.StrategySelector = {
    init: () => window.strategySelector?.initialize(),
    getCurrentStrategy: () => strategyState.getCurrentStrategy(),
    setStrategy: (strategyId) => strategyState.setStrategy(strategyId),
    on: (event, callback) => strategyState.on(event, callback),
    off: (event, callback) => strategyState.off(event, callback),
  }

  // Clean up event listeners on page unload
  window.addEventListener('beforeunload', () => {
    window.strategySelector?.cleanup()
  })

  // Initialize the strategy selector when the DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.strategySelector = new StrategySelectorUI()
    })
  } else {
    window.strategySelector = new StrategySelectorUI()
  }

  // Make the StrategySelectorUI and strategyState available globally
  window.StrategySelectorUI = StrategySelectorUI
  window.strategyState = strategyState
})()
