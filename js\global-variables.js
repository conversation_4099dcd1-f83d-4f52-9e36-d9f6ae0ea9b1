// StarCrypt Global Variables
// Single source of truth for all global variables

// Timeframes and timing
window.TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
window.LOW_TIMEFRAMES = ['1s', '5s', '15s', '1m', '4m', '24m', '168m']
window.TIMEFRAME_SECONDS = {
  '1s': 1, '5s': 5, '15s': 15, '1m': 60, '4m': 240, '24m': 1440, '168m': 10080,
  '5m': 300, '15m': 900, '1h': 3600, '4h': 14400, '1d': 86400, '1w': 604800,
}

// Current state - use let instead of const to allow reassignment
window.currentTf = '1h'
window.currentPair = 'xbtusdt'

// Load strategy from localStorage or default to 'admiral_toa'
window.currentStrategy = localStorage.getItem('lastSelectedStrategy') || 'admiral_toa'

// Wait for document to load, then ensure initial strategy is set properly
document.addEventListener('DOMContentLoaded', () => {
  // Set the initial strategy selector value from our global
  const mainStrategySelector = document.getElementById('strategySelector') ||
                              document.getElementById('mainStrategySelector')
  if (mainStrategySelector) {
    mainStrategySelector.value = window.currentStrategy
    // Trigger strategy change to ensure UI updates
    if (typeof window.handleStrategyChange === 'function') {
      window.handleStrategyChange({ target: mainStrategySelector })
    }
  }
})

// Indicators
window.INDICATORS = {
  momentum: ['rsi', 'stochRsi', 'williamsR', 'ultimateOscillator', 'mfi'],
  trend: ['macd', 'bollingerBands', 'adx', 'atr', 'vwap', 'fractal'],
  volume: ['volume'],
  ml: ['ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
}

// Flat list of all indicators for the signal matrix
window.MATRIX_INDICATORS = [
  'rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR',
  'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume',
  'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly',
]

// Indicator color schemes for consistent visual representation
window.INDICATOR_COLORS = {
  // Momentum indicators
  rsi: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  stochRsi: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  williamsR: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  ultimateOscillator: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  mfi: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },

  // Trend indicators
  macd: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  bollingerBands: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  adx: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  atr: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  vwap: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  fractal: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },

  // Volume indicator
  volume: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },

  // ML indicators
  ml: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  sentiment: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  entropy: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  correlation: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  time_anomaly: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
}

window.INDICATOR_DISPLAY_NAMES = {
  rsi: 'RSI',
  stochRsi: 'Stochastic RSI',
  macd: 'MACD',
  bollingerBands: 'Bollinger Bands',
  adx: 'ADX (Direction)',
  williamsR: 'Williams %R',
  ultimateOscillator: 'Ultimate Oscillator',
  mfi: 'Money Flow Index',
  volume: 'Volume Analysis',
  vwap: 'VWAP Guardian',
  atr: 'ATR (Volatility)',
  fractal: 'Fractal Pattern',
  ml: 'Machine Learning',
  sentiment: 'Sentiment Analysis',
  entropy: 'Market Entropy',
  correlation: 'Market Correlation',
  time_anomaly: 'TIME ANOMALY',
}

window.helperOrder = ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx']
window.logMessages = window.logMessages || []
window.enabledIndicators = [
  'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'adx', 'williamsR',
  'ultimateOscillator', 'mfi', 'vwap', 'fractal', 'volume', 'ml', 'sentiment',
  'entropy', 'correlation', 'time_anomaly',
]

// This is the one true definition of TRADING_STRATEGIES
// All other files should reference this, not redefine it
window.TRADING_STRATEGIES = {
  admiral_toa: {
    name: 'Admiral T.O.A. Convergence',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'The flagship strategy that combines momentum indicators (RSI, Stoch RSI) with trend confirmation (MACD), price position (Bollinger Bands), trend strength (ADX), volume analysis, and ML confirmation for high-confidence signals.',
    color: '#00FFFF',
  },
  neural_network_navigator: {
    name: 'AI Neural Network Navigator',
    indicators: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'Advanced AI strategy that uses neural networks to identify complex market patterns and predict price movements with high accuracy by combining traditional indicators with machine learning signals.',
    color: '#FF00FF',
  },
  momentum_blast: {
    name: 'Momentum Blast',
    indicators: ['rsi', 'stochRsi', 'macd', 'mfi', 'volume'],
    description: 'A more aggressive strategy focusing on momentum indicators with looser thresholds for more frequent trading signals.',
    color: '#FF0000',
  },
  tight_convergence: {
    name: 'Tight Convergence',
    indicators: ['bollingerBands', 'atr', 'adx', 'vwap', 'macd', 'volume'],
    description: 'Focuses on price consolidation patterns and breakouts from tight ranges using volatility and trend indicators for high-probability setups.',
    color: '#FFFF00',
  },
  scalping_sniper: {
    name: 'Scalping Sniper',
    indicators: ['rsi', 'macd', 'volume', 'bollingerBands', 'atr'],
    description: 'A rapid-fire strategy designed for capturing quick profits from short-term price movements using momentum and volatility indicators.',
    color: '#4B0082',
  },
  top_bottom_feeder: {
    name: 'Top/Bottom Feeder',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'mfi', 'volume', 'williamsR', 'ultimateOscillator'],
    description: 'Specialized for catching market extremes, focusing on deeply oversold or overbought conditions across multiple indicators.',
    color: '#FFA500',
  },
  trend_rider: {
    name: 'Trend Rider',
    indicators: ['adx', 'macd', 'rsi', 'williamsR', 'volume', 'vwap', 'atr'],
    description: 'Focuses on riding established trends by combining ADX for trend strength with momentum indicators for entry timing.',
    color: '#00FF00',
  },
  random_walk: {
    name: 'Random Walk Prototype',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'volume', 'williamsR'],
    description: 'An experimental strategy that combines technical analysis with random elements to identify potential market inefficiencies and sentiment shifts.',
    color: '#C0C0C0',
  },
  time_warp_scalper: {
    name: 'Time Warp Scalper',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'macd', 'williamsR', 'time_anomaly', 'volume'],
    description: 'A multi-timeframe scalping strategy that identifies temporary time-based anomalies for short-term trading opportunities.',
    color: '#800080',
  },
  ml_predictor: {
    name: 'ML Predictor',
    indicators: ['rsi', 'macd', 'ml', 'sentiment', 'correlation'],
    description: 'Uses machine learning algorithms to predict short-term price movements based on historical patterns and current market conditions.',
    color: '#1E90FF',
  },
  deep_learning_diver: {
    name: 'AI Deep Learning Diver',
    indicators: ['rsi', 'stochRsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
    description: 'Advanced AI strategy using deep learning neural networks to analyze market patterns across multiple timeframes and data sources.',
    color: '#4169E1',
  },
  ai_pattern_prophet: {
    name: 'AI Pattern Prophet',
    indicators: ['bollingerBands', 'macd', 'adx', 'ml', 'entropy', 'time_anomaly', 'fractal', 'volume'],
    description: 'Identifies recurring chart patterns and forecasts their completion probability using AI pattern recognition algorithms.',
    color: '#8A2BE2',
  },
  machine_learning_momentum: {
    name: 'AI ML Momentum Master',
    indicators: ['rsi', 'macd', 'williamsR', 'ml', 'volume', 'sentiment', 'correlation'],
    description: 'Enhanced momentum analysis powered by machine learning to identify the most statistically significant momentum signals.',
    color: '#9932CC',
  },
  sentiment_analysis_surfer: {
    name: 'AI Sentiment Analysis Surfer',
    indicators: ['rsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
    description: 'Combines social media sentiment analysis with technical indicators to identify potential market mood shifts.',
    color: '#BA55D3',
  },
  fractal_surge: {
    name: 'Fractal Surge',
    indicators: ['atr', 'macd', 'volume', 'fractal', 'bollingerBands', 'adx', 'rsi', 'stochRsi'],
    description: 'Focuses on fractal patterns and breakouts from established ranges for explosive moves.',
    color: '#FFFF00',
  },
  sentiment_blaster: {
    name: 'Sentiment Blaster',
    indicators: ['rsi', 'macd', 'volume', 'mfi', 'williamsR'],
    description: 'Analyzes market sentiment to identify extreme conditions for contrarian trading opportunities.',
    color: '#800080',
  },
  x_sentiment_blaster: {
    name: 'X Sentiment Blaster',
    indicators: ['rsi', 'macd', 'volume', 'mfi', 'williamsR', 'sentiment', 'ultimateOscillator'],
    description: 'AI-powered strategy that analyzes market sentiment through volume and momentum indicators, with confirmation from oscillators for sentiment-driven trades.',
    color: '#FF9800',
  },
  vwap_guardian: {
    name: 'VWAP Guardian',
    indicators: ['vwap', 'rsi', 'macd', 'volume', 'bollingerBands'],
    description: 'Uses Volume Weighted Average Price (VWAP) as a key reference point for identifying value zones and price deviations.',
    color: '#4682B4',
  },
  correlation_hunter: {
    name: 'Correlation Hunter',
    indicators: ['correlation', 'rsi', 'macd', 'volume', 'bollingerBands'],
    description: 'Analyzes correlations between different assets to identify divergences and potential reversals.',
    color: '#FF6347',
  },
  quantum_entropy: {
    name: 'Quantum Entropy',
    indicators: ['bollingerBands', 'atr', 'adx', 'williamsR', 'macd', 'volume'],
    description: 'Measures market chaos and volatility to identify potential breakouts from equilibrium states.',
    color: '#800000',
  },
  cross_asset_nebula: {
    name: 'Cross-Asset Nebula',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'atr'],
    description: 'Analyzes correlations between crypto and traditional markets to identify unique opportunities.',
    color: '#0000CD',
  },
}

// Define a function to ensure strategy switching updates all necessary states
window.switchStrategy = function (strategy) {
  if (!window.TRADING_STRATEGIES[strategy]) {
    console.error(`Invalid strategy: ${strategy}`)
    return false
  }

  window.currentStrategy = strategy
  console.log(`Strategy switched to: ${window.currentStrategy}`)

  // Update all strategy selectors in the UI
  const strategySelectors = document.querySelectorAll('#mainStrategySelector, .strategy-selector')
  strategySelectors.forEach(selector => {
    if (selector && selector.value !== strategy) {
      selector.value = strategy
    }
  })

  // Update UI and data as needed
  if (typeof updateSignalMatrix === 'function') {
    updateSignalMatrix()
  }

  // If we have updateStrategy function from WebSocket handler, use it
  if (typeof window.updateStrategy === 'function') {
    window.updateStrategy(strategy)
  } else {
    // Fallback if the WebSocket handler isn't loaded yet
    if (typeof ws !== 'undefined' && ws && ws.readyState === 1) {
      ws.send(JSON.stringify({
        type: 'strategy_change',
        strategy: window.currentStrategy,
      }))
    }
  }

  return true
}

console.log('Global variables initialized successfully')
