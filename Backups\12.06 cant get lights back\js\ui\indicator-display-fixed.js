/* =========================================================
   StarCrypt Indicator Display System - CLEAN IMPLEMENTATION
   This implementation fixes the following issues:
   1. Prevents [object Object] in the UI
   2. Ensures exactly 7 signal lights per indicator row
   3. <PERSON><PERSON><PERSON> handles neutral state (always gray, never blank)
   4. Prevents memory leaks and duplicate event handlers
   5. Optimizes performance with controlled refresh rates
 ========================================================= */

// Global tracking for initialization
window.indicatorsInitialized = false;

// Function to get proper signal color with 5-color logic
function getSignalColor(signal, strength = 0.5) {
  // Force neutral to be gray, never blank
  if (!signal || signal === 'neutral') {
    return '#808080'; // Medium gray for neutral
  }
  
  // 5-color logic implementation
  if (signal === 'buy') {
    return strength > 0.6 ? '#00FF00' : '#00AAFF'; // Strong vs mild buy
  } else if (signal === 'sell') {
    return strength > 0.6 ? '#FF0000' : '#FFA500'; // Strong vs mild sell
  }
  
  // Fallback
  return '#808080';
}

// Create signal lights for all indicators
function createAllIndicatorLights() {
  console.log('[IndicatorDisplay] Creating signal lights for all indicators');
  
  // Get all required indicators based on current strategy
  const indicators = getAllRequiredIndicators();
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
  
  // Process each indicator row
  indicators.forEach(indicator => {
    try {
      // Skip invalid indicators
      if (typeof indicator !== 'string') {
        console.error('[IndicatorDisplay] Invalid indicator:', indicator);
        return;
      }
      
      // Find or create the indicator row
      const row = getOrCreateIndicatorRow(indicator);
      if (!row) return;
      
      // Create signal lights cell
      let signalLightsCell = row.querySelector('.signal-lights-cell');
      if (!signalLightsCell) {
        signalLightsCell = document.createElement('td');
        signalLightsCell.className = 'signal-lights-cell';
        row.appendChild(signalLightsCell);
      } else {
        // Clear existing lights to prevent duplicates
        signalLightsCell.innerHTML = '';
      }
      
      // Create container for all lights
      const lightsContainer = document.createElement('div');
      lightsContainer.className = 'signal-lights-container';
      signalLightsCell.appendChild(lightsContainer);
      
      // Create 7 lights (one for each timeframe)
      timeframes.forEach(tf => {
        const light = document.createElement('div');
        // CRITICAL: use signal-circle class and grey-light instead of signal-light
        light.className = 'signal-circle grey-light';
        
        // CRITICAL: These data attributes are required for update-signal-lights.js to find the elements
        light.setAttribute('data-indicator', indicator);
        light.setAttribute('data-ind', indicator);  // Critical - this is what update-signal-lights.js looks for
        light.setAttribute('data-timeframe', tf);
        light.setAttribute('data-tf', tf);  // Critical - this is what update-signal-lights.js looks for
        
        light.id = `${indicator}-${tf}-signal`;
        light.setAttribute('data-tooltip', `${indicator.toUpperCase()} (${tf}): No data`);
        light.title = `${tf.toUpperCase()} Signal`;
        
        lightsContainer.appendChild(light);
      });
    } catch (error) {
      console.error(`[IndicatorDisplay] Error creating lights for ${indicator}:`, error);
    }
  });
}

// Create or get indicator row
function getOrCreateIndicatorRow(indicator) {
  // Find momentum table
  const momentumTable = document.querySelector('.momentum-table tbody');
  if (!momentumTable) {
    console.error('[IndicatorDisplay] Momentum table not found');
    return null;
  }
  
  // Look for existing row with proper data attribute
  let row = momentumTable.querySelector(`tr[data-indicator="${indicator}"]`);
  
  // Create new row if needed
  if (!row) {
    row = document.createElement('tr');
    row.setAttribute('data-indicator', indicator);
    row.setAttribute('data-ind', indicator); // Critical for updates
    row.id = `indicator-row-${indicator}`; // Add unique ID
    row.className = 'indicator-row signal-row'; // Important to include both classes
    
    // Create indicator name cell
    const nameCell = document.createElement('td');
    nameCell.className = 'indicator-name signal-name'; // Include both classes for compatibility
    nameCell.setAttribute('data-indicator', indicator); // Add data attribute to cell
    nameCell.textContent = indicator.toUpperCase();
    
    // Add mini-chart container
    const miniChartCell = document.createElement('td');
    miniChartCell.className = 'mini-chart-cell';
    
    // Create chart container
    const chartContainer = document.createElement('div');
    chartContainer.className = 'mini-chart-container';
    chartContainer.id = `${indicator}-chart-container`;
    miniChartCell.appendChild(chartContainer);
    
    // Add cells to row
    row.appendChild(nameCell);
    row.appendChild(miniChartCell);
    
    // Add row to table
    momentumTable.appendChild(row);
  }
  
  return row;
}

// Create mini charts for all indicators
function createAllMiniCharts() {
  console.log('[IndicatorDisplay] Creating mini charts for all indicators');
  
  // Get all required indicators
  const indicators = getAllRequiredIndicators();
  
  // Create charts with slight delays to avoid rendering issues
  let delay = 0;
  indicators.forEach(indicator => {
    setTimeout(() => {
      createMiniChart(indicator);
    }, delay);
    delay += 30; // Small delay between each chart creation
  });
}

// Create mini chart for a specific indicator
function createMiniChart(indicator) {
  try {
    // Find the chart container
    const chartContainer = document.getElementById(`${indicator}-chart-container`);
    if (!chartContainer) {
      console.warn(`[IndicatorDisplay] Chart container not found for ${indicator}`);
      return;
    }
    
    // Clear any existing content
    chartContainer.innerHTML = '';
    
    // Create canvas element
    const canvas = document.createElement('canvas');
    canvas.id = `${indicator}-chart`;
    canvas.width = 120;
    canvas.height = 40;
    chartContainer.appendChild(canvas);
    
    // Get canvas context
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error(`[IndicatorDisplay] Could not get context for ${indicator} chart`);
      return;
    }
    
    // Create data for chart
    const data = {
      labels: Array(24).fill(''),
      datasets: [{
        label: indicator.toUpperCase(),
        data: Array(24).fill(50), // Start with neutral values
        borderColor: 'rgba(128, 128, 128, 0.8)',
        backgroundColor: 'rgba(128, 128, 128, 0.2)',
        borderWidth: 1,
        pointRadius: 0,
        pointHoverRadius: 2
      }]
    };
    
    // Chart options
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      animation: false,
      plugins: {
        legend: { display: false },
        tooltip: { enabled: true }
      },
      scales: {
        x: { display: false },
        y: { 
          display: false,
          min: 0,
          max: 100
        }
      }
    };
    
    // Create chart
    const chart = new Chart(ctx, {
      type: 'line',
      data: data,
      options: options
    });
    
    // Store chart in global charts object
    window.indicatorCharts = window.indicatorCharts || {};
    window.indicatorCharts[indicator] = chart;
  } catch (error) {
    console.error(`[IndicatorDisplay] Error creating chart for ${indicator}:`, error);
  }
}

// Update all indicators
function updateAllIndicators() {
  try {
    // Skip if not initialized yet
    if (!window.indicatorsInitialized) return;
    
    console.log('[IndicatorDisplay] Updating all indicators');
    
    // Get indicators and timeframes
    const indicators = getAllRequiredIndicators();
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
    
    // Start with current timeframe for immediate feedback
    const currentTf = window.currentTf || '1h';
    indicators.forEach(indicator => {
      updateIndicator(indicator, currentTf);
    });
    
    // Then update other timeframes with delays
    let delay = 100;
    timeframes.forEach(tf => {
      if (tf === currentTf) return; // Skip current timeframe
      
      setTimeout(() => {
        indicators.forEach(indicator => {
          updateIndicator(indicator, tf);
        });
      }, delay);
      delay += 50;
    });
  } catch (error) {
    console.error('[IndicatorDisplay] Error updating indicators:', error);
  }
}

// Update a specific indicator for a specific timeframe
function updateIndicator(indicator, timeframe) {
  try {
    // Get signal data
    const data = getIndicatorData(indicator, timeframe);
    if (!data) return;
    
    // Update signal light
    updateSignalLight(indicator, timeframe, data);
    
    // Update chart if it's the current timeframe
    if (timeframe === window.currentTf) {
      updateChart(indicator, data);
    }
  } catch (error) {
    console.error(`[IndicatorDisplay] Error updating ${indicator} for ${timeframe}:`, error);
  }
}

// Get indicator data based on server response or simulation
function getIndicatorData(indicator, timeframe) {
  // Try to use real server data if available
  if (window.indicatorData && 
      window.indicatorData[timeframe] && 
      window.indicatorData[timeframe][indicator]) {
    return window.indicatorData[timeframe][indicator];
  }
  
  // Otherwise generate simulated data
  return {
    value: 50 + (Math.random() * 50 - 25), // 25-75 range
    signal: Math.random() > 0.7 ? (Math.random() > 0.5 ? 'buy' : 'sell') : 'neutral',
    strength: Math.random(),
    change: Math.random() * 10 - 5
  };
}

// Update signal light for an indicator - ensure compatibility with update-signal-lights.js
function updateSignalLight(indicator, timeframe, data) {
  // Find the signal light using selector that matches what update-signal-lights.js expects
  const light = document.querySelector(`.signal-circle[data-ind="${indicator}"][data-tf="${timeframe}"]`) ||
                document.getElementById(`${indicator}-${timeframe}-signal`);
  if (!light) return;
  
  // Make sure the light has the correct classes and data attributes
  light.classList.add('signal-circle'); // Ensure it has signal-circle class
  light.setAttribute('data-ind', indicator); // Critical for updates
  light.setAttribute('data-tf', timeframe); // Critical for updates
  
  // Calculate signal color
  const color = getSignalColor(data.signal, data.strength);
  
  // Set the appropriate color class
  light.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light');
  
  // Add the appropriate color class based on signal
  if (data.signal === 'buy' && data.strength > 0.6) {
    light.classList.add('green-light');
  } else if (data.signal === 'buy') {
    light.classList.add('blue-light');
  } else if (data.signal === 'sell' && data.strength > 0.6) {
    light.classList.add('red-light');
  } else if (data.signal === 'sell') {
    light.classList.add('orange-light');
  } else {
    light.classList.add('grey-light');
  }
  
  // Apply color as inline style as well for backwards compatibility
  light.style.backgroundColor = color || '#808080';
  
  // Safely format the value for display
  let displayValue = 'N/A';
  try {
    const numValue = parseFloat(data.value);
    displayValue = !isNaN(numValue) ? numValue.toFixed(1) : 'N/A';
  } catch (e) {
    console.warn(`Error formatting value for ${indicator}:`, data.value, e);
  }
  
  // Update tooltip with safe value
  const tooltipText = `${timeframe.toUpperCase()} ${indicator.toUpperCase()}: ${data.signal || 'N/A'} (${displayValue})`;
  light.setAttribute('data-tooltip', tooltipText);
  light.title = tooltipText;
  
  // Add pulse effect for strong signals
  if (data.strength > 0.7) {
    light.classList.add('pulse');
  } else {
    light.classList.remove('pulse');
  }
}

// Update chart for an indicator
function updateChart(indicator, data) {
  // Find chart
  const chart = window.indicatorCharts && window.indicatorCharts[indicator];
  if (!chart) return;
  
  // Update data
  chart.data.datasets[0].data.push(data.value);
  chart.data.datasets[0].data.shift();
  
  // Update color based on signal
  let borderColor, backgroundColor;
  if (data.signal === 'buy') {
    borderColor = 'rgba(0, 255, 0, 0.8)';
    backgroundColor = 'rgba(0, 255, 0, 0.2)';
  } else if (data.signal === 'sell') {
    borderColor = 'rgba(255, 0, 0, 0.8)';
    backgroundColor = 'rgba(255, 0, 0, 0.2)';
  } else {
    borderColor = 'rgba(128, 128, 128, 0.8)';
    backgroundColor = 'rgba(128, 128, 128, 0.2)';
  }
  
  chart.data.datasets[0].borderColor = borderColor;
  chart.data.datasets[0].backgroundColor = backgroundColor;
  
  // Update chart with animation: false for better performance
  chart.update();
}

// Get all required indicators for current strategy
function getAllRequiredIndicators() {
  try {
    // Get current strategy
    const strategy = window.currentStrategy || 'admiral_toa';
    
    // Get strategy details
    const strategyDetails = window.TRADING_STRATEGIES && window.TRADING_STRATEGIES[strategy];
    if (!strategyDetails) {
      console.warn(`[IndicatorDisplay] Strategy not found: ${strategy}`);
      return window.enabledIndicators || [];
    }
    
    // Return strategy indicators or fallback to default enabled indicators
    return strategyDetails.indicators || window.enabledIndicators || [];
  } catch (error) {
    console.error('[IndicatorDisplay] Error getting required indicators:', error);
    return [];
  }
}

// Cleanup existing intervals to prevent memory leaks
function cleanupExistingHandlers() {
  // Clear any existing indicator update intervals
  if (window.indicatorUpdateInterval) {
    clearInterval(window.indicatorUpdateInterval);
    window.indicatorUpdateInterval = null;
  }
  
  // Clear any existing chart instances
  if (window.indicatorCharts) {
    Object.values(window.indicatorCharts).forEach(chart => {
      try {
        chart.destroy();
      } catch (e) {
        // Ignore errors during cleanup
      }
    });
    window.indicatorCharts = {};
  }
}

// Initialize the indicator display system
function initializeIndicatorDisplay() {
  try {
    console.log('[IndicatorDisplay] Initializing indicator display system');
    
    // Clean up any existing handlers to prevent duplicates
    cleanupExistingHandlers();
    
    // Delay to ensure all necessary components are loaded
    setTimeout(() => {
      // Create indicator rows and signal lights
      createAllIndicatorLights();
      
      // Create mini charts
      createAllMiniCharts();
      
      // Set up update interval - store reference for cleanup
      window.indicatorUpdateInterval = setInterval(updateAllIndicators, 5000);
      
      // Mark as initialized
      window.indicatorsInitialized = true;
      
      // Do initial update
      updateAllIndicators();
      
      console.log('[IndicatorDisplay] Indicator display system initialized');
    }, 1000);
  } catch (error) {
    console.error('[IndicatorDisplay] Error initializing indicator display:', error);
  }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', initializeIndicatorDisplay);

// Export functions
window.updateAllIndicators = updateAllIndicators;
window.createAllIndicatorLights = createAllIndicatorLights;
window.createAllMiniCharts = createAllMiniCharts;
