// StarCrypt Enterprise - Main Application Controller
// Initializes and coordinates all UI components and data flow
if (!window.wsUrl) {
    window.wsUrl = window.location.hostname === 'localhost' 
        ? 'ws://localhost:8080' 
        : `wss://${window.location.host}`;
    console.log('WebSocket URL:', window.wsUrl);
}

// Wait for DOM to be fully loaded before initializing
document.addEventListener('DOMContentLoaded', async function() {
  console.log('StarCrypt Enterprise Initializing...');
  
  // Global variables are defined in global-variables.js
  if (!window.TRADING_STRATEGIES) {
    console.error('Error: TRADING_STRATEGIES not defined - check that global-variables.js is loaded');
  }
  
  // Set global variables and configurations
  window.currentPair = window.currentPair || 'xbtusdt';
  window.currentTf = window.currentTf || '1h';
  window.logMessages = window.logMessages || [];
  
  // Initialize UI components that don't depend on the strategy
  initializeBasicUI();
  
  // Wait for strategy manager to be ready
  try {
    const strategyManager = await waitForStrategyManager();
    if (strategyManager) {
      initializeStrategyDependentComponents(strategyManager);
    } else {
      console.warn('Strategy Manager not available, using default strategy');
      window.currentStrategy = 'admiral_toa';
      initializeFallbackStrategy();
    }
  } catch (error) {
    console.error('Error initializing strategy system:', error);
    window.currentStrategy = 'admiral_toa';
    initializeFallbackStrategy();
  }
  
  // Continue with the rest of the initialization
  initializeRemainingComponents();
});

/**
 * Wait for the strategy manager to be ready
 * @returns {Promise<Object|null>} The strategy manager or null if not available
 */
async function waitForStrategyManager() {
  // If strategy manager is already available, use it
  if (window.StarCrypt?.StrategyManager) {
    if (window.StarCrypt.StrategyManager.ready) {
      await new Promise(resolve => window.StarCrypt.StrategyManager.ready(resolve));
    }
    return window.StarCrypt.StrategyManager;
  }
  
  // If not available yet, wait a bit and try again
  return new Promise((resolve) => {
    const checkInterval = setInterval(() => {
      if (window.StarCrypt?.StrategyManager) {
        clearInterval(checkInterval);
        if (window.StarCrypt.StrategyManager.ready) {
          window.StarCrypt.StrategyManager.ready(() => resolve(window.StarCrypt.StrategyManager));
        } else {
          resolve(window.StarCrypt.StrategyManager);
        }
      }
    }, 100);
    
    // Timeout after 5 seconds
    setTimeout(() => {
      clearInterval(checkInterval);
      console.warn('Timeout waiting for strategy manager');
      resolve(null);
    }, 5000);
  });
}

/**
 * Initialize components that depend on the strategy manager
 * @param {Object} strategyManager - The strategy manager instance
 */
function initializeStrategyDependentComponents(strategyManager) {
  console.log('Strategy Manager initialized');
  
  // Get current strategy from manager
  const currentStrategy = strategyManager.getCurrentStrategy();
  if (currentStrategy && window.TRADING_STRATEGIES?.[currentStrategy]) {
    window.currentStrategy = currentStrategy;
    console.log('Current strategy set to:', currentStrategy);
  } else {
    console.warn('Invalid current strategy, using default');
    window.currentStrategy = 'admiral_toa';
    strategyManager.setStrategy('admiral_toa');
  }
  
  // Listen for strategy changes
  strategyManager.on('change', (event) => {
    console.log('Strategy changed to:', event.strategy);
    window.currentStrategy = event.strategy;
    
    // Update UI components that depend on strategy
    if (typeof updateStrategyInfoPanel === 'function') {
      updateStrategyInfoPanel(event.strategy);
    }
    
    // Update any other components that depend on strategy
    if (window.SignalManager) {
      window.SignalManager.onStrategyChange(event.strategy);
    }
  });
  
  // Initialize strategy UI components
  initializeStrategyUI(strategyManager);
}

/**
 * Initialize basic UI components that don't depend on the strategy
 */
function initializeBasicUI() {
  // Add any basic UI initialization here
  console.log('Initializing basic UI components');
}

/**
 * Initialize fallback strategy when strategy manager is not available
 */
function initializeFallbackStrategy() {
  console.warn('Using fallback strategy initialization');
  // Initialize any fallback UI components
  initializeStrategyUI(null);
}

/**
 * Initialize the rest of the application components
 */
function initializeRemainingComponents() {
  console.log('Initializing remaining components');
  // Add any remaining initialization code here
}

/**
 * Initialize strategy-related UI components
 * @param {Object|null} strategyManager - The strategy manager or null if not available
 */
function initializeStrategyUI(strategyManager) {
  // Add strategy UI initialization code here
  console.log('Initializing strategy UI');
  
  // Set enabled indicators based on current strategy
  window.enabledIndicators = [
    'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'adx', 
    'williamsR', 'ultimateOscillator', 'mfi', 'vwap', 'fractal', 
    'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'
  ];
  
  // Initialize strategy selection UI
  initializeStrategySelection(strategyManager);
}

/**
 * Initialize strategy selection UI components
 * @param {Object|null} strategyManager - The strategy manager or null if not available
 */
function initializeStrategySelection(strategyManager) {
  const strategySelect = document.getElementById('mainStrategySelector');
  const applyStrategyBtn = document.getElementById('applyStrategyBtn');
  
  if (!strategySelect || !applyStrategyBtn) {
    console.warn('Strategy selection elements not found');
    return;
  }
  
  // Function to handle strategy application
  const applyStrategy = () => {
    if (!strategySelect) return;
    
    const selectedStrategy = strategySelect.value;
    if (!selectedStrategy || selectedStrategy === window.currentStrategy) {
      return; // No change or invalid selection
    }
    
    console.log('Applying strategy:', selectedStrategy);
    
    try {
      // Use strategy manager if available, otherwise fallback to direct assignment
      if (strategyManager) {
        // The strategy manager will handle all the updates
        strategyManager.setStrategy(selectedStrategy);
      } else {
        // Fallback implementation if strategy manager is not available
        const previousStrategy = window.currentStrategy;
        window.currentStrategy = selectedStrategy;
        
        // Save to localStorage
        try {
          localStorage.setItem('currentStrategy', selectedStrategy);
        } catch (e) {
          console.warn('Could not save strategy to localStorage:', e);
        }
        
        // Update enabled indicators based on the new strategy
        const strategy = window.TRADING_STRATEGIES[selectedStrategy];
        if (strategy && Array.isArray(strategy.indicators)) {
          window.enabledIndicators = [...strategy.indicators];
          
          // Update the signal matrix
          if (typeof window.updateSignalMatrix === 'function') {
            window.updateSignalMatrix();
          }
          
          // Update any other components that depend on the strategy
          if (typeof window.onStrategyChanged === 'function') {
            window.onStrategyChanged(selectedStrategy, previousStrategy);
          }
        }
      }
      
      // Show success message
      const statusEl = document.getElementById('strategyStatus');
      if (statusEl) {
        statusEl.textContent = `Strategy updated to: ${selectedStrategy}`;
        statusEl.className = 'status success';
        setTimeout(() => {
          statusEl.textContent = '';
          statusEl.className = 'status';
        }, 3000);
      }
      
    } catch (error) {
      console.error('Error applying strategy:', error);
      
      // Revert the select to previous value
      if (strategySelect) {
        strategySelect.value = window.currentStrategy;
      }
      
      // Show error message
      const statusEl = document.getElementById('strategyStatus');
      if (statusEl) {
        statusEl.textContent = `Failed to update strategy: ${error.message || 'Unknown error'}`;
        statusEl.className = 'status error';
        setTimeout(() => {
          statusEl.textContent = '';
          statusEl.className = 'status';
        }, 5000);
      }
    }
  };
  
  // Set up event listeners
  if (strategySelect) {
    // Preview strategy on change
    strategySelect.addEventListener('change', (event) => {
      const selectedStrategy = event.target.value;
      console.log('Strategy preview:', selectedStrategy);
      
      // Update strategy info panel with the selected strategy details
      if (typeof window.updateStrategyInfoPanel === 'function') {
        window.updateStrategyInfoPanel(selectedStrategy);
      }
    });
    
    // Apply strategy on button click or Enter key
    const handleApply = () => {
      applyStrategy();
      return false;
    };
    
    if (applyStrategyBtn) {
      applyStrategyBtn.addEventListener('click', handleApply);
    }
    
    strategySelect.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        handleApply();
      }
    });
    
    // Set initial strategy from localStorage or default
    const savedStrategy = localStorage.getItem('currentStrategy');
    if (savedStrategy && window.TRADING_STRATEGIES[savedStrategy]) {
      window.currentStrategy = savedStrategy;
      strategySelect.value = savedStrategy;
      
      // Update UI to reflect the current strategy
      if (typeof window.updateStrategyInfoPanel === 'function') {
        window.updateStrategyInfoPanel(savedStrategy);
      }
    } else if (strategySelect.value !== window.currentStrategy) {
      strategySelect.value = window.currentStrategy;
    }
  }
  
  // Initialize signal matrix
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
  if (typeof window.createSignalMatrix === 'function') {
    if (typeof window.isSignalMatrixInitialized === 'undefined') {
      window.isSignalMatrixInitialized = true;
      window.createSignalMatrix('signalMatrixContainer', timeframes, window.enabledIndicators);
      console.log('Signal matrix initialized');
    } else {
      console.log('Signal matrix already initialized, skipping to avoid duplicates.');
    }
  } else {
    console.error('Signal matrix initialization function not found');
  }
  
  // Initialize strategy selector
  if (typeof window.initializeStrategySelector === 'function') {
    window.initializeStrategySelector();
    console.log('Strategy selector initialized');
  }
  
  // Initialize all event handlers
  if (typeof window.initializeEventHandlers === 'function') {
    window.initializeEventHandlers();
    console.log('Event handlers initialized');
  }
  
  // Initialize animations
  if (typeof window.initializeAnimations === 'function') {
    window.initializeAnimations();
    console.log('Animations initialized');
  }
  
  // Initialize charts
  if (typeof window.initializeCharts === 'function') {
    window.initializeCharts();
    console.log('Charts initialized');
  }
  
  // Initialize signal lights
  if (typeof window.updateAllSignalLights === 'function') {
    window.updateAllSignalLights();
    console.log('Signal lights initialized');
  }
  
  // Connect to WebSocket server
  // This happens automatically through websocket-init.js
  
  // Show initialization complete message
  console.log('StarCrypt Enterprise Initialized Successfully');
  
  // Hide loading overlay once everything is ready
  const loadingOverlay = document.getElementById('loading-overlay');
  if (loadingOverlay) {
    setTimeout(() => {
      loadingOverlay.style.opacity = '0';
      setTimeout(() => {
        loadingOverlay.style.display = 'none';
      }, 1000);
    }, 500);
  }
} // End of initializeRemainingComponents function

/**
 * Initialize strategy-related UI components
 * @param {Object|null} strategyManager - The strategy manager or null if not available
 */
function initializeStrategyUI(strategyManager) {
  // Add strategy UI initialization code here
  console.log('Initializing strategy UI');
  
  // Set enabled indicators based on current strategy
  window.enabledIndicators = [
    'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'adx', 
    'williamsR', 'ultimateOscillator', 'mfi', 'vwap', 'fractal', 
    'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'
  ];
  
  // Initialize strategy selection UI
  initializeStrategySelection(strategyManager);
}

// Global error handler to catch and log uncaught exceptions with descriptive details
window.onerror = function(message, source, lineno, colno, error) {
  console.error(`Global error: ${message} at ${source}:${lineno}:${colno}`, error);
  // Optionally, you can add more handling here, e.g., sending to server or UI notification
  return true; // Prevent default browser error handling
};
// Add this to your main.js
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error || event.message, event);
  return false;
});

// Add this to your WebSocket error handler
if (window.ws) {
  window.ws.onerror = function(error) {
      console.error('WebSocket error:', error);
  };
}