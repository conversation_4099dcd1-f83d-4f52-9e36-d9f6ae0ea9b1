// Initialize application components

document.addEventListener('DOMContentLoaded', () => {
    console.log('[Init] Starting application initialization...');
    
    // 1. Initialize configuration
    initConfig();
    
    // 2. Initialize WebSocket
    initWebSocket();
    
    // 3. Initialize UI components
    initUI();
    
    // 4. Initialize signal system
    initSignalSystem();
    
    console.log('[Init] Application initialization complete');
});

function initConfig() {
    console.log('[Init] Loading configuration...');
    // Add any config loading logic here
    
    // Example configuration
    window.config = {
        useLowTimeframes: false,
        TIMEFRAMES: ['1m', '5m', '15m', '1h', '4h', '1d'],
        LOW_TIMEFRAMES: ['1m', '5m', '15m'],
        indicatorsData: {},
        currentTf: '1h'
    };
}

function initWebSocket() {
    console.log('[Init] Initializing WebSocket...');
    window.wsCore = new WebSocketCore({
        url: `ws://${window.location.host}/ws`,
        onOpenCallback: () => {
            console.log('[WebSocket] Connected, initializing data...');
            requestInitialData();
        },
        onMessageCallback: (message) => {
            handleWebSocketMessage(message);
        },
        onErrorCallback: (error) => {
            console.error('[WebSocket] Error:', error);
        },
        onCloseCallback: (event) => {
            console.log(`[WebSocket] Disconnected: ${event.code} ${event.reason}`);
        }
    });
}

function initUI() {
    console.log('[Init] Initializing UI components...');
    
    // Initialize any UI components
    if (window.SignalSystem) {
        window.SignalSystem.init();
    }
    
    // Initialize signal lights
    if (typeof initializeSignalLights === 'function') {
        initializeSignalLights();
    }
}

function initSignalSystem() {
    console.log('[Init] Initializing signal system...');
    if (window.SignalSystem) {
        window.SignalSystem.init();
    }
}

function requestInitialData() {
    console.log('[Init] Requesting initial data...');
    // Example: wsCore.send({ type: 'subscribe', channels: ['ticker', 'trades'] });
}

function handleWebSocketMessage(message) {
    console.log('[WebSocket] Received message:', message.type);
    
    if (message.type === 'signal_update') {
        if (window.SignalSystem && typeof window.SignalSystem.processSignalUpdate === 'function') {
            window.SignalSystem.processSignalUpdate(message.data);
        }
    }
    // Add other message handlers as needed
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initConfig,
        initWebSocket,
        initUI,
        initSignalSystem,
        requestInitialData,
        handleWebSocketMessage
    };
}
