<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signal Management Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        
        h1, h2 {
            color: #4CAF50;
            text-align: center;
        }
        
        .test-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .test-panel {
            background-color: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .signal-display {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .signal-light {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .signal-strong-buy { background-color: #00C853; }
        .signal-buy { background-color: #69F0AE; color: #000; }
        .signal-neutral { background-color: #9E9E9E; }
        .signal-sell { background-color: #FF8A80; color: #000; }
        .signal-strong-sell { background-color: #FF3D00; }
        .signal-error { background-color: #9C27B0; }
        
        .control-panel {
            margin-top: 20px;
            padding: 15px;
            background-color: #333;
            border-radius: 5px;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .log {
            margin-top: 20px;
            padding: 10px;
            background-color: #222;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 3px 5px;
            border-bottom: 1px solid #444;
        }
        
        .log-entry.info { color: #4CAF50; }
        .log-entry.warn { color: #FFC107; }
        .log-entry.error { color: #F44336; }
    </style>
</head>
<body>
    <h1>Signal Management System Test</h1>
    
    <div class="test-container">
        <div class="test-panel">
            <h2>Signal Lights</h2>
            <div class="signal-display" id="signalDisplay">
                <!-- Signal lights will be added here by JavaScript -->
            </div>
            
            <div class="control-panel">
                <h3>Test Controls</h3>
                <div>
                    <button onclick="testSignal('RSI', '1m', 'buy', 0.8)">Buy Signal (RSI 1m)</button>
                    <button onclick="testSignal('MACD', '5m', 'strong-buy', 0.9)">Strong Buy (MACD 5m)</button>
                    <button onclick="testSignal('BB', '15m', 'neutral', 0.5)">Neutral (BB 15m)</button>
                    <button onclick="testSignal('Stoch', '1h', 'sell', 0.7)">Sell (Stoch 1h)</button>
                    <button onclick="testSignal('ADX', '4h', 'strong-sell', 0.9)">Strong Sell (ADX 4h)</button>
                    <button onclick="testError()">Test Error</button>
                </div>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>Signal Matrix</h2>
            <div id="signalMatrix">
                <!-- Signal matrix will be rendered here -->
                <p>Signal matrix will be displayed here when initialized</p>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>Event Log</h2>
            <div class="log" id="eventLog">
                <div class="log-entry info">System initialized. Waiting for events...</div>
            </div>
            <div style="margin-top: 10px;">
                <button onclick="clearLog()">Clear Log</button>
                <button onclick="toggleDebug()">Toggle Debug Mode</button>
            </div>
        </div>
    </div>
    
    <script>
        // Debug mode flag
        let debugMode = false;
        
        // Log function for the test page
        function log(message, type = 'info') {
            const logElement = document.getElementById('eventLog');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toISOString()}] ${message}`;
            logElement.prepend(entry);
            
            // Keep log to a reasonable size
            if (logElement.children.length > 100) {
                logElement.removeChild(logElement.lastChild);
            }
        }
        
        // Clear the log
        function clearLog() {
            document.getElementById('eventLog').innerHTML = 
                '<div class="log-entry info">Log cleared</div>';
        }
        
        // Toggle debug mode
        function toggleDebug() {
            debugMode = !debugMode;
            window.DEBUG_MODE = debugMode;
            log(`Debug mode ${debugMode ? 'enabled' : 'disabled'}`, 'info');
            
            // Update button text
            const button = document.querySelector('button[onclick="toggleDebug()"]');
            button.textContent = debugMode ? 'Disable Debug' : 'Enable Debug';
            
            // Set debug mode on signal manager if available
            if (window.StarCrypt?.SignalManager?.setDebug) {
                window.StarCrypt.SignalManager.setDebug(debugMode);
            }
        }
        
        // Test signal generation
        function testSignal(indicator, timeframe, signal, strength) {
            const signalData = {
                indicator,
                timeframe,
                signal,
                strength,
                timestamp: Date.now(),
                value: (Math.random() * 100).toFixed(2),
                previousValue: (Math.random() * 100).toFixed(2)
            };
            
            log(`Generated ${signal} signal for ${indicator} ${timeframe} (strength: ${strength})`, 'info');
            
            // Dispatch a custom event that the WebSocket processor would normally handle
            const event = new CustomEvent('websocket:message', {
                detail: {
                    type: 'signal_update',
                    data: signalData
                }
            });
            document.dispatchEvent(event);
        }
        
        // Test error handling
        function testError() {
            log('Testing error handling...', 'warn');
            
            // Dispatch an invalid signal to test error handling
            const event = new CustomEvent('websocket:message', {
                detail: {
                    type: 'signal_update',
                    data: { invalid: 'data' } // Missing required fields
                }
            });
            document.dispatchEvent(event);
        }
        
        // Initialize the test page
        document.addEventListener('DOMContentLoaded', () => {
            // Listen for signal manager events
            document.addEventListener('signalManager:ready', (event) => {
                log('Signal Manager is ready!', 'info');
                
                // Enable debug mode if needed
                if (debugMode) {
                    event.detail.signalManager.setDebug(true);
                }
            });
            
            document.addEventListener('signalManager:error', (event) => {
                log(`Error in Signal Manager: ${event.detail.error?.message || 'Unknown error'}`, 'error');
            });
            
            // Listen for signal updates
            document.addEventListener('signal-updated', (event) => {
                const { indicator, timeframe, signal, strength } = event.detail;
                log(`Signal updated: ${indicator} ${timeframe} - ${signal} (${strength})`, 'info');
            });
            
            // Simulate WebSocket messages
            document.addEventListener('websocket:message', (event) => {
                if (!window.StarCrypt?.SignalManager) {
                    log('Signal Manager not available yet', 'warn');
                    return;
                }
                
                const { type, data } = event.detail;
                
                if (type === 'signal_update') {
                    window.StarCrypt.SignalManager.updateSignal(data);
                }
            });
            
            // Create some test signal lights
            const signals = [
                { id: 'rsi_1m', indicator: 'RSI', timeframe: '1m' },
                { id: 'macd_5m', indicator: 'MACD', timeframe: '5m' },
                { id: 'bb_15m', indicator: 'Bollinger Bands', timeframe: '15m' },
                { id: 'stoch_1h', indicator: 'Stochastic', timeframe: '1h' },
                { id: 'adx_4h', indicator: 'ADX', timeframe: '4h' }
            ];
            
            const container = document.getElementById('signalDisplay');
            signals.forEach(signal => {
                const div = document.createElement('div');
                div.id = signal.id;
                div.className = 'signal-light signal-neutral';
                div.dataset.indicator = signal.indicator;
                div.dataset.timeframe = signal.timeframe;
                div.title = `${signal.indicator} (${signal.timeframe}) - No signal`;
                container.appendChild(div);
                
                // Add label
                const label = document.createElement('div');
                label.textContent = `${signal.indicator} (${signal.timeframe})`;
                label.style.fontSize = '12px';
                label.style.textAlign = 'center';
                label.style.width = '100%';
                container.appendChild(label);
            });
            
            log('Test page initialized. Click buttons to test signals.', 'info');
        });
    </script>
    
    <!-- Include the actual signal manager and dependencies -->
    <script src="js/signal-lights-manager.js"></script>
    <script src="js/init-signal-manager.js"></script>
</body>
</html>
