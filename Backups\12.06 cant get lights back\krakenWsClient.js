const WebSocket = require('ws');
const EventEmitter = require('events');
const KRAKEN_WS_URL = 'wss://ws.kraken.com/v2';

const INITIAL_RECONNECT_DELAY_MS = 1000;
const MAX_RECONNECT_DELAY_MS = 60000;
const RECONNECT_MULTIPLIER = 2;

class KrakenWsClient extends EventEmitter {
    constructor(defaultPairs = []) {
        super(); // Call EventEmitter constructor
        this.ws = null;
        this.subscriptions = new Map(); // To track subscriptions {channel: [symbols]}
        this.activePairs = new Set(); // To track pairs we need data for
        // Placeholder for OHLC data aggregators
        // this.ohlcAggregators = {}; // e.g., { 'BTC/USD': { '1m': new OHLC Aggregator(), '5m': ... }}
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.heartbeatIntervalId = null;
        this.subscribedPairs = new Set();
        this.defaultPairs = defaultPairs; // Use passed default pairs
        this.activeSubscriptions = new Map();
    }

    connect() {
        console.log(`Connecting to Kraken WebSocket: ${KRAKEN_WS_URL}`);
        this.ws = new WebSocket(KRAKEN_WS_URL);

        this.ws.onopen = () => {
            console.log('[Kraken WS Client] WebSocket connection established.');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.emit('open'); // Emit open event for other parts of the system if needed

            console.log('[Kraken WS Client] Attempting to subscribe/resubscribe to channels...');
            
            // Resubscribe to pairs that were previously explicitly added and might have been lost on reconnect
            this.subscribedPairs.forEach(pair => {
                console.log(`[Kraken WS Client] Resubscribing to existing pair: ${pair}`);
                this.subscribeToPair(pair); 
            });

            // Add default pairs if not already subscribed (e.g., on initial connect)
            this.defaultPairs.forEach(pair => {
                if (!this.subscribedPairs.has(pair)) {
                    console.log(`[Kraken WS Client] Adding default pair subscription: ${pair}`);
                    this.addPairSubscription(pair); // This calls subscribeToPair internally
                }
            });

            // Start heartbeat to keep connection alive if not already running
            if (this.heartbeatIntervalId === null) {
                console.log('[Kraken WS Client] Starting heartbeat.');
                this.startHeartbeat();
            } else {
                console.log('[Kraken WS Client] Heartbeat already running.');
            }
        };

        this.ws.on('message', (data) => {
            this._handleMessage(data);
        });

        this.ws.on('error', (error) => {
            console.error('Kraken WebSocket error:', error.message);
        });

        this.ws.on('close', (code, reason) => {
            console.log(`Kraken WebSocket connection closed. Code: ${code}, Reason: ${reason ? reason.toString() : 'N/A'}`);
            this.ws = null;
            // Implement reconnection logic, e.g., after a delay
            this.reconnectAttempts++;
            let delay = Math.min(INITIAL_RECONNECT_DELAY_MS * Math.pow(RECONNECT_MULTIPLIER, this.reconnectAttempts), MAX_RECONNECT_DELAY_MS);
            delay += Math.floor(Math.random() * 1000); // Add jitter
            console.log(`[Kraken WS Client] Reconnection attempt ${this.reconnectAttempts}. Waiting ${delay}ms before retrying...`);
            setTimeout(() => this.connect(), delay);
        });
    }

    _handleMessage(jsonData) {
        let message;
        try {
            message = JSON.parse(jsonData.toString());
        } catch (e) {
            console.error('Error parsing Kraken WebSocket message:', e, jsonData.toString());
            return;
        }

        // console.log('Received from Kraken:', JSON.stringify(message, null, 2)); // Verbose logging

        if (message.method === 'subscribe') {
            if (message.success) {
                console.log(`Successfully subscribed to Kraken channel: ${message.result.channel} for ${message.result.symbol || ''}`);
            } else {
                console.error(`Failed to subscribe to Kraken channel:`, message.error);
            }
            return;
        }

        if (message.channel === 'heartbeat') {
            // console.log('Kraken WebSocket heartbeat received.');
            return;
        }
        
        // TODO: Handle 'trade' messages for OHLC aggregation
        // TODO: Handle 'ticker' messages
        // TODO: Implement OHLC aggregation from trades

        if (message.channel === 'trade' && message.data) {
            // message.data is an array of trade objects
            // console.log(`Kraken trade data for ${message.data[0]?.symbol}:`, message.data);
            this.emit('kraken_trade', { symbol: message.data[0]?.symbol, trades: message.data });
        }

        if (message.channel === 'ticker' && message.data) {
            // message.data is an array with a single ticker object
            // console.log(`Kraken ticker data for ${message.data[0]?.symbol}:`, message.data[0]);
            this.emit('kraken_ticker', { symbol: message.data[0]?.symbol, ticker: message.data[0] });
        }
    }

    subscribeToPair(pair) {
        console.log(`[Kraken WS Client] Entered subscribeToPair for symbol: ${pair}. WS state: ${this.ws ? this.ws.readyState : 'null'}, WebSocket Object: ${this.ws ? 'Exists' : 'Does NOT Exist'}`);
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            console.log(`[Kraken WS Client] WebSocket not connected. Will subscribe once connected for pair: ${pair}`);
            return;
        }
        const subMsg = {
            method: 'subscribe',
            params: {
                channel: 'trade',
                symbol: [pair] // Symbol must be an array for Kraken WS v2
                // snapshot: true // true by default
            }
            // req_id: can be added for tracking
        };
        const msgString = JSON.stringify(subMsg);
        console.log(`[Kraken WS Client] Attempting to send TRADE subscription to Kraken: ${msgString}`);
        try {
            this.ws.send(msgString);
            this.activeSubscriptions.set(subMsg.reqid, { pair, channel: 'trade', status: 'pending' });
            console.log(`[Kraken WS Client] Subscription message for ${pair} TRADE SENT. reqid: ${subMsg.reqid}`);
        } catch (error) {
            console.error(`[Kraken WS Client] Error SENDING TRADE subscription for ${pair} (reqid: ${subMsg.reqid}):`, error);
        }
        this.subscribeToTicker(pair);
    }

    subscribeToTicker(symbols) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            console.log(`[Kraken WS Client] WebSocket not connected. Will subscribe once connected for ticker symbols: ${symbols}`);
            return;
        }
        const subMsg = {
            method: 'subscribe',
            params: {
                channel: 'ticker',
                symbol: Array.isArray(symbols) ? symbols : [symbols]
            }
        };
        console.log('Attempting to send TICKER subscription to Kraken:', JSON.stringify(subMsg));
        try {
            this.ws.send(JSON.stringify(subMsg));
            this.activeSubscriptions.set(subMsg.reqid, { pair: symbols, channel: 'ticker', status: 'pending' });
            console.log(`[Kraken WS Client] Subscription message for ${symbols} TICKER SENT. reqid: ${subMsg.reqid}`);
        } catch (error) {
            console.error(`[Kraken WS Client] Error SENDING TICKER subscription for ${symbols} (reqid: ${subMsg.reqid}):`, error);
        }
        (Array.isArray(symbols) ? symbols : [symbols]).forEach(s => this.activePairs.add(s));
        this.subscriptions.set('ticker', Array.from(this.activePairs)); 
    }

    _resubscribe() {
        console.log('Attempting to resubscribe to Kraken channels...');
        if (this.subscriptions.has('trade')) {
            const tradeSymbols = this.subscriptions.get('trade');
            if (tradeSymbols && tradeSymbols.length > 0) this.subscribeToTrades(tradeSymbols);
        }
        if (this.subscriptions.has('ticker')) {
            const tickerSymbols = this.subscriptions.get('ticker');
            if (tickerSymbols && tickerSymbols.length > 0) this.subscribeToTicker(tickerSymbols);
        }
    }

    addPairSubscription(pair) {
        this.subscribedPairs.add(pair);
        this.subscribeToPair(pair);
    }

    startHeartbeat() {
        this.heartbeatIntervalId = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ method: 'ping' })); // Correct ping for Kraken WS v2
                console.log('[Kraken WS Client] Heartbeat sent.');
            } else {
                console.log('[Kraken WS Client] Heartbeat failed to send. WebSocket not connected.');
            }
        }, 30000); // Send heartbeat every 30 seconds
    }

    // Placeholder for adding a pair to monitor
    addPair(pairSymbol) {
        if (!this.activePairs.has(pairSymbol)) {
            this.activePairs.add(pairSymbol);
            // If already connected, subscribe for this new pair
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.subscribeToPair(pairSymbol);
                this.subscribeToTicker([pairSymbol]);
            }
            console.log(`Added ${pairSymbol} to active pairs. Current active: ${Array.from(this.activePairs)}`);
        }
    }
}

// Export the class
module.exports = KrakenWsClient;

// Example Usage (for testing - to be removed or adapted in server.js)
/*
const client = new KrakenWsClient();
client.connect();

// Give it a moment to connect, then subscribe
setTimeout(() => {
    client.addPair('BTC/USD');
    client.addPair('ETH/USD');
    // client.subscribeToTrades(['XBT/USD', 'ETH/USD']); // Kraken uses XBT for Bitcoin in some contexts
    // client.subscribeToTicker(['XBT/USD', 'ETH/USD']);
}, 3000);
*/
