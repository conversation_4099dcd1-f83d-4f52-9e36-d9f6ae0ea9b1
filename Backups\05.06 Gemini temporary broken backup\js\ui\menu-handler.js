// js/ui/menu-handler.js
/**
 * StarCrypt Menu Handler
 * Centralized menu management for all UI menus with proper state management
 */
class MenuHandler {
  constructor() {
    this.activeMenu = null
    this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu']
    this.initialized = false
    this.clickHandlers = new Map()

    // Bind methods
    this.handleDocumentClick = this.handleDocumentClick.bind(this)
    this.handleMenuButtonClick = this.handleMenuButtonClick.bind(this)

    this.initialize()
  }

  initialize() {
    if (this.initialized) return

    // Setup menu buttons and event listeners
    this.setupMenuButtons()
    this.setupClickOutsideHandler()

    // Mark as initialized
    this.initialized = true

    console.log('[MenuHandler] Initialized menu handler')

    // Dispatch event that menu handler is ready
    document.dispatchEvent(new CustomEvent('menuHandler:ready'))
  }

  setupMenuButtons() {
    // Remove any existing click handlers
    this.removeAllClickHandlers()

    // Setup click handlers for each menu button
    this.setupClickHandler('strategyButton', () => this.toggleMenu('strategyMenu'))
    this.setupClickHandler('toggleMenuButton', () => this.toggleMenu('indicatorMenu'))
    this.setupClickHandler('toggleThresholdsButton', () => this.toggleMenu('thresholdsMenu'))
    this.setupClickHandler('toggleLogicButton', () => this.toggleMenu('logicMenu'))
  }

  setupClickHandler(buttonId, handler) {
    const button = document.getElementById(buttonId)
    if (!button) {
      console.warn(`[MenuHandler] Button not found: ${buttonId}`)
      return
    }

    // Store reference to handler for cleanup
    this.clickHandlers.set(buttonId, handler)

    // Add event listener
    button.addEventListener('click', this.handleMenuButtonClick)
  }

  handleMenuButtonClick(e) {
    e.preventDefault()
    e.stopPropagation()

    const buttonId = e.currentTarget.id
    const handler = this.clickHandlers.get(buttonId)

    if (handler) {
      handler()
    }
  }

  removeAllClickHandlers() {
    // Remove all event listeners
    this.clickHandlers.forEach((_, buttonId) => {
      const button = document.getElementById(buttonId)
      if (button) {
        button.removeEventListener('click', this.handleMenuButtonClick)
      }
    })

    this.clickHandlers.clear()
  }

  toggleMenu(menuId) {
    try {
      const menu = document.getElementById(menuId)
      if (!menu) {
        console.warn(`[MenuHandler] Menu not found: ${menuId}`)
        return
      }

      // If clicking the same menu, just close it
      if (this.activeMenu === menuId) {
        this.closeAllMenus()
        return
      }

      // Close all menus first
      this.closeAllMenus()

      // Open the selected menu
      menu.classList.add('active')
      this.activeMenu = menuId

      // Dispatch custom event for menu open
      const event = new CustomEvent('menu:opened', {
        detail: { menuId, menuElement: menu },
      })
      document.dispatchEvent(event)

      console.log(`[MenuHandler] Opened menu: ${menuId}`)
    } catch (error) {
      console.error(`[MenuHandler] Error toggling menu ${menuId}:`, error)
    }
  }

  closeAllMenus() {
    this.menus.forEach(menuId => {
      const menu = document.getElementById(menuId)
      if (menu) {
        menu.classList.remove('active')
      }
    })

    if (this.activeMenu) {
      console.log(`[MenuHandler] Closed all menus`)
      this.activeMenu = null
    }
  }

  setupClickOutsideHandler() {
    // Remove any existing listener to prevent duplicates
    document.removeEventListener('click', this.handleDocumentClick)

    // Add new listener with capture:true to ensure we catch the event first
    document.addEventListener('click', this.handleDocumentClick, { capture: true })
  }

  handleDocumentClick(e) {
    try {
      // Skip if clicking on a menu button (handled by button click handler)
      if (e.target.closest('.menu-button')) {
        return
      }

      // Skip if clicking inside an active menu
      if (e.target.closest('.menu-content.active')) {
        return
      }

      // Close all menus if clicking outside
      this.closeAllMenus()
    } catch (error) {
      console.error('[MenuHandler] Error in document click handler:', error)
    }
  }

  // Clean up all event listeners
  cleanup() {
    document.removeEventListener('click', this.handleDocumentClick, { capture: true })
    this.removeAllClickHandlers()
    this.initialized = false
  }
}

// Initialize the menu handler when the DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.menuHandler = new MenuHandler()
  })
} else {
  // DOM already loaded, initialize immediately
  window.menuHandler = new MenuHandler()
}

// Make the MenuHandler available globally
window.MenuHandler = MenuHandler
