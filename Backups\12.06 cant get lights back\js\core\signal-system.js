// --- HOTFIX: Neutralize conflicting legacy signal functions --- //
// This block disables functions from a rogue script that was conflicting with this module.
if (typeof window !== 'undefined') {
    const functionsToNeutralize = ['updateAllSignalLights', 'initializeSignalLights', 'updateSignalLightsNonRecursive'];
    functionsToNeutralize.forEach(fnName => {
        if (window[fnName] && !window[fnName].isSignalSystem) {
            console.warn(`[StarCrypt] Neutralizing conflicting global function: ${fnName}`);
            window[fnName] = () => { /* Conflicting function disabled */ };
        }
    });
}
// --- END HOTFIX ---

/**
 * Military-grade Signal System with self-healing and adaptive capabilities
 */
class SignalSystem {
    constructor() {
        this.state = {
            isInitialized: false,
            isUpdating: false,
            lastUpdateTime: 0,
            updateInterval: 1000,
            signalCache: new Map(),
            elementCache: new Map(),
            pendingUpdates: new Map(),
            updateThrottle: 500,
            updateTimer: null,
            container: null
        };

        this.config = {
            updateDelay: 2000,
            maxQueueSize: 1000,
            errorThreshold: 5,
            retryDelay: 1000,
            maxRetries: 3
        };

        this.callbacks = {
            onUpdate: [],
            onError: [],
            onInit: []
        };

    }

    init() {
        CoreUtils.log('info', 'SignalSystem initializing...');
        
        this.state.container = this.getSignalContainer();
        if (!this.state.container) {
            CoreUtils.log('warn', 'No signal container found, creating fallback');
            this.state.container = this.createFallbackContainer();
        }

        this.setupEventListeners();
        this.initializeSignalLights(); // Call directly for earlier initialization
        this.state.isInitialized = true;
        this.callbacks.onInit.forEach(callback => callback());
    }

    setupEventListeners() {
        // DOMContentLoaded event for other purposes if needed, but not for initial signal lights
        // Correct WebSocket message event listener
        document.addEventListener('websocket:message', (event) => {
            const message = event.detail;
            // The WebSocketManager dispatches the parsed data object in event.detail
            if (message && message.type === 'signal_update') {
                this.processSignalUpdate(message.data);
            } else if (message && message.type === 'indicator_data') {
                // Handle cases where the data is not nested, which might be part of the wiring issue
                this.processSignalUpdate(message);
            }
        });

        // Cleanup on unload
        window.addEventListener('beforeunload', () => this.cleanup());
    }

    initializeSignalLights() {
        CoreUtils.log('info', 'Initializing signal lights by wiring up existing DOM elements...');
        
        const existingSignalElements = Array.from(document.querySelectorAll('.signal-circle'));

        if (existingSignalElements.length === 0) {
            CoreUtils.log('warn', 'No signal circle elements found in the DOM to initialize.');
            return;
        }

        existingSignalElements.forEach(element => {
            const indicator = element.dataset.indicator;
            const timeframe = element.dataset.timeframe;

            if (indicator && timeframe) {
                const key = `${indicator}:${timeframe}`;
                // Add to caches if not already present
                if (!this.state.elementCache.has(key)) {
                    this.state.elementCache.set(key, element);
                }
                if (!this.state.signalCache.has(key)) {
                    // Store basic info; actual data will come from WebSocket
                    this.state.signalCache.set(key, { indicator, timeframe, status: 'neutral', value: 0, strength: 0.5 }); 
                }

                // Apply a default visual state immediately
                this.applySignalState(element, { color: 'grey', intensity: 1, tooltipText: 'Initializing...' }); 
            } else {
                CoreUtils.log('warn', 'Found signal circle with missing data-indicator or data-timeframe.', { element });
            }
        });

        CoreUtils.log('info', `Signal lights initialization pass complete. ${existingSignalElements.length} existing elements wired up.`);
    }

    createSignalElement(indicator, timeframe) {
        const element = CoreUtils.createElement('div', {
            className: 'signal-circle neutral-light',
            'data-indicator': indicator,
            'data-timeframe': timeframe
        });

        // Add tooltip
        const tooltip = CoreUtils.createElement('div', {
            className: 'signal-tooltip'
        });
        element.appendChild(tooltip);

        // Add event listeners
        element.addEventListener('click', () => this.handleSignalClick(indicator, timeframe));
        element.addEventListener('mouseover', () => this.showTooltip(element));
        element.addEventListener('mouseout', () => this.hideTooltip(element));

        // Add to DOM
        this.state.container.appendChild(element);
        
        // Cache element
        const key = `${indicator}:${timeframe}`;
        this.state.elementCache.set(key, element);
        this.state.signalCache.set(key, { indicator, timeframe });

        return element;
    }

    getSignalContainer() {
        return CoreUtils.safeQuery('#signalGrid') ||
               CoreUtils.safeQuery('#signalMatrix') ||
               CoreUtils.safeQuery('.signal-matrix') ||
               CoreUtils.safeQuery('.oracle-matrix') ||
               CoreUtils.safeQuery('#momentum-indicators') || 
               CoreUtils.safeQuery('.signals-container') || 
               document.body;
    }

    createFallbackContainer() {
        const container = CoreUtils.createElement('div', {
            id: 'momentum-indicators',
            className: 'signals-container'
        });
        document.body.appendChild(container);
        return container;
    }

    processSignalUpdate(data) {
        if (!this.state.isInitialized) {
            CoreUtils.log('warn', 'SignalSystem not initialized, queuing update');
            this.queueUpdate(data);
            return;
        }

        const key = `${data.indicator}:${data.timeframe}`;
        const element = this.state.elementCache.get(key);

        if (!element) {
            CoreUtils.log('warn', 'Signal element not found for update', { key });
            return; // Do not create elements on the fly
        }

        this.updateSignalElement(key, data);
    }

    updateSignalElement(key, data) {
        const element = this.state.elementCache.get(key);
        if (!element) return;

        try {
            const state = this.calculateSignalState(data);
            this.applySignalState(element, state);
            
            // Update cache
            this.state.signalCache.set(key, data);
            
            // Notify callbacks
            this.callbacks.onUpdate.forEach(callback => callback(data));
        } catch (error) {
            CoreUtils.log('error', 'Failed to update signal element', error);
            this.callbacks.onError.forEach(callback => callback(error));
        }
    }

    calculateSignalState(data) {
        // This logic is now aligned with the 5-color system from the 21.05 backup.
        // It expects a signalClass property from the incoming data.
        const signalClass = data.signalClass || 'neutral';
        const tooltipText = data.tooltip || `Status: ${signalClass}`;

        return { signalClass, tooltipText };
    }

    applySignalState(element, state) {
        const { signalClass, tooltipText } = state;

        // Map signalClass to the 5-color CSS classes from the backup
        let colorClass = 'grey-light'; // Default
        switch (signalClass) {
            case 'degen-buy':
                colorClass = 'green-light';
                break;
            case 'mild-buy':
                colorClass = 'blue-light';
                break;
            case 'neutral':
                colorClass = 'grey-light';
                break;
            case 'mild-sell':
                colorClass = 'orange-light';
                break;
            case 'degen-sell':
                colorClass = 'red-light';
                break;
        }

        // Efficiently update classes
        const prevColor = element.dataset.colorClass;
        if (prevColor !== colorClass) {
            element.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light');
            element.classList.add(colorClass);
            element.dataset.colorClass = colorClass; // Cache for efficiency
        }

        // Update tooltip
        const tooltip = element.querySelector('.signal-tooltip') || element;
        const prevTooltip = tooltip.dataset.tooltip;
        if (tooltipText && prevTooltip !== tooltipText) {
            tooltip.setAttribute('data-tooltip', tooltipText);
            tooltip.dataset.tooltip = tooltipText;
        }
    }

    handleSignalClick(indicator, timeframe) {
        CoreUtils.log('info', 'Signal clicked', { indicator, timeframe });
        
        // Trigger light logic preview
        const preview = document.querySelector(`.preview-light[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`);
        if (preview) {
            preview.classList.add('preview-active');
            setTimeout(() => {
                preview.classList.remove('preview-active');
            }, 2000);
        }
    }

    showTooltip(element) {
        const tooltip = element.querySelector('.signal-tooltip');
        if (tooltip) {
            tooltip.style.opacity = '1';
            tooltip.style.visibility = 'visible';
        }
    }

    hideTooltip(element) {
        const tooltip = element.querySelector('.signal-tooltip');
        if (tooltip) {
            tooltip.style.opacity = '0';
            tooltip.style.visibility = 'hidden';
        }
    }

    queueUpdate(data) {
        if (this.state.pendingUpdates.size >= this.config.maxQueueSize) {
            CoreUtils.log('warn', 'Update queue full, dropping oldest update');
            this.state.pendingUpdates.delete(this.state.pendingUpdates.keys().next().value);
        }

        const key = `${data.indicator}:${data.timeframe}`;
        this.state.pendingUpdates.set(key, data);
    }

    updateAllSignalLights() {
        if (!this.state.isInitialized) {
            CoreUtils.log('warn', 'SignalSystem not initialized, queuing full update');
            this.queueUpdate({ type: 'full_update' });
            return;
        }

        if (Date.now() - this.state.lastUpdateTime < this.config.updateDelay) {
            CoreUtils.log('info', 'Update throttled');
            return;
        }

        try {
            this.state.isUpdating = true;
            
            // Process pending updates
            for (const [key, data] of this.state.pendingUpdates.entries()) {
                this.updateSignalElement(key, data);
            }
            this.state.pendingUpdates.clear();

            // Update all cached signals
            for (const [key, data] of this.state.signalCache.entries()) {
                this.updateSignalElement(key, data);
            }

            this.state.lastUpdateTime = Date.now();
        } catch (error) {
            CoreUtils.log('error', 'Failed to update all signals', error);
            this.handleUpdateError(error);
        } finally {
            this.state.isUpdating = false;
        }
    }

    handleUpdateError(error) {
        this.state.errorCount = (this.state.errorCount || 0) + 1;
        
        if (this.state.errorCount >= this.config.errorThreshold) {
            CoreUtils.log('error', 'Too many errors, retrying update');
            
            if (this.state.retryCount >= this.config.maxRetries) {
                CoreUtils.log('error', 'Max retries reached, resetting');
                this.cleanup();
                this.init();
            } else {
                setTimeout(() => {
                    this.updateAllSignalLights();
                }, this.config.retryDelay);
            }
        }
    }

    cleanup() {
        CoreUtils.log('info', 'SignalSystem cleaning up...');
        
        // Clear all caches
        this.state.signalCache.clear();
        this.state.elementCache.clear();
        this.state.pendingUpdates.clear();

        // Remove event listeners
        this.callbacks.onUpdate = [];
        this.callbacks.onError = [];
        this.callbacks.onInit = [];

        // Clear timers
        if (this.state.updateTimer) {
            clearTimeout(this.state.updateTimer);
            this.state.updateTimer = null;
        }

        // Reset state
        this.state.isInitialized = false;
        this.state.isUpdating = false;
        this.state.lastUpdateTime = 0;
        this.state.errorCount = 0;
        this.state.retryCount = 0;
    }

    addEventListener(type, callback) {
        if (!this.callbacks[type]) {
            CoreUtils.log('warn', `Unknown event type: ${type}`);
            return;
        }

        if (typeof callback !== 'function') {
            CoreUtils.log('error', 'Callback must be a function');
            return;
        }

        this.callbacks[type].push(callback);
    }

    removeEventListener(type, callback) {
        if (!this.callbacks[type]) return;
        this.callbacks[type] = this.callbacks[type].filter(cb => cb !== callback);
    }

    isReady() {
        return this.state.isInitialized;
    }

    getStatus() {
        return {
            isInitialized: this.state.isInitialized,
            isUpdating: this.state.isUpdating,
            lastUpdateTime: this.state.lastUpdateTime,
            signalCount: this.state.signalCache.size,
            elementCount: this.state.elementCache.size,
            pendingUpdates: this.state.pendingUpdates.size,
            errorCount: this.state.errorCount || 0,
            retryCount: this.state.retryCount || 0
        };
    }
}

// Export a singleton instance for the browser
if (typeof window !== 'undefined') {
    // Use a singleton pattern to ensure only one instance exists
    if (!window.SignalSystem) {
        window.SignalSystem = new SignalSystem();
        // CoreUtils.log is not defined here yet, use console.log
        console.log('[SignalSystem] Instance created and assigned to window.SignalSystem. Call init() manually.');
    } else {
        console.warn('[SignalSystem] Instance already exists on window.SignalSystem.');
    }
}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SignalSystem;
}
