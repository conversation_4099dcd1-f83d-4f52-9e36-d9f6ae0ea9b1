/**
 * WebSocket Processor
 * Handles processing of WebSocket messages with rate limiting, error handling, and recursion protection
 */
class WebSocketProcessor {
    /**
     * Create a new WebSocketProcessor
     * @param {Object} options - Configuration options
     * @param {number} [options.maxBatchSize=10] - Maximum number of messages to process in a batch
     * @param {number} [options.maxQueueSize=1000] - Maximum number of messages to queue before dropping new ones
     * @param {number} [options.processDelay=10] - Delay in ms between processing batches
     * @param {number} [options.maxDepth=5] - Maximum recursion depth for message processing
     * @param {number} [options.maxConsecutiveErrors=10] - Maximum consecutive errors before pausing
     * @param {number} [options.errorResetTime=60000] - Time in ms to wait before resetting error counter
     */
    constructor({
        maxBatchSize = 10,
        maxQueueSize = 1000,
        processDelay = 10,
        maxDepth = 5,
        maxConsecutiveErrors = 10,
        errorResetTime = 60000
    } = {}) {
        this.maxBatchSize = maxBatchSize;
        this.maxQueueSize = maxQueueSize;
        this.processDelay = processDelay;
        this.maxDepth = maxDepth;
        this.maxConsecutiveErrors = maxConsecutiveErrors;
        this.errorResetTime = errorResetTime;
        
        this.messageQueue = [];
        this.messageTimestamps = new Map();
        this.messageHandlers = new Map();
        this.processedMessages = new Set();
        this.isProcessing = false;
        this.isPaused = false;
        this.consecutiveErrors = 0;
        this.lastErrorTime = 0;
        this.maxProcessedMessages = maxQueueSize * 2; // Keep some history to prevent duplicates
        
        // Bind methods
        this.queueMessage = this.queueMessage.bind(this);
        this.processQueue = this.processQueue.bind(this);
        this.processBatch = this.processBatch.bind(this);
        this.processMessage = this.processMessage.bind(this);
        this.addMessageHandler = this.addMessageHandler.bind(this);
        this.removeMessageHandler = this.removeMessageHandler.bind(this);
        this.pauseProcessing = this.pauseProcessing.bind(this);
        this.resumeProcessing = this.resumeProcessing.bind(this);
        this.cleanup = this.cleanup.bind(this);
    }
    
    /**
     * Queue a message for processing
     * @param {Object} message - The message to queue
     */
    queueMessage(message) {
        try {
            if (!message || typeof message !== 'object') {
                console.warn('[WebSocket] Invalid message format:', message);
                return;
            }
            
            // Skip processing if we're in a bad state
            if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                console.warn('[WebSocket] Skipping message due to error state');
                return;
            }
            
            const now = Date.now();
            const messageId = message._id || `msg_${now}_${Math.random().toString(36).substr(2, 9)}`;
            
            // Skip if we've already processed this message
            if (this.processedMessages.has(messageId)) {
                console.debug(`[WebSocket] Skipping duplicate message: ${messageId}`);
                return;
            }
            
            // Add to processed messages with cleanup
            this.processedMessages.add(messageId);
            if (this.processedMessages.size > this.maxProcessedMessages) {
                const firstId = Array.from(this.processedMessages)[0];
                this.processedMessages.delete(firstId);
            }
            
            // Add to queue if not full
            if (this.messageQueue.length >= this.maxQueueSize) {
                console.warn('[WebSocket] Message queue full, dropping message:', messageId);
                return;
            }
            
            // Add to queue with metadata
            this.messageQueue.push({
                ...message,
                _messageId: messageId,
                _receivedAt: now,
                _processingDepth: 0 // Track recursion depth for this message
            });
            
            // Start processing if not already running
            if (!this.isProcessing && !this.isPaused) {
                // Use setTimeout to break the call stack
                setTimeout(() => this.processQueue().catch(console.error), 0);
            }
        } catch (error) {
            console.error('[WebSocket] Error in queueMessage:', error);
            this.consecutiveErrors++;
            this.lastErrorTime = Date.now();
            
            if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                this.pauseProcessing(this.errorResetTime);
            }
        }
    }
    
    /**
     * Process the message queue with rate limiting and error handling
     */
    async processQueue() {
        // Check if we're already processing or paused
        if (this.isProcessing || this.isPaused) {
            // If paused with a timeout, check if we should resume
            if (this.isPaused && this.pauseUntil > 0 && Date.now() >= this.pauseUntil) {
                this.resumeProcessing();
            }
            return;
        }
        
        // Mark as processing
        this.isProcessing = true;
        
        try {
            // Check error state
            if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                const timeSinceLastError = Date.now() - (this.lastErrorTime || 0);
                if (timeSinceLastError < this.errorResetTime) {
                    console.warn(`[WebSocket] Paused due to ${this.consecutiveErrors} consecutive errors, resuming in ${Math.ceil((this.errorResetTime - timeSinceLastError) / 1000)}s`);
                    this.pauseProcessing(this.errorResetTime - timeSinceLastError);
                    return;
                } else {
                    // Reset error counter after cooldown
                    this.consecutiveErrors = 0;
                    console.log('[WebSocket] Error cooldown expired, resuming processing');
                }
            }
            
            // Process messages in batches
            while (this.messageQueue.length > 0 && !this.isPaused) {
                // Check rate limit
                const now = Date.now();
                const oneSecondAgo = now - 1000;
                const recentMessages = Array.from(this.messageTimestamps.values())
                    .filter(timestamp => timestamp > oneSecondAgo);
                
                if (recentMessages.length >= this.rateLimit) {
                    // We're over the rate limit, pause briefly
                    const nextAllowed = Math.ceil((recentMessages[0] + 1000 - now) / 1000);
                    console.warn(`[WebSocket] Rate limit exceeded (${recentMessages.length}/${this.rateLimit} msgs/sec), pausing for ${nextAllowed}s`);
                    this.pauseProcessing(nextAllowed * 1000);
                    break;
                }
                
                // Get next batch
                const batch = this.messageQueue.splice(0, this.maxBatchSize);
                
                try {
                    // Process batch with error boundaries
                    await this.processBatch(batch);
                    
                    // Clean up old timestamps
                    this.cleanupOldMessages();
                    
                } catch (batchError) {
                    console.error('[WebSocket] Batch processing error:', batchError);
                    // If we hit a critical error, pause processing
                    if (!this.isPaused) {
                        this.pauseProcessing(5000); // Pause for 5 seconds
                    }
                    break;
                }
                
                // Add delay between batches if there are more messages
                if (this.messageQueue.length > 0) {
                    await new Promise(resolve => setTimeout(resolve, this.processDelay));
                }
            }
            
        } catch (error) {
            console.error('[WebSocket] Critical error in processQueue:', error);
            this.pauseProcessing(10000); // Pause for 10 seconds on critical error
            
        } finally {
            // Only mark as not processing if we're not in a paused state
            if (!this.isPaused) {
                this.isProcessing = false;
            }
        }
    }
    
    /**
     * Process a batch of messages with error handling
     * @param {Array} batch - Batch of messages to process
     */
    async processBatch(batch) {
        // Process messages sequentially to prevent stack overflow
        const results = [];
        for (const message of batch) {
            try {
                // Track message timestamp for rate limiting
                const timestamp = Date.now();
                const messageId = message._messageId || `msg_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
                this.messageTimestamps.set(messageId, timestamp);
                
                // Check recursion depth
                if (message._processingDepth >= this.maxDepth) {
                    console.warn(`[WebSocket] Max processing depth (${this.maxDepth}) reached for message:`, messageId);
                    continue;
                }
                
                // Increment processing depth
                const nextMessage = {
                    ...message,
                    _processingDepth: (message._processingDepth || 0) + 1
                };
                
                // Process the message with a timeout
                try {
                    const result = await Promise.race([
                        this.processMessage(nextMessage),
                        new Promise((_, reject) => 
                            setTimeout(() => reject(new Error('Message processing timeout')), 5000)
                        )
                    ]);
                    results.push(result);
                    
                    // Reset error counter on successful processing
                    this.consecutiveErrors = 0;
                    
                } catch (error) {
                    console.error('[WebSocket] Error in message processing:', {
                        error,
                        messageId: nextMessage._messageId || 'unknown',
                        type: nextMessage.type || 'unknown',
                        depth: nextMessage._processingDepth
                    });
                    
                    // Track consecutive errors
                    this.consecutiveErrors++;
                    this.lastErrorTime = Date.now();
                    
                    // If too many errors, pause processing
                    if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                        console.error(`[WebSocket] Pausing processing after ${this.consecutiveErrors} consecutive errors`);
                        this.pauseProcessing(this.errorResetTime);
                        break; // Stop processing this batch
                    }
                }
                
                // Add a small delay between messages to prevent UI blocking
                await new Promise(resolve => setTimeout(resolve, 5));
                
            } catch (batchError) {
                console.error('[WebSocket] Critical error in batch processing:', batchError);
                this.consecutiveErrors++;
                this.lastErrorTime = Date.now();
                
                if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                    this.pauseProcessing(this.errorResetTime);
                    break;
                }
            }
        }
        return results;
    }
    
    /**
     * Process a single message
     * @param {Object} message - The message to process
     */
    async processMessage(message) {
        if (!message || !message.type) {
            throw new Error('Invalid message format: missing type');
        }
        
        // Get handlers for this message type
        const handlers = this.messageHandlers.get(message.type);
        if (!handlers || handlers.size === 0) {
            console.debug(`[WebSocket] No handlers registered for message type: ${message.type}`);
            return;
        }
        
        // Process all handlers in sequence with error boundaries
        for (const handler of handlers) {
            if (typeof handler !== 'function') {
                console.warn(`[WebSocket] Invalid handler for message type ${message.type}`);
                continue;
            }
            
            try {
                // Execute handler with a timeout
                await Promise.race([
                    handler(message),
                    new Promise((_, reject) => {
                        setTimeout(() => reject(new Error('Handler timeout')), 10000);
                    })
                ]);
                
                // Reset error counter on successful handler execution
                this.consecutiveErrors = 0;
                
            } catch (error) {
                console.error(`[WebSocket] Error in ${message.type} handler:`, {
                    error,
                    messageId: message._messageId,
                    handler: handler.name || 'anonymous'
                });
                
                // Track consecutive errors but continue with other handlers
                this.consecutiveErrors++;
                this.lastErrorTime = Date.now();
                
                // If too many errors, pause processing
                if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                    this.pauseProcessing(this.errorResetTime);
                    throw error; // Stop processing this message
                }
            }
            
            // Add a small delay between handlers to prevent UI blocking
        }
    }
    return results;
}

/**
 * Process a single message
 * @param {Object} message - The message to process
 */
async processMessage(message) {
    if (!message || !message.type) {
        throw new Error('Invalid message format: missing type');
    }
    
    // Get handlers for this message type
    const handlers = this.messageHandlers.get(message.type);
    if (!handlers || handlers.size === 0) {
        console.debug(`[WebSocket] No handlers registered for message type: ${message.type}`);
        return;
    }
    
    // Process all handlers in sequence with error boundaries
    for (const handler of handlers) {
        if (typeof handler !== 'function') {
            console.warn(`[WebSocket] Invalid handler for message type ${message.type}`);
            continue;
        }
        
        try {
            // Execute handler with a timeout
            await Promise.race([
                handler(message),
                new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Handler timeout')), 10000);
                })
            ]);
            
            // Reset error counter on successful handler execution
            this.consecutiveErrors = 0;
            
        } catch (error) {
            console.error(`[WebSocket] Error in ${message.type} handler:`, {
                error,
                messageId: message._messageId,
                handler: handler.name || 'anonymous'
            });
            
            // Track consecutive errors but continue with other handlers
            this.consecutiveErrors++;
            this.lastErrorTime = Date.now();
            
            // If too many errors, pause processing
            if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                this.pauseProcessing(this.errorResetTime);
                throw error; // Stop processing this message
            }
        }
        
        // Add a small delay between handlers to prevent UI blocking
        await new Promise(resolve => setTimeout(resolve, 1));
    }
}

/**
 * Add a message handler
 * @param {string} type - Message type to handle
 * @param {Function} handler - Handler function
 * @returns {Function} Unsubscribe function
 */
addMessageHandler(type, handler) {
    try {
        // Validate input parameters
        if (typeof type !== 'string' || !type.trim()) {
            throw new Error('Message type must be a non-empty string');
        }
        if (typeof handler !== 'function') {
            throw new Error('Handler must be a function');
        }
        
        // Ensure we have a Set for this message type
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, new Set());
        }
    }
    
    /**
     * Pause message processing
     * @param {number} duration - Duration to pause in milliseconds
     */
    pauseProcessing(duration = 0) {
        this.isPaused = true;
        this.pauseUntil = duration > 0 ? Date.now() + duration : Infinity;
        
        if (duration > 0) {
            setTimeout(() => this.resumeProcessing(), duration);
        }
        
        console.log(`[WebSocket] Processing paused${duration > 0 ? ` for ${duration}ms` : ''}`);
    }
    
    /**
     * Resume message processing
     */
    resumeProcessing() {
        if (this.isPaused) {
            this.isPaused = false;
            this.pauseUntil = 0;
            this.consecutiveErrors = 0; // Reset error counter on resume
            console.log('[WebSocket] Processing resumed');
            
            // Restart processing if there are messages in the queue
            if (this.messageQueue.length > 0) {
                this.processQueue();
            }
        }
    }
    
    /**
     * Clean up old messages and timestamps
     */
    cleanupOldMessages() {
        const now = Date.now();
        const oneHourAgo = now - 3600000; // 1 hour
        
        // Clean up old message timestamps
        for (const [id, timestamp] of this.messageTimestamps.entries()) {
            if (timestamp < oneHourAgo) {
                this.messageTimestamps.delete(id);
            }
        }
        
        // Clean up processed messages if we have too many
        if (this.processedMessages.size > this.maxProcessedMessages * 1.5) {
            const excess = this.processedMessages.size - this.maxProcessedMessages;
            const toRemove = Array.from(this.processedMessages).slice(0, excess);
            toRemove.forEach(id => this.processedMessages.delete(id));
            console.debug(`[WebSocket] Cleaned up ${toRemove.length} old processed messages`);
        }
    }
    
    /**
     * Clean up resources
     */
    destroy() {
        clearInterval(this.cleanupInterval);
        this.messageQueue = [];
        this.processedMessages.clear();
        this.messageTimestamps.clear();
        this.messageHandlers.clear();
        this.isProcessing = false;
        this.isPaused = true;
    }
    
    /**
     * Queue a message for processing
     * @param {Object} message - The message to queue
     */
    queueMessage(message) {
        if (!message || typeof message !== 'object') {
            console.warn('[WebSocket] Invalid message format:', message);
            return;
        }
        
        const now = Date.now();
        const messageId = message._id || `msg_${now}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Skip if we've already processed this message
        if (this.processedMessages.has(messageId)) {
            console.debug(`[WebSocket] Skipping duplicate message: ${messageId}`);
            return;
        }
        
        // Add to processed messages with cleanup
        this.processedMessages.add(messageId);
        if (this.processedMessages.size > this.maxProcessedMessages) {
            const firstId = Array.from(this.processedMessages)[0];
            this.processedMessages.delete(firstId);
        }
        
        // Add to queue if not full
        if (this.messageQueue.length < this.maxQueueSize) {
            this.messageQueue.push({
                ...message,
                _messageId: messageId,
                _receivedAt: now
            });
            
            // Start processing if not already running
            if (!this.isProcessing && !this.isPaused) {
                this.processQueue();
            }
        } else {
            console.warn('[WebSocket] Message queue full, dropping message:', messageId);
        }
    }
    
    /**
     * Process the message queue with rate limiting and error handling
     */
    async processQueue() {
        // Check if we're already processing or paused
        if (this.isProcessing || this.isPaused) {
            // If paused with a timeout, check if we should resume
            if (this.isPaused && this.pauseUntil > 0 && Date.now() >= this.pauseUntil) {
                this.resumeProcessing();
            } else {
                return;
            }
        }
        
        // Mark as processing
        this.isProcessing = true;
        
        try {
            // Check error state
            if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                const timeSinceLastError = Date.now() - (this.lastErrorTime || 0);
                if (timeSinceLastError < this.errorResetTime) {
                    console.warn(`[WebSocket] Paused due to ${this.consecutiveErrors} consecutive errors, resuming in ${Math.ceil((this.errorResetTime - timeSinceLastError) / 1000)}s`);
                    this.pauseProcessing(this.errorResetTime - timeSinceLastError);
                    return;
                } else {
                    // Reset error counter after cooldown
                    this.consecutiveErrors = 0;
                    console.log('[WebSocket] Error cooldown expired, resuming processing');
                }
            }
            
            // Process messages in batches
            while (this.messageQueue.length > 0 && !this.isPaused) {
                // Check rate limit
                const now = Date.now();
                const oneSecondAgo = now - 1000;
                const recentMessages = Array.from(this.messageTimestamps.values())
                    .filter(timestamp => timestamp > oneSecondAgo);
                
                if (recentMessages.length >= this.rateLimit) {
                    // We're over the rate limit, pause briefly
                    const nextAllowed = Math.ceil((recentMessages[0] + 1000 - now) / 1000);
                    console.warn(`[WebSocket] Rate limit exceeded (${recentMessages.length}/${this.rateLimit} msgs/sec), pausing for ${nextAllowed}s`);
                    this.pauseProcessing(nextAllowed * 1000);
                    break;
                }
                
                // Get next batch
                const batch = this.messageQueue.splice(0, this.maxBatchSize);
                
                try {
                    // Process batch with error boundaries
                    await Promise.all(
                        batch.map(message => {
                            // Track message timestamp for rate limiting
                            const timestamp = Date.now();
                            const messageId = message._messageId || `msg_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
                            this.messageTimestamps.set(messageId, timestamp);
                            
                            return this.processMessage(message).catch(error => {
                                console.error('[WebSocket] Error in message batch:', {
                                    error,
                                    messageId: message._messageId || 'unknown',
                                    type: message.type || 'unknown'
                                });
                                
                                // Track consecutive errors
                                this.consecutiveErrors++;
                                this.lastErrorTime = Date.now();
                                
                                // If too many errors, pause processing
                                if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                                    console.error(`[WebSocket] Pausing processing after ${this.consecutiveErrors} consecutive errors`);
                                    this.pauseProcessing(this.errorResetTime);
                                    throw error; // Stop processing this batch
                                }
                            });
                        })
                    );
                    
                    // Clean up old timestamps
                    this.cleanupOldMessages();
                    
                } catch (batchError) {
                    console.error('[WebSocket] Batch processing error:', batchError);
                    // If we hit a critical error, pause processing
                    if (!this.isPaused) {
                        this.pauseProcessing(5000); // Pause for 5 seconds
                    }
                    break;
                }
                
                // Add delay between batches if there are more messages
                if (this.messageQueue.length > 0) {
                    await new Promise(resolve => setTimeout(resolve, this.processDelay));
                }
            }
            
        } catch (error) {
            console.error('[WebSocket] Critical error in processQueue:', error);
            this.pauseProcessing(10000); // Pause for 10 seconds on critical error
            
        } finally {
            // Only mark as not processing if we're not in a paused state
            if (!this.isPaused) {
                this.isProcessing = false;
            }
        }
    }
    
    /**
     * Process a single message
     * @param {Object} message - The message to process
     */
    async processMessage(message) {
        // Track processing depth
        this.currentDepth++;
        
        try {
            // Safety check for maximum depth
            if (this.currentDepth > this.maxDepth) {
                console.error(`[WebSocket] Max processing depth (${this.maxDepth}) reached, dropping message`, {
                    messageId: message._messageId,
                    type: message.type,
                    currentDepth: this.currentDepth
                });
                return;
            }
            
            const { type, data, _id, _messageId } = message;
            const messageId = _id || _messageId || 'unknown';
            
            // Skip if already processed
            if (message._processed) {
                console.debug(`[WebSocket] Skipping already processed message: ${type} (${messageId})`);
                return;
            }
            
            // Mark as processed with timestamp
            message._processed = true;
            message._processedAt = Date.now();
            
            try {
                // Process message based on type
                if (type && this.messageHandlers.has(type)) {
                    const handlers = this.messageHandlers.get(type);
                    for (const handler of handlers) {
                        try {
                            // Add small delay to prevent stack overflow
                            await new Promise(resolve => setTimeout(resolve, 1));
                            
                            // Execute handler with error boundary
                            await handler(data || {});
                            
                            // Reset error counter on successful processing
                            this.consecutiveErrors = 0;
                            
                        } catch (error) {
                            // Track consecutive errors
                            this.consecutiveErrors = (this.consecutiveErrors || 0) + 1;
                            this.lastErrorTime = Date.now();
                            
                            console.error(`[WebSocket] Error in handler for ${type} (${this.consecutiveErrors}/${this.maxConsecutiveErrors}):`, {
                                error,
                                messageId,
                                handler: handler.name || 'anonymous',
                                currentDepth: this.currentDepth
                            });
                            
                            // If too many errors, pause processing
                            if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                                console.error(`[WebSocket] Pausing processing after ${this.consecutiveErrors} consecutive errors`);
                                this.pauseProcessing();
                                break;
                            }
                        }
                    }
                }
                
                // Dispatch event for this message type if not a system message
                if (type && !type.startsWith('system.')) {
                    try {
                        const event = new CustomEvent(`websocket:${type}`, { 
                            detail: { 
                                ...(data || {}), 
                                _wsProcessed: true,
                                _messageId: messageId,
                                _processedAt: message._processedAt,
                                _processingDepth: this.currentDepth
                            } 
                        });
                        
                        // Dispatch with error handling
                        document.dispatchEvent(event);
                        
                    } catch (eventError) {
                        console.error(`[WebSocket] Error dispatching event for ${type}:`, {
                            error: eventError,
                            messageId,
                            currentDepth: this.currentDepth
                        });
                    }
                }
                
            } catch (processError) {
                console.error(`[WebSocket] Unexpected error processing message ${messageId} (${type}):`, {
                    error: processError,
                    message,
                    currentDepth: this.currentDepth
                });
            }
            
        } catch (error) {
            console.error(`[WebSocket] Critical error in processMessage:`, {
                error,
                message: message || 'No message available',
                currentDepth: this.currentDepth
            });
            
        } finally {
            // Always decrement depth
            this.currentDepth = Math.max(0, this.currentDepth - 1);
        }
    }
    
    /**
     * Add a message handler for a specific message type
     * @param {string} type - The message type to handle
     * @param {Function} handler - The handler function
     */
    addMessageHandler(type, handler) {
        if (typeof handler !== 'function') {
            console.error('[WebSocket] Handler must be a function');
            return;
        }
        
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, new Set());
        }
        
        this.messageHandlers.get(type).add(handler);
    }
    
    /**
     * Remove a message handler
     * @param {string} type - The message type
     * @param {Function} handler - The handler function to remove
     */
    removeMessageHandler(type, handler) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            handlers.delete(handler);
            
            if (handlers.size === 0) {
                this.messageHandlers.delete(type);
            }
        }
    }
    
    /**
     * Pause message processing
     * @param {number} [duration=0] - Duration to pause in milliseconds (0 = until manually resumed)
     */
    pauseProcessing(duration = 0) {
        this.isPaused = true;
        this.pauseUntil = duration > 0 ? Date.now() + duration : 0;
        console.warn(`[WebSocket] Processing paused${duration > 0 ? ` for ${duration}ms` : ''}`);
        
        // If duration is specified, set a timeout to resume
        if (duration > 0) {
            setTimeout(() => {
                if (this.isPaused && this.pauseUntil > 0 && Date.now() >= this.pauseUntil) {
                    this.resumeProcessing();
                }
            }, duration);
        }
    }
    
    /**
     * Resume message processing
     */
    resumeProcessing() {
        if (this.isPaused) {
            this.isPaused = false;
            this.pauseUntil = 0;
            this.consecutiveErrors = 0;
            console.log('[WebSocket] Processing resumed');
            
            // Process any queued messages
            if (this.messageQueue.length > 0) {
                this.processQueue();
            }
        }
    }
    
    /**
     * Clean up old message timestamps and processed messages
     */
    cleanupOldMessages() {
        const now = Date.now();
        const oneMinuteAgo = now - 60000; // Keep timestamps for 1 minute
        
        // Clean up message timestamps
        for (const [id, timestamp] of this.messageTimestamps.entries()) {
            if (timestamp < oneMinuteAgo) {
                this.messageTimestamps.delete(id);
            }
        }
        
        // Clean up processed messages if set is too large
        if (this.processedMessages.size > this.maxProcessedMessages * 1.5) {
            const excess = this.processedMessages.size - this.maxProcessedMessages;
            const idsToRemove = Array.from(this.processedMessages).slice(0, excess);
            idsToRemove.forEach(id => this.processedMessages.delete(id));
            console.debug(`[WebSocket] Cleaned up ${excess} old processed messages`);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebSocketProcessor;
} else {
    window.WebSocketProcessor = WebSocketProcessor;
}
