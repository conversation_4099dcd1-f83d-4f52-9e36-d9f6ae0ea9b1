// websocket-handler.js
// Handles WebSocket connection and message processing for StarCrypt
// Add this to websocket-handler.js
function logWebSocketMessages() {
  if (window.ws) {
      const originalOnMessage = window.ws.onmessage;
      window.ws.onmessage = function(event) {
          console.log('WebSocket message received:', event.data);
          if (originalOnMessage) {
              originalOnMessage.call(this, event);
          }
      };
  }
}

// Call this after WebSocket connection is established
logWebSocketMessages();
// Initialize WebSocket variables with defensive checks
(function() {
  if (typeof window === 'undefined') {
    // Node.js environment
    global.wsLastMessageTime = null;
    global.updateTimer = null;
  } else {
    // Browser environment
    window.wsLastMessageTime = window.wsLastMessageTime || null;
    window.updateTimer = window.updateTimer || null;
    window.ws = window.ws || null;
    window.reconnectAttempts = window.reconnectAttempts || 0;
  }
})();

// Debug log to confirm initialization
console.log('WebSocket handler initialized with wsLastMessageTime:', 
  typeof window !== 'undefined' ? window.wsLastMessageTime : global.wsLastMessageTime);

// Helper functions for indicator calculations
function calculateRSI(prices, period = 14) {
  if (prices.length < period) return 50;
  
  const gains = [];
  const losses = [];
  
  for (let i = 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    if (change > 0) {
      gains.push(change);
    } else {
      losses.push(-change);
    }
  }
  
  const avgGain = gains.slice(-period).reduce((a, b) => a + b, 0) / period;
  const avgLoss = losses.slice(-period).reduce((a, b) => a + b, 0) / period;
  
  return avgLoss === 0 ? 100 : 100 - (100 / (1 + (avgGain / avgLoss)));
}

function calculateMACD(prices, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
  if (prices.length < slowPeriod) return 0;
  
  const fastEMA = calculateEMA(prices, fastPeriod);
  const slowEMA = calculateEMA(prices, slowPeriod);
  const macdLine = fastEMA - slowEMA;
  const signalLine = calculateEMA(prices, signalPeriod);
  
  return macdLine - signalLine;
}

function calculateEMA(prices, period) {
  if (prices.length < period) return 0;
  
  const multiplier = 2 / (period + 1);
  let ema = prices[0];
  
  for (let i = 1; i < prices.length; i++) {
    ema = (prices[i] - ema) * multiplier + ema;
  }
  
  return ema;
}

function calculateSMA(prices, period) {
  if (prices.length < period) return 0;
  
  const sum = prices.slice(-period).reduce((a, b) => a + b, 0);
  return sum / period;
}

function calculateStochRSI(prices, period = 14) {
  if (prices.length < period) return 50;
  
  const rsi = calculateRSI(prices, period);
  const min = Math.min(...prices.slice(-period));
  const max = Math.max(...prices.slice(-period));
  
  return (rsi - min) / (max - min) * 100;
}

function calculateWilliamsR(highs, lows, closes, period = 14) {
  if (highs.length < period) return -50;
  
  const hh = Math.max(...highs.slice(-period));
  const ll = Math.min(...lows.slice(-period));
  const close = closes[closes.length - 1];
  
  return ((hh - close) / (hh - ll)) * -100;
}

function calculateMFI(highs, lows, closes, volumes, period = 14) {
  if (highs.length < period) return 50;
  
  const typicalPrices = highs.map((h, i) => (h + lows[i] + closes[i]) / 3);
  const moneyFlows = typicalPrices.map((tp, i) => tp * volumes[i]);
  
  const positiveFlows = moneyFlows.filter((mf, i) => closes[i] > closes[i - 1]);
  const negativeFlows = moneyFlows.filter((mf, i) => closes[i] < closes[i - 1]);
  
  const positiveSum = positiveFlows.reduce((a, b) => a + b, 0);
  const negativeSum = negativeFlows.reduce((a, b) => a + b, 0);
  
  return negativeSum === 0 ? 100 : 100 - (100 / (1 + (positiveSum / negativeSum)));
}

function calculateBollingerBands(prices, period = 20, stdDev = 2) {
  if (prices.length < period) return { middle: 0, upper: 0, lower: 0 };
  
  const sma = calculateSMA(prices, period);
  const deviations = prices.map(p => p - sma);
  const variance = deviations.reduce((a, b) => a + b * b, 0) / period;
  const std = Math.sqrt(variance);
  
  return {
    middle: sma,
    upper: sma + (std * stdDev),
    lower: sma - (std * stdDev)
  };
}

function calculateADX(highs, lows, closes, period = 14) {
  if (highs.length < period) return 0;
  
  const tr = highs.map((h, i) => Math.max(
    h - lows[i],
    Math.abs(h - closes[i - 1]),
    Math.abs(lows[i] - closes[i - 1])
  ));
  
  const plusDM = highs.map((h, i) => {
    if (i === 0) return 0;
    return h > highs[i - 1] && lows[i] <= lows[i - 1] ? h - highs[i - 1] : 0;
  });
  
  const minusDM = lows.map((l, i) => {
    if (i === 0) return 0;
    return l < lows[i - 1] && highs[i] <= highs[i - 1] ? lows[i - 1] - l : 0;
  });
  
  const plusDI = calculateEMA(plusDM, period) / calculateEMA(tr, period) * 100;
  const minusDI = calculateEMA(minusDM, period) / calculateEMA(tr, period) * 100;
  
  const dx = Math.abs(plusDI - minusDI) / (plusDI + minusDI) * 100;
  return calculateEMA(dx, period);
}

function calculateATR(highs, lows, closes, period = 14) {
  if (highs.length < period) return 0;
  
  const tr = highs.map((h, i) => Math.max(
    h - lows[i],
    Math.abs(h - closes[i - 1]),
    Math.abs(lows[i] - closes[i - 1])
  ));
  
  return calculateEMA(tr, period);
}

function calculateVWAP(highs, lows, closes, volumes) {
  if (highs.length === 0) return 0;
  
  const typicalPrices = highs.map((h, i) => (h + lows[i] + closes[i]) / 3);
  const totalVolume = volumes.reduce((a, b) => a + b, 0);
  
  const weightedSum = typicalPrices.reduce((sum, tp, i) => sum + tp * volumes[i], 0);
  return weightedSum / totalVolume;
}

// Global variables and constants
window.ws = null;
window.reconnectAttempts = 0;
let MAX_RECONNECT_ATTEMPTS = 5; // Changed from const to let since it's modified later
window.RECONNECT_DELAY = 2000;
window.KRAKEN_WS_URL = 'wss://ws.kraken.com';

// Ensure globals are defined to prevent ReferenceErrors
if (typeof window.wsLastMessageTime === 'undefined') window.wsLastMessageTime = null;
if (typeof window.updateTimer === 'undefined') window.updateTimer = null;

// Supported pairs and mappings
window.SUPPORTED_PAIRS = ['XBT/USD', 'ETH/USD'];
window.PAIR_MAP = {
    // Bitcoin pairs
    'XBT/USD': 'XBT/USD',
    'XBT/USDT': 'XBT/USD',
    'XBTUSD': 'XBT/USD',
    'XBTUSDT': 'XBT/USD',
    'XXBTZUSD': 'XBT/USD',
    'XXBTZUSDT': 'XBT/USD',
    'xbtusdt': 'XBT/USD',
    'xbtusd': 'XBT/USD',
    'xbtusdt': 'XBT/USD',
    'xbtusd': 'XBT/USD',
    'xbt': 'XBT/USD',
    'xbtusd': 'XBT/USD',
    
    // Ethereum pairs
    'ETH/USD': 'ETH/USD',
    'ETH/USDT': 'ETH/USD',
    'ETHUSD': 'ETH/USD',
    'ETHUSDT': 'ETH/USD',
    'XETHZUSD': 'ETH/USD',
    'XETHZUSDT': 'ETH/USD',
    'ethusdt': 'ETH/USD',
    'ethusd': 'ETH/USD',
    'eth': 'ETH/USD',
    'ethusd': 'ETH/USD'
};

// Initialize global variables
window.wsLastMessageTime = null;
window.currentStrategy = 'admiral_toa';
window.updateTimer = null;

// Main WebSocket handler
(function() {
  // Local references to global variables
  const ws = window.ws;
  const reconnectAttempts = window.reconnectAttempts;
  const MAX_RECONNECT_ATTEMPTS = window.MAX_RECONNECT_ATTEMPTS;
  const RECONNECT_DELAY = window.RECONNECT_DELAY;
  const KRAKEN_WS_URL = window.KRAKEN_WS_URL;
  const SUPPORTED_PAIRS = window.SUPPORTED_PAIRS;
  const PAIR_MAP = window.PAIR_MAP;
  // [FIX] Always use window.wsLastMessageTime for global access
// Removed local destructure: use window.wsLastMessageTime everywhere.
  const currentStrategy = window.currentStrategy;
  // [FIX] Always use window.updateTimer for global access
// Removed local destructure: use window.updateTimer everywhere.

  // Function to normalize pair format
  function normalizePair(pair) {
    // First try exact match from global PAIR_MAP
    if (window.PAIR_MAP[pair]) {
        return window.PAIR_MAP[pair];
    }

    // Clean input: remove whitespace, convert to uppercase
    let cleanedPair = pair.trim().toUpperCase();
    
    // Try different normalization formats
    const formats = [
        cleanedPair, // original
        cleanedPair.replace(/[^A-Z0-9]/g, ''), // remove special chars
        cleanedPair.replace(/([A-Z]+)([A-Z]+)/, '$1/$2'), // add slash
        cleanedPair.replace('USD', 'ZUSD'), // add Z prefix for USD
        cleanedPair.replace(/([A-Z]+)ZUSD/, 'X$1ZUSD') // add X prefix for crypto
    ];

    // Check each format against PAIR_MAP
    for (const format of formats) {
        if (window.PAIR_MAP[format]) {
            return window.PAIR_MAP[format];
        }
    }

    // Try specific pair mappings
    const specificMappings = {
        'XBT/USD': 'XBT/USD',
        'XBT/USDT': 'XBT/USD',
        'XBTUSD': 'XBT/USD',
        'XBTUSDT': 'XBT/USD',
        'ETH/USD': 'ETH/USD',
        'ETH/USDT': 'ETH/USD',
        'ETHUSD': 'ETH/USD',
        'ETHUSDT': 'ETH/USD'
    };

    if (specificMappings[cleanedPair]) {
        return specificMappings[cleanedPair];
    }

    console.warn(`No Kraken pair mapping for: ${pair}`);
    return null;
    // Return original if no mapping found
    return pair;
  }

  // Timeframes
  const TIMEFRAMES = ['1', '5', '15', '60', '240', '1440'];
  
  // Data storage
  window.liveData = {};
  window.indicatorsData = {};
  window.lastApiUpdates = {};
  
  // Queue for WebSocket messages to prevent stack overflow
  const messageQueue = [];
  let isProcessingQueue = false;

  // Initialize WebSocket connection
  function initWebSocket() {
    if (window.ws) {
      window.ws.close();
    }

    // Connect to WebSocket server on port 8080
    const wsProtocol = 'ws:';
    const wsHost = 'localhost'; // Always connect to localhost
    const wsPort = '8080'; // WebSocket server port
    const wsPath = '/ws';
    const wsUrl = `${wsProtocol}//${wsHost}:${wsPort}${wsPath}`;
    
    console.log(`[WebSocket] Connecting to: ${wsUrl}`);
    
    try {
      window.ws = new WebSocket(wsUrl);
      
      // Set binary type to arraybuffer for better performance
      window.ws.binaryType = 'arraybuffer';
      
      // Reset reconnect attempts on successful connection
      window.reconnectAttempts = 0;
      
      // Connection opened
      window.ws.onopen = () => {
        console.log('[WebSocket] Connected to server');
        
        // Subscribe to default pair and timeframe
        if (window.currentPair && window.currentTimeframe) {
          window.ws.send(JSON.stringify({
            type: 'subscribe',
            pair: window.currentPair,
            timeframe: window.currentTimeframe
          }));
        }
      };
      
      // Listen for messages
      window.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('[WebSocket] Received message:', data);
          // Process incoming messages here
        } catch (error) {
          console.error('[WebSocket] Error parsing message:', error);
        }
      };
      
      // Handle connection close
      window.ws.onclose = (event) => {
        console.log(`[WebSocket] Connection closed:`, event.code, event.reason);
        
        // Attempt to reconnect with exponential backoff
        if (window.reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
          const delay = Math.min(1000 * Math.pow(2, window.reconnectAttempts), 30000);
          console.log(`[WebSocket] Attempting to reconnect in ${delay}ms (attempt ${window.reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);
          
          setTimeout(() => {
            window.reconnectAttempts++;
            initWebSocket();
          }, delay);
        } else {
          console.error('[WebSocket] Max reconnection attempts reached');
        }
      };
      
      // Handle errors
      window.ws.onerror = (error) => {
        console.error('[WebSocket] Error:', error);
      };
      
    } catch (error) {
      console.error('[WebSocket] Connection error:', error);
      // Attempt to reconnect on error
      if (window.reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        const delay = Math.min(1000 * Math.pow(2, window.reconnectAttempts), 30000);
        console.log(`[WebSocket] Will attempt to reconnect in ${delay}ms (attempt ${window.reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);
        
        setTimeout(() => {
          window.reconnectAttempts++;
          initWebSocket();
        }, delay);
      } else {
        console.error('[WebSocket] Max reconnection attempts reached');
      }
    };
    
    // Handle incoming messages
    window.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('[WebSocket] Received message:', data);
        
        if (data.type === 'ohlc') {
          const pair = data.pair;
          const timeframe = data.timeframe;
          const candle = data.data;
          
          if (pair && timeframe && candle) {
            // Update OHLC chart
            updateOHLCChart(pair, timeframe, candle);
            
            // Update signal matrix
            updateSignalMatrix(pair, timeframe, candle);
          }
        } else if (data.type === 'ticker') {
          const pair = data.pair;
          const ticker = data.data;
          
          if (pair && ticker) {
            // Update ticker display
            updateTicker(pair, ticker);
            
            // Update signal matrix with latest price
            updateSignalMatrix(pair, '1m', {
              timestamp: ticker.timestamp,
              open: ticker.open,
              high: ticker.high,
              low: ticker.low,
              close: ticker.close,
              volume: ticker.volume
            });
          }
        }
        
        // Handle trade data
        else if (data.type === 'trade') {
          const pair = data.pair;
          const trade = data.data;
          
          if (pair && trade) {
            // Update trade display
            updateTradeDisplay(pair, trade);
            
            // Update signal matrix with latest trade
            updateSignalMatrix(pair, '1m', {
              timestamp: trade.timestamp,
              open: trade.price,
              high: trade.price,
              low: trade.price,
              close: trade.price,
              volume: trade.volume
            });
          }
        }
        
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
        console.error('Message data:', event.data);
      }
    };
    
    window.ws.onclose = () => {
      console.log('WebSocket connection closed');
      // Only retry if we were connected before
      if (window.wsLastMessageTime) {
        setTimeout(initWebSocket, 5000); // Retry after 5 seconds
      }
    };
    
    window.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  // Process message queue with rate limiting and deduplication
  function processMessageQueue() {
    // Safety check - prevent processing if queue is being cleared
    if (!messageQueue || !Array.isArray(messageQueue)) {
      console.warn('Message queue is not properly initialized');
      isProcessingQueue = false;
      return;
    }
    
    if (messageQueue.length === 0) {
      isProcessingQueue = false;
      return;
    }
    
    // Prevent re-entrancy and detect potential infinite loops
    if (isProcessingQueue) {
      console.warn('processMessageQueue called while already processing');
      return;
    }
    
    isProcessingQueue = true;
    const startTime = Date.now();
    let processedCount = 0;
    const MAX_PROCESS_TIME_MS = 50; // Max time to spend processing messages in one batch (ms)
    const MAX_MESSAGES_PER_BATCH = 10; // Max number of messages to process in one batch
    
    try {
      // Process messages in batches with time limits
      while (messageQueue.length > 0 && 
             processedCount < MAX_MESSAGES_PER_BATCH && 
             Date.now() - startTime < MAX_PROCESS_TIME_MS) {
        
        const data = messageQueue.shift();
        if (!data) continue;
        
        try {
          // Skip logging for heartbeat messages
          if (data.type === 'heartbeat') {
            processedCount++;
            continue;
          }
          
          // Handle empty or invalid data
          if (typeof data !== 'object') {
            console.log('Received invalid WebSocket message data, ignoring');
            continue;
          }
          
          // Log message (except for high-frequency updates)
          if (data.type !== 'priceUpdate' && window.logMessages) {
            window.logMessages.push(`[${new Date().toLocaleString()}] Received ${data.type || 'unknown'} message`);
            if (window.logMessages.length > 100) window.logMessages.shift(); // Prevent memory leaks
            if (typeof window.updateLogger === 'function') window.updateLogger();
          }
          
          // Process message based on type
          switch (data.type) {
            case 'connection_established':
              if (window.logMessages) {
                window.logMessages.push(`[${new Date().toLocaleString()}] Connected to StarCrypt server: ${data.message}`);
                if (window.updateLogger) window.updateLogger();
              }
              break;
              
            case 'indicators':
              // Throttle indicator updates to prevent UI freezes
              const now = Date.now();
              if (window.lastIndicatorUpdate && (now - window.lastIndicatorUpdate < 100)) { // 100ms throttle
                // If we're getting updates too fast, skip this one
                continue;
              }
              window.lastIndicatorUpdate = now;
              
              // Initialize data structures if they don't exist
              if (!window.indicatorsData) window.indicatorsData = {};
              if (!window.indicatorsData[data.timeframe]) window.indicatorsData[data.timeframe] = {};
              
              try {
                // Sanitize and update indicator data
                if (Array.isArray(data.indicators)) {
                  const sanitizedIndicators = data.indicators.map(ind => ({
                    ...ind,
                    data: Array.isArray(ind.data) ? ind.data.map(val => Number.isFinite(val) ? val : 0) : [],
                    labels: Array.isArray(ind.labels) ? ind.labels.map(String) : []
                  }));
                  
                  // Batch update indicators
                  for (const indicator of sanitizedIndicators) {
                    window.indicatorsData[data.timeframe][indicator.name] = indicator;
                  }
                  
                  // Update signal lights with debouncing
                  if (typeof window.updateAllSignalLights === 'function') {
                    if (!window.signalLightUpdatePending) {
                      window.signalLightUpdatePending = true;
                      requestAnimationFrame(() => {
                        window.updateAllSignalLights();
                        window.signalLightUpdatePending = false;
                      });
                    }
                  }
                  
                  // Update mini charts
                  if (typeof window.updateMiniCharts === 'function') {
                    window.updateMiniCharts(data.pair);
                  }
                }
              } catch (e) {
                console.error('Error processing indicators:', e);
              }
              break;
              
        case 'priceUpdate':
          // Update current price
          window.currentPrice = data.price;
          
          // Update price display
          const priceElement = document.getElementById('currentPrice');
          if (priceElement) {
            const previousPrice = parseFloat(priceElement.getAttribute('data-price') || '0');
            const newPrice = parseFloat(data.price);
            
            // Update price
            priceElement.textContent = `${data.pair.toUpperCase()}: $${newPrice.toFixed(2)}`;
            priceElement.setAttribute('data-price', newPrice.toString());
            
            // Add price direction class
            if (newPrice > previousPrice) {
              priceElement.classList.remove('price-down');
              priceElement.classList.add('price-up', 'price-flash');
            } else if (newPrice < previousPrice) {
              priceElement.classList.remove('price-up');
              priceElement.classList.add('price-down', 'price-flash');
            }
            
            // Remove flash class after animation
            setTimeout(() => {
              priceElement.classList.remove('price-flash');
            }, 500);
          }
          break;
          
        case 'strategy_change_confirmation':
          // Strategy change confirmed
          if (window.logMessages) {
            window.logMessages.push(`[${new Date().toLocaleString()}] Strategy change confirmed: ${data.message}`);
            if (window.updateLogger) window.updateLogger();
          }
          
          // Update UI
          if (typeof window.updateSignalMatrix === 'function') {
            window.updateSignalMatrix();
          }
          break;
          
        case 'coin_change_confirmation':
          // Coin change confirmed
          if (window.logMessages) {
            window.logMessages.push(`[${new Date().toLocaleString()}] Coin change confirmed: ${data.message}`);
            if (window.updateLogger) window.updateLogger();
          }
          break;
          
        case 'error':
          // Error message
          if (window.logMessages) {
            window.logMessages.push(`[${new Date().toLocaleString()}] Server error: ${data.message}`);
            if (window.updateLogger) window.updateLogger();
          }
          break;
          

          
        case 'price':
          // Update price if valid
          if (data.price && !isNaN(parseFloat(data.price))) {
            window.currentPrice = parseFloat(data.price);
            // Update UI with new price
            if (typeof window.updatePriceDisplay === 'function') {
              window.updatePriceDisplay(window.currentPrice);
            }
          } else {
            console.warn('Received price message with invalid price data');
          }
          break;
          
        case 'strategy_update':
          // Update strategy if valid
          if (data.strategy && typeof data.strategy === 'string') {
            window.currentStrategy = data.strategy;
            console.log(`Strategy updated to ${data.strategy}`);
            // Update UI with new strategy
            if (typeof window.updateStrategyDisplay === 'function') {
              window.updateStrategyDisplay(data.strategy);
            }
          } else {
            console.warn('Received strategy_update message with invalid strategy data');
          }
          break;
          
        case 'pair_update':
          // Update trading pair if valid
          if (data.pair && typeof data.pair === 'string') {
            window.currentPair = data.pair;
            console.log(`Trading pair updated to ${data.pair}`);
            // Update UI with new pair
            if (typeof window.updatePairDisplay === 'function') {
              window.updatePairDisplay(data.pair);
            }
          } else {
            console.warn('Received pair_update message with invalid pair data');
          }
          break;
          
        case 'alerts':
          // Update alerts if valid
          if (data.alerts && Array.isArray(data.alerts)) {
            window.recentAlerts = data.alerts;
            // Update UI with new alerts
            if (typeof window.updateRecentAlerts === 'function') {
              window.updateRecentAlerts(data.alerts);
            }
          } else {
            console.warn('Received alerts message with invalid alerts data');
          }
          break;
          
        default:
          // Unknown message type
          console.log(`Unknown message type: ${data.type}`);
          break;
      }
    } catch (error) {
      console.error('Error processing WebSocket message:', error);
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Error processing WebSocket message: ${error.message}`);
        if (window.updateLogger) window.updateLogger();
      }
    } finally {
      // Continue processing the queue with a small delay to prevent stack overflow
      setTimeout(processMessageQueue, 0);
    }
  }

  // Add robust global error handler
  if (!window.__errorHandlerInstalled) {
    const originalOnError = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
      // Prevent error loops
      if (window.__errorHandling) return true;
      window.__errorHandling = true;
      
      try {
        // Skip common non-critical errors
        if (message === 'Script error.' && !source) {
          return true; // Ignore cross-origin script errors
        }
        
        console.error('Global error:', {
          message,
          source,
          lineno,
          colno,
          error: error && error.stack ? error.stack : error
        });
        
        // Call original handler if it exists
        if (typeof originalOnError === 'function') {
          return originalOnError(message, source, lineno, colno, error);
        }
        
        return false;
      } finally {
        window.__errorHandling = false;
      }
    };
    window.__errorHandlerInstalled = true;
  }

  // Global variables
  // (Removed duplicate assignments to wsLastMessageTime and updateTimer; these are now set only at the top for safety)
  window.currentStrategy = 'admiral_toa';

  // Connect WebSocket when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initWebSocket);
  } else {
    // DOM already loaded
    initWebSocket();
  }
})(); // Closing IIFE
