const WebSocket = require('ws');
const url = require('url');
const { v4: uuidv4 } = require('uuid');
const { fetchLivePrice, initializeLiveData } = require('./data-fetcher');
const { calculateAllIndicators } = require('./indicators');

// Configuration
const DEFAULT_PAIR = 'xbtusdt';
const DEFAULT_TIMEFRAME = '1h';
const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];

// Data storage
const clients = new Set();
const clientSubscriptions = new Map();
const historicalData = {};
const indicatorsData = {};
const lastLivePrice = {};

// WebSocket server setup
function setupWebSocketServer(server) {
  const wss = new WebSocket.Server({ server });

  // Handle new WebSocket connections
  wss.on('connection', (ws, req) => {
    try {
      const ip = req.socket.remoteAddress;
      console.log(`New WebSocket connection from ${ip}`);
      
      // Parse URL parameters
      const parameters = url.parse(req.url, true).query;
      const pair = (parameters.pair || DEFAULT_PAIR).toLowerCase();
      
      // Add client to active connections
      clients.add(ws);
      ws.isAlive = true;
      
      // Initialize live data for this pair if needed
      initializeLiveData(pair);
      
      // Handle incoming messages from client
      ws.on('message', async (message) => {
        try {
          const msg = JSON.parse(message);
          console.log('Received message:', msg.type);

          await handleClientMessage(ws, msg);
        } catch (error) {
          console.error('Error processing message:', error);
          sendError(ws, 'Error processing message', error.message);
        }
      });
      
      // Handle pong messages for heartbeat
      ws.on('pong', () => {
        ws.isAlive = true;
      });
      
      // Handle client disconnection
      ws.on('close', () => {
        console.log(`Client ${ip} disconnected`);
        cleanupClient(ws);
      });
      
      // Handle errors
      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        cleanupClient(ws);
      });
      
    } catch (error) {
      console.error('Error in WebSocket connection handler:', error);
      if (ws.readyState === WebSocket.OPEN) {
        ws.close(1011, 'Server error');
      }
    }
  });

  // Set up heartbeat to check for dead connections
  const interval = setInterval(() => {
    wss.clients.forEach((ws) => {
      if (ws.isAlive === false) {
        cleanupClient(ws);
        return ws.terminate();
      }
      ws.isAlive = false;
      ws.ping();
    });
  }, 30000);

  // Clean up on server close
  wss.on('close', () => {
    clearInterval(interval);
    clients.forEach(cleanupClient);
  });

  return wss;
}

// Handle client messages
async function handleClientMessage(ws, msg) {
  try {
    switch (msg.type) {
      case 'requestData':
      case 'requestIndicators':
        await handleDataRequest(ws, msg);
        break;
      case 'subscribe':
        handleSubscription(ws, msg);
        break;
      case 'unsubscribe':
        handleUnsubscription(ws, msg);
        break;
      default:
        console.warn(`Unknown message type: ${msg.type}`);
        sendError(ws, `Unknown message type: ${msg.type}`);
    }
  } catch (error) {
    console.error('Error handling client message:', error);
    sendError(ws, 'Error processing message', error.message);
  }
}

// Handle data requests from client
async function handleDataRequest(ws, msg) {
  const pair = (msg.pair || DEFAULT_PAIR).toLowerCase();
  const timeframe = msg.timeframe || DEFAULT_TIMEFRAME;
  
  if (!pair || !timeframe) {
    throw new Error('Missing pair or timeframe in request');
  }

  console.log(`Processing ${msg.type} for ${pair}/${timeframe}`);

  // Check if we have data for this pair/timeframe
  const hasData = historicalData[pair]?.[timeframe]?.length > 0;
  
  if (!hasData) {
    console.log(`No data available for ${pair}/${timeframe}, fetching...`);
    await fetchAndUpdateData(pair, timeframe);
  }

  // Prepare response
  const response = {
    type: msg.type === 'requestData' ? 'historicalData' : 'indicators',
    pair,
    timeframe,
    data: msg.type === 'requestData' 
      ? historicalData[pair]?.[timeframe] || [] 
      : indicatorsData[pair]?.[timeframe] || {},
    lastUpdate: new Date().toISOString()
  };

  ws.send(JSON.stringify(response));
}

// Handle subscription requests
function handleSubscription(ws, msg) {
  const pair = (msg.pair || DEFAULT_PAIR).toLowerCase();
  const timeframes = Array.isArray(msg.timeframes) 
    ? msg.timeframes 
    : [msg.timeframe || DEFAULT_TIMEFRAME];

  // Get or create subscription
  let subscription = clientSubscriptions.get(ws);
  if (!subscription) {
    subscription = { pair, timeframes: new Set() };
    clientSubscriptions.set(ws, subscription);
  }

  // Add timeframes to subscription
  timeframes.forEach(tf => {
    if (TIMEFRAMES.includes(tf)) {
      subscription.timeframes.add(tf);
    }
  });

  console.log(`Client subscribed to ${pair} on timeframes: ${[...subscription.timeframes].join(', ')}`);
  
  // Send confirmation
  ws.send(JSON.stringify({
    type: 'subscriptionUpdate',
    pair,
    timeframes: [...subscription.timeframes],
    status: 'subscribed'
  }));
}

// Handle unsubscription requests
function handleUnsubscription(ws, msg) {
  const subscription = clientSubscriptions.get(ws);
  if (!subscription) return;

  const timeframes = Array.isArray(msg.timeframes) 
    ? msg.timeframes 
    : [msg.timeframe];

  if (timeframes.length === 0) {
    // Unsubscribe from all timeframes
    clientSubscriptions.delete(ws);
  } else {
    // Unsubscribe from specific timeframes
    timeframes.forEach(tf => {
      subscription.timeframes.delete(tf);
    });
    
    // If no more timeframes, remove subscription
    if (subscription.timeframes.size === 0) {
      clientSubscriptions.delete(ws);
    }
  }

  console.log(`Client unsubscribed from timeframes: ${timeframes.join(', ')}`);
  
  // Send confirmation
  ws.send(JSON.stringify({
    type: 'subscriptionUpdate',
    pair: subscription.pair,
    timeframes: subscription.timeframes.size > 0 ? [...subscription.timeframes] : [],
    status: 'unsubscribed'
  }));
}

// Clean up client resources
function cleanupClient(ws) {
  clients.delete(ws);
  clientSubscriptions.delete(ws);
  
  // Close the WebSocket if it's still open
  if (ws.readyState === WebSocket.OPEN) {
    ws.terminate();
  }
}

// Send error message to client
function sendError(ws, message, details = '') {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({
      type: 'error',
      message,
      details,
      timestamp: new Date().toISOString()
    }));
  }
}

module.exports = {
  setupWebSocketServer,
  clients,
  clientSubscriptions,
  historicalData,
  indicatorsData,
  lastLivePrice
};
