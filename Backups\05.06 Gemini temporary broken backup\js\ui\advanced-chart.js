// Advanced WebGL-based Chart Component
class AdvancedChart {
  constructor(canvasId, options = {}) {
    this.canvas = document.getElementById(canvasId)
    if (!this.canvas) {
      console.error(`Canvas element with id '${canvasId}' not found`)
      return
    }

    this.ctx = this.canvas.getContext('webgl2', {
      antialias: true,
      alpha: true,
      preserveDrawingBuffer: true,
    })

    if (!this.ctx) {
      console.error('WebGL2 not supported, falling back to 2D canvas')
      this.ctx = this.canvas.getContext('2d')
      this.isWebGL = false
    } else {
      this.isWebGL = true
      this.initWebGL()
    }

    // Default options
    this.options = {
      theme: 'dark',
      showGrid: true,
      showCrosshair: true,
      showVolume: true,
      timeFormat: 'HH:mm',
      priceFormat: '0.00000000',
      volumeFormat: '0.0000',
      ...options,
    }

    // State
    this.data = []
    this.candles = []
    this.indicators = {}
    this.visibleRange = 100
    this.offset = 0
    this.scale = 1
    this.isDragging = false
    this.lastX = 0
    this.lastY = 0
    this.crosshair = { x: -1, y: -1, visible: false }
    this.dimensions = {
      width: 0,
      height: 0,
      priceScaleWidth: 80,
      timeScaleHeight: 30,
      volumeHeight: 60,
      padding: { top: 20, right: 10, bottom: 0, left: 0 },
    }

    // Initialize
    this.resizeObserver = new ResizeObserver(() => this.handleResize())
    this.resizeObserver.observe(this.canvas)
    this.setupEventListeners()
    this.handleResize()

    // Initialize WebGL if available
    if (this.isWebGL) {
      this.initShaders()
      this.initBuffers()
    }
  }

  // WebGL Initialization
  initWebGL() {
    // Enable blending for transparency
    this.ctx.enable(this.ctx.BLEND)
    this.ctx.blendFunc(this.ctx.SRC_ALPHA, this.ctx.ONE_MINUS_SRC_ALPHA)

    // Set clear color based on theme
    this.ctx.clearColor(0.05, 0.06, 0.1, 1.0)
    this.ctx.clear(this.ctx.COLOR_BUFFER_BIT)
  }

  // Shader Programs
  initShaders() {
    // Vertex shader source
    const vsSource = `
      attribute vec2 a_position;
      uniform vec2 u_resolution;
      uniform mat3 u_matrix;
      
      void main() {
        vec2 position = (u_matrix * vec3(a_position, 1)).xy;
        
        // Convert from pixels to clip space
        vec2 clipSpace = ((position / u_resolution) * 2.0 - 1.0) * vec2(1, -1);
        gl_Position = vec4(clipSpace, 0, 1);
      }
    `

    // Fragment shader source
    const fsSource = `
      precision mediump float;
      uniform vec4 u_color;
      
      void main() {
        gl_FragColor = u_color;
      }
    `

    // Create shader program
    this.shaderProgram = this.createShaderProgram(vsSource, fsSource)

    // Get attribute and uniform locations
    this.programInfo = {
      program: this.shaderProgram,
      attribLocations: {
        position: this.ctx.getAttribLocation(this.shaderProgram, 'a_position'),
      },
      uniformLocations: {
        resolution: this.ctx.getUniformLocation(this.shaderProgram, 'u_resolution'),
        color: this.ctx.getUniformLocation(this.shaderProgram, 'u_color'),
        matrix: this.ctx.getUniformLocation(this.shaderProgram, 'u_matrix'),
      },
    }
  }

  // Create shader program from source
  createShaderProgram(vsSource, fsSource) {
    const vertexShader = this.loadShader(this.ctx.VERTEX_SHADER, vsSource)
    const fragmentShader = this.loadShader(this.ctx.FRAGMENT_SHADER, fsSource)

    const shaderProgram = this.ctx.createProgram()
    this.ctx.attachShader(shaderProgram, vertexShader)
    this.ctx.attachShader(shaderProgram, fragmentShader)
    this.ctx.linkProgram(shaderProgram)

    if (!this.ctx.getProgramParameter(shaderProgram, this.ctx.LINK_STATUS)) {
      console.error(`Unable to initialize the shader program: ${this.ctx.getProgramInfoLog(shaderProgram)}`)
      return null
    }

    return shaderProgram
  }

  // Load shader from source
  loadShader(type, source) {
    const shader = this.ctx.createShader(type)
    this.ctx.shaderSource(shader, source)
    this.ctx.compileShader(shader)

    if (!this.ctx.getShaderParameter(shader, this.ctx.COMPILE_STATUS)) {
      console.error(`An error occurred compiling the shaders: ${this.ctx.getShaderInfoLog(shader)}`)
      this.ctx.deleteShader(shader)
      return null
    }

    return shader
  }

  // Initialize buffers
  initBuffers() {
    this.buffers = {
      position: this.ctx.createBuffer(),
      color: this.ctx.createBuffer(),
      indices: this.ctx.createBuffer(),
    }
  }

  // Event Handlers
  setupEventListeners() {
    this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this))
    this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this))
    this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this))
    this.canvas.addEventListener('mouseleave', this.handleMouseLeave.bind(this))
    this.canvas.addEventListener('wheel', this.handleWheel.bind(this), { passive: false })

    // Touch events for mobile
    this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false })
    this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false })
    this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this))
  }

  handleResize() {
    // Get display size of canvas
    const displayWidth = this.canvas.clientWidth
    const displayHeight = this.canvas.clientHeight

    // Check if canvas is not the same size as display
    if (this.canvas.width !== displayWidth || this.canvas.height !== displayHeight) {
      // Update canvas dimensions
      this.canvas.width = displayWidth
      this.canvas.height = displayHeight

      // Update internal dimensions
      this.dimensions.width = displayWidth
      this.dimensions.height = displayHeight

      // Update WebGL viewport
      if (this.isWebGL) {
        this.ctx.viewport(0, 0, this.ctx.drawingBufferWidth, this.ctx.drawingBufferHeight)
      }

      // Redraw
      this.render()
    }
  }

  // Data Management
  setData(data) {
    if (!Array.isArray(data)) {
      console.error('Data must be an array')
      return
    }

    this.data = data
    this.candles = data.map(item => ({
      time: new Date(item.time),
      open: parseFloat(item.open),
      high: parseFloat(item.high),
      low: parseFloat(item.low),
      close: parseFloat(item.close),
      volume: parseFloat(item.volume) || 0,
    }))

    // Reset view
    this.offset = Math.max(0, this.candles.length - this.visibleRange)
    this.render()
  }

  addIndicator(name, data, options = {}) {
    this.indicators[name] = {
      data,
      type: options.type || 'line',
      color: options.color || this.getRandomColor(),
      visible: options.visible !== false,
      yAxis: options.yAxis || 'left',
      lineWidth: options.lineWidth || 2,
    }
    this.render()
  }

  // Rendering
  render() {
    if (this.isWebGL) {
      this.renderWebGL()
    } else {
      this.render2D()
    }
  }

  renderWebGL() {
    if (!this.ctx || !this.shaderProgram) return

    // Clear the canvas
    this.ctx.clear(this.ctx.COLOR_BUFFER_BIT)

    // Set the shader program
    this.ctx.useProgram(this.shaderProgram)

    // Set resolution uniform
    this.ctx.uniform2f(
      this.programInfo.uniformLocations.resolution,
      this.canvas.width,
      this.canvas.height,
    )

    // Draw grid
    this.drawGridWebGL()

    // Draw candles
    this.drawCandlesWebGL()

    // Draw indicators
    Object.entries(this.indicators).forEach(([name, indicator]) => {
      if (indicator.visible) {
        this.drawIndicatorWebGL(name, indicator)
      }
    })

    // Draw crosshair if visible
    if (this.crosshair.visible) {
      this.drawCrosshairWebGL()
    }

    // Draw axis
    this.drawAxisWebGL()
  }

  render2D() {
    const { ctx } = this
    const { width, height } = this.dimensions

    // Clear canvas
    ctx.clearRect(0, 0, width, height)

    // Draw background
    ctx.fillStyle = this.options.theme === 'dark' ? '#0a0e17' : '#ffffff'
    ctx.fillRect(0, 0, width, height)

    // Draw grid
    this.drawGrid2D()

    // Draw volume if enabled
    if (this.options.showVolume) {
      this.drawVolume2D()
    }

    // Draw candles
    this.drawCandles2D()

    // Draw indicators
    Object.entries(this.indicators).forEach(([name, indicator]) => {
      if (indicator.visible) {
        this.drawIndicator2D(name, indicator)
      }
    })

    // Draw crosshair if visible
    if (this.crosshair.visible) {
      this.drawCrosshair2D()
    }

    // Draw axis
    this.drawAxis2D()
  }

  // Drawing methods for WebGL and 2D contexts would be implemented here
  // ...

  // Event handlers for mouse and touch events
  // ...

  // Helper methods for coordinate conversion
  // ...
}

// Export to window
window.AdvancedChart = AdvancedChart
