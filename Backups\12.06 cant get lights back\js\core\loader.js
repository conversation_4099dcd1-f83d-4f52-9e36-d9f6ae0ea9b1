document.addEventListener('DOMContentLoaded', () => {
    console.log('[StarCrypt Loader] DOMContentLoaded event fired. Initializing dynamic script loader.');

    function loadScript(src, callback) {
        console.log(`[StarCrypt Loader] Preparing to load script: ${src}`);
        const script = document.createElement('script');
        script.src = src;
        script.async = false; // Ensure sequential execution
        script.onload = () => {
            console.log(`[StarCrypt Loader] Successfully loaded ${src}. Executing callback if present.`);
            if (callback) {
                try {
                    callback();
                } catch (e) {
                    console.error(`[StarCrypt Loader] Error in callback for ${src}:`, e);
                }
            }
        };
        script.onerror = () => {
            console.error(`[StarCrypt Loader] Failed to load script: ${src}`);
        };
        document.head.appendChild(script);
        console.log(`[StarCrypt Loader] Appended ${src} to document.head.`);
    }

    // Load signal-matrix.js first
    console.log('[StarCrypt Loader] Queuing load for js/ui/signal-matrix.js');
    loadScript('js/ui/signal-matrix.js', () => {
        console.log('[StarCrypt Loader] Callback for js/ui/signal-matrix.js executing. Queuing load for js/core/signal-system.js');
        // Then load signal-system.js
        loadScript('js/core/signal-system.js', () => {
            console.log('[StarCrypt Loader] Callback for js/core/signal-system.js executing.');
            // Finally, initialize SignalSystem after both scripts are loaded
            if (window.SignalSystem && typeof window.SignalSystem.init === 'function') {
                console.log('[StarCrypt Loader] SignalSystem found. Attempting to call window.SignalSystem.init().');
                try {
                    window.SignalSystem.init();
                    console.log('[StarCrypt Loader] window.SignalSystem.init() called successfully.');
                } catch (e) {
                    console.error('[StarCrypt Loader] Error calling window.SignalSystem.init():', e);
                }
            } else {
                console.error('[StarCrypt Loader] SignalSystem not found or init method is missing after loading scripts. window.SignalSystem:', window.SignalSystem);
            }
        });
    });
    console.log('[StarCrypt Loader] Script loading sequence initiated.');
});
