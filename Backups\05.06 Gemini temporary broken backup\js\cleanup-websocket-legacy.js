/**
 * WebSocket Legacy Cleanup Script
 * Removes any remaining references to the old WebSocket processor
 * and ensures a clean initialization state.
 */

(function () {
  'use strict'

  // Clean up old global references
  const cleanupGlobals = () => {
    const legacyGlobals = [
      'WebSocketProcessor', // Old global reference
      'wsProcessorOld', // Old instance reference
      'wsManagerOld', // Old manager reference
    ]

    legacyGlobals.forEach(globalName => {
      if (window[globalName]) {
        console.log(`[Cleanup] Removing legacy global: ${globalName}`)
        try {
          // If it has a cleanup method, call it first
          if (typeof window[globalName].cleanup === 'function') {
            window[globalName].cleanup()
          }
          // If it has a disconnect method, call it
          if (typeof window[globalName].disconnect === 'function') {
            window[globalName].disconnect()
          }
          // Remove the global
          delete window[globalName]
        } catch (error) {
          console.error(`[Cleanup] Error cleaning up ${globalName}:`, error)
        }
      }
    })
  }

  // Clean up event listeners
  const cleanupEventListeners = () => {
    // List of events that might have been used by the old implementation
    const legacyEvents = [
      'websocket:message',
      'websocket:open',
      'websocket:close',
      'websocket:error',
    ]

    legacyEvents.forEach(eventName => {
      // Clone the event listeners to avoid modifying the list while iterating
      const listeners = window.getEventListeners ?
        window.getEventListeners(document)[eventName] || [] : []

      if (listeners && listeners.length > 0) {
        console.log(`[Cleanup] Found ${listeners.length} listeners for ${eventName}`)
        listeners.forEach(listener => {
          document.removeEventListener(eventName, listener.listener, listener.useCapture)
        })
      }
    })
  }

  // Main cleanup function
  const cleanup = () => {
    console.log('[Cleanup] Starting WebSocket legacy cleanup...')

    // Clean up globals
    cleanupGlobals()

    // Clean up event listeners if possible
    if (typeof window.getEventListeners === 'function') {
      cleanupEventListeners()
    } else {
      console.log('[Cleanup] getEventListeners not available, skipping event listener cleanup')
    }

    console.log('[Cleanup] WebSocket legacy cleanup complete')
  }

  // Run cleanup when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', cleanup)
  } else {
    // DOM already loaded, run cleanup immediately
    setTimeout(cleanup, 0)
  }

  // Export for testing
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
      cleanup,
      cleanupGlobals,
      cleanupEventListeners,
    }
  }
})()
