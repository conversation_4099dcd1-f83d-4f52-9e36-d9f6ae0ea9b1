```javascript
const express = require('express');
const WebSocket = require('ws');
const fetch = require('node-fetch').default;
const url = require('url');
const { RateLimiter } = require('limiter');

// Kraken WebSocket setup
const KRAKEN_WS_URL = 'wss://ws.kraken.com/';
let krakenWs = new WebSocket(KRAKEN_WS_URL);
const clients = new Set(); // Track connected clients
let wsConnected = true; // Track WebSocket connection status
let reconnectDelay = 1000; // Initial reconnection delay (ms)

// Rate limiter for Kraken REST API (15 requests per minute)
const krakenLimiter = new RateLimiter({ tokensPerInterval: 15, interval: 'minute' });

// Create express app
const app = express();

// Use port 8080 for WebSocket as that's what the client expects
const wss = new WebSocket.Server({ port: 8080, clientTracking: true })
  .on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.error('WebSocket server port 8080 in use. Trying port 8081.');
      return new WebSocket.Server({ port: 8081, clientTracking: true });
    } else {
      console.error(`WebSocket server error: ${error.message}`);
    }
  });

// Fixed port handling for asynchronous errors
const server = app.listen(3000, () => {
  console.log(`StarCrypt Enterprise server running on port ${server.address().port}`);
});
server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error('Express server port 3000 in use. Trying port 3001.');
    app.listen(3001, () => {
      console.log(`StarCrypt Enterprise server running on port 3001`);
    });
  } else {
    console.error(error);
  }
});

// Kraken WebSocket connection
krakenWs.on('open', () => {
  console.log('Connected to Kraken WebSocket');
  wsConnected = true;
  reconnectDelay = 1000; // Reset reconnection delay
  const subscribeMsg = {
    event: 'subscribe',
    pair: ['XBT/USD', 'ETH/USD', 'LTC/USD', 'XRP/USD', 'ADA/USD', 'SOL/USD', 'DOT/USD', 'DOGE/USD'],
    subscription: { name: 'ticker' }
  };
  krakenWs.send(JSON.stringify(subscribeMsg));
});

// Handle Kraken WebSocket errors
krakenWs.on('error', (error) => {
  console.error('Kraken WebSocket error:', error);
});

// Handle Kraken WebSocket close
krakenWs.on('close', () => {
  console.log('Disconnected from Kraken WebSocket. Reconnecting...');
  wsConnected = false;
  setTimeout(() => {
    console.log(`Reconnecting to Kraken WebSocket in ${reconnectDelay/1000}s...`);
    krakenWs = new WebSocket(KRAKEN_WS_URL);
    reconnectDelay = Math.min(reconnectDelay * 2, 60000); // Exponential backoff
  }, reconnectDelay);
});

// Helper function to broadcast to all clients
function broadcast(data) {
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(data);
    }
  });
}

// Configuration
const DEFAULT_PAIR = 'xbtusdt';
const DEFAULT_TIMEFRAME = '1h';
const DEFAULT_STRATEGY = 'admiral_toa';
const SUPPORTED_PAIRS = ['xbtusdt', 'ethusdt', 'ltcusdt', 'xrpusdt', 'adausdt', 'solusdt', 'dotusdt', 'dogeusdt'];
const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
const INTERVALS = { '1m': 1, '5m': 5, '15m': 15, '1h': 60, '4h': 240, '1d': 1440, '1w': 10080 };
const TIMEFRAME_SECONDS = { '1m': 60, '5m': 300, '15m': 900, '1h': 3600, '4h': 14400, '1d': 86400, '1w': 604800 };
const MAX_CANDLES = 720;
const REQUEST_DELAY = 1000; // 1 second delay between API requests
const FETCH_COOLDOWN = 30000; // 30 seconds cooldown for OHLC fetches

// Data storage
let historicalData = {};
let liveData = {}; // WebSocket-derived partial candles
let indicatorsData = {};
let lastLivePrice = {};
let subscriptions = new Map();
const lastFetchTime = {};
const activeFetches = {};
const lastPeriodicUpdate = {};

// Kraken pair mappings
const KRAKEN_PAIRS = {
  'xbtusdt': 'XBTUSD',
  'ethusdt': 'ETHUSD',
  'ltcusdt': 'LTCUSD',
  'xrpusdt': 'XRPUSD',
  'adausdt': 'ADAUSD',
  'solusdt': 'SOLUSD',
  'dotusdt': 'DOTUSD',
  'dogeusdt': 'DOGEUSD'
};

// Process Kraken WebSocket messages
krakenWs.on('message', (data) => {
  try {
    const message = JSON.parse(data);
    if (Array.isArray(message) && message[2] === 'ticker') {
      const tickerData = message[1];
      const krakenPair = message[3];
      const pair = Object.keys(KRAKEN_PAIRS).find(k => KRAKEN_PAIRS[k] === krakenPair);
      if (!pair) return;

      const price = parseFloat(tickerData.c[0]);
      const bid = parseFloat(tickerData.b[0]);
      const ask = parseFloat(tickerData.a[0]);
      const timestamp = Date.now();

      // Update live price cache
      lastLivePrice[pair] = { price, bid, ask, timestamp };
      broadcast(JSON.stringify({ type: 'livePrice', pair, data: lastLivePrice[pair] }));

      // Update liveData for partial candles
      liveData[pair] = liveData[pair] || {};
      TIMEFRAMES.forEach(tf => {
        liveData[pair][tf] = liveData[pair][tf] || {
          prices: [],
          timestamps: [],
          partialCandle: { o: price, h: price, l: price, c: price, v: 0 }
        };
        const intervalMs = TIMEFRAME_SECONDS[tf] * 1000;
        const lastTimestamp = liveData[pair][tf].timestamps[0] || timestamp;
        if (timestamp - lastTimestamp >= intervalMs) {
          // Start new partial candle
          liveData[pair][tf].prices.push(liveData[pair][tf].partialCandle.c);
          liveData[pair][tf].timestamps.push(timestamp);
          liveData[pair][tf].partialCandle = { o: price, h: price, l: price, c: price, v: 0 };
          // Limit to MAX_CANDLES
          if (liveData[pair][tf].prices.length > MAX_CANDLES) {
            liveData[pair][tf].prices.shift();
            liveData[pair][tf].timestamps.shift();
          }
        } else {
          // Update partial candle
          liveData[pair][tf].partialCandle.h = Math.max(liveData[pair][tf].partialCandle.h, price);
          liveData[pair][tf].partialCandle.l = Math.min(liveData[pair][tf].partialCandle.l, price);
          liveData[pair][tf].partialCandle.c = price;
          liveData[pair][tf].partialCandle.v += 1; // Simulate volume
        }
        // Calculate indicators from live data
        const indicators = calculateAllIndicatorsFromLiveData(pair, tf, liveData[pair][tf]);
        broadcast(JSON.stringify({
          type: 'liveIndicators',
          pair,
          timeframe: tf,
          data: indicators,
          isConfirmed: false
        }));
      });

      // Broadcast raw ticker for legacy compatibility
      broadcast(JSON.stringify({
        type: 'kraken_ticker',
        data: tickerData
      }));
    }
  } catch (error) {
    console.error('Error processing Kraken message:', error);
  }
});

// Fetch Kraken OHLC data
async function fetchOHLC(pair, timeframe, forceUpdate = false) {
  try {
    const fetchKey = `${pair}-${timeframe}`;
    if (activeFetches[fetchKey]) {
      console.log(`Waiting for in-progress fetch of ${pair}/${timeframe}...`);
      return await activeFetches[fetchKey];
    }
    const now = Date.now();
    const lastFetch = lastFetchTime[fetchKey] || 0;
    if (!forceUpdate && historicalData[pair]?.[timeframe]?.length > 0 && now - lastFetch < FETCH_COOLDOWN) {
      console.log(`Using cached OHLC data for ${pair}/${timeframe} (${historicalData[pair][timeframe].length} candles)`);
      return historicalData[pair][timeframe];
    }
    const fetchPromise = (async () => {
      try {
        await krakenLimiter.removeTokens(1);
        console.log(`Rate limit tokens remaining: ${krakenLimiter.getTokensRemaining()}`);
        lastFetchTime[fetchKey] = now;
        const krakenPair = KRAKEN_PAIRS[pair];
        if (!krakenPair) {
          console.warn(`No Kraken pair mapping for ${pair}`);
          return [];
        }
        const interval = INTERVALS[timeframe];
        console.log(`Fetching OHLC for ${pair}/${timeframe}: ${krakenPair}, interval: ${interval}`);
        const response = await fetch(`https://api.kraken.com/0/public/OHLC?pair=${krakenPair}&interval=${interval}`);
        const data = await response.json();
        if (data.error && data.error.length > 0) {
          console.warn(`Kraken API error for ${pair}/${timeframe}: ${data.error.join(', ')}`);
          return [];
        }
        const resultKey = Object.keys(data.result).find(key => key !== 'last');
        if (!resultKey || !data.result[resultKey] || !Array.isArray(data.result[resultKey])) {
          console.warn(`Invalid data format from Kraken for ${pair}/${timeframe}`);
          return [];
        }
        const candles = data.result[resultKey].slice(-MAX_CANDLES).map(c => ({
          t: parseInt(c[0]) * 1000,
          o: parseFloat(c[1]),
          h: parseFloat(c[2]),
          l: parseFloat(c[3]),
          c: parseFloat(c[4]),
          v: parseFloat(c[6]),
          x: parseInt(c[0]) * 1000
        }));
        historicalData[pair] = historicalData[pair] || {};
        historicalData[pair][timeframe] = candles;
        console.log(`Fetched OHLC for ${pair}/${timeframe}: ${candles.length} candles`);
        return candles;
      } catch (apiError) {
        console.error(`Error fetching OHLC for ${pair}/${timeframe}: ${apiError.message}`);
        return [];
      } finally {
        delete activeFetches[fetchKey];
      }
    })();
    activeFetches[fetchKey] = fetchPromise;
    return await fetchPromise;
  } catch (e) {
    console.error(`Error in fetchOHLC for ${pair}/${timeframe}: ${e.message}`);
    return [];
  }
}

// Fetch live price (REST fallback)
async function fetchLivePrice(pair) {
  try {
    await krakenLimiter.removeTokens(1);
    console.log(`Rate limit tokens remaining: ${krakenLimiter.getTokensRemaining()}`);
    const krakenPair = KRAKEN_PAIRS[pair];
    if (!krakenPair) {
      console.warn(`No Kraken pair mapping for ${pair}`);
      return null;
    }
    const response = await fetch(`https://api.kraken.com/0/public/Ticker?pair=${krakenPair}`);
    const data = await response.json();
    if (data.error && data.error.length > 0) {
      console.warn(`Kraken API error for ${pair}: ${data.error.join(', ')}`);
      return null;
    }
    const resultKey = Object.keys(data.result)[0];
    if (!data.result || !data.result[resultKey]) {
      console.warn(`Invalid data format from Kraken for ${pair}`);
      return null;
    }
    const ticker = data.result[resultKey];
    const priceData = {
      price: parseFloat(ticker.c[0]),
      bid: parseFloat(ticker.b[0]),
      ask: parseFloat(ticker.a[0]),
      timestamp: Date.now()
    };
    console.log(`Fetched price for ${pair}: $${priceData.price}`);
    return priceData;
  } catch (e) {
    console.error(`Error fetching price for ${pair}: ${e.message}`);
    return null;
  }
}

// Indicator calculations
function calculateRSI(closes, period = 14) {
  if (closes.length < period + 1) return {};
  let gains = 0, losses = 0;
  for (let i = 1; i <= period; i++) {
    const diff = closes[i] - closes[i - 1];
    if (diff >= 0) gains += diff;
    else losses -= diff;
  }
  let avgGain = gains / period;
  let avgLoss = losses / period;
  const rs = avgGain / (avgLoss || 1);
  const rsi = 100 - (100 / (1 + rs));
  let signalClass = 'neutral';
  if (rsi > 70) signalClass = 'degen-sell';
  else if (rsi > 60) signalClass = 'mild-sell';
  else if (rsi < 30) signalClass = 'degen-buy';
  else if (rsi < 40) signalClass = 'mild-buy';
  return {
    value: rsi,
    overbought: rsi > 70,
    oversold: rsi < 30,
    signalClass,
    color: rsi > 70 ? '#FF0000' : rsi > 60 ? '#FFA500' : rsi < 30 ? '#00FF00' : rsi < 40 ? '#0000FF' : '#808080'
  };
}

function calculateStochRSI(closes, period = 14) {
  const rsiValues = closes.slice(-period - 1).map((_, i) => calculateRSI(closes.slice(i, i + period + 1)).value).filter(v => v);
  if (rsiValues.length < period) return {};
  const rsi = rsiValues[rsiValues.length - 1];
  const high = Math.max(...rsiValues);
  const low = Math.min(...rsiValues);
  const stoch = ((rsi - low) / (high - low || 1)) * 100;
  let signalClass = 'neutral';
  if (stoch > 80) signalClass = 'degen-sell';
  else if (stoch > 70) signalClass = 'mild-sell';
  else if (stoch < 20) signalClass = 'degen-buy';
  else if (stoch < 30) signalClass = 'mild-buy';
  return {
    value: stoch,
    overbought: stoch > 80,
    oversold: stoch < 20,
    signalClass,
    color: stoch > 80 ? '#FF0000' : stoch > 70 ? '#FFA500' : stoch < 20 ? '#00FF00' : stoch < 30 ? '#0000FF' : '#808080'
  };
}

function calculateMACD(closes, fast = 12, slow = 26, signal = 9) {
  if (closes.length < Math.max(fast, slow, signal)) return {};
  const emaFast = calculateEMA(closes, fast);
  const emaSlow = calculateEMA(closes, slow);
  const macd = emaFast - emaSlow;
  const macdHistory = [];
  for (let i = 0; i < closes.length; i++) {
    if (i < slow - 1) macdHistory.push(0);
    else {
      const fastEMA = calculateEMA(closes.slice(0, i + 1), fast);
      const slowEMA = calculateEMA(closes.slice(0, i + 1), slow);
      macdHistory.push(fastEMA - slowEMA);
    }
  }
  const signalLine = calculateEMA(macdHistory.slice(-signal), signal);
  const histogram = macd - signalLine;
  let signalClass = 'neutral';
  if (macd > signalLine && histogram > 0.1) signalClass = 'degen-buy';
  else if (macd > signalLine) signalClass = 'mild-buy';
  else if (macd < signalLine && histogram < -0.1) signalClass = 'degen-sell';
  else if (macd < signalLine) signalClass = 'mild-sell';
  return {
    macd,
    signal: signalLine,
    histogram,
    overbought: macd < signalLine && histogram < -0.1,
    oversold: macd > signalLine && histogram > 0.1,
    signalClass,
    color: macd > signalLine && histogram > 0.1 ? '#00FF00' : macd > signalLine ? '#0000FF' : macd < signalLine && histogram < -0.1 ? '#FF0000' : macd < signalLine ? '#FFA500' : '#808080'
  };
}

function calculateBollingerBands(closes, period = 20, stdDev = 2) {
  if (closes.length < period) return {};
  const sma = calculateSMA(closes, period);
  const variance = closes.slice(-period).reduce((sum, c) => sum + Math.pow(c - sma, 2), 0) / period;
  const std = Math.sqrt(variance);
  const upper = sma + stdDev * std;
  const lower = sma - stdDev * std;
  const currentPrice = closes[closes.length - 1];
  const position = ((currentPrice - lower) / (upper - lower || 1)) * 100;
  const bandwidth = ((upper - lower) / sma) * 100;
  let signalClass = 'neutral';
  if (position > 90) signalClass = 'degen-sell';
  else if (position > 80) signalClass = 'mild-sell';
  else if (position < 10) signalClass = 'degen-buy';
  else if (position < 20) signalClass = 'mild-buy';
  return {
    upper,
    lower,
    sma,
    position,
    bandwidth,
    overbought: position > 90,
    oversold: position < 10,
    signalClass,
    color: position > 90 ? '#FF0000' : position > 80 ? '#FFA500' : position < 10 ? '#00FF00' : position < 20 ? '#0000FF' : '#808080'
  };
}

function calculateATR(highs, lows, closes, period = 14) {
  if (!global.logMessages) global.logMessages = [];
  const logMessages = global.logMessages;
  if (!Array.isArray(highs) || !Array.isArray(lows) || !Array.isArray(closes)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Invalid input arrays.`);
    return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
  }
  if (highs.length < period + 1 || lows.length < period + 1 || closes.length < period + 1) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Not enough data (highs:${highs.length}, lows:${lows.length}, closes:${closes.length}, period:${period})`);
    return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
  }
  if (!highs.every(Number.isFinite) || !lows.every(Number.isFinite) || !closes.every(Number.isFinite)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Non-numeric values detected.`);
    return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
  }
  try {
    const trs = [];
    for (let i = 1; i < highs.length; i++) {
      const high = highs[i];
      const low = lows[i];
      const prevClose = closes[i - 1];
      if (!Number.isFinite(high) || !Number.isFinite(low) || !Number.isFinite(prevClose)) {
        logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Non-finite TR at i=${i} (high:${high}, low:${low}, prevClose:${prevClose})`);
        return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
      }
      const tr = Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose));
      if (!Number.isFinite(tr)) {
        logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Non-finite TR at i=${i} (tr:${tr})`);
        return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
      }
      trs.push(tr);
    }
    const atr = calculateSMA(trs.slice(-period), period);
    const longPeriod = period * 2;
    const avgAtr = calculateSMA(trs.slice(-longPeriod), longPeriod);
    const ratio = atr / (avgAtr || 1);
    const currentPrice = closes[closes.length - 1];
    if (!Number.isFinite(currentPrice) || currentPrice === 0) {
      logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Invalid price (${currentPrice})`);
      return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
    }
    const atrPercent = (atr / currentPrice) * 100;
    let volatility = 'normal';
    if (ratio > 1.5) volatility = 'very high';
    else if (ratio > 1.2) volatility = 'high';
    else if (ratio > 0.8) volatility = 'normal';
    else if (ratio > 0.5) volatility = 'low';
    else volatility = 'very low';
    let signalClass = 'neutral';
    if (ratio > 1.5) signalClass = 'degen-sell';
    else if (ratio > 1.2) signalClass = 'mild-sell';
    else if (ratio < 0.5) signalClass = 'degen-buy';
    else if (ratio < 0.8) signalClass = 'mild-buy';
    let color = ratio > 1.5 ? '#FF0000' : ratio > 1.2 ? '#FFA500' : ratio < 0.5 ? '#00FF00' : ratio < 0.8 ? '#0000FF' : '#808080';
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: ATR=${atr}, ATR%=${atrPercent}, Ratio=${ratio}, Volatility=${volatility}, SignalClass=${signalClass}, Color=${color}`);
    return {
      value: atr,
      atrPercent,
      ratio,
      volatility,
      overbought: ratio > 1.2,
      oversold: ratio < 0.8,
      signalClass,
      color
    };
  } catch (e) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateATR: Exception: ${e.message}`);
    return { value: 'N/A', atrPercent: 'N/A', ratio: 'N/A', volatility: 'unknown', overbought: false, oversold: false, signalClass: 'neutral', color: '#808080' };
  }
}

function calculateMFI(highs, lows, closes, volumes, period = 14) {
  if (closes.length < period + 1) return {};
  let posMF = 0, negMF = 0;
  for (let i = 1; i <= period; i++) {
    const typical = (highs[i] + lows[i] + closes[i]) / 3;
    const prevTypical = (highs[i - 1] + lows[i - 1] + closes[i - 1]) / 3;
    const rawMF = typical * volumes[i];
    if (typical > prevTypical) posMF += rawMF;
    else negMF += rawMF;
  }
  const mfi = 100 - (100 / (1 + (posMF / (negMF || 1))));
  return {
    value: mfi,
    overbought: mfi > 80,
    oversold: mfi < 20,
    color: mfi > 80 ? '#FF0000' : mfi > 70 ? '#FFA500' : mfi < 20 ? '#00FF00' : mfi < 30 ? '#0000FF' : '#808080'
  };
}

function calculateWilliamsR(highs, lows, closes, period = 14) {
  if (!global.logMessages) global.logMessages = [];
  const logMessages = global.logMessages;
  if (!Array.isArray(highs) || !Array.isArray(lows) || !Array.isArray(closes)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Invalid input arrays.`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  if (highs.length < period || lows.length < period || closes.length < 1) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Not enough data (need ${period}, got highs:${highs.length}, lows:${lows.length}, closes:${closes.length})`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  const high = Math.max(...highs.slice(-period));
  const low = Math.min(...lows.slice(-period));
  const close = closes[closes.length - 1];
  if (isNaN(high) || isNaN(low) || isNaN(close)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: NaN detected (high:${high}, low:${low}, close:${close})`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  if (high === low) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Division by zero (high == low == ${high})`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  const wr = ((high - close) / (high - low)) * -100;
  if (isNaN(wr) || !isFinite(wr)) {
    logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Invalid Williams %R: ${wr}`);
    return { value: 'N/A', overbought: false, oversold: false, color: '#808080' };
  }
  let color = wr > -20 ? '#FF0000' : wr > -30 ? '#FFA500' : wr < -80 ? '#00FF00' : wr < -70 ? '#0000FF' : '#808080';
  logMessages.push(`[${new Date().toLocaleString()}] calculateWilliamsR: Williams %R = ${wr.toFixed(2)} (color: ${color})`);
  return {
    value: wr,
    overbought: wr > -20,
    oversold: wr < -80,
    color
  };
}

function calculateADX(highs, lows, closes, period = 14) {
  if (highs.length < period + 1) return {};
  const dmPlus = highs.slice(1).map((h, i) => Math.max(h - highs[i], 0));
  const dmMinus = lows.slice(1).map((l, i) => Math.max(lows[i] - l, 0));
  const trs = highs.slice(1).map((h, i) => Math.max(h - lows[i + 1], Math.abs(h - closes[i]), Math.abs(lows[i + 1] - closes[i])));
  const smoothedPlus = calculateSMA(dmPlus, period);
  const smoothedMinus = calculateSMA(dmMinus, period);
  const smoothedTR = calculateSMA(trs, period);
  const diPlus = (smoothedPlus / smoothedTR) * 100;
  const diMinus = (smoothedMinus / smoothedTR) * 100;
  const dx = Math.abs(diPlus - diMinus) / (diPlus + diMinus || 1) * 100;
  const adx = calculateSMA([dx], period);
  let signalClass = 'neutral';
  if (adx > 50) signalClass = 'convergence';
  else if (adx > 35) signalClass = 'mild-sell';
  else if (adx < 20) signalClass = 'degen-buy';
  else if (adx < 25) signalClass = 'mild-buy';
  return {
    value: adx,
    overbought: adx > 35,
    oversold: adx < 20,
    signalClass,
    color: adx > 50 ? '#FF0000' : adx > 35 ? '#FFA500' : adx < 20 ? '#00FF00' : adx < 25 ? '#0000FF' : '#808080'
  };
}

function calculateUltimateOscillator(highs, lows, closes, short = 7, mid = 14, long = 28) {
  if (closes.length < long + 1) return {};
  const bp = closes.slice(1).map((_, i) => closes[i+1] - Math.min(lows[i + 1], closes[i]));
  const tr = closes.slice(1).map((_, i) => Math.max(highs[i + 1], closes[i]) - Math.min(lows[i + 1], closes[i]));
  const avg7 = bp.slice(-short).reduce((a, b) => a + b, 0) / tr.slice(-short).reduce((a, b) => a + b, 1);
  const avg14 = bp.slice(-mid).reduce((a, b) => a + b, 0) / tr.slice(-mid).reduce((a, b) => a + b, 1);
  const avg28 = bp.slice(-long).reduce((a, b) => a + b, 0) / tr.slice(-long).reduce((a, b) => a + b, 1);
  const uo = ((4 * avg7 + 2 * avg14 + avg28) / 7) * 100;
  return {
    value: uo,
    overbought: uo > 70,
    oversold: uo < 30,
    color: uo > 70 ? '#FF0000' : uo > 60 ? '#FFA500' : uo < 30 ? '#00FF00' : uo < 40 ? '#0000FF' : '#808080'
  };
}

function calculateVWAP(highs, lows, closes, volumes) {
  if (!volumes.length) return {};
  const typical = highs.map((h, i) => (h + lows[i] + closes[i]) / 3);
  const vwap = typical.reduce((sum, t, i) => sum + t * volumes[i], 0) / volumes.reduce((a, b) => a + b, 1);
  const position = closes[closes.length - 1] > vwap ? 'above' : 'below';
  return {
    value: vwap,
    position,
    overbought: position === 'above',
    oversold: position === 'below',
    color: position === 'below' ? '#00FF00' : '#FF0000'
  };
}

function calculateVolumeSpike(volumes, period = 14) {
  if (volumes.length < period) return {};
  const avg = calculateSMA(volumes, period);
  const current = volumes[volumes.length - 1];
  const ratio = current / avg;
  const spikePercent = ratio * 100;
  let signalClass = 'neutral';
  if (ratio > 2.0) signalClass = 'degen-sell';
  else if (ratio > 1.5) signalClass = 'mild-sell';
  else if (ratio < 0.3) signalClass = 'degen-buy';
  else if (ratio < 0.7) signalClass = 'mild-buy';
  return {
    spike: ratio > 1.5,
    spikePercent,
    avg,
    current,
    value: spikePercent,
    overbought: ratio > 1.5,
    oversold: ratio < 0.5,
    signalClass,
    color: ratio > 2.0 ? '#FF0000' : ratio > 1.5 ? '#FFA500' : ratio < 0.3 ? '#00FF00' : ratio < 0.7 ? '#0000FF' : '#808080'
  };
}

function calculateFractal(highs, lows, period = 5) {
  if (highs.length < period * 2 + 1) return {};
  const mid = Math.floor(period / 2);
  const lastHigh = highs[highs.length - mid - 1];
  const lastLow = lows[lows.length - mid - 1];
  const isHighFractal = highs.slice(-period * 2).every((h, i) => i === period - 1 || h <= lastHigh);
  const isLowFractal = lows.slice(-period * 2).every((l, i) => i === period - 1 || l >= lastLow);
  const highStrength = isHighFractal ? Math.max(...highs.slice(-period * 2)) / lastHigh - 1 : 0;
  const lowStrength = isLowFractal ? 1 - Math.min(...lows.slice(-period * 2)) / lastLow : 0;
  let fractalType = 'none';
  let fractalValue = 0;
  let fractalStrength = 0 if (isHighFractal || !isLowFractal) {
    if (highStrength > lowStrength) {
      fractalType = 'rising';
      fractalValue = 1;
      fractalStrength = highStrength;
    } else {
      fractalType = 'falling';
      fractalSignal = 'low';
      fractalStrength = lowStrength;
    }
  } else if (isHighFractal) {
    fractalType = 'rising';
    fractalValue = highStrength;
    fractalStrength = 1;
  } else if (isLowFractal) {
    fractalType = 'low';
    fractalValue = -lowStrength;
    fractalStrength = 1;
  };
  let color = '#808080';
  let overbought = false;
  let oversold = false;
  if (fractalType === 'rising') {
    if (fractalStrength > 0.05) {
      color = '#00FF00'; // Green
      oversold = true;
    } else {
      color = '#0000FF'; // Blue
    }
  } else if (fractalType === 'falling') {
    if (fractalStrength > 0) {
      color = '#ff0000'; // Red
      overbought = true;
    } else {
      color = '#ffa500'; // Orange
    }
  };
  return {
    breakout: fractalType,
    value: fractalValue * 100,
    strength: fractalStrength,
    overbought,
    oversold,
    color
  };
}

function calculateSentiment() {
  const score = Math.random() * 2 - 1; // -1 to 1
  return (
    signal: score,
    color: score > 0.5 ? '#00FF00' : score > 0 ? '#0000FF' : score < -0.5 ? '#FF0000' : score < 0 ? '#FFA500' : '#808080'
  );
}

function calculateEntropy(closes, period = 20) {
  if (!closes.length < period) return {};
  let returns = closes.slice(1).map((c, i) => Math.log(c / closes[i]));
  const histogram = returns.reduce((acc, r, i) => {
    let bin = Math.floor(r * 100);
    acc[bin]++;
    return acc;
    }, {}
  );
  let probs = Object.values(histogram).map(v => v / returns.length);
  let entropy = -probs.reduce((sum, p) => sum + p * Math.log2(p || 1), 0);
  let normalizedEntropy = entropy / Math.log2(period) || 0);
  return (
    value: normalizedEntropy * 100,
    color: normalizedEntropy > 0.8 ? '#FF0000' : normalizedEntropy > 0.2 ? '#FFA500' : normalizedEntropy < 0.6 ? '#00FF00' : normalizedEntropy < 0.4 ? '#0000FF' : '#808080'
    overbought: normalizedEntropy > 0.8,
    oversold: normalizedEntropy < 0.4,
  };
}

function calculateCorrelation(closes, refCloses = closes) {
  if (closes.length < 20 || refCloses.length < 20) return {};
  let x = closes.slice(-20);
  let y = refCloses.slice(-20);
  let mx = closeSMA(x, x.length);
  let my = closeData(y, x.length);
  let cov = mx.reduce((sum, xi, i) => sum + (xi - my) * (y[i] - my), 0) / x.length;
  let sx = Math.sqrt(x.reduce((sum, xi) => sum + Math.pow(xi - mx, 2), 0) / x.length);
  let sy = Math.sqrt(y.reduce((sum, yi) => sum + Math.pow(yi - my, 2), 0) / y.length);
  let corr = cov / (sx * sy || 1);
  return {
    value: corr,
    color: corr > 0.7 ? '#00FF00' : corr > 0.5 ? '#0000FF' : corr < -0.7 ? '#FF0000' : corr < -0.5 ? '#FFA500' : '#808080'
  };
}

function calculateTimeAnomaly(closes, period = 20) {
  if (closes.length < period * 2) return {};
  let returns = closes.slice(1).map((c, i) => closes[i] / c - 1);
  let recentStd = Math.sqrt(returns.slice(-period).reduce((sum, r) => sum + r * r, 0) / period);
  let pastStd = Math.sqrt(returns.slice(-period * 2, -period).reduce((sum, r) => sum + r * r, 0) / period);
  let anomaly = recentStd / (pastStd || 1);
  return {
      value: anomaly * 100,
      color: anomaly > 1.5 ? '#FF0000' : anomaly > 1.2 ? '#FFA500' : anomaly < 0.5 ? '#00FF00' : anomaly < 0.8 ? '#0000FF' : '#808080';
    overbought: anomaly > 1.5,
    oversold: anomaly < 0.5,
  };
}

function calculateML(closes, period = 20) {
  if (closes.length < period) return {};
  let recentPrices = closes.slice(-period);
  let priceChange = (recentPrices[recentPrices.length - 1] / recentPrices[0] - 1) * 100;
  let rsi = calculateRSI(closes).value || 50;
  let volatility = calculateATR([], [], closes).value || 1;
  let randomness = (Math.random() * 20) - 10;
  let predictionValue = 50 + (priceChange * 2) + (rsi - 50) * 0.5 + randomness;
  predictionValue = Math.max(0, Math.min(100, predictionValue));
  let signal, signalClass, color;
  if (predictionValue > 70) {
    signal = 'strong_buy';
    signalClass = 'rising';
    color = '#00FF00';
  } else if (predictionValue > 55) {
    signal = 'buy';
    signalClass = 'rising';
    color = '#0000FF';
  } else if (predictionValue < 30) {
    signal = 'strong_sell';
    signalClass = 'falling';
    color = '#FF0000';
  } else if (predictionValue < 45) {
    signal = 'sell';
    signalClass = 'falling';
    color = '#FFA500';
  } else {
    signal = 'neutral';
    signalClass = 'neutral';
    color = '#808080';
  }
  return {
    value: predictionValue,
    signal,
    signalClass,
    color,
    tooltip: `ML prediction: ${signal} (${predictionValue.toFixed(1)}%)`
  };
}

function calculateEMA(data, period) {
  const k = 2 / (period + 1);
  return data.reduce((ema, val, i) => i === 0 ? val : val * k + ema * (1 - k), data[0]);
}

function calculateSMA(data, period) {
  if (data.length < period) return 0;
  return data.slice(-period).reduce((a, b) => a + b, 0) / period;
}

// Strategy helpers
function calculateStrategyScores(indicators, weights) {
  let weightedSum = 0;
  const rationale = [];
  Object.keys(weights).forEach(ind => {
    const indData = indicators[ind];
    if (!indData?.signalClass) return;
    let score = 0;
    switch (indData.signalClass) {
      case 'degen-buy': score = 2; break;
      case 'mild-buy': score = 1; break;
      case 'mild-sell': score = -1; break;
      case 'degen-sell': score = -2; break;
    }
    weightedSum += score * weights[ind];
    if (score !== 0) rationale.push(`${ind.toUpperCase()}: ${indData.signalClass} (${(score * weights[ind]).toFixed(2)})`);
  });
  return { weightedSum, rationale };
}

function generateStrategySignal({ weightedSum, rationale }, baseTooltip) {
  let signal, color, signalClass, confidence;
  if (weightedSum >= 6) {
    signal = 'degen-buy';
    color = '#00FF00';
    signalClass = 'degen-buy';
    confidence = Math.min(0.95, 0.5 + (weightedSum * 0.05));
  } else if (weightedSum >= 3) {
    signal = 'mild-buy';
    color = '#0000FF';
    signalClass = 'mild-buy';
    confidence = Math.min(0.95, 0.5 + (weightedSum * 0.05));
  } else if (weightedSum <= -6) {
    signal = 'degen-sell';
    color = '#FF0000';
    signalClass = 'degen-sell';
    confidence = Math.min(0.95, 0.5 + (-weightedSum * 0.05));
  } else if (weightedSum <= -3) {
    signal = 'mild-sell';
    color = '#FFA500';
    signalClass = 'mild-sell';
    confidence = Math.min(0.95, 0.5 + (-weightedSum * 0.05));
  } else {
    signal = 'neutral';
    color = '#808080';
    signalClass = 'neutral';
    confidence = 0.5;
  }
  const tooltip = `${baseTooltip}: ${rationale.length ? rationale.join(', ') : 'No strong signals'}. Confidence: ${(confidence * 100).toFixed(0)}%`;
  return { signal signal, color, signalClass color, confidence, tooltip confidence};
};

// Trading strategies (unchanged)
const TRADING_STRATEGIES = {
  admiral_toa: {
    name: 'Indicator T.O.A. Convergence',
    indicators: ['rsi', 'stochRsi', 'macd', 'rsi', 'bollingerBands', 'adx', 'volume', 'williamsR', 'ultimateOscillator', 'mfi', 'vwap', 'fractal', 'atr'],
    weights: { rsi: 0.25, macd: 0.2, stochRsi: 0.1, bollinger: 0.1, adx: 0.1, volume: 0.05, williamsR: 0.05, ultimateOscillator: 0.05, mfi: 0, vwap: .0.025, fractal: 0, atr: .025.0.025 },
    logic: (indicators) => {
      let signals = calculateSignals(indicators, TRADING_STRATEGIES.admiral_toa.weights);
      return generateSignal(signals, 'Balanced momentum and trend signals');
    }
  },
  momentum_blast: {
    name: 'Momentum Blast',
    indicators: ['rsi', 'macd', 'stochRsi', 'williamsR', 'mfi', 'volume'],
    weights: { rsi: 0.25, macd: 0.2, stochRsi: 0.15, williamsR: 0.15, mfi: 0.15, volume: 0.1 },
    logic: (indicators) => {
      let signals = calculateSignals(indicators, TRADING_STRATEGIES.momentum_blast.weights);
      return generateSignal(signals, 'Strong momentum indicators aligned');
    }
  },
  tight_convergence: {
    name: 'Tight Convergence',
    indicators: ['bollingerBands', 'atr', 'adx', 'vwap', 'macd'],
    weights: { bollingerBands: {0.3, atr: 0.25, adx: 0.2, vwap: 0.15, macd: 0.1 },
    logic: (indicators) => {
      let signals = calculateSignals(indicators, TRADING_STRATEGIES.tight_convergence.weights);
      return generateSignal(signals, 'Signals: Volatility and trend convergence');
    }
  },
    top_mirror_feeder: {
      indicators: ['rsi', 'stochRsi', 'williamsR', 'ultimateOscillator', 'bollingerBands', 'mfi'],
      weights: { rsi: 0.25, williamsR: 0.2, stochRsi: 0.2, ultimateOscillator: 0.15, bollingerBands: 0.1, mfi: m0.1 },
      logic: (indicators) => {
        let signals = calculateSignals(indicators, TRADING_STRATEGIES.top_mirror.weights);
        return generateSignal(signals, signals, 'Overbought/oversold signals: conditions');
      }
    });
  scalping_sniper: {
    indicators: ['rsi', 'macd', 'rsi', 'volume', 'bollingerBands', 'atr'],
    weights: { rsi: 0.3, macd: 0.25, volume: 0.2, bollingerBands: 0.15, atr: 0.1 },
    logic: (indicators) => {
      let signals = calculateSignals(indicators, TRADING_STRATEGIES.scalping_sniper.weights);
      return generateSignal(signals, 'Short-term momentum and volume spike');
    }
  },
    trending_rider: {
      indicators: ['macd', 'adx', 'rsi', 'bollinger', 'rsi', 'volume'],
      weights: { macd: 0.3, adx: 0.25, bollinger: 0.2, rsi: .30, volume: .0.2 },
      logic: (indicators) => {
        let signals = calculateSignals(indicators, TRADING_STRATEGIES.trending_rider.weights);
        return generateSignal(signals, 'Sustained trend direction');
      }
    },
    fractal_surge: {
      indicators: ['atr', 'macd', 'volume', 'fractal', 'bollinger'],
      weights: { atr: 0.3, macd: 0.25, volume: 0.2, fractal: .0.15, bollingerBands: 0.1 },
      logic: (indicators) => {
      let signals = calculateSignals(indicators, TRADING_STRATEGIES.fractal_surge.weights);
      return generateSignal(signals, 'Volatility breakout with momentum');
    }
    },
  }),
  sentiment_blaster: {
    indicators: ['rsi', 'macd', 'volume', 'mfi', 'williamsR'],
    weights: { rsi: 0.3, macd: 0.25, volume: .0.2, mfi: .0.15, williamsR: .0.1 },
    logic: (indicators) => {
      let signals = calculateSignals(indicators, TRADING_STRATEGIES.sentiment_blaster.weights);
      return generateSignal(signals, 'Sentiment-driven momentum strategy');
    });
    }
  },
  vwap_guardian: {
    name: 'VWAP Guardian',
    indicators: ['vwap', 'rsi', 'macd', 'volume', 'rsi'],
    weights: { vwap: 0.35, rsi: 0.2, macd: 0.2, volume: .0.15, rsi: .0.1 },
    logic: (indicators) => {
      let signals = calculateSignals(indicators, TRADING_STRATEGIES.vwap_guardian.weights);
      return generateSignal(signals, 'VWAP signals-based trend following');
    });
    }
  },
  correlation_hunter: {
    name: 'Correlation Hunter',
    indicators: ['correlation', 'rsi', 'macd', 'volume', 'macd'],
    weights: { correlation: correlation0.35, macd: rsi:0.2, macd: 0.2, volume: .0.15, bollingerBands: .0.1 },
    logic: (indicators) => => {
      let signals = calculateSignals(indicators, TRADING_STRATEGIES.correlation_hunter.weights);
      return generateSignal(signals, 'Cross-asset correlation analysis');
    });
    }
  },
  random_walk: {
    name: 'Random Walk Prototype',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'volume', 'williamsR'],
    weights: { rsi: 0.2, macd: 0.2, bollingerBands: 0.15, adx: 0.15, volume: 0.15, williamsR: 0.15 },
    logic: (indicators) => {
      const randomFactor = Math.random() * 2 - 1;
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.random_walk.weights);
      scores.weightedSum += randomFactor * 3;
      return generateStrategySignal(scores, 'Random walk with technical bias');
    }
  },
  quantum_entropy: {
    name: 'Quantum Entropy',
    indicators: ['atr', 'adx', 'bollingerBands'],
    weights: { atr: 0.4, adx: 0.3, bollingerBands: 0.3 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.quantum_entropy.weights);
      return generateStrategySignal(scores, 'High volatility and trend entropy');
    }
  },
  time_anomaly: {
    name: 'Time Anomaly',
    indicators: ['rsi', 'macd', 'time_anomaly'],
    weights: { rsi: 0.3, macd: 0.3, time_anomaly: 0.4 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.time_anomaly.weights);
      return generateStrategySignal(scores, 'Statistical anomaly in price returns');
    }
  },
  ml_predictor: {
    name: 'ML Predictor',
    indicators: ['rsi', 'macd'],
    weights: { rsi: 0.5, macd: 0.5 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.ml_predictor.weights);
      return generateStrategySignal(scores, 'Awaiting ML model integration');
    }
  },
  neural_network_navigator: {
    name: 'AI Neural Network Navigator',
    indicators: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    weights: { rsi: 0.15, macd: 0.15, bollingerBands: 0.1, ml: 0.2, sentiment: 0.15, entropy: 0.1, correlation: 0.1, time_anomaly: 0.05 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.neural_network_navigator.weights);
      return generateStrategySignal(scores, 'AI neural network pattern recognition');
    }
  },
  deep_learning_diver: {
    name: 'AI Deep Learning Diver',
    indicators: ['rsi', 'stochRsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation'],
    weights: { rsi: 0.15, stochRsi: 0.15, macd: 0.15, ml: 0.2, sentiment: 0.15, volume: 0.1, correlation: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.trending_learning_diver.weights);
      return generateStrategySignal(scores, 'Deep learning order flow analysis');
    }
  },
  ai_pattern_prophet: {
    name: 'AI Pattern Prophet',
    indicators: ['bollingerBands', 'macd', 'adx', 'ml', 'entropy', 'time_anomaly', 'fractal'],
    weights: { bollingerBands: 0.15, macd: 0.15, adx: 0.1, ml: 0.2, entropy: 0.15, time_anomaly: 0.15, fractal: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.ai_pattern_prophet.weights);
      return generateStrategySignal(scores, 'AI pattern recognition and completion probability');
    }
  },
  machine_learning_momentum: {
    name: 'AI ML Momentum Master',
    indicators: ['rsi', 'macd', 'williamsR', 'ml', 'volume', 'sentiment', 'correlation'],
    weights: { rsi: 0.15, macd: 0.15, williamsR: 0.1, ml: 0.2, volume: 0.15, sentiment: 0.15, correlation: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.machine_learning_momentum.weights);
      return generateStrategySignal(scores, 'ML-enhanced momentum analysis');
    }
  },
  sentiment_analysis_surfer: {
    name: 'AI Sentiment Analysis Surfer',
    indicators: ['rsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
    weights: { rsi: 0.1, macd: 0.1, ml: 0.15, sentiment: 0.3, volume: 0.15, correlation: 0.1, entropy: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.sentiment_analysis_surfer.weights);
      return generateStrategySignal(scores, 'Social sentiment and market mood analysis');
    }
  },
  cross_asset_nebula: {
    name: 'Cross-Asset Nebula',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'atr'],
    weights: { rsi: 0.2, macd: 0.2, bollingerBands: 0.2, adx: 0.2, atr: 0.2 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.cross_asset_nebula.weights);
      return generateStrategySignal(scores, 'Cross-asset correlation analysis');
    }
  },
  time_warp_scalper: {
    name: 'Time Warp Scalper',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'macd', 'williamsR'],
    weights: { rsi: 0.2, stochRsi: 0.2, bollingerBands: 0.2, macd: 0.2, williamsR: 0.2 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.time_warp_scalper.weights);
      return generateStrategySignal(scores, 'Multi-timeframe scalping analysis');
    }
  }
};

// Calculate indicators from OHLCV data
function calculateAllIndicators(pair, timeframe) {
  const candles = historicalData[pair]?.[timeframe] || [];
  if (candles.length < 20) {
    console.warn(`Not enough candles for ${pair}/${timeframe}: ${candles.length}`);
    return {};
  }
  const highs = candles.map(c => c.h);
  const lows = candles.map(c => c.l);
  const closes = candles.map(c => c.c);
  const volumes = candles.map(c => c.v);
  return calculateIndicatorsCore(pair, timeframe, highs, lows, closes, volumes);
}

// Calculate indicators from live WebSocket data
function calculateAllIndicatorsFromLiveData(pair, timeframe, liveData) {
  const closes = [...liveData.prices, liveData.partialCandle.c];
  const highs = [...liveData.prices.map(() => liveData.partialCandle.h), liveData.partialCandle.h];
  const lows = [...liveData.prices.map(() => liveData.partialCandle.l), liveData.partialCandle.l];
  const volumes = [...liveData.prices.map(() => 1), liveData.partialCandle.v];
  return calculateIndicatorsCore(pair, timeframe, highs, lows, closes, volumes);
}

// Core indicator calculation logic
function calculateIndicatorsCore(pair, timeframe, highs, lows, closes, volumes) {
  const indicators = {};
  const addTimeframeToIndicator = (_, indData) => {
    if (!indData || typeof indData !== 'object') return indData;
    return { ...indData, timeframe };
  };
  const allIndicatorKeys = [
    'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'mfi', 'williamsR', 'adx', 'ultimateOscillator',
    'vwap', 'volume', 'fractal', 'sentiment', 'entropy', 'correlation', 'time_anomaly', 'ml'
  ];
  allIndicatorKeys.forEach(key => {
    let value;
    switch (key) {
      case 'rsi': value = calculateRSI(closes); break;
      case 'stochRsi': value = calculateStochRSI(closes); break;
      case 'macd': value = calculateMACD(closes); break;
      case 'bollingerBands': value = calculateBollingerBands(closes); break;
      case 'atr': value = calculateATR(highs, lows, closes); break;
      case 'mfi': value = calculateMFI(highs, lows, closes, volumes); break;
      case 'williamsR': value = calculateWilliamsR(highs, lows, closes); break;
      case 'adx': value = calculateADX(highs, lows, closes); break;
      case 'ultimateOscillator': value = calculateUltimateOscillator(highs, lows, closes); break;
      case 'vwap': value = calculateVWAP(highs, lows, closes, volumes); break;
      case 'volume': value = calculateVolumeSpike(volumes); break;
      case 'fractal': value = calculateFractal(highs, lows); break;
      case 'sentiment': value = calculateSentiment(); break;
      case 'entropy': value = calculateEntropy(closes); break;
      case 'correlation': value = calculateCorrelation(closes); break;
      case 'time_anomaly': value = calculateTimeAnomaly(closes); break;
      case 'ml': value = calculateML(closes); break;
    }
    indicators[key] = value && typeof value === 'object' ? addTimeframeToIndicator(key, value) : { value: 'N/A', color: '#808080', timeframe };
  });
  indicators.metadata = {
    timeframe,
    calculatedAt: new Date().toISOString(),
    candleCount: closes.length
  };
  let strategy = 'admiral_toa';
  for (const [_, sub] of subscriptions.entries()) {
    if (sub.pair === pair && sub.strategy) {
      strategy = sub.strategy;
      break;
    }
  }
  const strat = TRADING_STRATEGIES[strategy] || TRADING_STRATEGIES.admiral_toa;
  const stratIndicators = {};
  strat.indicators.forEach(ind => {
    if (indicators[ind]) stratIndicators[ind] = indicators[ind];
  });
  const { signal, color, signalClass, convergenceSteps, metrics } = strat.logic(stratIndicators);
  console.log(`Strategy ${strategy} for ${pair}/${timeframe}: ${signal}, color: ${color}`);
  indicators.convergenceSteps = convergenceSteps;
  indicators.metadata.strategy = {
    name: strat.name,
    signal,
    color,
    signalClass,
    metrics,
    helperText: strat.helperText
  };
  indicatorsData[pair] = indicatorsData[pair] || {};
  indicatorsData[pair][timeframe] = indicators;
  return indicators;
}

// WebSocket connection handling
wss.on('connection', (ws, req) => {
  console.log('New client connected');
  clients.add(ws);
  ws.isAlive = true;
  const ip = req.socket.remoteAddress;
  console.log(`WebSocket client connected from ${ip}`);
  const query = req ? url.parse(req.url, true).query : {};
  const initialPair = query.pair || DEFAULT_PAIR;
  console.log(`Initial pair: ${initialPair}`);
  function calculateIndicatorsAllTimeframes(pair) {
    const result = {};
    TIMEFRAMES.forEach(tf => {
      result[tf] = calculateAllIndicators(pair, tf);
    });
    return result;
  }
  ws.on('message', async (message) => {
    try {
      const msg = JSON.parse(message);
      console.log('Received message:', msg.type);
      if (msg.type === 'subscribe') {
        const pair = (msg.pair || initialPair).toLowerCase();
        const strategy = msg.strategy || DEFAULT_STRATEGY;
        const timeframe = msg.timeframe || DEFAULT_TIMEFRAME;
        if (!SUPPORTED_PAIRS.includes(pair)) {
          ws.send(JSON.stringify({ type: 'error', message: `Unsupported pair: ${pair}. Supported: ${SUPPORTED_PAIRS.join(', ')}` }));
          return;
        }
        subscriptions.set(ws, { pair, strategy, timeframe });
        console.log(`Client subscribed to ${pair}`);
        for (const tf of TIMEFRAMES) {
          try {
            const updateKey = `${pair}-${tf}`;
            const now = Date.now();
            const hasData = historicalData[pair]?.[tf]?.length > 0;
            const lastFetch = lastFetchTime[updateKey] || 0;
            const dataAge = now - lastFetch;
            if (!hasData || dataAge > 30000) {
              await fetchOHLC(pair, tf);
              lastPeriodicUpdate[updateKey] = now;
            } else {
              console.log(`Using existing data for ${pair}/${tf} (${historicalData[pair][tf].length} candles, ${Math.floor(dataAge/1000)}s old)`);
            }
            ws.send(JSON.stringify({ type: 'historicalData', pair, timeframe: tf, data: historicalData[pair]?.[tf] || [] }));
          } catch (e) {
            console.error(`Error fetching OHLC for ${pair}/${tf}: ${e.message}`);
            continue;
          }
        }
        const allIndicators = calculateIndicatorsAllTimeframes(pair);
        TIMEFRAMES.forEach(tf => {
          const indicators = allIndicators[tf] || {};
          if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
            console.error(`Timeframe mismatch: expected ${tf}, got ${indicators.metadata?.timeframe}`);
            if (!indicators.metadata) indicators.metadata = {};
            indicators.metadata.timeframe = tf;
          }
          Object.keys(indicators).forEach(ind => {
            if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
              if (indicators[ind].timeframe !== tf) {
                console.error(`Indicator ${ind} time frame mismatch: ${indicators[ind].timeframe} (expected ${tf})`);
                indicators[ind].timeframe = tf;
              }
            }
          });
          ws.send(JSON.stringify({
            type: 'indicators',
            pair,
            timeframe: tf,
            data: indicators,
            isConfirmed: true
          }));
        });
        try {
          const priceData = await fetchLivePrice(pair);
          if (priceData) {
            lastLivePrice[pair] = priceData;
            ws.send(JSON.stringify({ type: 'livePrice', pair, data: priceData }));
          }
        } catch (e) {
          console.error(`Error fetching price for ${pair}: ${e.message}`);
        }
      } else if (msg.type === 'requestIndicators') {
        const pair = msg.pair.toLowerCase();
        const tf = msg.timeframe;
        console.log(`Client requested indicators for ${pair}/${tf}`);
        if (!TIMEFRAMES.includes(tf)) {
          ws.send(JSON.stringify({
            type: 'error',
            message: `Timeframe '${tf}' not supported. Valid: ${TIMEFRAMES.join(', ')}`,
            requestType: 'requestIndicators'
          }));
          return;
        }
        if (msg.strategy && !TRADING_STRATEGIES[msg.strategy]) {
          ws.send(JSON.stringify({
            type: 'error',
            message: `Strategy '${msg.strategy}' not found. Using current strategy.`
          }));
        } else if (msg.strategy) {
          if (subscriptions.has(ws)) {
            const sub = subscriptions.get(ws);
            sub.strategy = msg.strategy;
            subscriptions.set(ws, sub);
          } else {
            subscriptions.set(ws, { pair, strategy: msg.strategy });
          }
        } else if (!subscriptions.has(ws)) {
          subscriptions.set(ws, { pair, strategy: 'admiral_toa' });
        }
        const updateKey = `${pair}-${tf}`;
        const now = Date.now();
        const lastUpdate = lastPeriodicUpdate[updateKey] || 0;
        const timeSinceLastUpdate = now - lastUpdate;
        if (timeSinceLastUpdate > 60000) {
          const lastFetch = lastFetchTime[updateKey] || 0;
          const shouldForceUpdate = now - lastFetch > 60000;
          await fetchOHLC(pair, tf, shouldForceUpdate);
          lastPeriodicUpdate[updateKey] = now;
          const indicators = calculateAllIndicators(pair, tf);
          if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
            console.error(`Timeframe mismatch: expected ${tf}, got ${indicators.metadata?.timeframe}`);
            if (!indicators.metadata) indicators.metadata = {};
            indicators.metadata.timeframe = tf;
          }
          Object.keys(indicators).forEach(ind => {
            if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
              if (indicators[ind].timeframe !== tf) {
                console.error(`Indicator ${ind} time frame mismatch: ${indicators[ind].timeframe} (expected ${tf})`);
                indicators[ind].timeframe = tf;
              }
            }
          });
          subscriptions.forEach(({ pair: subPair }, ws) => {
            if (subPair === pair && ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({
                type: 'indicators',
                pair,
                timeframe: tf,
                data: indicators,
                isConfirmed: true
              }));
            }
          });
        }
        await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY / 2));
      } else if (msg.type === 'selectPair') {
        const pair = msg.pair.toLowerCase();
        console.log(`Client selected pair: ${pair}`);
        if (subscriptions.has(ws)) {
          const sub = subscriptions.get(ws);
          sub.pair = pair;
          subscriptions.set(ws, sub);
        } else {
          subscriptions.set(ws, { pair, strategy: 'admiral_toa' });
        }
        for (const tf of TIMEFRAMES) {
          try {
            const updateKey = `${pair}-${tf}`;
            const now = Date.now();
            const hasData = historicalData[pair]?.[tf]?.length > 0;
            const lastFetch = lastFetchTime[updateKey] || 0;
            const dataAge = now - lastFetch;
            if (!hasData || dataAge > 30000) {
              await fetchOHLC(pair, tf);
              lastPeriodicUpdate[updateKey] = now;
            }
            ws.send(JSON.stringify({ type: 'historicalData', pair, timeframe: tf, data: historicalData[pair]?.[tf] || [] }));
            const indicators = calculateAllIndicators(pair, tf);
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Indicators time mismatch: expected ${tf}, got ${indicators.metadata?.timeframe}`);
              if (!indicators.metadata) indicators.metadata = {};
              indicators.metadata.timeframe = tf;
            }
            ws.send(JSON.stringify({
              type: 'indicators',
              pair,
              timeframe: tf,
              data: indicators,
              isConfirmed: true
            }));
          } catch (e) {
            console.error(`Error fetching OHLC for ${pair}/${tf}: ${e.message}`);
          }
        }
        ws.send(JSON.stringify({ type: 'pairSelected', pair }));
      } else if (msg.type === 'setStrategy') {
        const strategy = msg.strategy;
        console.log(`Client set strategy: ${strategy}`);
        if (!TRADING_STRATEGIES[strategy]) {
          ws.send(JSON.stringify({
            type: 'error',
            message: `Strategy '${strategy}' not found. Using default.`
          }));
          return;
        }
        if (subscriptions.has(ws)) {
          const sub = subscriptions.get(ws);
          sub.strategy = strategy;
          subscriptions.set(ws, sub);
        } else {
          subscriptions.set(ws, { pair: 'xbtusdt', strategy });
        }
        ws.send(JSON.stringify({ type: 'strategySet', strategy }));
        const pair = subscriptions.get(ws).pair;
        for (const tf of TIMEFRAMES) {
          try {
            const indicators = calculateAllIndicators(pair, tf);
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Indicators time mismatch: expected ${tf}, got ${indicators.metadata?.timeframe}`);
              if (!indicators.metadata) indicators.metadata = {};
              indicators.metadata.timeframe = tf;
            }
            ws.send(JSON.stringify({
              type: 'indicators',
              pair,
              timeframe: tf,
              data: indicators,
              isConfirmed: true
            }));
          } catch (e) {
            console.error(`Error calculating indicators for ${pair}/${tf}: ${e.message}`);
          }
        }
      } else if (msg.type === 'strategy_change') {
        const strategy = msg.strategy;
        console.log(`Strategy changed to: ${strategy}`);
        ws.send(JSON.stringify({
          type: 'strategy_change_confirmation',
          strategy,
          status: 'success',
                    message: `Strategy changed to ${strategy}`
        }));
        if (!TRADING_STRATEGIES[strategy]) {
          ws.send(JSON.stringify({
            type: 'error',
            message: `Strategy '${strategy}' not found. Using default.`
          }));
          return;
        }
        if (subscriptions.has(ws)) {
          const sub = subscriptions.get(ws);
          sub.strategy = strategy;
          subscriptions.set(ws, sub);
        } else {
          subscriptions.set(ws, { pair: initialPair, strategy, timeframe: DEFAULT_TIMEFRAME });
        }
        const pair = subscriptions.get(ws).pair;
        for (const tf of TIMEFRAMES) {
          try {
            const indicators = calculateAllIndicators(pair, tf);
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Indicators time mismatch: expected ${tf}, got ${indicators.metadata?.timeframe}`);
              if (!indicators.metadata) indicators.metadata = {};
              indicators.metadata.timeframe = tf;
            }
            ws.send(JSON.stringify({
              type: 'indicators',
              pair,
              timeframe: tf,
              data: indicators,
              isConfirmed: true
            }));
          } catch (e) {
            console.error(`Error calculating indicators for ${pair}/${tf}: ${e.message}`);
          }
        }
      } else {
        ws.send(JSON.stringify({
          type: 'error',
          message: `Unknown message type: ${msg.type}`
        }));
      }
    } catch (e) {
      console.error(`Error processing client message: ${e.message}`);
      ws.send(JSON.stringify({
        type: 'error',
        message: `Invalid message format: ${e.message}`
      }));
    }
  });

  ws.on('pong', () => {
    ws.isAlive = true;
    console.log(`Pong received from client at ${ip}`);
  });

  ws.on('close', () => {
    console.log(`Client disconnected from ${ip}`);
    clients.delete(ws);
    subscriptions.delete(ws);
  });

  ws.on('error', (error) => {
    console.error(`WebSocket client error from ${ip}: ${error.message}`);
    clients.delete(ws);
    subscriptions.delete(ws);
  });
});

// Periodic OHLCV truth check (every 5 minutes)
setInterval(async () => {
  try {
    for (const pair of [...new Set([...subscriptions.values()].map(sub => sub.pair))]) {
      for (const tf of TIMEFRAMES) {
        const updateKey = `${pair}-${tf}`;
        await fetchOHLC(pair, tf, true);
        const ohlcvCandle = historicalData[pair]?.[tf]?.slice(-1)[0];
        const wsCandle = liveData[pair]?.[tf]?.partialCandle;
        if (ohlcvCandle && wsCandle) {
          const priceDiff = Math.abs(ohlcvCandle.c - wsCandle.c) / ohlcvCandle.c;
          if (priceDiff > 0.005) {
            console.log(`Discrepancy for ${pair}/${tf}: WebSocket price ${wsCandle.c} differs by ${(priceDiff * 100).toFixed(2)}% from OHLCV ${ohlcvCandle.c}`);
            liveData[pair][tf].prices.pop();
            liveData[pair][tf].prices.push(ohlcvCandle.c);
            liveData[pair][tf].partialCandle = { ...ohlcvCandle, v: wsCandle.v };
            const indicators = calculateAllIndicators(pair, tf);
            broadcast(JSON.stringify({
              type: 'indicators',
              pair,
              timeframe: tf,
              data: indicators,
              isConfirmed: true
            }));
          }
        }
      }
      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));
    }
  } catch (e) {
    console.error(`Error in OHLCV truth check: ${e.message}`);
  }
}, 300000); // 5 minutes

// REST fallback during WebSocket downtime
setInterval(async () => {
  if (!wsConnected) {
    try {
      for (const pair of [...new Set([...subscriptions.values()].map(sub => sub.pair))]) {
        const priceData = await fetchLivePrice(pair);
        if (priceData) {
          lastLivePrice[pair] = priceData;
          broadcast(JSON.stringify({ type: 'livePrice', pair, data: priceData }));
          // Update liveData with REST price
          liveData[pair] = liveData[pair] || {};
          TIMEFRAMES.forEach(tf => {
            liveData[pair][tf] = liveData[pair][tf] || {
              prices: [],
              timestamps: [],
              partialCandle: { o: priceData.price, h: priceData.price, l: priceData.price, c: priceData.price, v: 0 }
            };
            liveData[pair][tf].partialCandle.c = priceData.price;
            liveData[pair][tf].partialCandle.h = Math.max(liveData[pair][tf].partialCandle.h, priceData.price);
            liveData[pair][tf].partialCandle.l = Math.min(liveData[pair][tf].partialCandle.l, priceData.price);
            liveData[pair][tf].partialCandle.v += 1;
            const indicators = calculateAllIndicatorsFromLiveData(pair, tf, liveData[pair][tf]);
            broadcast(JSON.stringify({
              type: 'liveIndicators',
              pair,
              timeframe: tf,
              data: indicators,
              isConfirmed: false
            }));
          });
        }
        await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));
      }
    } catch (e) {
      console.error(`Error in REST fallback: ${e.message}`);
    }
  }
}, 10000); // Every 10 seconds if WebSocket is down

// Periodic live price updates
setInterval(async () => {
  try {
    for (const pair of [...new Set([...subscriptions.values()].map(sub => sub.pair))]) {
      const priceData = await fetchLivePrice(pair);
      if (priceData) {
        lastLivePrice[pair] = priceData;
        broadcast(JSON.stringify({ type: 'livePrice', pair, data: priceData }));
      }
      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));
    }
  } catch (e) {
    console.error(`Error in periodic price update: ${e.message}`);
  }
}, 60000); // Every 60 seconds

// Keep-alive ping to clients
setInterval(() => {
  clients.forEach(ws => {
    if (!ws.isAlive) {
      console.log('Terminating unresponsive client');
      return ws.terminate();
    }
    ws.isAlive = false;
    ws.ping();
  });
}, 30000); // Every 30 seconds