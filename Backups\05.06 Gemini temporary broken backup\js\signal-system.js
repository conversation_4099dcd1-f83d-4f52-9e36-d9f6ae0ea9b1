/**
 * Unified Signal System - Combines signal management and rendering
 */
class SignalSystem {
  constructor() {
    // State
    this.isInitialized = false
    this.isUpdating = false
    this.lastUpdateTime = 0
    this.updateTimer = null
    this.signalState = new Map()
    this.pendingUpdates = new Map()
    this.elementCache = new Map()
    this.tooltipCache = new Map()
    this.updateQueue = []
    this.isProcessingQueue = false
    this.errorCount = 0
    this.maxErrors = 5
    this.updateInProgress = new Set()
    this.currentTimeframe = '1h'

    // Configuration
    this.config = {
      updateDebounce: 50,
      maxUpdateTime: 500,
      maxBatchSize: 5,
      updateCooldown: 500,
      signalColors: {
        'strong-buy': '#00FF00',
        'mild-buy': '#00AAFF',
        neutral: '#808080',
        'mild-sell': '#FFA500',
        'strong-sell': '#FF0000',
        error: '#FF00FF',
      },
      defaultTimeframes: ['1m', '5m', '15m', '1h', '4h', '1d', '1w'],
      batchSizes: {
        '1m': 3,
        '5m': 3,
        '15m': 2,
        '1h': 2,
        '4h': 1,
        '1d': 1,
        '1w': 1,
      },
    }

    // Setup listeners
    this.setupEventListeners()
    this.setupTimeframeListener()
  }

  /**
     * Initialize the signal system
     */
  init() {
    if (this.isInitialized) return
    console.log('[SignalSystem] Initializing...')

    // Initialize all signal elements
    const signals = document.querySelectorAll('.signal-circle')
    signals.forEach(signal => {
      const indicator = signal.dataset.indicator || signal.dataset.ind
      const timeframe = signal.dataset.timeframe || signal.dataset.tf
      if (indicator && timeframe) {
        const key = `${indicator}:${timeframe}`
        this.signalState.set(key, {
          value: 0,
          strength: 0.5,
          timestamp: Date.now(),
          status: 'neutral',
        })
        this.updateSignalElement(key, this.signalState.get(key))
      }
    })

    this.isInitialized = true
    console.log('[SignalSystem] Initialized')

    // Dispatch ready event
    const event = new CustomEvent('signalSystem:ready', {
      detail: { signalSystem: this },
    })
    document.dispatchEvent(event)
  }

  /**
     * Setup event listeners
     */
  setupEventListeners() {
    document.addEventListener('click', this.handleSignalClick.bind(this))
    document.addEventListener('change', this.handleIndicatorToggle.bind(this))
    window.addEventListener('resize', this.debounce(this.handleWindowResize.bind(this), 100))

    // Handle signal hover events
    const signals = document.querySelectorAll('.signal-circle')
    signals.forEach(signal => {
      signal.addEventListener('mouseenter', this.handleSignalHover.bind(this))
      signal.addEventListener('mouseleave', this.handleSignalLeave.bind(this))
    })
  }

  /**
     * Setup timeframe listener
     */
  setupTimeframeListener() {
    document.addEventListener('timeframeChanged', (event) => {
      const { timeframe } = event.detail || {}
      if (!timeframe) return

      console.log(`[SignalSystem] Timeframe changed to ${timeframe}`)
      this.currentTimeframe = timeframe
      this.signalState.clear()
      this.updateAllSignalLights()
    })
  }

  /**
     * Update all signal lights
     * @param {boolean} force - If true, bypass rate limiting
     */
  updateAllSignalLights(force = false) {
    if (this.errorCount >= this.maxErrors) {
      console.warn('[SignalSystem] Updates temporarily disabled due to too many errors')
      return
    }

    if (!force && !this.canUpdate()) {
      return
    }

    this.isUpdating = true
    try {
      const signals = document.querySelectorAll('.signal-circle')
      signals.forEach(signal => {
        const indicator = signal.dataset.indicator || signal.dataset.ind
        const timeframe = signal.dataset.timeframe || signal.dataset.tf
        if (indicator && timeframe) {
          const key = `${indicator}:${timeframe}`
          const data = this.signalState.get(key)
          if (data) {
            this.updateSignalElement(key, data)
          }
        }
      })
    } catch (error) {
      console.error('[SignalSystem] Error updating signals:', error)
      this.handleError(error)
    } finally {
      this.isUpdating = false
    }
  }

  /**
     * Update a single signal
     * @param {string} indicator - Indicator name
     * @param {string} timeframe - Timeframe
     * @param {Object} data - Signal data
     */
  updateSignal(indicator, timeframe, data) {
    const key = `${indicator}:${timeframe}`

    // Add to pending updates
    this.pendingUpdates.set(key, data)

    // Process updates if not already processing
    if (!this.isProcessingQueue) {
      this.processUpdates()
    }
  }

  /**
     * Process pending updates
     */
  processUpdates() {
    if (this.isProcessingQueue || this.pendingUpdates.size === 0) {
      return
    }

    this.isProcessingQueue = true
    try {
      // Process updates in batches
      const batch = Array.from(this.pendingUpdates.entries())
      for (const [key, data] of batch) {
        if (this.updateInProgress.has(key)) {
          continue
        }

        this.updateInProgress.add(key)
        try {
          this.signalState.set(key, data)
          this.updateSignalElement(key, data)
        } catch (error) {
          console.error(`[SignalSystem] Error updating ${key}:`, error)
          this.handleError(error)
        } finally {
          this.updateInProgress.delete(key)
        }
      }

      this.pendingUpdates.clear()
    } catch (error) {
      console.error('[SignalSystem] Error processing updates:', error)
      this.handleError(error)
    } finally {
      this.isProcessingQueue = false
    }
  }

  /**
     * Update a single signal element
     * @param {string} key - Signal key
     * @param {Object} data - Signal data
     */
  updateSignalElement(key, data) {
    const [indicator, timeframe] = key.split(':')
    const signal = document.querySelector(`[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`)

    if (!signal) {
      console.warn(`[SignalSystem] Signal element not found: ${key}`)
      return
    }

    // Update signal class
    const signalClass = this.getSignalClass(data.status, data.strength)
    signal.className = `signal-circle ${signalClass}`

    // Update tooltip
    const tooltip = signal.querySelector('.signal-tooltip')
    if (tooltip) {
      tooltip.textContent = `Status: ${data.status}\nValue: ${data.value}\nStrength: ${data.strength}`
    }
  }

  /**
     * Get signal class based on status and strength
     * @param {string} status - Signal status
     * @param {number} strength - Signal strength
     * @returns {string} - Signal class
     */
  getSignalClass(status, strength = 0.5) {
    const baseClass = status.toLowerCase().replace(/[^a-z]/g, '-')
    return `${baseClass}-light`
  }

  /**
     * Handle errors
     * @param {Error} error - Error object
     */
  handleError(error) {
    this.errorCount++
    if (this.errorCount >= this.maxErrors) {
      console.warn('[SignalSystem] Too many errors, pausing updates')
      this.pauseUpdates()
    }
  }

  /**
     * Pause signal updates
     */
  pauseUpdates() {
    this.isUpdating = false
    this.isProcessingQueue = false
    this.pendingUpdates.clear()
    this.updateInProgress.clear()

    // Clear any pending timers
    if (this.updateTimer) {
      clearTimeout(this.updateTimer)
      this.updateTimer = null
    }
  }

  /**
     * Check if we can update
     * @returns {boolean} - Can update
     */
  canUpdate() {
    const now = Date.now()
    return !this.isUpdating &&
               !this.isProcessingQueue &&
               (now - this.lastUpdateTime >= this.config.updateCooldown)
  }

  /**
     * Debounce function
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in ms
     * @returns {Function} - Debounced function
     */
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  /**
     * Handle signal click
     * @param {Event} event - Click event
     */
  handleSignalClick(event) {
    const target = event.target.closest('.signal-circle')
    if (!target) return

    const indicator = target.dataset.indicator || target.dataset.ind
    const timeframe = target.dataset.timeframe || target.dataset.tf
    if (indicator && timeframe) {
      this.dispatchEvent('signalClick', { indicator, timeframe })
    }
  }

  /**
     * Handle signal hover
     * @param {Event} event - Hover event
     */
  handleSignalHover(event) {
    const target = event.target.closest('.signal-circle')
    if (!target) return

    const tooltip = target.querySelector('.signal-tooltip')
    if (tooltip) {
      tooltip.style.display = 'block'
      this.positionTooltip(target, tooltip)
    }
  }

  /**
     * Handle signal leave
     * @param {Event} event - Leave event
     */
  handleSignalLeave(event) {
    const target = event.target.closest('.signal-circle')
    if (!target) return

    const tooltip = target.querySelector('.signal-tooltip')
    if (tooltip) {
      tooltip.style.display = 'none'
    }
  }

  /**
     * Position tooltip
     * @param {Element} signal - Signal element
     * @param {Element} tooltip - Tooltip element
     */
  positionTooltip(signal, tooltip) {
    const rect = signal.getBoundingClientRect()
    tooltip.style.left = `${rect.left + rect.width / 2}px`
    tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`
  }

  /**
     * Handle indicator toggle
     * @param {Event} event - Change event
     */
  handleIndicatorToggle(event) {
    const target = event.target.closest('[data-indicator]')
    if (!target) return

    const indicator = target.dataset.indicator
    const timeframe = target.dataset.timeframe || this.currentTimeframe
    if (indicator && timeframe) {
      this.dispatchEvent('indicatorToggle', { indicator, timeframe, value: event.target.checked })
    }
  }

  /**
     * Handle window resize
     */
  handleWindowResize() {
    this.updateAllSignalLights(true)
  }

  /**
     * Dispatch custom event
     * @param {string} type - Event type
     * @param {Object} detail - Event detail
     */
  dispatchEvent(type, detail) {
    const event = new CustomEvent(`signalSystem:${type}`, { detail })
    document.dispatchEvent(event)
  }
}

// Export for CommonJS and browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SignalSystem
} else if (typeof window !== 'undefined') {
  window.SignalSystem = SignalSystem
  // Initialize global instance
  window.signalSystem = new SignalSystem()
  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', () => window.signalSystem.init())
}
