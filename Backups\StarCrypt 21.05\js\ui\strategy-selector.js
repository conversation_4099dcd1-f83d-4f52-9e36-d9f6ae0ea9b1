// Strategy selector functionality for StarCrypt
// Implements the strategy selection menu and functionality

// Use the global TRADING_STRATEGIES defined in global-variables.js
// No need to redefine here, just ensure it exists
if (!window.TRADING_STRATEGIES) {
  console.error('Error: TRADING_STRATEGIES not defined in global-variables.js')
}

/* Original TRADING_STRATEGIES definition - now defined in global-variables.js
window.TRADING_STRATEGIES = {
  admiral_toa: {
    name: 'Admiral T.O.A. Convergence',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'The flagship strategy that combines momentum indicators (RSI, Stoch RSI) with trend confirmation (MACD), price position (Bollinger Bands), trend strength (ADX), volume analysis, and ML confirmation for high-confidence signals.',
    helperText: `
      <p><strong>Step 1:</strong> Check RSI and <PERSON>och RSI for momentum confirmation</p>
      <p><strong>Step 2:</strong> Confirm with MACD crossover or histogram direction</p>
      <p><strong>Step 3:</strong> Verify price position relative to Bollinger Bands</p>
      <p><strong>Step 4:</strong> Check ADX for trend strength (>25 is strong trend)</p>
      <p><strong>Step 5:</strong> Look for volume confirmation of the move</p>
      <p><strong>Step 6:</strong> Verify ML signals align with traditional indicators</p>
      <p><strong>Step 7:</strong> Check for time anomalies and correlation patterns</p>
    `
  },
  neural_network_navigator: {
    name: 'AI Neural Network Navigator',
    indicators: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'Advanced AI strategy that uses neural networks to identify complex market patterns and predict price movements with high accuracy by combining traditional indicators with machine learning signals.',
    helperText: `
      <p><strong>Step 1:</strong> Monitor ML signal for pattern recognition & ML alerts</p>
      <p><strong>Step 2:</strong> Confirm with RSI and MACD for momentum alignment</p>
      <p><strong>Step 3:</strong> Check Bollinger Bands for volatility context</p>
      <p><strong>Step 4:</strong> Analyze sentiment data for market mood confirmation</p>
      <p><strong>Step 5:</strong> Review entropy and correlation metrics for market structure</p>
      <p><strong>Step 6:</strong> Look for time anomalies that could indicate reversal points</p>
    `
  },
  momentum_blast: {
    name: 'Momentum Blast',
    indicators: ['rsi', 'stochRsi', 'macd', 'mfi', 'volume'],
    description: 'A more aggressive strategy focusing on momentum indicators with looser thresholds for more frequent trading signals.',
    helperText: `
      <p><strong>Step 1:</strong> Look for RSI crossing above 60 (bullish) or below 40 (bearish)</p>
      <p><strong>Step 2:</strong> Confirm with Stoch RSI crossing above 70 (bullish) or below 30 (bearish)</p>
      <p><strong>Step 3:</strong> Check MACD for crossover or histogram direction change</p>
      <p><strong>Step 4:</strong> Verify MFI aligns with other momentum indicators</p>
      <p><strong>Step 5:</strong> Ensure volume is increasing in the direction of the move</p>
      <p><strong>Note:</strong> This strategy generates more signals but with lower accuracy - use smaller position sizes!</p>
    `
  },
  top_bottom_feeder: {
    name: 'Top/Bottom Feeder',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'mfi', 'volume'],
    description: 'Specialized for catching market extremes, focusing on deeply oversold or overbought conditions across multiple indicators.',
    helperText: `
      <p><strong>Step 1:</strong> Wait for RSI below 20 (buy) or above 80 (sell)</p>
      <p><strong>Step 2:</strong> Confirm with Stoch RSI below 10 (buy) or above 90 (sell)</p>
      <p><strong>Step 3:</strong> Verify price is at or beyond Bollinger Bands (2.5 standard deviations)</p>
      <p><strong>Step 4:</strong> Check MFI for extreme readings aligned with other indicators</p>
      <p><strong>Step 5:</strong> Look for volume climax or exhaustion</p>
      <p><strong>Step 6:</strong> Wait for the first sign of reversal before entering</p>
      <p><strong>Note:</strong> This strategy has fewer signals but higher win rate when properly executed</p>
    `
  },
  trend_rider: {
    name: 'Trend Rider',
    indicators: ['adx', 'macd', 'rsi', 'williamsR', 'volume'],
    description: 'Focuses on riding established trends by combining ADX for trend strength with momentum indicators for entry timing.',
    helperText: `
      <p><strong>Step 1:</strong> Confirm ADX is above 25 to ensure a strong trend exists</p>
      <p><strong>Step 2:</strong> Check MACD alignment with the trend direction</p>
      <p><strong>Step 3:</strong> Look for RSI above 50 in uptrends or below 50 in downtrends</p>
      <p><strong>Step 4:</strong> Use Williams %R pullbacks to -50 in uptrends or -50 in downtrends as entry points</p>
      <p><strong>Step 5:</strong> Verify volume is supporting the trend direction</p>
      <p><strong>Note:</strong> This strategy works best in clearly trending markets, avoid ranging conditions</p>
    `
  }
};
*/

// Initialize strategy selector (single canonical)
function initializeStrategySelector() {
  // Close menu when clicking outside
  function closeAllMenus() {
    const menu = document.getElementById('strategyMenu')
    if (menu && menu.classList.contains('open')) {
      menu.classList.remove('open')
    }
    // Add other menus/dropdowns if needed
  }

  document.addEventListener('mousedown', (event) => {
    const menu = document.getElementById('strategyMenu')
    if (menu && menu.classList.contains('open')) {
      if (!menu.contains(event.target) && event.target.id !== 'strategyButton') {
        closeAllMenus()
      }
    }
  })

  // Get strategy selector elements
  const strategyButton = document.getElementById('strategyButton')
  const strategyMenu = document.getElementById('strategyMenu')
  const strategySelector = document.getElementById('strategySelector')
  const applyStrategyButton = document.getElementById('applyStrategyButton')

  // If elements don't exist, create them
  if (!strategyButton) {
    console.error('Strategy button not found, cannot initialize strategy selector')
    return
  }

  // Create strategy menu if it doesn't exist
  if (!strategyMenu) {
    const menu = document.createElement('div')
    menu.id = 'strategyMenu'
    menu.className = 'menu-content'
    document.body.appendChild(menu)

    menu.innerHTML = `
      <div class="strategy-controls" id="strategyControls">
        <h3>Strategy Selector</h3>
        <div class="strategy-description">
          Select a trading strategy to optimize your signal lights and indicators. Each strategy focuses on different market conditions and trading styles.
        </div>
        <select id="strategySelector" class="strategy-selector">
          ${Object.entries(window.TRADING_STRATEGIES).map(([key, strategy]) =>
    `<option value="${key}">${strategy.name}</option>`,
  ).join('')}
        </select>
        <div id="strategyDescription" class="strategy-info">
          ${window.TRADING_STRATEGIES.admiral_toa.description}
        </div>
        <div id="strategyHelper" class="strategy-helper">
          ${window.TRADING_STRATEGIES.admiral_toa.helperText}
        </div>
        <button id="applyStrategyButton" class="apply-strategy-button">Apply Strategy</button>
      </div>
    `
  }

  // Add event listeners
  strategyButton.addEventListener('click', toggleStrategyMenu)

  // Get the newly created elements
  const newStrategySelector = document.getElementById('strategySelector')
  const newApplyStrategyButton = document.getElementById('applyStrategyButton')

  if (newStrategySelector) {
    newStrategySelector.addEventListener('change', updateStrategyDescription)
  }

  if (newApplyStrategyButton) {
    newApplyStrategyButton.addEventListener('click', applySelectedStrategy)
  }

  // Initialize with displaying current strategy description only
  const mainStrategySelector = document.getElementById('strategySelector')
  if (mainStrategySelector && window.currentStrategy) {
    // Set selector to match current strategy without triggering change
    mainStrategySelector.value = window.currentStrategy
    updateStrategyDescription() // Only update description, no strategy switch
  }
}

// Toggle strategy menu visibility
function toggleStrategyMenu() {
  const strategyMenu = document.getElementById('strategyMenu')
  if (strategyMenu) {
    strategyMenu.style.display = strategyMenu.style.display === 'block' ? 'none' : 'block'

    // Hide other menus
    const otherMenus = document.querySelectorAll('.menu-content:not(#strategyMenu)')
    otherMenus.forEach(menu => {
      menu.style.display = 'none'
    })
  }
}

// Update strategy description based on selected strategy
function updateStrategyDescription() {
  const strategySelector = document.getElementById('strategySelector')
  const strategyDescription = document.getElementById('strategyDescription')
  const strategyHelper = document.getElementById('strategyHelper')

  if (strategySelector && strategyDescription && strategyHelper) {
    const selectedStrategy = strategySelector.value
    const strategy = window.TRADING_STRATEGIES[selectedStrategy]

    if (strategy) {
      strategyDescription.innerHTML = strategy.description
      strategyHelper.innerHTML = strategy.helperText
    }
  }
}

// Apply the selected strategy and update all relevant UI
function applySelectedStrategy() {
  console.log('Applying strategy...')
  const strategySelector = document.getElementById('strategySelector')
  if (!strategySelector) {
    console.error('Strategy selector not found')
    return
  }

  const selectedStrategy = strategySelector.value
  console.log('Selected strategy:', selectedStrategy)

  // Ensure TRADING_STRATEGIES is available
  if (!window.TRADING_STRATEGIES) {
    console.error('TRADING_STRATEGIES not defined')
    return
  }

  const strategy = window.TRADING_STRATEGIES[selectedStrategy]
  if (!strategy) {
    console.error('Strategy not found:', selectedStrategy)
    return
  }

  console.log('Strategy found:', strategy.name)

  // Show the strategy switch animation (always)
  if (typeof window.showStrategyAnimation === 'function') {
    window.showStrategyAnimation(selectedStrategy)
  }

  // Update current strategy globally
  window.currentStrategy = selectedStrategy
  console.log('Current strategy updated to:', window.currentStrategy)

  // Update helper text with the new strategy
  const helperContainer = document.getElementById('helper-container')
  if (helperContainer) {
    const helperContent = helperContainer.querySelector('.helper-content')
    if (helperContent && strategy.helperText) {
      helperContent.innerHTML = strategy.helperText
      console.log('Helper text updated')
    }
  }

  // Update strategy name in the header
  const strategyNameElement = document.querySelector('.helper-container .section-header h3')
  if (strategyNameElement) {
    strategyNameElement.textContent = `Trading Helper: ${strategy.name}`
    console.log('Strategy name updated in header')
  }

  // Send strategy change to server via WebSocket to ensure server-side synchronization
  try {
    if (typeof window.ensureWebSocketAndSend === 'function') {
      const message = JSON.stringify({
        type: 'strategy_change',
        strategy: selectedStrategy,
      })
      window.ensureWebSocketAndSend(message)
      console.log(`Strategy change sent to server: ${selectedStrategy}`)

      if (typeof window.logMessages !== 'undefined') {
        window.logMessages.push(`[${new Date().toLocaleString()}] Strategy changed to ${strategy.name}`)
        if (typeof window.updateLogger === 'function') window.updateLogger()
      }
    }
  } catch (error) {
    console.error('Error sending strategy change to server:', error)
  }

  // Update indicator menu: tick only indicators in strategy, others unticked but available
  if (typeof window.updateIndicatorMenu === 'function') {
    window.updateIndicatorMenu(selectedStrategy, true) // true = reset to strategy indicators
    console.log('Indicator menu updated')
  }

  // Update strategy info
  updateStrategyDescription()
  if (typeof window.updateStrategyInfoPanel === 'function') {
    window.updateStrategyInfoPanel(selectedStrategy)
    console.log('Strategy info panel updated')
  }

  // Update enabled indicators
  if (window.enabledIndicators) {
    window.enabledIndicators.forEach(ind => {
      ind.enabled = strategy.indicators.includes(ind.name)
    })
    console.log('Enabled indicators updated')
  }

  // Request fresh data from server for the new strategy
  if (typeof window.requestServerData === 'function') {
    console.log('Requesting fresh data from server for new strategy')
    window.requestServerData()
  }

  // Show loading overlay during strategy change
  const loadingOverlay = document.getElementById('loading-overlay')
  if (loadingOverlay) {
    loadingOverlay.style.display = 'flex'
    console.log('Showing loading overlay during strategy change')

    // Hide loading overlay after data is received (or timeout)
    setTimeout(() => {
      loadingOverlay.style.display = 'none'
      console.log('Strategy change complete, hiding loading overlay')

      // Force update of signal lights
      if (typeof window.updateAllSignalLights === 'function') {
        console.log('Forcing signal light update')
        window.updateAllSignalLights()
      }

      // Force update of signal matrix
      if (typeof window.updateSignalMatrix === 'function') {
        console.log('Forcing signal matrix update')
        window.updateSignalMatrix()
      }
    }, 2000) // 2 second timeout to allow for data to be received
  }

  // Update signal lights and matrix immediately (no debounce) after server data is received
  // The WebSocket message handler will trigger these updates when new data arrives

  // Hide the menu
  const strategyMenu = document.getElementById('strategyMenu')
  if (strategyMenu) strategyMenu.style.display = 'none'
}

// Update the indicator menu: always show all indicators, tick those in the given strategy or as customized
window.updateIndicatorMenu = function (selectedStrategy, resetToStrategy) {
  const menu = document.getElementById('indicatorMenu')
  if (!menu) return
  const allIndicators = window.ALL_INDICATORS || [
    'rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly',
  ]
  let activeIndicators = []
  if (resetToStrategy && selectedStrategy && window.TRADING_STRATEGIES[selectedStrategy]) {
    activeIndicators = window.TRADING_STRATEGIES[selectedStrategy].indicators
  } else if (window.enabledIndicators) {
    activeIndicators = window.enabledIndicators.filter(i => i.enabled).map(i => i.name)
  }
  menu.innerHTML = allIndicators.map(ind => {
    const checked = activeIndicators.includes(ind) ? 'checked' : ''
    return `<label><input type="checkbox" class="indicator-toggle" data-name="${ind}" ${checked}>${ind.toUpperCase()}</label>`
  }).join('')
}

// Attach indicator menu event listeners for custom strategies
function attachIndicatorMenuHandler() {
  const menu = document.getElementById('indicatorMenu')
  if (!menu) return
  menu.addEventListener('change', (e) => {
    if (e.target.classList.contains('indicator-toggle')) {
      const indName = e.target.getAttribute('data-name')
      const checked = e.target.checked
      if (window.enabledIndicators) {
        const ind = window.enabledIndicators.find(i => i.name === indName)
        if (ind) ind.enabled = checked
      }
      // Update matrix and signal lights immediately
      if (typeof updateSignalMatrix === 'function') updateSignalMatrix()
      if (typeof updateAllSignalLights === 'function') updateAllSignalLights()
    }
  })
}

// Ensure indicator menu handler is attached after DOMContentLoaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', attachIndicatorMenuHandler)
} else {
  attachIndicatorMenuHandler()
}

// Initialize when the DOM is loaded
// Ensure only one canonical strategy selector and indicator menu
window.ALL_INDICATORS = [
  'rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly',
]
document.addEventListener('DOMContentLoaded', () => {
  try {
    initializeStrategySelector()
    if (typeof updateIndicatorMenu === 'function') updateIndicatorMenu('admiral_toa', true)
  } catch (e) {
    console.error('initializeStrategySelector failed:', e)
  }
})
// Fallback: try to initialize after 2s if not already present
setTimeout(() => {
  if (!document.getElementById('strategySelector')) {
    try {
      initializeStrategySelector()
      if (typeof updateIndicatorMenu === 'function') updateIndicatorMenu('admiral_toa', true)
    } catch (e) {
      console.error('Fallback initializeStrategySelector failed:', e)
    }
  }
}, 2000)

// Make functions available globally
window.toggleStrategyMenu = toggleStrategyMenu
window.updateStrategyDescription = updateStrategyDescription
window.applySelectedStrategy = applySelectedStrategy
window.updateIndicatorMenu = window.updateIndicatorMenu
// We use the global TRADING_STRATEGIES from global-variables.js
