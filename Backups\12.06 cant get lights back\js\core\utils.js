/**
 * Military-grade utility functions for StarCrypt
 */

class CoreUtils {
    static get VERSION() {
        return '1.0.0';
    }

    /**
     * Safe DOM element query with fallback
     * @param {string} selector - CSS selector
     * @param {HTMLElement} [context=document] - Query context
     * @returns {HTMLElement|null}
     */
    static safeQuery(selector, context = document) {
        try {
            const element = context.querySelector(selector);
            if (!element) {
                console.warn(`[CoreUtils] Element not found: ${selector}`);
                return null;
            }
            return element;
        } catch (error) {
            console.error(`[CoreUtils] Query error: ${error.message}`);
            return null;
        }
    }

    /**
     * Create element with attributes safely
     * @param {string} tagName - Element type
     * @param {Object} [attributes={}] - Element attributes
     * @returns {HTMLElement}
     */
    static createElement(tagName, attributes = {}) {
        const element = document.createElement(tagName);
        Object.entries(attributes).forEach(([key, value]) => {
            if (key.startsWith('data-')) {
                element.dataset[key.replace('data-', '')] = value;
            } else {
                element.setAttribute(key, value);
            }
        });
        return element;
    }

    /**
     * Debounce function with error handling
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in ms
     * @param {boolean} [immediate=false] - Execute immediately
     * @returns {Function}
     */
    static debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            try {
                const later = () => {
                    timeout = null;
                    if (!immediate) func.apply(this, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(this, args);
            } catch (error) {
                console.error(`[CoreUtils] Debounce error: ${error.message}`);
            }
        };
    }

    /**
     * Throttle function with error handling
     * @param {Function} func - Function to throttle
     * @param {number} wait - Wait time in ms
     * @returns {Function}
     */
    static throttle(func, wait) {
        let lastTime = 0;
        return function throttledFunction(...args) {
            try {
                const now = Date.now();
                if (now - lastTime >= wait) {
                    func.apply(this, args);
                    lastTime = now;
                }
            } catch (error) {
                console.error(`[CoreUtils] Throttle error: ${error.message}`);
            }
        };
    }

    /**
     * Safe JSON parsing with fallback
     * @param {string} data - JSON string
     * @param {any} [fallback=null] - Fallback value
     * @returns {any}
     */
    static safeParseJSON(data, fallback = null) {
        try {
            return JSON.parse(data);
        } catch (error) {
            console.error(`[CoreUtils] JSON parse error: ${error.message}`);
            return fallback;
        }
    }

    /**
     * Generate unique ID
     * @returns {string}
     */
    static generateID() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Get current system status
     * @returns {Object}
     */
    static getSystemStatus() {
        return {
            timestamp: Date.now(),
            uptime: performance.now(),
            memory: {
                used: process.memoryUsage().heapUsed / 1024 / 1024,
                total: process.memoryUsage().heapTotal / 1024 / 1024
            }
        };
    }

    /**
     * Log with timestamp and level
     * @param {string} level - Log level
     * @param {string} message - Log message
     * @param {any} [data] - Additional data
     */
    static log(level, message, data = null) {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        
        switch (level.toLowerCase()) {
            case 'error':
                console.error(logEntry, data);
                break;
            case 'warn':
                console.warn(logEntry, data);
                break;
            case 'info':
                console.info(logEntry, data);
                break;
            case 'debug':
                console.debug(logEntry, data);
                break;
            default:
                console.log(logEntry, data);
        }
    }
}

// Export for browser
if (typeof window !== 'undefined') {
    window.CoreUtils = CoreUtils;
}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CoreUtils;
}
