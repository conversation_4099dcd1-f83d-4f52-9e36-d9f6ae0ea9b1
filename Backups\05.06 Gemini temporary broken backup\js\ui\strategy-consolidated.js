/**
 * Consolidated Strategy Module
 * Handles strategy selection, initialization, and UI updates
 */

// Global state
window.strategyState = {
  isInitialized: false,
  currentStrategy: null,
  currentPair: 'xbtusdt',
  timeframes: window.TIMEFRAMES || ['1m', '5m', '15m', '1h', '4h', '1d', '1w'],
  indicators: {},
  helperOrder: [],
  menuOpen: false,
}

// Constants
const UPDATE_THROTTLE_MS = 1000

// Error handling
class StrategyError extends Error {
  constructor(message, code = 'STRATEGY_ERROR') {
    super(message)
    this.name = 'StrategyError'
    this.code = code
  }
}

/**
 * Initializes the strategy system
 */
async function initializeStrategySystem() {
  try {
    if (strategyState.isInitialized) {
      console.log('[Strategy] Already initialized')
      return
    }

    // Initialize state
    strategyState.isInitialized = true
    strategyState.timeframes = DEFAULT_TIMEFRAMES
    strategyState.currentStrategy = loadSavedStrategy()

    // Initialize indicators
    initializeIndicators()

    // Initialize UI
    await initializeStrategyUI()

    // Start update loop
    startUpdateLoop()

    console.log('[Strategy] System initialized successfully')
  } catch (error) {
    console.error('[Strategy] Initialization error:', error)
    throw error
  }
}

/**
 * Loads saved strategy or uses default
 * @returns {string} Current strategy name
 */
function loadSavedStrategy() {
  const savedStrategy = localStorage.getItem('currentStrategy') || 'admiral_toa'
  if (window.TRADING_STRATEGIES[savedStrategy]) {
    return savedStrategy
  }
  console.warn(`Saved strategy ${savedStrategy} not found, using default`)
  return 'admiral_toa'
}

/**
 * Initializes indicators
 */
function initializeIndicators() {
  strategyState.indicators = {
    momentum: ['rsi', 'stochRsi', 'williamsR', 'ultimateOscillator', 'mfi'],
    trend: ['macd', 'bollingerBands', 'adx', 'atr', 'vwap', 'fractal'],
    volume: ['volume'],
    ml: ['ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
  }

  strategyState.helperOrder = [
    'rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR',
    'ultimateOscillator', 'mfi', 'adx',
  ]
}

/**
 * Initializes strategy UI
 */
async function initializeStrategyUI() {
  try {
    // Initialize strategy selector
    await initializeStrategySelector()

    // Initialize indicator displays
    await initializeIndicatorDisplays()

    // Initialize matrix
    await initializeSignalMatrix()
  } catch (error) {
    console.error('[Strategy] UI initialization error:', error)
    throw error
  }
}

/**
 * Starts the update loop with throttling
 */
function startUpdateLoop() {
  setInterval(async () => {
    if (!strategyState.isInitialized) return

    try {
      await updateStrategyUI()
    } catch (error) {
      console.error('[Strategy] Update loop error:', error)
    }
  }, UPDATE_THROTTLE_MS)
}

/**
 * Updates the strategy UI
 */
async function updateStrategyUI() {
  try {
    // Update strategy selector
    await updateStrategySelector()

    // Update indicator displays
    await updateIndicatorDisplays()

    // Update signal matrix
    await updateSignalMatrix()
  } catch (error) {
    console.error('[Strategy] UI update error:', error)
  }
}

/**
 * Exports all functions to global namespace
 */
window.initializeStrategySystem = initializeStrategySystem
window.loadSavedStrategy = loadSavedStrategy
window.initializeIndicators = initializeIndicators
window.initializeStrategyUI = initializeStrategyUI
window.startUpdateLoop = startUpdateLoop
window.updateStrategyUI = updateStrategyUI

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  initializeStrategySystem()
})
