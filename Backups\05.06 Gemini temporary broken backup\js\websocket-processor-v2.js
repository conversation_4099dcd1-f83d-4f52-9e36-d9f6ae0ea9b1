/**
 * WebSocket Processor with Stack Overflow Protection
 *
 * A robust message processor that handles WebSocket messages with proper queueing,
 * error handling, and stack overflow protection.
 */
class WebSocketProcessorV2 {
  /**
     * Create a new WebSocketProcessor
     * @param {Object} options - Configuration options
     * @param {number} [options.maxBatchSize=10] - Maximum messages per batch
     * @param {number} [options.maxQueueSize=1000] - Max queue size before dropping
     * @param {number} [options.processDelay=10] - Delay between batches (ms)
     * @param {number} [options.maxDepth=5] - Max recursion depth
     * @param {number} [options.maxConsecutiveErrors=10] - Max errors before pausing
     * @param {number} [options.errorResetTime=60000] - Time to reset error counter (ms)
     */
  constructor({
    maxBatchSize = 10,
    maxQueueSize = 1000,
    processDelay = 10,
    maxDepth = 5,
    maxConsecutiveErrors = 10,
    errorResetTime = 60000,
  } = {}) {
    // Configuration
    this.maxBatchSize = maxBatchSize
    this.maxQueueSize = maxQueueSize
    this.processDelay = processDelay
    this.maxDepth = maxDepth
    this.maxConsecutiveErrors = maxConsecutiveErrors
    this.errorResetTime = errorResetTime

    // State
    this.messageQueue = []
    this.messageTimestamps = new Map()
    this.messageHandlers = new Map()
    this.processedMessages = new Set()
    this.currentlyProcessing = new Set()
    this.isProcessing = false
    this.isPaused = false
    this.consecutiveErrors = 0
    this.lastErrorTime = 0
    this.maxProcessedMessages = maxQueueSize * 2
    this.cleanupInterval = null

    // Bind methods
    this.queueMessage = this.queueMessage.bind(this)
    this.processQueue = this.processQueue.bind(this)
    this.processBatch = this.processBatch.bind(this)
    this.processMessage = this.processMessage.bind(this)
    this.addMessageHandler = this.addMessageHandler.bind(this)
    this.removeMessageHandler = this.removeMessageHandler.bind(this)
    this.pauseProcessing = this.pauseProcessing.bind(this)
    this.resumeProcessing = this.resumeProcessing.bind(this)
    this.cleanup = this.cleanup.bind(this)
    this.cleanupOldMessages = this.cleanupOldMessages.bind(this)

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => this.cleanupOldMessages(), 30000)
  }

  /**
     * Queue a message for processing
     * @param {Object} message - The message to queue
     * @returns {string} - Message ID
     */
  queueMessage(message) {
    if (!message || typeof message !== 'object') {
      console.error('Invalid message format')
      return null
    }

    // Generate unique ID if not provided
    if (!message._messageId) {
      message._messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    // Check if message was already processed
    if (this.processedMessages.has(message._messageId)) {
      return message._messageId
    }

    // Add timestamp
    message._timestamp = message._timestamp || Date.now()

    // Add to queue if not full
    if (this.messageQueue.length < this.maxQueueSize) {
      this.messageQueue.push(message)
      this.messageTimestamps.set(message._messageId, message._timestamp)

      // Start processing if not already running
      if (!this.isProcessing && !this.isPaused) {
        this.processQueue().catch(console.error)
      }
    } else {
      console.warn('Message queue full, dropping message:', message._messageId)
    }

    return message._messageId
  }

  /**
     * Process the message queue
     */
  async processQueue() {
    if (this.isProcessing || this.isPaused || this.messageQueue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      // Process messages in batches
      while (this.messageQueue.length > 0 && !this.isPaused) {
        const batch = this.messageQueue.splice(0, this.maxBatchSize)
        await this.processBatch(batch)

        // Add delay between batches
        if (this.messageQueue.length > 0 && this.processDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, this.processDelay))
        }
      }
    } catch (error) {
      console.error('Error processing message queue:', error)
      this.handleError(error)
    } finally {
      this.isProcessing = false

      // Check for more messages
      if (this.messageQueue.length > 0 && !this.isPaused) {
        setTimeout(() => this.processQueue().catch(console.error), 0)
      }
    }
  }

  /**
     * Process a batch of messages
     * @param {Array} batch - Batch of messages to process
     */
  async processBatch(batch) {
    for (const message of batch) {
      if (this.isPaused) break

      try {
        await this.processMessage(message)

        // Mark as processed
        this.processedMessages.add(message._messageId)

        // Reset error counter on success
        this.consecutiveErrors = 0
      } catch (error) {
        console.error('Error processing message:', {
          error,
          messageId: message._messageId,
          type: message.type,
        })

        this.handleError(error)
      }

      // Small delay between messages
      if (this.processDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, 1))
      }
    }
  }

  /**
     * Process a single message
     * @param {Object} message - The message to process
     */
  processMessage(message) {
    // Validate message
    if (!message || !message.type) {
      console.error('Invalid message format: missing type')
      return
    }

    // Check if message is already being processed
    if (this.currentlyProcessing.has(message._messageId)) {
      console.log(`Message ${message._messageId} already being processed, skipping`)
      return
    }

    // Track processing
    this.currentlyProcessing.add(message._messageId)

    try {
      // Get handlers for this message type
      const handlers = this.messageHandlers.get(message.type)
      if (!handlers || handlers.size === 0) {
        console.debug(`No handlers for message type: ${message.type}`)
        return
      }

      // Process handlers synchronously
      handlers.forEach(handler => {
        if (typeof handler !== 'function') {
          console.error(`Invalid handler for message type ${message.type}`)
          return
        }

        try {
          // Execute handler with timeout
          setTimeout(() => {
            try {
              handler(message)
            } catch (error) {
              console.error(`Error in ${message.type} handler:`, {
                error,
                messageId: message._messageId,
                handler: handler.name || 'anonymous',
              })
              this.handleError(error)
            }
          }, 0)
        } catch (error) {
          console.error(`Error scheduling handler for ${message.type}:`, error)
          this.handleError(error)
        }
      })
    } finally {
      // Clean up processing state
      this.currentlyProcessing.delete(message._messageId)
      this.processedMessages.add(message._messageId)

      // Limit processed messages size
      if (this.processedMessages.size > this.maxProcessedMessages) {
        this.processedMessages.delete([...this.processedMessages][0])
      }
    }
  }

  /**
     * Add a message handler
     * @param {string} messageType - The message type to handle
     * @param {Function} handler - The handler function
     * @returns {Function} - Unsubscribe function
     */
  addMessageHandler(messageType, handler) {
    if (typeof handler !== 'function') {
      throw new Error('Handler must be a function')
    }

    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set())
    }

    this.messageHandlers.get(messageType).add(handler)

    // Return unsubscribe function
    return () => this.removeMessageHandler(messageType, handler)
  }

  /**
     * Remove a message handler
     * @param {string} messageType - The message type
     * @param {Function} handler - The handler function to remove
     */
  removeMessageHandler(messageType, handler) {
    if (this.messageHandlers.has(messageType)) {
      const handlers = this.messageHandlers.get(messageType)
      handlers.delete(handler)

      if (handlers.size === 0) {
        this.messageHandlers.delete(messageType)
      }
    }
  }

  /**
     * Pause message processing
     * @param {number} [duration] - Optional duration in ms to pause for
     */
  pauseProcessing(duration) {
    this.isPaused = true

    if (duration > 0) {
      setTimeout(() => this.resumeProcessing(), duration)
    }
  }

  /**
     * Resume message processing
     */
  resumeProcessing() {
    if (this.isPaused) {
      this.isPaused = false
      this.consecutiveErrors = 0
      this.processQueue().catch(console.error)
    }
  }

  /**
     * Handle errors and update error state
     * @param {Error} error - The error that occurred
     */
  handleError(error) {
    this.consecutiveErrors++
    this.lastErrorTime = Date.now()

    // Pause processing if too many errors
    if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
      console.error(`Pausing processing due to ${this.consecutiveErrors} consecutive errors`)
      this.pauseProcessing(this.errorResetTime)

      // Auto-resume after error reset time
      setTimeout(() => {
        console.log('Resuming processing after error timeout')
        this.resumeProcessing()
      }, this.errorResetTime)
    }
  }

  /**
     * Clean up old messages and resources
     */
  cleanupOldMessages() {
    const now = Date.now()
    const maxAge = 5 * 60 * 1000 // 5 minutes

    // Clean up old timestamps
    for (const [id, timestamp] of this.messageTimestamps.entries()) {
      if (now - timestamp > maxAge) {
        this.messageTimestamps.delete(id)
        this.processedMessages.delete(id)
      }
    }

    // Clean up processed messages set if it gets too large
    if (this.processedMessages.size > this.maxProcessedMessages) {
      const array = Array.from(this.processedMessages)
      this.processedMessages = new Set(array.slice(-this.maxProcessedMessages))
    }
  }

  /**
     * Clean up all resources
     */
  cleanup() {
    try {
      // Stop processing
      this.isPaused = true
      this.isProcessing = false

      // Clear queue and state
      this.messageQueue = []
      this.messageTimestamps.clear()
      this.processedMessages.clear()
      this.currentlyProcessing.clear()

      // Clear handlers
      this.messageHandlers.clear()

      // Clear intervals
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval)
        this.cleanupInterval = null
      }

      // Reset state
      this.consecutiveErrors = 0
      this.lastErrorTime = 0

      console.log('WebSocketProcessor cleanup complete')
    } catch (error) {
      console.error('Error during cleanup:', error)
    }
  }
}

// Export for CommonJS and browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WebSocketProcessorV2
} else if (typeof window !== 'undefined') {
  window.WebSocketProcessorV2 = WebSocketProcessorV2
}
