// File: js/ui/menu-handlers.js

/**
 * Initializes click handlers for all primary menu buttons.
 * Uses 'data-target' attribute on buttons to find the menu panel to toggle.
 * Closes other open menu panels when a new one is opened.
 */
function initializeMenuButtonHandlers() {
  try {
    const menuButtons = document.querySelectorAll('.menu-container .menu-button[data-target]')
    if (!menuButtons.length) {
      console.warn('[MenuHandlers] No menu buttons with data-target found in .menu-container')
      return
    }

    menuButtons.forEach(button => {
      // Clone to remove any old/conflicting listeners (good practice from backup)
      const newButton = button.cloneNode(true)
      if (button.parentNode) {
        button.parentNode.replaceChild(newButton, button)
      }

      newButton.addEventListener('click', function (e) {
        e.preventDefault()
        e.stopPropagation()

        const targetId = this.getAttribute('data-target')
        if (!targetId) {
          console.warn('[MenuHandlers] Button clicked without data-target:', this.id)
          return
        }

        const targetMenu = document.getElementById(targetId)
        if (!targetMenu) {
          console.warn('[MenuHandlers] Target menu panel not found for ID:', targetId)
          return
        }

        const isCurrentlyVisible = targetMenu.style.display === 'block'

        // Close all other .menu-content panels
        document.querySelectorAll('.menu-content').forEach(menu => {
          if (menu.id !== targetId) {
            menu.style.display = 'none'
          }
        })

        // Toggle the clicked menu
        targetMenu.style.display = isCurrentlyVisible ? 'none' : 'block'

        // console.log(`[MenuHandlers] Toggled menu: ${targetId} to ${targetMenu.style.display}`);
      })
    })
    console.log('[MenuHandlers] Menu button handlers initialized for', menuButtons.length, 'buttons.')
  } catch (err) {
    console.error('[MenuHandlers] Error initializing menu buttons:', err)
  }
}

/**
 * Sets up a global click listener to close any open menu panels
 * if a click occurs outside of them and not on a menu button.
 */
function setupClickOutsideToCloseMenus() {
  document.addEventListener('click', (event) => {
    const openMenus = document.querySelectorAll('.menu-content[style*="display: block"]')
    if (!openMenus.length) return

    const clickedOnMenuButton = event.target.closest('.menu-container .menu-button[data-target]')

    let clickedInsideOpenMenu = false
    openMenus.forEach(menu => {
      if (menu.contains(event.target)) {
        clickedInsideOpenMenu = true
      }
    })

    if (!clickedInsideOpenMenu && !clickedOnMenuButton) {
      openMenus.forEach(menu => {
        menu.style.display = 'none'
        // console.log(`[MenuHandlers] Closed menu via click-outside: ${menu.id}`);
      })
    }
  })
  console.log('[MenuHandlers] Click-outside-to-close menus handler initialized.')
}

// Initialize menu handlers when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
  initializeMenuButtonHandlers()
  setupClickOutsideToCloseMenus()
  console.log('[MenuHandlers] DOMContentLoaded: All menu handlers set up.')
})
