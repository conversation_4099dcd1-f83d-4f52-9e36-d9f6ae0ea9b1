# WebSocket Migration Guide

This guide explains how to migrate from the old WebSocket implementation to the new, more robust WebSocket system in StarCrypt.

## Key Improvements

1. **Better Error Handling**: Comprehensive error handling and recovery mechanisms
2. **Automatic Reconnection**: Built-in reconnection with exponential backoff
3. **Resource Management**: Proper cleanup of WebSocket resources
4. **Subscription Management**: Track and manage WebSocket subscriptions
5. **Memory Leak Prevention**: Proper cleanup of event listeners and timers
6. **Type Safety**: Improved TypeScript types for better development experience

## Breaking Changes

### 1. Initialization

**Old Way:**
```javascript
// Old initialization
const ws = new WebSocket('wss://api.example.com');
ws.onopen = () => console.log('Connected');
ws.onerror = (e) => console.error('Error:', e);
```

**New Way:**
```javascript
// New initialization
import { WebSocketInit } from './js/websocket-init-new';

// Initialize with configuration
await WebSocketInit.init('wss://api.example.com', {
    autoReconnect: true,
    maxReconnectAttempts: 10,
    reconnectInterval: 1000,
    maxReconnectInterval: 30000,
    pingInterval: 30000,
    pingTimeout: 10000,
    debug: true
});

// Check if initialized
if (WebSocketInit.isInitialized()) {
    console.log('WebSocket is ready');
}
```

### 2. Subscribing to Data

**Old Way:**
```javascript
// Old subscription
ws.send(JSON.stringify({
    type: 'subscribe',
    channel: 'prices',
    pairs: ['BTC/USD']
}));
```

**New Way:**
```javascript
// New subscription
WebSocketInit.subscribeToPrice('BTC/USD');

// With options
WebSocketInit.subscribeToPrice('ETH/USD', {
    interval: '1m',
    limit: 100
});
```

### 3. Handling Messages

**Old Way:**
```javascript
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'price') {
        // Handle price update
    }
};
```

**New Way:**
```javascript
// Listen for price updates
window.addEventListener('priceUpdate', (event) => {
    const { detail } = event;
    console.log('Price update:', detail);
});

// Listen for signal updates
window.addEventListener('signalUpdate', (event) => {
    const { detail } = event;
    console.log('Signal update:', detail);
});

// Listen for order book updates
window.addEventListener('orderbookUpdate', (event) => {
    const { detail } = event;
    console.log('Order book update:', detail);
});
```

### 4. Error Handling

**Old Way:**
```javascript
ws.onerror = (error) => {
    console.error('WebSocket error:', error);
};

ws.onclose = (event) => {
    console.log('WebSocket closed:', event.code, event.reason);
};
```

**New Way:**
```javascript
// Error handling is built into the WebSocketManager
// You can still listen for specific events if needed
window.addEventListener('websocket:error', (event) => {
    console.error('WebSocket error:', event.detail);
});

window.addEventListener('websocket:disconnected', (event) => {
    console.log('WebSocket disconnected:', event.detail);
});
```

### 5. Cleanup

**Old Way:**
```javascript
// Old cleanup
ws.close();
```

**New Way:**
```javascript
// New cleanup
WebSocketInit.cleanup({
    clearSubscriptions: true,
    reason: 'User requested cleanup'
});
```

## Migration Steps

1. **Backup Your Code**
   - Make sure to back up your current WebSocket-related code

2. **Update Dependencies**
   - Ensure you have the latest version of the WebSocketManager

3. **Replace Initialization**
   - Update your WebSocket initialization code to use the new `WebSocketInit`

4. **Update Event Handlers**
   - Replace direct WebSocket event handlers with the new event-based system

5. **Update Subscriptions**
   - Replace manual subscription messages with the new subscription methods

6. **Test Thoroughly**
   - Test all WebSocket functionality to ensure everything works as expected

## New Features

### Automatic Reconnection
```javascript
// The WebSocket will automatically attempt to reconnect if the connection is lost
// with exponential backoff
```

### Subscription Management
```javascript
// Subscribe to multiple pairs
['BTC/USD', 'ETH/USD', 'XRP/USD'].forEach(pair => {
    WebSocketInit.subscribeToPrice(pair);
});

// Unsubscribe when no longer needed
WebSocketInit.unsubscribeFromPrice('XRP/USD');
```

### Debugging
```javascript
// Enable debug mode for detailed logs
WebSocketInit.init('wss://api.example.com', { debug: true });
```

## Troubleshooting

### Common Issues

1. **Connection Issues**
   - Verify the WebSocket URL is correct
   - Check for network/firewall issues
   - Ensure the server is running and accessible

2. **Subscription Issues**
   - Verify you're using the correct channel and pair names
   - Check the WebSocket connection state with `WebSocketInit.isInitialized()`

3. **Performance Issues**
   - Reduce the number of subscriptions if experiencing performance problems
   - Increase the ping interval if needed

## Example Migration

### Before Migration
```javascript
// Old WebSocket code
const ws = new WebSocket('wss://api.example.com');

ws.onopen = () => {
    console.log('Connected');
    ws.send(JSON.stringify({
        type: 'subscribe',
        channel: 'prices',
        pairs: ['BTC/USD']
    }));
};

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'price') {
        updatePriceUI(data);
    }
};

ws.onerror = (error) => {
    console.error('WebSocket error:', error);
};
```

### After Migration
```javascript
// New WebSocket code
import { WebSocketInit } from './js/websocket-init-new';

// Initialize WebSocket
async function initWebSocket() {
    try {
        await WebSocketInit.init('wss://api.example.com', {
            autoReconnect: true,
            debug: true
        });
        
        // Subscribe to price updates
        WebSocketInit.subscribeToPrice('BTC/USD');
        
        // Listen for price updates
        window.addEventListener('priceUpdate', (event) => {
            updatePriceUI(event.detail);
        });
        
    } catch (error) {
        console.error('Failed to initialize WebSocket:', error);
    }
}

// Initialize when the page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initWebSocket);
} else {
    initWebSocket();
}

// Clean up when the page unloads
window.addEventListener('beforeunload', () => {
    WebSocketInit.cleanup({ clearSubscriptions: true });
});
```

## Additional Resources

- [WebSocket API Documentation](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)
- [StarCrypt WebSocket Guide](./WEBSOCKET_GUIDE.md)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)

## Support

If you encounter any issues during migration, please:
1. Check the browser's developer console for errors
2. Review the WebSocket connection in the Network tab
3. Open an issue on GitHub with details about the problem

Happy coding! 🚀
