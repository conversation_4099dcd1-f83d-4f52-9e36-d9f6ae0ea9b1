/**
 * Consolidated WebSocket Module
 * Combines connection management, message processing, and error handling
 */

class WebSocketManager {
  constructor(options = {}) {
    // Connection settings
    this.url = options.url || `ws://${window.location.host}/ws`
    this.protocols = options.protocols || []
    this.ws = null

    // Reconnection settings
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10
    this.reconnectInterval = options.reconnectInterval || 1000
    this.maxReconnectInterval = options.maxReconnectInterval || 30000
    this.connectionTimeout = options.connectionTimeout || 5000
    this.shouldReconnect = true

    // Message processing settings
    this.maxBatchSize = options.maxBatchSize || 10
    this.maxQueueSize = options.maxQueueSize || 1000
    this.processDelay = options.processDelay || 10
    this.maxConsecutiveErrors = options.maxConsecutiveErrors || 10
    this.errorResetTime = options.errorResetTime || 60000

    // State
    this.messageQueue = []
    this.messageHandlers = new Map()
    this.processedMessages = new Set()
    this.currentlyProcessing = new Set()
    this.isProcessing = false
    this.isPaused = false
    this.consecutiveErrors = 0
    this.lastErrorTime = 0

    // Connection state
    this.isConnected = false
    this.isConnecting = false

    // Ping/pong
    this.pingInterval = options.pingInterval || 30000
    this.pingTimeout = options.pingTimeout || 10000
    this.lastPongTime = 0

    // Stack protection
    this.maxDepth = options.maxDepth || 5
    this.currentDepth = 0

    // Bind methods
    this.connect = this.connect.bind(this)
    this.disconnect = this.disconnect.bind(this)
    this.send = this.send.bind(this)
    this.onMessage = this.onMessage.bind(this)
    this.onOpen = this.onOpen.bind(this)
  }

  initializeWebSocket() {
    try {
      this.ws = new WebSocket(this.url)

      // Bind handlers with recursion protection
      this.ws.onopen = this.onOpen.bind(this)
      this.ws.onclose = this.onClose.bind(this)
      this.ws.onerror = this.onError.bind(this)
      this.ws.onmessage = this.onMessage.bind(this)
    } catch (error) {
      console.error('[WebSocket] Failed to initialize:', error)
      this.handleError(error)
    }
  }

  onOpen() {
    console.log('[WebSocket] Connected')
    this.reconnectAttempts = 0
    this.errorCount = 0

    // Process any queued messages
    if (this.messageQueue.length > 0) {
      this.processMessageQueue()
    }
  }

  handleDisconnection() {
    // Clear any pending updates
    this.clearPendingUpdates()
  }

  handleError(error) {
    this.errorCount++
    if (this.errorCount > this.maxErrors) {
      console.error('[WebSocket] Too many errors, disabling updates')
      this.close()
    }
  }

  sendSubscription() {
    const message = {
      type: 'subscribe',
      data: {
        pairs: ['xbtusdt'],
        timeframes: window.TIMEFRAMES,
        indicators: Object.keys(window.MATRIX_INDICATORS),
      },
    }

    window.WebSocketService.sendMessage(this.connectionId, message)
  }

  clearPendingUpdates() {
    // Clear any pending updates
    if (window.SignalManager) {
      window.SignalManager.clearPendingUpdates()
    }
  }

  close() {
    if (this.connectionId) {
      window.WebSocketService.closeConnection(this.connectionId)
      this.connectionId = null
    }
  }

  // Cleanup legacy WebSocket instances
  static cleanupLegacy() {
    try {
      // Remove event listeners
      const wsInstances = Object.values(window).filter(item =>
        item instanceof WebSocket,
      )

      wsInstances.forEach(ws => {
        if (ws.close) {
          ws.close()
        }
      })

      // Remove global WebSocket references
      Object.keys(window).forEach(key => {
        if (window[key] instanceof WebSocket) {
          delete window[key]
        }
      })

      console.log('[WebSocket] Legacy cleanup complete')
    } catch (error) {
      console.error('[WebSocket] Legacy cleanup error:', error)
    }
  }
}

// Initialize WebSocket when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    // Cleanup legacy instances first
    WebSocketManager.cleanupLegacy()

    // Initialize new instance
    window.wsManager = new WebSocketManager()
  })
} else {
  // Cleanup legacy instances first
  WebSocketManager.cleanupLegacy()

  // Initialize new instance
  window.wsManager = new WebSocketManager()
}

// Export for global access
window.WebSocketManager = WebSocketManager
