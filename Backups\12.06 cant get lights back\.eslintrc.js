module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
    es6: true,
    amd: true,
    commonjs: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:import/errors',
    'plugin:import/warnings',
    'plugin:node/recommended',
  ],
  plugins: ['@stylistic/js', 'import', 'node'],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
      modules: true,
    },
  },
  rules: {
    // Stylistic rules
    '@stylistic/js/indent': ['error', 2],
    '@stylistic/js/linebreak-style': ['error', 'windows'],
    '@stylistic/js/quotes': ['error', 'single'],
    '@stylistic/js/semi': ['error', 'always'],
    '@stylistic/js/space-before-function-paren': ['error', 'always'],
    '@stylistic/js/space-before-blocks': 'error',
    '@stylistic/js/space-infix-ops': 'error',
    '@stylistic/js/space-in-parens': ['error', 'never'],
    '@stylistic/js/array-bracket-spacing': ['error', 'never'],
    '@stylistic/js/object-curly-spacing': ['error', 'always'],
    '@stylistic/js/arrow-spacing': 'error',
    '@stylistic/js/no-trailing-spaces': 'error',
    '@stylistic/js/comma-dangle': ['error', 'always-multiline'],

    // Best practices
    'no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
    'no-console': ['warn', { allow: ['warn', 'error', 'info', 'debug'] }],
    'no-var': 'error',
    'prefer-const': 'error',
    eqeqeq: ['error', 'always'],
    'no-multiple-empty-lines': ['error', { max: 1, maxEOF: 0 }],
    'eol-last': ['error', 'always'],
    'require-atomic-updates': 'off',

    // Import rules
    'import/no-unresolved': 'error',
    'import/named': 'error',
    'import/namespace': 'error',
    'import/default': 'error',
    'import/export': 'error',
    'import/order': [
      'error',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index',
        ],
        'newlines-between': 'always',
      },
    ],
  },
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.js', '.json'],
        moduleDirectory: ['node_modules', './'],
      },
    },
    node: {
      tryExtensions: ['.js', '.json', '.node', '.ts'],
    },
  },
  overrides: [
    {
      files: ['**/*.js'],
      excludedFiles: ['**/node_modules/**', '**/dist/**'],
      rules: {
        strict: ['error', 'global'],
        'node/no-unsupported-features/es-syntax': 'off',
        'node/no-missing-import': 'off',
        'node/no-unpublished-import': 'off',
      },
    },
    {
      files: ['**/*.test.js', '**/*.spec.js'],
      env: {
        jest: true,
        mocha: true,
      },
      rules: {
        'no-console': 'off',
      },
    },
    {
      files: [
        '**/webpack.config.js',
        '**/babel.config.js',
        '**/server.js',
        '**/websocket-server.js',
        '**/scripts/**/*.js',
      ],
      env: {
        node: true,
        browser: false,
      },
      rules: {
        'node/no-unpublished-require': 'off',
        'node/no-missing-require': 'off',
        'node/no-unsupported-features/node-builtins': 'off',
        'no-console': 'off',
        'no-undef': 'off',
      },
    },
    {
      files: ['**/public/**/*.js'],
      env: {
        browser: true,
        node: false,
      },
    },
  ],
}
