/**
 * @module MenuController
 * @description Centralized menu management system with robust error handling and state management
 */

import { MenuStateManager } from './menu-state.js'
import { MenuEvents } from './menu-events.js'
import { MenuUtils } from './menu-utils.js'
import { <PERSON>rror<PERSON>andler } from '../utils/error-handler.js'
import { PerformanceMonitor } from '../utils/performance-monitor.js'

export class MenuController {
  constructor() {
    // State Management
    this.state = new MenuStateManager()

    // Event Handlers
    this.events = new MenuEvents()

    // Utility Classes
    this.utils = new MenuUtils()

    // Error Handling
    this.errorHandler = new ErrorHandler({
      maxErrors: 5,
      recoveryDelay: 2000,
      errorThreshold: 0.1,
    })

    // Performance Monitoring
    this.performance = new PerformanceMonitor({
      targetFPS: 60,
      maxLatency: 100,
      batchInterval: 100,
    })

    // Configuration
    this.config = {
      animationDuration: 300,
      transitionDelay: 100,
      maxMenuDepth: 3,
      keyboardNavigation: true,
      ariaSupport: true,
    }

    // Internal State
    this.isInitialized = false
    this.activeMenu = null
    this.menuStack = []
    this.errorCount = 0
    this.lastUpdate = 0
    this.updateQueue = []
    this.batchUpdates = []
  }

  /**
     * Initialize the menu system with error boundaries and performance monitoring
     * @returns {Promise<void>}
     */
  async initialize() {
    if (this.isInitialized) return

    try {
      // Initialize components
      await this.initializeComponents()

      // Setup event listeners
      this.setupEventListeners()

      // Load preferences
      await this.loadPreferences()

      // Initialize performance monitoring
      this.performance.start()

      // Mark as initialized
      this.isInitialized = true
      console.log('[MenuController] Initialized successfully')

      // Dispatch initialization event
      this.events.dispatch('initialized', { controller: this })
    } catch (error) {
      this.handleError(error, 'initialization')
    }
  }

  /**
     * Initialize all menu components
     * @private
     */
  async initializeComponents() {
    try {
      // Initialize menu elements
      await this.initializeMenuElements()

      // Setup keyboard navigation
      this.setupKeyboardNavigation()

      // Initialize ARIA support
      this.initializeARIA()
    } catch (error) {
      throw new Error(`Failed to initialize components: ${error.message}`)
    }
  }

  /**
     * Initialize menu elements with error handling
     * @private
     */
  initializeMenuElements() {
    try {
      // Query menu elements
      const menuButtons = Array.from(document.querySelectorAll('.menu-button[data-target]'))
      const menus = Array.from(document.querySelectorAll('.menu-content'))

      // Validate elements
      if (!menuButtons.length || !menus.length) {
        throw new Error('No menu elements found')
      }

      // Store elements
      this.menuButtons = menuButtons
      this.menus = menus

      // Setup menu relationships
      this.setupMenuRelationships()
    } catch (error) {
      throw new Error(`Failed to initialize menu elements: ${error.message}`)
    }
  }

  /**
     * Setup menu relationships and event handlers
     * @private
     */
  setupMenuRelationships() {
    try {
      // Map menu buttons to their targets
      this.menuButtons.forEach(button => {
        const targetId = button.dataset.target
        const menu = document.getElementById(targetId)

        if (!menu) {
          console.warn(`Menu button target not found: ${targetId}`)
          return
        }

        button.menuTarget = menu
        menu.menuButton = button

        // Add click handler
        button.addEventListener('click', (e) => this.handleMenuClick(e))
      })
    } catch (error) {
      throw new Error(`Failed to setup menu relationships: ${error.message}`)
    }
  }

  /**
     * Handle menu click events with error boundaries
     * @param {Event} event - The click event
     * @private
     */
  handleMenuClick(event) {
    try {
      const button = event.currentTarget
      const menuId = button.dataset.target

      // Track interaction
      this.trackInteraction('click', button, menuId)

      // Toggle menu
      this.toggleMenu(menuId)
    } catch (error) {
      this.handleError(error, 'menu-click')
    }
  }

  /**
     * Toggle menu visibility with animations
     * @param {string} menuId - The ID of the menu to toggle
     * @private
     */
  toggleMenu(menuId) {
    try {
      // Get menu element
      const menu = document.getElementById(menuId)
      if (!menu) {
        throw new Error(`Menu not found: ${menuId}`)
      }

      // Check if menu is already active
      if (this.activeMenu === menuId) {
        this.closeMenu(menuId)
      } else {
        this.openMenu(menuId)
      }
    } catch (error) {
      throw new Error(`Failed to toggle menu: ${error.message}`)
    }
  }

  /**
     * Open a menu with animations
     * @param {string} menuId - The ID of the menu to open
     * @private
     */
  openMenu(menuId) {
    try {
      // Get menu element
      const menu = document.getElementById(menuId)
      if (!menu) {
        throw new Error(`Menu not found: ${menuId}`)
      }

      // Close any open menus
      this.closeAllMenus()

      // Add menu to stack
      this.menuStack.push(menuId)

      // Update state
      this.activeMenu = menuId

      // Add active class with animation
      menu.classList.add('menu-transition')
      setTimeout(() => {
        menu.classList.add('active')
      }, 10)

      // Update ARIA attributes
      menu.setAttribute('aria-hidden', 'false')
      menu.setAttribute('aria-expanded', 'true')

      // Update button state
      const button = document.querySelector(`[data-target="${menuId}"]`)
      if (button) {
        button.setAttribute('aria-expanded', 'true')
      }

      // Dispatch open event
      this.events.dispatch('menu-open', { menuId })
    } catch (error) {
      throw new Error(`Failed to open menu: ${error.message}`)
    }
  }

  /**
     * Close a menu with animations
     * @param {string} menuId - The ID of the menu to close
     * @private
     */
  closeMenu(menuId) {
    try {
      // Get menu element
      const menu = document.getElementById(menuId)
      if (!menu) {
        throw new Error(`Menu not found: ${menuId}`)
      }

      // Remove active class with animation
      menu.classList.remove('active')

      // Update state
      this.activeMenu = null

      // Remove from stack
      const index = this.menuStack.indexOf(menuId)
      if (index > -1) {
        this.menuStack.splice(index, 1)
      }

      // Update ARIA attributes
      menu.setAttribute('aria-hidden', 'true')
      menu.setAttribute('aria-expanded', 'false')

      // Update button state
      const button = document.querySelector(`[data-target="${menuId}"]`)
      if (button) {
        button.setAttribute('aria-expanded', 'false')
      }

      // Dispatch close event
      this.events.dispatch('menu-close', { menuId })
    } catch (error) {
      throw new Error(`Failed to close menu: ${error.message}`)
    }
  }

  /**
     * Close all open menus
     * @private
     */
  closeAllMenus() {
    try {
      // Get all open menus
      const openMenus = document.querySelectorAll('.menu-content.active')

      // Close each menu
      openMenus.forEach(menu => {
        this.closeMenu(menu.id)
      })

      // Reset state
      this.activeMenu = null
      this.menuStack = []

      // Dispatch close all event
      this.events.dispatch('menu-close-all')
    } catch (error) {
      throw new Error(`Failed to close all menus: ${error.message}`)
    }
  }

  /**
     * Track menu interactions for analytics
     * @param {string} type - Type of interaction (click, keyboard, etc.)
     * @param {HTMLElement} element - The interacting element
     * @param {string} menuId - The ID of the menu being interacted with
     * @private
     */
  trackInteraction(type, element, menuId) {
    try {
      const interaction = {
        timestamp: Date.now(),
        type,
        elementId: element.id,
        menuId,
        context: this.getContext(),
      }

      // Send to analytics
      this.utils.trackAnalytics('menu-interaction', interaction)
    } catch (error) {
      console.error('[MenuController] Error tracking interaction:', error)
    }
  }

  /**
     * Get current context for analytics
     * @returns {Object} Current context information
     * @private
     */
  getContext() {
    return {
      activeMenu: this.activeMenu,
      menuStack: [...this.menuStack],
      timestamp: Date.now(),
      performance: this.performance.getStats(),
    }
  }

  /**
     * Handle errors with recovery
     * @param {Error} error - The error object
     * @param {string} context - Context where error occurred
     * @private
     */
  handleError(error, context) {
    try {
      // Log error
      console.error(`[MenuController] Error in ${context}:`, error)

      // Increment error count
      this.errorCount++

      // Track error
      this.errorHandler.track(error, context)

      // Check for recovery needed
      if (this.errorCount >= this.errorHandler.maxErrors) {
        this.recoverFromErrors()
      }
    } catch (error) {
      console.error('[MenuController] Error handling failed:', error)
    }
  }

  /**
     * Attempt to recover from errors
     * @private
     */
  recoverFromErrors() {
    try {
      // Close all menus
      this.closeAllMenus()

      // Reset state
      this.resetState()

      // Reinitialize
      this.initialize()

      // Dispatch recovery event
      this.events.dispatch('error-recovery')
    } catch (error) {
      console.error('[MenuController] Recovery failed:', error)
    }
  }

  /**
     * Reset menu state
     * @private
     */
  resetState() {
    try {
      // Reset all state
      this.activeMenu = null
      this.menuStack = []
      this.errorCount = 0
      this.updateQueue = []
      this.batchUpdates = []

      // Reset performance monitoring
      this.performance.reset()

      // Reset error handler
      this.errorHandler.reset()
    } catch (error) {
      throw new Error(`Failed to reset state: ${error.message}`)
    }
  }

  /**
     * Clean up resources
     * @private
     */
  cleanup() {
    try {
      // Remove event listeners
      if (this.menuButtons && this.menuButtons.length > 0) {
        this.removeEventListeners()
      }

      // Stop performance monitoring if initialized
      if (this.performance) {
        this.performance.stop()
      }

      // Reset state if exists
      if (this.state) {
        this.resetState()
      }

      // Nullify references
      this.menuButtons = null
      this.menus = null
      this.state = null
      this.events = null
      this.utils = null
    } catch (error) {
      this.logger.error('Error during cleanup:', error)
    }
  }

  /**
     * Destroy the menu controller
     * @private
     */
  destroy() {
    try {
      // Cleanup resources
      this.cleanup()

      // Close all menus
      this.closeAllMenus()

      // Mark as not initialized
      this.isInitialized = false
    } catch (error) {
      console.error('[MenuController] Destruction failed:', error)
    }
  }
}

// Export singleton instance
const menuController = new MenuController()
export default menuController
