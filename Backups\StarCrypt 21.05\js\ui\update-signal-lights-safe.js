// update-signal-lights-safe.js - Completely rewritten to eliminate stack overflows
// This file handles updating the signal lights based on indicator data

// Track states to prevent recursive calls
let isUpdating = false
let updatePending = false
const reportedMissingIndicators = new Set()

// Add debug mode (disabled by default)
window.DEBUG_SIGNALS = false

/**
 * Public function to update all signal lights - uses a queue approach instead of recursion
 */
function updateAllSignalLights() {
  // If already updating, schedule a future update instead of executing now
  if (isUpdating) {
    console.log('Already updating signal lights, queuing another update')
    updatePending = true
    return
  }

  // Set updating flag to prevent multiple concurrent updates
  isUpdating = true

  try {
    // Log start of update
    console.log('Updating signal lights...')

    // Clear any existing update timers
    if (window._signalUpdateTimer) {
      clearTimeout(window._signalUpdateTimer)
      window._signalUpdateTimer = null
    }

    // Check if we have indicator data from the server
    if (!window.indicatorsData || Object.keys(window.indicatorsData).length === 0) {
      console.log('No indicator data available, will try again when data arrives')
      isUpdating = false
      return
    }

    // Use requestAnimationFrame for better performance
    // This ensures we don't block the UI thread and also gives time for the DOM to update
    window._signalUpdateTimer = requestAnimationFrame(() => {
      try {
        // Perform the actual update using a non-recursive approach
        updateSignalLightsNonRecursive()
      } catch (err) {
        console.error('Error in signal light update:', err)
      } finally {
        // Always clear the updating flag when done
        isUpdating = false

        // If another update was requested while we were updating, schedule it now
        if (updatePending) {
          updatePending = false
          // Small delay to prevent tight loop
          setTimeout(updateAllSignalLights, 100)
        }
      }
    })
  } catch (err) {
    console.error('Unexpected error in updateAllSignalLights:', err)
    isUpdating = false
  }
}

/**
 * Non-recursive implementation of signal light updates
 */
function updateSignalLightsNonRecursive() {
  // Skip if no data available
  if (!window.indicatorsData) {
    console.warn('No indicator data available')
    return
  }

  // Define indicator groups (moved from global scope to local)
  const INDICATORS = {
    momentum: ['rsi', 'stochRsi', 'macd', 'williamsR', 'mfi', 'ultimateOscillator'],
    trend: ['adx', 'bollingerBands', 'atr', 'vwap', 'fractal'],
    volume: ['volume', 'obv', 'cmf'],
    advanced: ['ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
  }

  // Define timeframes (moved from global scope to local)
  const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

  // Batching to improve performance
  batchUpdateSignalLights(INDICATORS, TIMEFRAMES)
}

/**
 * Batch updates all signal lights using a queue mechanism
 */
function batchUpdateSignalLights(INDICATORS, TIMEFRAMES) {
  // Create a flat list of all indicator-timeframe pairs to process
  const updateQueue = []

  // Add all timeframes and indicators to the queue
  for (const tf of TIMEFRAMES) {
    // Skip timeframes that don't have data
    if (!window.indicatorsData[tf]) continue

    // Add all indicators from each category
    for (const category in INDICATORS) {
      for (const ind of INDICATORS[category]) {
        // Only add if we have data for this indicator
        if (window.indicatorsData[tf] && window.indicatorsData[tf][ind]) {
          updateQueue.push({ indicator: ind, timeframe: tf })
        }
      }
    }
  }

  console.log(`Processing ${updateQueue.length} indicator-timeframe pairs`)

  // Instead of processing all at once, use a batch approach with requestAnimationFrame
  processBatch(updateQueue, 0, 50) // Process 50 items per frame
}

/**
 * Process a batch of signal light updates
 */
function processBatch(queue, startIndex, batchSize) {
  const endIndex = Math.min(startIndex + batchSize, queue.length)

  // Process this batch
  for (let i = startIndex; i < endIndex; i++) {
    const { indicator, timeframe } = queue[i]
    updateSingleSignalLight(indicator, timeframe)
  }

  // If there are more items to process, schedule the next batch
  if (endIndex < queue.length) {
    requestAnimationFrame(() => {
      processBatch(queue, endIndex, batchSize)
    })
  } else {
    console.log('Finished updating all signal lights')

    // After updating all signals, update the convergence indicators
    updateConvergenceIndicators()
  }
}

/**
 * Updates a single signal light without recursion
 */
function updateSingleSignalLight(ind, tf) {
  try {
    // First check if we have data for this indicator
    if (!window.indicatorsData[tf] || !window.indicatorsData[tf][ind]) {
      return false
    }

    // Find the circle element for the given indicator and timeframe
    const selector = `.signal-circle[data-ind="${ind}"][data-tf="${tf}"]`
    let circle = document.querySelector(selector)

    // If circle doesn't exist, create it - but don't recursively call this function
    if (!circle) {
      // Only report missing indicators once
      const key = `${ind}-${tf}`
      if (!reportedMissingIndicators.has(key)) {
        reportedMissingIndicators.add(key)
        console.log(`Creating missing signal light for ${ind} on ${tf}`)
      }

      // Find a container for the signal lights
      const container =
        document.getElementById('signalGrid') ||
        document.getElementById('signalMatrix') ||
        document.querySelector('.signal-matrix') ||
        document.querySelector('.oracle-matrix')

      // If we found a container, create the missing circle
      if (container) {
        circle = document.createElement('div')
        circle.className = 'signal-circle grey-light neutral'
        circle.dataset.ind = ind
        circle.dataset.tf = tf
        container.appendChild(circle)
      } else {
        return false // Couldn't create
      }
    }

    // Get the signal state for this indicator/timeframe
    let signalState = null
    if (typeof getSignalState === 'function') {
      signalState = getSignalState(ind, tf, window.indicatorsData[tf][ind], window.indicatorsData[tf])
    } else {
      signalState = window.indicatorsData[tf][ind]
    }

    // If no valid signal state, set to disabled
    if (!signalState) {
      circle.className = 'signal-circle disabled'
      circle.setAttribute('data-signal', 'disabled')
      return false
    }

    // Only change on real color changes to avoid flicker
    const prevColor = circle.dataset.currentColor
    const prevTooltip = circle.dataset.currentTooltip

    // Get the signal class from the state
    const signalClass = signalState.signalClass || 'neutral'

    // Map signal class to color class
    let colorClass
    switch (signalClass) {
      case 'degen-buy':
        colorClass = 'green-light'
        break
      case 'mild-buy':
        colorClass = 'blue-light'
        break
      case 'neutral':
        colorClass = 'grey-light'
        break
      case 'mild-sell':
        colorClass = 'orange-light'
        break
      case 'degen-sell':
        colorClass = 'red-light'
        break
      default:
        colorClass = 'grey-light'
    }

    // Update color class when it changes
    if (prevColor !== signalState.color) {
      // First remove all possible color classes
      circle.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light')

      // Remove all possible signal classes
      circle.classList.remove('degen-buy', 'mild-buy', 'neutral', 'mild-sell', 'degen-sell')

      // Add the appropriate color class
      circle.classList.add(colorClass)

      // Also add the signal class for additional styling
      circle.classList.add(signalClass)

      // Set data attributes for reference
      circle.dataset.currentColor = signalState.color
      circle.dataset.signalClass = signalClass

      // Log for debugging
      if (window.DEBUG_SIGNALS) {
        console.log(`Updated signal light ${ind}:${tf} to ${signalClass} (${colorClass})`)
      }
    }

    // Update tooltip when it changes
    if (signalState.tooltip && prevTooltip !== signalState.tooltip) {
      circle.setAttribute('data-tooltip', signalState.tooltip)
      circle.dataset.currentTooltip = signalState.tooltip
    }

    return true
  } catch (err) {
    console.error(`Error updating signal light for ${ind} on ${tf}:`, err)
    return false
  }
}

/**
 * Updates the convergence indicators (like overall strategy strength)
 * Completely separate from the main signal light update to avoid recursion
 */
function updateConvergenceIndicators() {
  try {
    // Update the strategy strength display if it exists
    const strengthElement = document.getElementById('strategyStrength')
    if (strengthElement) {
      // Calculate strength based on signal convergence
      const strength = calculateStrategyStrength()
      strengthElement.textContent = `${strength}%`

      // Update color based on strength
      if (strength >= 80) {
        strengthElement.className = 'green-value'
      } else if (strength >= 60) {
        strengthElement.className = 'blue-value'
      } else if (strength >= 40) {
        strengthElement.className = 'neutral-value'
      } else if (strength >= 20) {
        strengthElement.className = 'orange-value'
      } else {
        strengthElement.className = 'red-value'
      }
    }

    // Other convergence indicators can be updated here
  } catch (err) {
    console.error('Error updating convergence indicators:', err)
  }
}

/**
 * Calculate the strategy strength based on signal convergence
 * Returns a percentage from 0-100
 */
function calculateStrategyStrength() {
  // Default to 50% if we can't calculate
  let strength = 50

  try {
    // Count green vs red signals as a very simple metric
    let green = 0
    let red = 0
    let total = 0

    // Get all signal lights
    const signals = document.querySelectorAll('.signal-circle')

    signals.forEach(signal => {
      if (signal.classList.contains('green-light') || signal.classList.contains('degen-buy')) {
        green++
      } else if (signal.classList.contains('blue-light') || signal.classList.contains('mild-buy')) {
        green += 0.5
      } else if (signal.classList.contains('red-light') || signal.classList.contains('degen-sell')) {
        red++
      } else if (signal.classList.contains('orange-light') || signal.classList.contains('mild-sell')) {
        red += 0.5
      }
      total++
    })

    if (total > 0) {
      // Balance between buy and sell signals
      const buyRatio = green / total
      const sellRatio = red / total

      if (buyRatio > sellRatio) {
        strength = Math.round(50 + (buyRatio * 50))
      } else {
        strength = Math.round(50 - (sellRatio * 50))
      }

      // Cap at 0-100
      strength = Math.max(0, Math.min(100, strength))
    }
  } catch (err) {
    console.error('Error calculating strategy strength:', err)
  }

  return strength
}

// Make the functions available globally
window.updateAllSignalLights = updateAllSignalLights
