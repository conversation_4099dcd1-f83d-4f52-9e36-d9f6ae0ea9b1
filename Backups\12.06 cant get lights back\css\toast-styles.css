/* Toast Notifications */
.strategy-toast {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(26, 26, 42, 0.95);
  color: #00FFFF;
  padding: 12px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  font-family: 'Orbitron', sans-serif;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  border: 1px solid #00FFFF55;
  backdrop-filter: blur(5px);
  opacity: 0;
  animation: toastFadeIn 0.3s ease-out forwards;
  transition: all 0.3s ease;
}

.strategy-toast.fade-out {
  animation: toastFadeOut 0.3s ease-in forwards;
}

@keyframes toastFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

@keyframes toastFadeOut {
  from {
    opacity: 1;
    transform: translate(-50%, 0);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -20px);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .strategy-toast {
    width: 90%;
    max-width: 320px;
    font-size: 13px;
    padding: 10px 16px;
    text-align: center;
  }
}
