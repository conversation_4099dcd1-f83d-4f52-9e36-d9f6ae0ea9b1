/**
 * @module ThresholdManager
 * @description Manages threshold settings for trading signals
 */

import Logger from '../utils/logger.js'

export class ThresholdManager {
  constructor() {
    this.thresholds = new Map()
    this.logger = Logger.getInstance()
    this.logger.setContext('ThresholdManager')
  }

  /**
     * Set threshold for a specific indicator
     * @param {string} indicator - The indicator name
     * @param {number} value - The threshold value
     * @returns {boolean} True if threshold was set successfully
     */
  setThreshold(indicator, value) {
    try {
      if (!indicator || typeof value !== 'number') {
        this.logger.error(`Invalid parameters for setThreshold: ${indicator}, ${value}`)
        return false
      }

      this.thresholds.set(indicator, value)
      this.logger.info(`Threshold set for ${indicator}: ${value}`)
      return true
    } catch (error) {
      this.logger.error(`Error setting threshold: ${error.message}`)
      return false
    }
  }

  /**
     * Get threshold for a specific indicator
     * @param {string} indicator - The indicator name
     * @returns {number|null} The threshold value or null if not set
     */
  getThreshold(indicator) {
    try {
      return this.thresholds.get(indicator) || null
    } catch (error) {
      this.logger.error(`<PERSON>rror getting threshold: ${error.message}`)
      return null
    }
  }

  /**
     * Get all thresholds
     * @returns {Map} Map of all thresholds
     */
  getAllThresholds() {
    try {
      return new Map(this.thresholds)
    } catch (error) {
      this.logger.error(`Error getting all thresholds: ${error.message}`)
      return new Map()
    }
  }

  /**
     * Reset thresholds to default values
     */
  resetToDefaults() {
    try {
      this.thresholds.clear()
      this.logger.info('Thresholds reset to defaults')
    } catch (error) {
      this.logger.error(`Error resetting thresholds: ${error.message}`)
    }
  }

  /**
     * Validate threshold value
     * @param {number} value - The threshold value to validate
     * @returns {boolean} True if value is valid
     */
  validateThreshold(value) {
    return typeof value === 'number' && !isNaN(value)
  }

  /**
     * Load thresholds from storage
     */
  async loadFromStorage() {
    try {
      const stored = localStorage.getItem('thresholds')
      if (stored) {
        const thresholds = JSON.parse(stored)
        this.thresholds = new Map(Object.entries(thresholds))
        this.logger.info('Loaded thresholds from storage')
      }
    } catch (error) {
      this.logger.error(`Error loading thresholds: ${error.message}`)
    }
  }

  /**
     * Save thresholds to storage
     */
  async saveToStorage() {
    try {
      localStorage.setItem('thresholds', JSON.stringify(Object.fromEntries(this.thresholds)))
      this.logger.info('Saved thresholds to storage')
    } catch (error) {
      this.logger.error(`Error saving thresholds: ${error.message}`)
    }
  }

  /**
     * Update threshold UI elements
     */
  updateUI() {
    try {
      const thresholdElements = document.querySelectorAll('.threshold-input')
      thresholdElements.forEach(element => {
        const indicator = element.dataset.indicator
        const value = this.getThreshold(indicator)
        if (value !== null) {
          element.value = value
        }
      })
    } catch (error) {
      this.logger.error(`Error updating UI: ${error.message}`)
    }
  }
}

// Export singleton instance
export const thresholdManager = new ThresholdManager()
