            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            padding: 0.5rem;
            background: rgba(0, 0, 0, 0.7);
            border-bottom: 1px solid #333;
          }
          .menu-button {
            padding: 0.5rem 1rem;
            background: #1a1a2e;
            color: #e6e6e6;
            border: 1px solid #333;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Orbitron', sans-serif;
            font-size: 0.9rem;
            transition: all 0.2s;
          }
          .menu-button:hover {
            background: #2a2a3e;
            transform: translateY(-1px);
          }
          .menu-content {
            display: none;
            position: absolute;
            background: rgba(26, 26, 46, 0.95);
            border: 1px solid #333;
            border-radius: 4px;
            padding: 1rem;
            z-index: 1000;
            min-width: 300px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
          }
          .menu-content h3 {
            margin-top: 0;
            color: #00ffff;
            border-bottom: 1px solid #333;
            padding-bottom: 0.5rem;
          }
        </style>
        
        <div class="menu-buttons">
          <button class="menu-button" id="strategyButton">Strategy</button>
          <button class="menu-button" id="toggleMenuButton">Indicators</button>
          <button class="menu-button" id="toggleThresholdsButton">Thresholds</button>
          <button class="menu-button" id="toggleLogicButton">Signal Logic</button>
          <button class="menu-button" id="toggleLightLogicButton">Light Logic</button>
          <button class="menu-button" id="toggleTimeframesButton">Timeframes</button>
          <button class="menu-button" id="refreshIndicatorsButton">Refresh</button>
          <button class="menu-button" id="forceUpdateLightsButton">Force Update</button>
          <button class="menu-button" id="themeToggleButton">Theme</button>
        </div>
        
        <div class="menu-content" id="strategyMenu">
          <h3>Strategy Selector</h3>
          <p>Failed to load strategy menu. Please refresh the page.</p>
        </div>
        
        <div class="menu-content" id="indicatorMenu">
          <h3>Indicators</h3>
          <p>Failed to load indicators. Please refresh the page.</p>
        </div>
        
        <div class="menu-content" id="thresholdsMenu">
          <h3>Thresholds</h3>
          <p>Failed to load thresholds. Please refresh the page.</p>
        </div>
        
        <div class="menu-content" id="logicMenu">
          <h3>Signal Logic</h3>
          <p>Failed to load signal logic. Please refresh the page.</p>
        </div>
        
        <div class="menu-content" id="lightLogicMenu">
          <h3>Light Logic</h3>
          <p>Failed to load light logic. Please refresh the page.</p>
        </div>
        
        <div class="menu-content" id="timeframesMenu">
          <h3>Timeframes</h3>
          <p>Failed to load timeframes. Please refresh the page.</p>
        </div>
      `;
      
      console.log('[MenuLoader] Fallback menu created successfully');
      
      // Dispatch loaded event for fallback menu
      document.dispatchEvent(new CustomEvent('menus:loaded', { 
        detail: { 
          success: true,
          isFallback: true,
          container: this.menuContainer
        } 
      }));
      
      return true;
      
    } catch (error) {
      console.error('[MenuLoader] Failed to create fallback menu:', error);
      
      // Try a more basic fallback
      if (this.menuContainer) {
        this.menuContainer.innerHTML = `
          <div style="background: #1a1a2e; color: #ff0000; padding: 1rem; font-family: Arial, sans-serif;">
            <h3>Error Loading Menu</h3>
            <p>Failed to load the menu system. Please refresh the page.</p>
            <p>${error.message || 'Unknown error'}</p>
          </div>
        `;
      }
      
      // Dispatch error event
      this.dispatchError(`Failed to create fallback menu: ${error.message}`);
      
      return false;
    }
  }
  
  dispatchError(errorMessage) {
    const errorEvent = new CustomEvent('menus:error', { 
      detail: { 
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString()
      } 
    });
    document.dispatchEvent(errorEvent);
    this.initialized = true;
  }
}

// Initialize the menu loader when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('[MenuLoader] DOM loaded, initializing menu loader');
  
  // Small delay to ensure other critical elements are loaded
  setTimeout(() => {
    const menuLoader = new MenuLoader();
    menuLoader.initialize();
    
    // Make menuLoader globally available if needed
    window.menuLoader = menuLoader;
  }, 100);
});
