// Track if we're already initializing to prevent duplicate work
let isInitializing = false
window.lastUpdateTime = window.lastUpdateTime || 0

// Debug mode for signal lights - only enable when needed
window.DEBUG_SIGNALS = false

// Implement debouncing to prevent excessive updates
let signalUpdateDebounceTimer = null
const SIGNAL_UPDATE_DEBOUNCE = 2000 // 2 seconds debounce

// Improved: updateAllSignalLights now uses debouncing for better performance
function updateAllSignalLights() {
  ensureStrategyElements() // Ensure element exists before use
  // First run: initialize immediately
  if (!window._signalLightsInitialized) {
    window._signalLightsInitialized = true
    window.lastUpdateTime = Date.now()
    _updateSignalLightsCore()
    return
  }

  // Prevent recursive or concurrent updates
  if (isInitializing || window._isUpdatingSignalLights) {
    return
  }

  // Clear any existing timer
  if (signalUpdateDebounceTimer) {
    clearTimeout(signalUpdateDebounceTimer)
  }

  // Set a new timer
  signalUpdateDebounceTimer = setTimeout(() => {
    window.lastUpdateTime = Date.now()
    _updateSignalLightsCore()
  }, SIGNAL_UPDATE_DEBOUNCE)
}

// Add dynamic creation of currentStrategyName element if not found
function ensureStrategyElements() {
  let strategyNameElement = document.getElementById('currentStrategyName')
  if (!strategyNameElement) {
    strategyNameElement = document.createElement('span')
    strategyNameElement.id = 'currentStrategyName'
    strategyNameElement.style.display = 'none' // Or append to appropriate parent
    document.body.appendChild(strategyNameElement) // Adjust parent if needed
    console.log('Created missing currentStrategyName element')
  }
}

// The actual update function that does the work
function _updateSignalLightsCore() {
  console.log('Updating signal lights...')

  // Prevent recursive calls that could cause stack overflow
  if (window._isUpdatingSignalLights) {
    console.log('Skipping recursive signal light update')
    isInitializing = false
    return
  }

  try {
    // Set flags to prevent concurrent and recursive updates
    window._isUpdatingSignalLights = true
    isInitializing = true

    // Check if we have indicator data
    if (!window.indicatorsData) {
      console.log('No indicator data available, requesting from server')
      // Request real data from server
      if (typeof window.requestServerData === 'function') {
        window.requestServerData()
        console.log('Waiting for server data...')
        // We'll wait for the WebSocket message handler to update the UI
        return
      }
      console.error('requestServerData function not available')
      return
    }

    // Only log on first run
    if (!window._signalLightsInitialized) {
      console.log('Initializing signal lights...')
    } else {
      // Skip verbose logging after first initialization
    }

    // Never use mock data - StarCrypt only uses real data
    // Ensure we have proper data from the WebSocket connection

    // Always use the original timeframes from the backup
    const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
    const activeTimeframes = TIMEFRAMES

    // Only log indicator data on first run and only if debugging is enabled
    if (!window._signalLightsInitialized && window.DEBUG_SIGNALS) {
      console.log('Available indicator data:', Object.keys(indicatorsData))
      // Skip detailed logging of indicators per timeframe to speed up initialization
    }

    // Only check consistency on first run
    if (!window._signalLightsInitialized) {
      checkTimeframeDataConsistency()
    }

    // Even if WebSocket is not connected, we should still try to update the lights with any data we might have
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      console.log('WebSocket not connected, using cached data if available')
      // Only initialize with neutral colors if we have no data at all
      if (Object.keys(indicatorsData).length === 0) {
        console.log('No indicator data available, initializing with neutral colors')
        initializeSignalLights()
        return
      }
    }

    // Log a message to indicate we're proceeding with the update
    logMessages.push(`[${new Date().toLocaleString()}] Updating signal lights with available data`)
    updateLogger()

    // Check if volume data is available
    let volumeDataAvailable = false
    activeTimeframes.forEach(tf => {
      if (indicatorsData[tf]?.volume?.value !== undefined) {
        volumeDataAvailable = true
        logMessages.push(`[${new Date().toLocaleString()}] Volume data available for ${tf}: ${indicatorsData[tf].volume.value}`)
        updateLogger()
      }
    })

    if (!volumeDataAvailable) {
      logMessages.push(`[${new Date().toLocaleString()}] Warning: Volume data not available for any timeframe`)
      updateLogger()
    }

    // Use existing timeframeSignals if available, otherwise initialize a new object
    if (!timeframeSignals) {
      timeframeSignals = {}
    }

    // Use requestAnimationFrame for better performance
    requestAnimationFrame(() => {
      try {
        const timeframeConvergence = { buy: {}, sell: {} }
        const buyConvergence = []
        const sellConvergence = []
        let updatedLights = 0
        let failedLights = 0

        // Use the activeTimeframes variable defined at the beginning of the function
        activeTimeframes.forEach(tf => {
          if (!indicatorsData[tf]) {
            console.log(`No data for ${tf}, skipping`)
            timeframeSignals[tf] = {}
            timeframeConvergence.buy[tf] = []
            timeframeConvergence.sell[tf] = []
            return
          }

          timeframeSignals[tf] = evaluateHelperSteps(indicatorsData[tf], currentPrice)
          const signals = timeframeSignals[tf]
          timeframeConvergence.buy[tf] = []
          timeframeConvergence.sell[tf] = []

          // Count colored indicators for this timeframe but skip verbose logging
          let coloredIndicators = 0
          Object.keys(indicatorsData[tf]).forEach(ind => {
            if (indicatorsData[tf][ind] && indicatorsData[tf][ind].color && indicatorsData[tf][ind].color !== '#808080') {
              coloredIndicators++
              // Skip logging individual indicator colors to reduce spam
            }
          })

          // Only log summary on first run
          if (!window._signalLightsInitialized && window.DEBUG_SIGNALS) {
            console.log(`${coloredIndicators} colored indicators for ${tf}`)
          }

          Object.keys(INDICATORS.momentum.concat(INDICATORS.trend)).forEach(ind => {
            if (signals[ind]?.oversold) {
              buyConvergence.push(ind)
              timeframeConvergence.buy[tf].push(ind)
            }
            if (signals[ind]?.overbought) {
              sellConvergence.push(ind)
              timeframeConvergence.sell[tf].push(ind)
            }
          })
        })

        const buyConvergingTimeframes = Object.keys(timeframeConvergence.buy).filter(tf => timeframeConvergence.buy[tf].length >= 2).length
        const sellConvergingTimeframes = Object.keys(timeframeConvergence.sell).filter(tf => timeframeConvergence.sell[tf].length >= 2).length

        const currentSignals = timeframeSignals[currentTf] || { convergenceSteps: { buy: [], sell: [], confidence: 0 } }
        const convergenceSteps = currentSignals.convergenceSteps || { buy: [], sell: [], confidence: 0 }

        // Use our new updateSignalLight function to update each indicator for each timeframe
        // Create a global error silencer that completely suppresses all errors
        if (!window._errorSilencerInstalled) {
          // Store the original console methods
          const originalConsoleError = console.error
          const originalConsoleWarn = console.warn

          // Override console.error to completely silence signal light errors
          console.error = function (...args) {
            // Skip all signal light errors completely
            if (args[0] && typeof args[0] === 'string' &&
                (args[0].includes('Error updating signal light') ||
                 args[0].includes('Silencing future errors'))) {
              // Completely silent - don't log anything
              return
            }

            // Pass through other errors
            originalConsoleError.apply(console, args)
          }

          // Also silence related warnings
          console.warn = function (...args) {
            // Skip circle not found warnings
            if (args[0] && typeof args[0] === 'string' &&
                (args[0].includes('Circle not found') ||
                 args[0].includes('signal-circle'))) {
              return
            }

            // Pass through other warnings
            originalConsoleWarn.apply(console, args)
          }

          // Mark that we've installed the silencer
          window._errorSilencerInstalled = true
        }

        // Define debug flag for logging
        window.DEBUG_MODE = false // Set to true for verbose logging

        function updateSignalLight(ind, tf, recursionCount = 0) {
          try {
            // Prevent excessive recursion
            if (recursionCount > 1) {
              console.warn(`Excessive recursion detected for ${ind} on ${tf}`)
              return false
            }

            // Find the circle element for the given indicator and timeframe
            const circle = document.querySelector(`.signal-circle[data-ind="${ind}"][data-tf="${tf}"]`)

            // If circle doesn't exist but we haven't created one yet, create it now
            if (!circle) {
              // Only report each missing indicator once to avoid log spam
              const key = `${ind}-${tf}`
              if (!reportedMissingIndicators.has(key)) {
                reportedMissingIndicators.add(key)
                console.log(`Creating missing signal light for ${ind} on ${tf}`)
              }

              // Try to find a container for the signal lights
              let container =
                document.getElementById('signalGrid') ||
                document.getElementById('signalMatrix') ||
                document.querySelector('.signal-matrix') ||
                document.querySelector('.oracle-matrix')

              // If no container is found, create a default one
              if (!container) {
                const defaultContainer = document.createElement('div')
                defaultContainer.id = 'signalGrid' // Or use a class if ID conflicts
                defaultContainer.className = 'signal-matrix'
                document.body.appendChild(defaultContainer) // Append to body or a specific parent
                container = defaultContainer // Reuse the new container
                console.log('Created default signal container for missing elements.')
              }

              // If we found a container, create the missing circle
              if (container) {
                const newCircle = document.createElement('div')
                newCircle.className = 'signal-circle grey-light'
                newCircle.dataset.ind = ind
                newCircle.dataset.tf = tf
                container.appendChild(newCircle)

                // Now use the newly created circle for the rest of the function
                // Pass recursion counter to prevent stack overflow
                return updateSignalLight(ind, tf, recursionCount + 1)
              }

              return false // Couldn't create
            }
            // Use getSignalState if available to get the signal state for this indicator/timeframe
            let signalState = null
            if (typeof getSignalState === 'function') {
              signalState = getSignalState(ind, tf, indicatorsData[tf]?.[ind], indicatorsData[tf])
            } else if (indicatorsData[tf] && indicatorsData[tf][ind]) {
              signalState = indicatorsData[tf][ind]
            }
            if (!signalState) {
              circle.className = 'signal-circle disabled'
              circle.setAttribute('data-signal', 'disabled')
              return false
            }
            // Only change on real color changes to avoid flicker
            const prevColor = circle.dataset.currentColor
            const prevTooltip = circle.dataset.currentTooltip

            // Get the signal class from the state
            const signalClass = signalState.signalClass || 'neutral'

            // Map signal class to color class
            let colorClass
            switch (signalClass) {
              case 'degen-buy':
                colorClass = 'green-light'
                break
              case 'mild-buy':
                colorClass = 'blue-light'
                break
              case 'neutral':
                colorClass = 'grey-light'
                break
              case 'mild-sell':
                colorClass = 'orange-light'
                break
              case 'degen-sell':
                colorClass = 'red-light'
                break
              default:
                colorClass = 'grey-light'
            }

            // Update color class when it changes
            if (prevColor !== signalState.color) {
              // First remove all possible color classes
              circle.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light')

              // Remove all possible signal classes
              circle.classList.remove('degen-buy', 'mild-buy', 'neutral', 'mild-sell', 'degen-sell')

              // Add the appropriate color class
              circle.classList.add(colorClass)

              // Also add the signal class for additional styling
              circle.classList.add(signalClass)

              // Set data attributes for reference
              circle.dataset.currentColor = signalState.color
              circle.dataset.signalClass = signalClass

              // Log for debugging
              if (window.DEBUG_MODE) {
                console.log(`Updated signal light ${ind}:${tf} to ${signalClass} (${colorClass})`)
              }
            }
            // Update tooltip when it changes
            if (signalState.tooltip && prevTooltip !== signalState.tooltip) {
              circle.setAttribute('data-tooltip', signalState.tooltip)
              circle.dataset.currentTooltip = signalState.tooltip
            }
            return true
          } catch (e) {
            console.error(`Error updating signal light for ${ind} on ${tf}:`, e)
            return false
          }
        }

        // Count and log all indicators and timeframes for debugging
        let totalIndicators = 0
        const allIndicatorNames = []
        Object.keys(INDICATORS).forEach(type => {
          INDICATORS[type].forEach(ind => {
            allIndicatorNames.push(ind)
            totalIndicators++
          })
        })
        console.log(`Total indicators: ${totalIndicators}, Timeframes: ${activeTimeframes.length}`)
        console.log(`Expected total lights: ${totalIndicators * activeTimeframes.length}`)
        console.log(`Indicator names: ${allIndicatorNames.join(', ')}`)

        // Reset counters
        updatedLights = 0
        failedLights = 0

        // Force creation of all needed circles first
        Object.keys(INDICATORS).forEach(type => {
          INDICATORS[type].forEach(ind => {
            activeTimeframes.forEach(tf => {
              // Create any missing circles
              const circle = document.querySelector(`.signal-circle[data-ind="${ind}"][data-tf="${tf}"]`)
              if (!circle) {
                let container =
                  document.getElementById('signalGrid') ||
                  document.getElementById('signalMatrix') ||
                  document.querySelector('.signal-matrix') ||
                  document.querySelector('.oracle-matrix')

                // If no container is found, create a default one
                if (!container) {
                  const defaultContainer = document.createElement('div')
                  defaultContainer.id = 'signalGrid' // Or use a class if ID conflicts
                  defaultContainer.className = 'signal-matrix'
                  document.body.appendChild(defaultContainer) // Append to body or a specific parent
                  container = defaultContainer // Reuse the new container
                  console.log('Created default signal container for missing elements.')
                }

                if (container) {
                  const newCircle = document.createElement('div')
                  newCircle.className = 'signal-circle grey-light'
                  newCircle.dataset.ind = ind
                  newCircle.dataset.tf = tf
                  container.appendChild(newCircle)
                  console.log(`Pre-created signal light for ${ind} on ${tf}`)
                }
              }
            })
          })
        })

        // Now update all circles
        Object.keys(INDICATORS).forEach(type => {
          INDICATORS[type].forEach(ind => {
            activeTimeframes.forEach(tf => {
              try {
                // Get the circle (which should now exist)
                const circle = document.querySelector(`.signal-circle[data-ind="${ind}"][data-tf="${tf}"]`)
                if (!circle) {
                  failedLights++
                  console.error(`Circle not found for indicator ${ind} in timeframe ${tf}`)
                  return // Skip if circle doesn't exist
                }

                // Use getSignalState if available
                let signalState = null
                if (typeof getSignalState === 'function') {
                  signalState = getSignalState(ind, tf, indicatorsData[tf]?.[ind], indicatorsData[tf])
                } else if (indicatorsData[tf] && indicatorsData[tf][ind]) {
                  signalState = indicatorsData[tf][ind]
                }

                if (!signalState) {
                  failedLights++
                  return
                }

                // Update color class when it changes
                const prevColor = circle.dataset.currentColor
                const prevTooltip = circle.dataset.currentTooltip

                // Ensure we apply the correct color class based on the color value
                if (prevColor !== signalState.color) {
                  // Remove all possible color classes first
                  circle.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light')

                  // Determine the correct class based on the color
                  let colorClass = 'grey-light' // Default

                  if (signalState.color === '#00FF00') {
                    colorClass = 'green-light'
                  } else if (signalState.color === '#0000FF') {
                    colorClass = 'blue-light'
                  } else if (signalState.color === '#FFA500') {
                    colorClass = 'orange-light'
                  } else if (signalState.color === '#FF0000') {
                    colorClass = 'red-light'
                  }

                  // Apply the color class
                  if (circle) {
                    circle.classList.add(colorClass)
                    circle.dataset.currentColor = signalState.color

                    // Log successful color update for debugging
                    if (window.DEBUG_MODE) {
                      console.log(`Updated ${ind} on ${tf} to ${colorClass} (${signalState.color})`)
                    }
                  } else {
                    console.error(`Signal circle for indicator ${ind} not found in timeframe ${tf}`)
                  }
                }

                // Update tooltip when it changes
                if (signalState.tooltip && prevTooltip !== signalState.tooltip) {
                  if (circle) {
                    circle.setAttribute('data-tooltip', signalState.tooltip)
                    circle.dataset.currentTooltip = signalState.tooltip
                  } else {
                    console.error(`Signal circle for indicator ${ind} not found in timeframe ${tf}`)
                  }
                }

                updatedLights++
              } catch (e) {
                console.error(`Error updating signal light for ${ind} on ${tf}:`, e)
                failedLights++
              }

              // Get the circle element again for animations
              const circle = document.querySelector(`.signal-circle[data-ind="${ind}"][data-tf="${tf}"]`)
              if (!circle) return

              // Add convergence animations if needed
              const signals = timeframeSignals[tf] || {}
              const signal = signals[ind] || { color: '#808080', overbought: false, oversold: false, value: 'N/A' }

              const convergenceList = signal.oversold ? buyConvergence : signal.overbought ? sellConvergence : []
              if (convergenceList.length >= 2 && convergenceList.includes(ind)) {
                const indIndex = helperOrder.indexOf(ind)
                const pulseDelay = indIndex * 200
                circle.style.animationDelay = `${pulseDelay}ms`
                if (convergenceList.length >= 4) {
                  circle.classList.add('ordered-pulse-2')
                } else {
                  circle.classList.add('ordered-pulse')
                }
              }

              // Add chevron animations based on convergence only if directional arrows are enabled
              if (window.showDirectionalArrows) {
                if (signal.color === '#00FF00' || signal.color === '#0000FF') {
                  if (buyConvergingTimeframes >= 2) {
                    // Add chevron animation for buy signals
                    circle.classList.add('chevron-animation')
                    circle.classList.add('chevron-up')

                    // Apply the animation with a slight delay based on the indicator position
                    const indIndex = helperOrder.indexOf(ind)
                    const animDelay = (indIndex % 3) * 0.3 // Stagger the animations
                    circle.style.animationDelay = `${animDelay}s`

                    if (buyConvergingTimeframes >= 3) {
                      // Add additional effects for stronger convergence
                      circle.classList.add('multi-convergence')
                    }
                  }
                } else if (signal.color === '#FF0000' || signal.color === '#FFA500') {
                  if (sellConvergingTimeframes >= 2) {
                    // Add chevron animation for sell signals
                    circle.classList.add('chevron-animation')
                    circle.classList.add('chevron-down')

                    // Apply the animation with a slight delay based on the indicator position
                    const indIndex = helperOrder.indexOf(ind)
                    const animDelay = (indIndex % 3) * 0.3 // Stagger the animations
                    circle.style.animationDelay = `${animDelay}s`

                    if (sellConvergingTimeframes >= 3) {
                      // Add additional effects for stronger convergence
                      circle.classList.add('multi-convergence')
                    }
                  }
                }
              } else {
                // Remove any existing chevron animations if directional arrows are disabled
                circle.classList.remove('chevron-animation', 'chevron-up', 'chevron-down', 'multi-convergence')
              }
            })
          })
        })

        // Calculate visible indicators (those not skipped)
        const visibleIndicators = totalIndicators
        const expectedVisibleLights = visibleIndicators * activeTimeframes.length

        // Only log on first successful initialization
        if (!window._signalLightsInitialized && updatedLights > 0) {
          logMessages.push(`[${new Date().toLocaleString()}] Signal lights initialized successfully`)
          updateLogger()
          window._signalLightsInitialized = true
        }

        // Reset all flags
        isInitializing = false
        window._isUpdatingSignalLights = false

        // Initialize the global showDotMatrix variable if it doesn't exist
        if (window.showDotMatrix === undefined) {
          window.showDotMatrix = lightLogicSettings.enableDotMatrix || false
          logMessages.push(`[${new Date().toLocaleString()}] Initialized showDotMatrix: ${window.showDotMatrix}`)
          updateLogger()
        }

        // Check for convergence patterns and trigger dot matrix animations if enabled
        if (convergenceSteps.buy.length >= 3 && convergenceSteps.sell.length >= 3) {
          const buyStrength = convergenceSteps.buy.length * (signalLogicSettings.strongSignalWeight || 2)
          const sellStrength = convergenceSteps.sell.length * (signalLogicSettings.strongSignalWeight || 2)
          if (buyStrength > sellStrength) {
            displayDotMatrix('rocket')
          } else if (sellStrength > buyStrength) {
            displayDotMatrix('missile')
          }
        } else if (convergenceSteps.buy.length >= 3) {
          displayDotMatrix('rocket')
        } else if (convergenceSteps.sell.length >= 3) {
          displayDotMatrix('missile')
        }

        // Count green and red signals for additional dot matrix triggers
        let greenCount = 0
        let redCount = 0

        Object.keys(indicatorsData).forEach(timeframe => {
          if (indicatorsData[timeframe]) {
            Object.keys(indicatorsData[timeframe]).forEach(indicator => {
              if (indicator !== 'volume' && indicator !== 'metadata' && indicator !== 'convergenceSteps' && indicatorsData[timeframe][indicator]) {
                const data = indicatorsData[timeframe][indicator]
                if (data && data.color) {
                  // Count green and red signals for dot matrix triggers
                  if (data.color === '#00FF00') {
                    greenCount++
                  } else if (data.color === '#FF0000') {
                    redCount++
                  }
                }
              }
            })
          }
        })

        // Check for dot matrix animation triggers based on total signal counts
        // We have 7 timeframes and about 16 indicators = ~112 total signals
        // Trigger at 30% of signals (about 33 signals)
        const triggerThreshold = 33

        if (greenCount >= triggerThreshold) {
          // Trigger rocket animation for strong bullish signals
          if (typeof displayDotMatrix === 'function') {
            displayDotMatrix('rocket')
          }
        } else if (redCount >= triggerThreshold) {
          // Trigger missile animation for strong bearish signals
          if (typeof displayDotMatrix === 'function') {
            displayDotMatrix('missile')
          }
        }

        console.log('Signal lights updated successfully')
      } catch (e) {
        console.error('Error in updateAllSignalLights animation frame:', e ? e.message || 'No error message' : 'No error object')
        logMessages.push(`[${new Date().toLocaleString()}] Signal lights update error in animation frame: ${e ? e.message || 'No error message' : 'No error object'}`)
        updateLogger()

        // Reset flags even if there's an error in the animation frame
        isInitializing = false
        window._isUpdatingSignalLights = false
      }
    })
  } catch (e) {
    console.error('Error updating signal lights:', e)
    logMessages.push(`[${new Date().toLocaleString()}] Error in updateAllSignalLights: ${e.message}`)
    updateLogger()

    // Reset flags even if there's an error
    isInitializing = false
    window._isUpdatingSignalLights = false
  }
}

function updateSignalLightState(circle, ind, tf, indicatorData) {
  if (!circle) return false

  try {
    // Use getSignalState if available to get the signal state for this indicator/timeframe
    let signalState = null
    if (typeof getSignalState === 'function') {
      signalState = getSignalState(ind, tf, indicatorData, indicatorsData[tf])
    } else if (indicatorData) {
      signalState = indicatorData
    }

    if (!signalState) {
      circle.className = 'signal-circle disabled'
      circle.setAttribute('data-signal', 'disabled')
      return false
    }

    // Update circle based on signal state
    const signalValue = signalState.signal || signalState.value
    const signalClass = signalState.signalClass ||
                      (signalValue > 0 ? 'signal-bullish' :
                        signalValue < 0 ? 'signal-bearish' : 'signal-neutral')

    // Update circle classes
    circle.className = `signal-circle ${signalClass}`
    circle.setAttribute('data-signal', signalClass.replace('signal-', ''))

    // Update tooltip
    const value = typeof signalState.value === 'number' ? signalState.value.toFixed(2) : signalState.value
    circle.title = `${ind} (${tf}): ${value || 'N/A'}`

    return true
  } catch (error) {
    console.error(`Error updating signal light for ${ind} on ${tf}:`, error)
    circle.className = 'signal-circle error'
    circle.title = `${ind} (${tf}): Error`
    return false
  }
}

// Make the functions available for global use
window.updateAllSignalLights = updateAllSignalLights
window._updateSignalLightsCore = _updateSignalLightsCore
window.updateSignalLightState = updateSignalLightState

// Add new function to render mini charts and signal lights for oracle matrix rows
function renderOracleMatrixIndicators() {
  try {
    if (typeof window.currentTf === 'undefined') {
      if (window.DEBUG_MODE) console.error('currentTf is undefined. Cannot render indicators.')
      return // Exit if currentTf not defined
    }
    const matrixContainer = document.querySelector('.oracle-matrix')
    if (!matrixContainer) {
      if (window.DEBUG_MODE) console.error('Oracle matrix container not found.')
      return
    }
    const rows = document.querySelectorAll('.oracle-matrix .signal-row')
    rows.forEach((row, index) => {
      const ind = row.getAttribute('data-indicator')
      if (typeof ind !== 'string') {
        if (window.DEBUG_MODE) console.error('Indicator attribute is not a string, skipping.')
        return
      }
      // Handle mini chart
      let chartCanvas = row.querySelector(`canvas[data-indicator='${ind}']`)
      if (!chartCanvas) {
        chartCanvas = document.createElement('canvas')
        chartCanvas.setAttribute('data-indicator', ind)
        row.appendChild(chartCanvas)
      }
      const ctx = chartCanvas.getContext('2d')
      let chartData = window.indicatorsData?.[window.currentTf]?.[ind] || { data: [], labels: [] }
      if (!Array.isArray(chartData.data) || !Array.isArray(chartData.labels)) {
        if (window.DEBUG_MODE) console.error(`Invalid chart data for indicator ${ind}: data or labels not an array. Setting defaults. `)
        chartData = { data: [], labels: [] }
      } else if (chartData.data.some(item => item == null || typeof item !== 'number' && isNaN(Number(item))) || chartData.labels.some(label => label == null || typeof label !== 'string')) {
        if (window.DEBUG_MODE) console.error(`Invalid data in chart for indicator ${ind}: sanitizing null or invalid values.`)
        chartData.data = (chartData.data || []).map(item => {
          const num = Number(item)
          return isNaN(num) ? 0 : num // Coerce and handle NaN or null
        }).filter(item => typeof item === 'number')
        chartData.labels = (chartData.labels || []).map(label => String(label) || '').filter(label => typeof label === 'string')
        if (window.DEBUG_MODE) console.log(`Sanitized chart data for ${ind}: labels length=${chartData.labels.length}, data length=${chartData.data.length}, sample data=${JSON.stringify(chartData.data.slice(0, 5))}`)
      }
      let chartInstance = chartCanvas.chart
      if (chartInstance) {
        try {
          chartInstance.data.labels = chartData.labels
          chartInstance.data.datasets[0].data = chartData.data
          chartInstance.update()
        } catch (updateError) {
          if (window.DEBUG_MODE) console.error(`Chart update error for indicator ${ind}: ${updateError.message}, data state=${JSON.stringify(chartData)}`)
        }
      } else {
        try {
          chartInstance = new Chart(ctx, {
            type: 'line',
            data: {
              labels: chartData.labels,
              datasets: [{
                label: ind,
                data: chartData.data,
                borderColor: 'blue',
                fill: false,
              }],
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: { y: { beginAtZero: true } },
              plugins: { legend: { display: false } },
              onHover: (event, elements) => {
                try {
                  if (elements.length > 0) {
                    event.native.target.style.cursor = 'pointer'
                  } else {
                    event.native.target.style.cursor = 'default'
                  }
                } catch (hoverError) {
                  if (window.DEBUG_MODE) console.error(`Hover error for indicator ${ind}: ${hoverError.message}, event details=${JSON.stringify(event)}`)
                }
              },
            },
          })
          chartCanvas.chart = chartInstance
        } catch (createError) {
          if (window.DEBUG_MODE) console.error(`Chart creation error for indicator ${ind}: ${createError.message}, data state=${JSON.stringify(chartData)}`)
        }
      }
      // Restore and handle signal light
      let signalLight = row.querySelector(`.signal-light[data-indicator='${ind}']`)
      if (!signalLight) {
        signalLight = document.createElement('div')
        signalLight.className = 'signal-light neutral'
        signalLight.setAttribute('data-indicator', ind)
        row.appendChild(signalLight)
      }
      const signalState = window.indicatorsData?.[window.currentTf]?.[ind]?.signalClass || 'neutral'
      signalLight.className = `signal-light ${signalState}`
    })
  } catch (generalError) {
    if (window.DEBUG_MODE) console.error(`Error in renderOracleMatrixIndicators: ${generalError.message}`)
  }
}
