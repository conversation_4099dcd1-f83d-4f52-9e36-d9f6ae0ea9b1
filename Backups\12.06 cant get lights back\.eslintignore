# Dependencies
node_modules/

# Build outputs
dist/
build/
coverage/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files
**/*.test.js
**/*.spec.js

# Temporary files
*.tmp
*.bak
*.swp
*~

# Local development
.vercel/
.netlify/

# Cache
.cache/

# Backup files
*.bak
*.backup

# IDE specific files
.idea/
.vscode/
*.sublime-workspace
*.sublime-project

# Local development
.local/

# Ignore minified files
*.min.js

# Ignore bundle files
*.bundle.js

# Ignore coverage directory
coverage/

# Ignore build directory
build/

# Ignore dist directory
dist/

# Ignore docs
docs/

# Ignore cache
.cache/

# Ignore temp directories
tmp/
temp/

# Ignore config files
*.config.js
*.config.json

# Ignore service worker files
sw.js
workbox-*.js

# Ignore webpack bundle analyzer reports
report.html

# Ignore package-lock and yarn.lock
package-lock.json
yarn.lock

# Ignore all dotfiles except .eslintrc.js and .prettierrc
.*
!.eslintrc.json
!.prettierrc
!.gitignore

# Ignore all files in the following directories
**/node_modules/**
**/bower_components/**
**/vendor/**
**/dist/**
**/build/**
**/coverage/**
**/temp/**
**/tmp/**
**/logs/**
**/public/**
**/static/**
**/docs/**
**/.cache/**
**/.next/**
**/out/**
**/.vercel/**
**/.netlify/**
**/.github/**
**/.vscode/**
**/.idea/**
**/__mocks__/**
**/__snapshots__/**
**/cypress/**
**/test/**
**/tests/**
**/spec/**
**/specs/**
