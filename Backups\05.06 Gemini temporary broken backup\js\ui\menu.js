      }
    }
  }

  showMenu(menuId, button) {
    const menu = document.getElementById(menuId);
    if (!menu) return;

    // Position the menu below the button
    const rect = button.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const menuHeight = menu.offsetHeight || 300; // Default height if not rendered yet
    
    // Position below or above button based on available space
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    
    if (spaceBelow > menuHeight || spaceBelow > spaceAbove) {
      // Position below button
      menu.style.top = `${rect.bottom + window.scrollY}px`;
      menu.style.transformOrigin = 'top center';
    } else {
      // Position above button
      menu.style.bottom = `${viewportHeight - rect.top + window.scrollY}px`;
      menu.style.transformOrigin = 'bottom center';
    }
    
    // Center horizontally
    const left = rect.left + (rect.width / 2) - (menu.offsetWidth / 2);
    menu.style.left = `${Math.max(10, Math.min(left, window.innerWidth - menu.offsetWidth - 10))}px`;
    
    // Show menu with animation
    menu.classList.add('show');
    button.classList.add('active');
    this.activeMenu = menuId;
    
    // Add active state to current selection
    this.updateActiveStates(menuId);
  }

  hideMenu(menuId) {
    const menu = document.getElementById(menuId);
    if (!menu) return;
    
    // Add closing animation class
    menu.classList.add('closing');
    
    // Wait for animation to complete before hiding
    setTimeout(() => {
      menu.classList.remove('show', 'closing');
      const button = document.querySelector(`.menu-button[aria-controls="${menuId}"]`);
      if (button) button.classList.remove('active');
      
      if (this.activeMenu === menuId) {
        this.activeMenu = null;
      }
    }, 150); // Match this with CSS transition duration
  }

  hideAllMenus() {
    this.menus.forEach(({ menu }) => {
      menu.classList.remove('show');
    });
    
    document.querySelectorAll('.menu-button.active').forEach(btn => {
      btn.classList.remove('active');
    });
    
    this.activeMenu = null;
  }

  // Initialize existing menus in the DOM
  initializeMenus() {
    document.querySelectorAll('[data-menu]').forEach(button => {
      const menuId = button.getAttribute('data-menu');
      const menu = document.getElementById(menuId);
      
      if (menu) {
        menu.classList.add('menu-content');
        button.classList.add('menu-button');
        button.setAttribute('aria-controls', menuId);
        
        button.addEventListener('click', (e) => {
          e.stopPropagation();
          this.toggleMenu(menuId, button);
        });
        
        this.menus.set(menuId, { button, menu });
      }
    });
  }
}

// Initialize menu system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.menuSystem = new MenuSystem();
});
