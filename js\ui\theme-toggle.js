/**
 * Theme Toggle Component
 * Provides a UI for switching between different themes with smooth animations
 */
class ThemeToggle {
  constructor() {
    // Reference to the ThemeManager
    this.themeManager = window.ThemeManager

    // DOM elements
    this.themeToggle = null
    this.themeIcon = null
    this.themeText = null

    // State
    this.initialized = false
    this.isAnimating = false

    // Theme configuration
    this.themeConfig = {
      dark: { icon: '🌙', label: 'Dark' },
      light: { icon: '☀️', label: 'Light' },
      cosmic: { icon: '🌌', label: 'Cosmic' },
      matrix: { icon: '🟢', label: 'Matrix' },
      default: { icon: '🎨', label: 'Theme' },
    }

    // Bind methods
    this.init = this.init.bind(this)
    this.toggleTheme = this.toggleTheme.bind(this)
    this.updateThemeIcon = this.updateThemeIcon.bind(this)
    this.handleKeyDown = this.handleKeyDown.bind(this)
    this.animateButton = this.animateButton.bind(this)
  }

  /**
     * Initialize the theme toggle
     */
  /**
     * Initialize the theme toggle
     */
  init() {
    if (this.initialized) return

    try {
      // Create theme toggle button
      this.themeToggle = document.createElement('button')
      this.themeToggle.id = 'theme-toggle'
      this.themeToggle.className = 'theme-toggle'
      this.themeToggle.setAttribute('aria-label', 'Toggle theme')
      this.themeToggle.setAttribute('role', 'button')
      this.themeToggle.setAttribute('tabindex', '0')
      this.themeToggle.setAttribute('aria-pressed', 'false')

      // Create icon and text elements
      this.themeToggle.innerHTML = `
                <span class="theme-icon" aria-hidden="true">${this.themeConfig.default.icon}</span>
                <span class="theme-text">${this.themeConfig.default.label}</span>
                <span class="sr-only">Toggle theme</span>
            `

      // Store references to DOM elements
      this.themeIcon = this.themeToggle.querySelector('.theme-icon')
      this.themeText = this.themeToggle.querySelector('.theme-text')

      // Set initial tooltip
      this.updateTooltip()

      // Add event listeners
      this.themeToggle.addEventListener('click', this.toggleTheme)
      this.themeToggle.addEventListener('keydown', (e) => {
        // Handle space and enter keys for accessibility
        if (e.key === ' ' || e.key === 'Enter') {
          e.preventDefault()
          this.toggleTheme()
        }
      })

      // Add keyboard shortcut (Alt+T)
      document.addEventListener('keydown', this.handleKeyDown)

      // Add to the theme toggle container if it exists, otherwise find a suitable location
      const container = document.getElementById('themeToggleContainer') ||
                             document.querySelector('header') ||
                             document.body

      // Use requestAnimationFrame to ensure smooth insertion
      requestAnimationFrame(() => {
        container.appendChild(this.themeToggle)

        // Add transition class after a small delay to prevent initial animation
        setTimeout(() => {
          this.themeToggle.classList.add('theme-toggle--ready')
        }, 50)
      })

      // Listen for theme changes
      this.themeManager.onThemeChange((theme) => {
        this.updateThemeIcon(theme)
        this.animateButton()
      })

      // Initialize with current theme
      if (this.themeManager.currentTheme) {
        this.updateThemeIcon(this.themeManager.currentTheme)
      } else {
        // Fallback to system preference if available
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        this.updateThemeIcon(prefersDark ? 'dark' : 'light')
      }

      this.initialized = true
      if (window.DEBUG) {
        console.log('[ThemeToggle] Initialized')
      }
    } catch (error) {
      console.error('[ThemeToggle] Error initializing theme toggle:', error)
    }
  }

  /**
     * Handle keyboard shortcut (Alt+T)
     */
  handleKeyDown(e) {
    if (e.altKey && e.key.toLowerCase() === 't') {
      e.preventDefault()
      this.toggleTheme()
    }
  }

  /**
     * Toggle between available themes with animation
     */
  toggleTheme() {
    if (this.isAnimating) return

    if (!this.themeManager) {
      console.warn('[ThemeToggle] ThemeManager not available')
      return
    }

    // Get current theme and available themes
    const currentTheme = this.themeManager.currentTheme || 'dark'
    const availableThemes = this.themeManager.getAvailableThemes?.() || ['light', 'dark']

    // Find next theme in the list (or first if at the end)
    const currentIndex = availableThemes.indexOf(currentTheme)
    const nextIndex = (currentIndex + 1) % availableThemes.length
    const nextTheme = availableThemes[nextIndex]

    // Apply the theme
    this.themeManager.setTheme(nextTheme)

    // Update ARIA attributes
    this.themeToggle.setAttribute('aria-pressed', nextTheme === 'dark' ? 'true' : 'false')

    // Animate the button
    this.animateButton()

    // Dispatch custom event
    const event = new CustomEvent('themeToggle', {
      detail: { theme: nextTheme },
    })
    this.themeToggle.dispatchEvent(event)
  }

  /**
     * Update the theme icon and text based on the current theme
     * @param {string} themeName - The name of the current theme
     */
  updateThemeIcon(themeName) {
    if (!this.themeToggle || !this.themeIcon || !this.themeText) return

    // Get theme configuration or use defaults
    const theme = this.themeConfig[themeName] || this.themeConfig.default

    // Update icon and text with animation
    this.themeIcon.textContent = theme.icon
    this.themeText.textContent = theme.label

    // Update tooltip and ARIA attributes
    this.updateTooltip(themeName)

    // Update data-theme attribute for styling
    this.themeToggle.setAttribute('data-theme', themeName)

    // Add transition class for smooth changes
    this.themeToggle.classList.add('theme-transition')

    // Remove transition class after animation completes
    setTimeout(() => {
      this.themeToggle.classList.remove('theme-transition')
    }, 500)
  }

  /**
     * Update the tooltip and ARIA attributes
     * @param {string} themeName - The name of the current theme
     */
  updateTooltip(themeName = this.themeManager?.currentTheme || 'dark') {
    if (!this.themeToggle) return

    const theme = this.themeConfig[themeName] || this.themeConfig.default
    const tooltipText = `Toggle theme (Alt+T) - ${theme.label}`

    // Update tooltip and ARIA attributes
    this.themeToggle.setAttribute('title', tooltipText)
    this.themeToggle.setAttribute('data-tooltip', tooltipText)
    this.themeToggle.setAttribute('aria-label', `Toggle theme. Current theme: ${theme.label}`)
  }

  /**
     * Animate the button when theme changes
     */
  animateButton() {
    if (this.isAnimating) return

    this.isAnimating = true
    this.themeToggle.classList.add('animate')

    // Remove animation class after it completes
    setTimeout(() => {
      this.themeToggle.classList.remove('animate')
      this.isAnimating = false
    }, 500)
  }

  /**
     * Clean up event listeners and DOM elements
     */
  cleanup() {
    // Remove event listeners
    if (this.themeToggle) {
      this.themeToggle.removeEventListener('click', this.toggleTheme)
      this.themeToggle.removeEventListener('keydown', this.handleKeyDown)

      // Remove from DOM with animation
      this.themeToggle.style.opacity = '0'
      this.themeToggle.style.transform = 'scale(0.8)'

      // Remove after animation completes
      setTimeout(() => {
        if (this.themeToggle && this.themeToggle.parentNode) {
          this.themeToggle.parentNode.removeChild(this.themeToggle)
        }
      }, 200)
    }

    // Remove document event listeners
    document.removeEventListener('keydown', this.handleKeyDown)

    // Reset state
    this.initialized = false
    this.themeToggle = null
    this.themeIcon = null
    this.themeText = null
  }
}

// Initialize the theme toggle when the DOM is ready
function initializeThemeToggle() {
  if (!window.ThemeManager) {
    console.warn('[ThemeToggle] ThemeManager not available. Waiting for it to load...')
    setTimeout(initializeThemeToggle, 100)
    return
  }

  window.themeToggle = new ThemeToggle()
  window.themeToggle.init()
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeThemeToggle)
} else {
  initializeThemeToggle()
}

// Clean up on page unload
window.addEventListener('beforeunload', () => {
  if (window.themeToggle && typeof window.themeToggle.cleanup === 'function') {
    window.themeToggle.cleanup()
  }
})
