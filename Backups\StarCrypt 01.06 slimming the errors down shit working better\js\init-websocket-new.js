/**
 * WebSocket Initialization for StarCrypt
 * Uses the new WebSocketProcessor for reliable message handling
 */

// Immediately-invoked function expression to avoid polluting global scope
(function () {
  'use strict'

  // Configuration
  const config = {
    // WebSocket configuration
    ws: {
      reconnectDelay: 1000, // Initial delay between reconnection attempts (ms)
      maxReconnectDelay: 30000, // Maximum delay between reconnection attempts (ms)
      reconnectDecay: 1.5, // Rate of increase of the reconnect delay
      maxReconnectAttempts: 10, // Maximum number of reconnection attempts (0 for unlimited)
      timeout: 10000, // Connection timeout (ms)
      heartbeatInterval: 30000, // Heartbeat interval (ms)
      debug: true, // Enable debug logging
    },

    // WebSocketProcessor configuration
    processor: {
      maxBatchSize: 5, // Process up to 5 messages at once
      maxQueueSize: 1000, // Maximum number of messages to queue
      processDelay: 10, // Delay between processing batches (ms)
      maxDepth: 5, // Maximum recursion depth for message processing
      maxConsecutiveErrors: 10, // Maximum errors before pausing
      errorResetTime: 60000, // Time to wait before resetting error counter (ms)
    },
  }

  // Global references
  let wsManager = null
  let wsProcessor = null
  let reconnectAttempts = 0
  let reconnectTimer = null
  let heartbeatTimer = null
  let isPageVisible = true

  /**
     * Initialize WebSocket connection and message processing
     */
  function init() {
    // Initialize WebSocket processor with V2
    wsProcessor = new WebSocketProcessorV2({
      maxBatchSize: 10,
      maxQueueSize: 1000,
      processDelay: 10,
      maxDepth: 5,
      maxConsecutiveErrors: 10,
      errorResetTime: 60000,
    })

    try {
      // Ensure WebSocketManager is available
      if (typeof WebSocketManager === 'undefined') {
        throw new Error('WebSocketManager is not defined. Make sure websocket-manager.js is loaded before this script.')
      }

      // Set up WebSocket manager with error handling
      wsManager = new WebSocketManager({
        url: getWebSocketUrl(),
        reconnect: true,
        reconnectInterval: config.ws.reconnectDelay,
        maxReconnectAttempts: config.ws.maxReconnectAttempts,
        maxReconnectInterval: config.ws.maxReconnectDelay,
        reconnectDecay: config.ws.reconnectDecay,
        timeout: config.ws.timeout,
        debug: config.ws.debug,
      })

      console.log('WebSocketManager initialized successfully')
    } catch (error) {
      console.error('Failed to initialize WebSocketManager:', error)
      // Try to recover by reloading the page after a delay
      setTimeout(() => {
        console.log('Attempting to recover by reloading the page...')
        window.location.reload()
      }, 5000)
    }

    // Set up event handlers
    setupEventHandlers()

    // Set up page visibility change handler
    setupPageVisibilityHandler()

    // Set up message handlers
    setupMessageHandlers()

    // Start heartbeat
    startHeartbeat()

    // Initial connection
    connect()

    // Log initialization
    log('WebSocket initialization complete')
  }

  /**
     * Set up WebSocket event handlers
     */
  function setupEventHandlers() {
    if (!wsManager) return

    // Connection opened
    wsManager.on('open', () => {
      log('WebSocket connected')
      reconnectAttempts = 0

      // Subscribe to channels
      subscribeToChannels()

      // Restart heartbeat
      startHeartbeat()
    })

    // Connection closed
    wsManager.on('close', (event) => {
      log(`WebSocket disconnected: ${event.code} ${event.reason || 'No reason provided'}`)
      stopHeartbeat()

      // Clear any pending messages
      if (wsProcessor) {
        wsProcessor.pauseProcessing()
      }

      // Attempt to reconnect if page is visible
      if (isPageVisible) {
        scheduleReconnect()
      }
    })

    // Connection error
    wsManager.on('error', (error) => {
      console.error('WebSocket error:', error)

      // Schedule reconnection on error
      if (isPageVisible) {
        scheduleReconnect()
      }
    })

    // Message received
    wsManager.on('message', (message) => {
      try {
        // Parse message if it's a string
        const parsedMessage = typeof message === 'string' ? JSON.parse(message) : message

        // Add timestamp and queue for processing
        if (wsProcessor) {
          wsProcessor.queueMessage({
            ...parsedMessage,
            _receivedAt: Date.now(),
          })
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error, message)
      }
    })
  }

  /**
     * Set up message handlers for different message types
     */
  function setupMessageHandlers() {
    if (!wsProcessor) return

    // Price update handler
    wsProcessor.addMessageHandler('price', (message) => {
      dispatchEvent('priceUpdate', message)
    })

    // Signal update handler
    wsProcessor.addMessageHandler('signal', (message) => {
      dispatchEvent('signalUpdate', message)
    })

    // Indicator update handler
    wsProcessor.addMessageHandler('indicator', (message) => {
      dispatchEvent('indicatorUpdate', message)
    })

    // Heartbeat handler
    wsProcessor.addMessageHandler('heartbeat', (message) => {
      dispatchEvent('heartbeat', message)

      // Reset reconnect attempts on successful heartbeat
      reconnectAttempts = 0

      // Send pong response
      if (wsManager && wsManager.isConnected()) {
        wsManager.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }))
      }
    })

    // Error handler
    wsProcessor.addMessageHandler('error', (message) => {
      console.error('Server error:', message)
      dispatchEvent('websocketError', message)
    })
  }

  /**
     * Subscribe to WebSocket channels
     */
  function subscribeToChannels() {
    if (!wsManager || !wsManager.isConnected()) return

    // Subscribe to price updates
    wsManager.send(JSON.stringify({
      type: 'subscribe',
      channels: ['prices', 'signals', 'indicators'],
    }))

    log('Subscribed to WebSocket channels')
  }

  /**
     * Start the WebSocket connection
     */
  function connect() {
    if (!wsManager) return

    try {
      log('Connecting to WebSocket...')
      wsManager.connect()
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error)
      scheduleReconnect()
    }
  }

  /**
     * Schedule a reconnection attempt
     */
  function scheduleReconnect() {
    // Clear any existing timer
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    // Don't attempt to reconnect if we've exceeded max attempts
    if (config.ws.maxReconnectAttempts > 0 && reconnectAttempts >= config.ws.maxReconnectAttempts) {
      log('Max reconnection attempts reached')
      return
    }

    // Calculate delay with exponential backoff
    const delay = Math.min(
      config.ws.reconnectDelay * Math.pow(config.ws.reconnectDecay, reconnectAttempts),
      config.ws.maxReconnectDelay,
    )

    reconnectAttempts++
    log(`Reconnecting in ${Math.round(delay / 1000)} seconds (attempt ${reconnectAttempts})`)

    // Schedule reconnection
    reconnectTimer = setTimeout(() => {
      if (isPageVisible) {
        connect()
      }
    }, delay)
  }

  /**
     * Start the heartbeat timer
     */
  function startHeartbeat() {
    stopHeartbeat()

    heartbeatTimer = setInterval(() => {
      if (wsManager && wsManager.isConnected()) {
        wsManager.send(JSON.stringify({
          type: 'ping',
          timestamp: Date.now(),
        }))
      }
    }, config.ws.heartbeatInterval)
  }

  /**
     * Stop the heartbeat timer
     */
  function stopHeartbeat() {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  /**
     * Set up page visibility change handler
     */
  function setupPageVisibilityHandler() {
    // Handle page visibility changes
    const handleVisibilityChange = () => {
      isPageVisible = !document.hidden

      if (isPageVisible) {
        log('Page is now visible, reconnecting WebSocket...')

        // Clear any pending reconnect timers
        if (reconnectTimer) {
          clearTimeout(reconnectTimer)
          reconnectTimer = null
        }

        // Reconnect if needed
        if (!wsManager || !wsManager.isConnected()) {
          connect()
        } else if (wsProcessor) {
          // Resume processing if paused
          wsProcessor.resumeProcessing()
        }
      } else {
        log('Page is now hidden, pausing WebSocket...')

        // Pause processing when page is hidden
        if (wsProcessor) {
          wsProcessor.pauseProcessing()
        }
      }
    }

    // Set up event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange, false)

    // Also handle page unload
    window.addEventListener('beforeunload', cleanup)
  }

  /**
     * Clean up resources
     */
  function cleanup() {
    log('Cleaning up WebSocket resources...')

    // Clear timers
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    stopHeartbeat()

    // Clean up WebSocket manager
    if (wsManager) {
      wsManager.disconnect()
      wsManager = null
    }

    // Clean up processor
    if (wsProcessor) {
      wsProcessor.cleanup()
      wsProcessor = null
    }

    // Remove event listeners
    document.removeEventListener('visibilitychange', handleVisibilityChange, false)
    window.removeEventListener('beforeunload', cleanup)
  }

  /**
     * Dispatch a custom event
     * @param {string} eventName - Event name
     * @param {Object} detail - Event detail object
     */
  function dispatchEvent(eventName, detail = {}) {
    try {
      const event = new CustomEvent(eventName, {
        bubbles: true,
        cancelable: true,
        detail,
      })

      document.dispatchEvent(event)
    } catch (error) {
      console.error(`Error dispatching event ${eventName}:`, error)
    }
  }

  /**
     * Get WebSocket URL based on current host
     * @returns {string} WebSocket URL
     */
  function getWebSocketUrl() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    return `${protocol}//${host}/ws` // Adjust path as needed
  }

  /**
     * Log a debug message
     * @param {string} message - Message to log
     */
  function log(message) {
    if (config.ws.debug) {
      console.debug(`[WebSocket] ${message}`)
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init)
  } else {
    // DOM already loaded, initialize immediately
    setTimeout(init, 0)
  }

  // Export for debugging
  if (typeof window !== 'undefined') {
    window.__wsDebug = {
      wsManager: () => wsManager,
      wsProcessor: () => wsProcessor,
      reset: () => {
        cleanup()
        init()
      },
    }
  }
})()
