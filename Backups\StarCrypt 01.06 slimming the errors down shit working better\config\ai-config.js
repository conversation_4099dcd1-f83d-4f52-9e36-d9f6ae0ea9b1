// AI Configuration
module.exports = {
  // Model training parameters
  training: {
    epochs: 100,
    batchSize: 32,
    validationSplit: 0.2,
    learningRate: 0.001,
    earlyStopping: {
      patience: 10,
      minDelta: 0.0001,
    },
  },

  // Feature engineering parameters
  features: {
    lookbackPeriod: 50, // Number of previous time steps to consider
    predictionHorizon: 5, // Predict 5 candles ahead

    // Technical indicator parameters
    rsiPeriod: 14,
    macd: {
      fast: 12,
      slow: 26,
      signal: 9,
    },
    bollingerBands: {
      period: 20,
      stdDev: 2,
    },
    atrPeriod: 14,
    adxPeriod: 14,
    volumePeriod: 14,
  },

  // Model architecture
  model: {
    type: 'LSTM',
    layers: [
      { type: 'lstm', units: 64, returnSequences: false },
      { type: 'dropout', rate: 0.2 },
      { type: 'dense', units: 32, activation: 'relu' },
      { type: 'dense', units: 1, activation: 'sigmoid' },
    ],
    optimizer: 'adam',
    loss: 'binaryCrossentropy',
    metrics: ['accuracy', 'precision', 'recall'],
  },

  // Backtesting parameters
  backtesting: {
    initialBalance: 10000, // Starting balance in USD
    commission: 0.001, // 0.1% commission per trade
    slippage: 0.0005, // 0.05% slippage
    riskPerTrade: 0.01, // 1% risk per trade
    maxDrawdown: 0.2, // 20% max drawdown
    warmupPeriod: 100, // Number of initial candles to skip for indicators
  },

  // Strategy parameters
  strategies: {
    defaultWeight: 0.5, // Default weight for strategy signals
    aiWeight: 0.7, // Weight for AI predictions (vs. technical indicators)
    minConfidence: 0.6, // Minimum confidence level for trade execution
    maxOpenTrades: 5, // Maximum number of concurrent open trades
    trailingStop: 0.02, // 2% trailing stop
    takeProfit: 0.05, // 5% take profit
    stopLoss: 0.03, // 3% stop loss
  },

  // Data sources
  data: {
    historical: {
      enabled: true,
      source: 'kraken', // 'kraken', 'binance', 'local'
      maxBars: 1000, // Maximum number of historical bars to load
      updateInterval: 60000, // 1 minute
    },
    realtime: {
      enabled: true,
      source: 'kraken',
      updateInterval: 5000, // 5 seconds
    },
  },

  // Performance optimization
  performance: {
    useWebWorkers: true, // Use Web Workers for CPU-intensive tasks
    maxWorkers: 4, // Maximum number of Web Workers
    batchSize: 100, // Batch size for parallel processing
    cacheTTL: 300000, // 5 minutes cache TTL
    logLevel: 'info', // 'error', 'warn', 'info', 'debug', 'trace'
  },

  // API keys (should be stored in environment variables in production)
  api: {
    kraken: {
      key: process.env.KRAKEN_API_KEY || '',
      secret: process.env.KRAKEN_API_SECRET || '',
    },
    binance: {
      key: process.env.BINANCE_API_KEY || '',
      secret: process.env.BINANCE_API_SECRET || '',
    },
    // Add other exchange APIs as needed
  },

  // Model persistence
  persistence: {
    enabled: true,
    storage: 'filesystem', // 'filesystem', 'mongodb', 'postgres'
    path: './models', // Path for local model storage
    autoSave: true, // Auto-save models after training
    autoLoad: true, // Auto-load models on startup
  },

  // Monitoring and alerts
  monitoring: {
    enabled: true,
    metrics: ['accuracy', 'precision', 'recall', 'sharpe', 'drawdown'],
    alerts: {
      email: {
        enabled: false,
        recipients: [],
        smtp: {
          host: '',
          port: 587,
          secure: false,
          auth: {
            user: '',
            pass: '',
          },
        },
      },
      webhook: {
        enabled: false,
        url: '',
      },
    },
  },

  // Feature toggles
  features: {
    useSentimentAnalysis: true,
    useNewsAnalysis: false,
    useSocialMedia: false,
    useAlternativeData: false,
    enableReinforcementLearning: false,
    enableGeneticOptimization: false,
  },

  // Versioning
  version: '1.0.0',
  lastUpdated: '2025-05-24',
}
