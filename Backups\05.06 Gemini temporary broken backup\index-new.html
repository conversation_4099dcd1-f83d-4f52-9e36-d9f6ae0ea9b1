<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StarCrypt - Advanced Crypto Analysis</title>
    
    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Source+Code+Pro:wght@400;600&display=swap" rel="stylesheet">
    
    <!-- Core CSS -->
    <link rel="stylesheet" href="css/market-trend.css">
    <link rel="stylesheet" href="css/fixes.css">
    
    <!-- Theme -->
    <style>
        :root {
            --primary: #6c63ff;
            --primary-dark: #5649e6;
            --secondary: #4a5568;
            --success: #48bb78;
            --danger: #f56565;
            --warning: #ecc94b;
            --info: #4299e1;
            --dark: #2d3748;
            --light: #f7fafc;
            --background: #1a202c;
            --card-bg: #2d3748;
            --text: #e2e8f0;
            --text-muted: #a0aec0;
            --border: #4a5568;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background);
            color: var(--text);
            line-height: 1.6;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        /* Layout */
        .app-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: 100vh;
        }
        
        .sidebar {
            background: var(--dark);
            border-right: 1px solid var(--border);
            padding: 1.5rem 1rem;
            overflow-y: auto;
        }
        
        .main-content {
            padding: 1.5rem;
            overflow-y: auto;
        }
        
        /* Navigation */
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-item {
            margin-bottom: 0.5rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: var(--text-muted);
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(108, 99, 255, 0.1);
            color: var(--primary);
        }
        
        .nav-link i {
            margin-right: 0.75rem;
            width: 20px;
            text-align: center;
        }
        
        /* Cards */
        .card {
            background: var(--card-bg);
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            margin-bottom: 1.5rem;
            overflow: hidden;
        }
        
        .card-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            background: var(--primary);
            color: white;
        }
        
        .btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .btn-sm {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
        }
        
        /* Responsive */
        @media (max-width: 1024px) {
            .app-container {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div style="padding: 1rem 0.5rem 2rem;">
                <h2 style="margin: 0; font-size: 1.5rem; color: var(--primary);">StarCrypt</h2>
                <p style="margin: 0.25rem 0 0; color: var(--text-muted); font-size: 0.875rem;">Advanced Crypto Analysis</p>
            </div>
            
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" data-section="dashboard">
                            <i>📊</i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="markets">
                            <i>📈</i> Markets
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="signals">
                            <i>🚦</i> Signals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="strategies">
                            <i>⚙️</i> Strategies
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="settings">
                            <i>⚙️</i> Settings
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header style="margin-bottom: 2rem; display: flex; justify-content: space-between; align-items: center;">
                <h1 style="margin: 0; font-size: 1.875rem;">Market Dashboard</h1>
                <div>
                    <button class="btn" id="connect-wallet">
                        <i>🔌</i> Connect Wallet
                    </button>
                </div>
            </header>
            
            <!-- Market Overview -->
            <div class="card">
                <div class="card-header">
                    <h2 style="margin: 0; font-size: 1.25rem;">Market Overview</h2>
                    <div>
                        <select id="timeframe-selector" class="btn btn-sm" style="margin-right: 0.5rem;">
                            <option value="1h">1H</option>
                            <option value="4h" selected>4H</option>
                            <option value="1d">1D</option>
                            <option value="1w">1W</option>
                        </select>
                        <button class="btn btn-sm" id="refresh-data">
                            <i>🔄</i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Price Chart -->
                    <div id="price-chart" style="height: 400px; margin-bottom: 1.5rem;">
                        <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: var(--text-muted);">
                            Loading price chart...
                        </div>
                    </div>
                    
                    <!-- Signal Matrix -->
                    <div id="signal-matrix" style="margin-top: 1.5rem;">
                        <h3 style="margin-top: 0; margin-bottom: 1rem;">Signal Matrix</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); gap: 1rem;">
                            <!-- Signal lights will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Market Data Grid -->
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 1.5rem; margin-top: 1.5rem;">
                <!-- Order Book -->
                <div class="card">
                    <div class="card-header">
                        <h3 style="margin: 0;">Order Book</h3>
                    </div>
                    <div class="card-body">
                        <div id="order-book">
                            <!-- Order book will be populated here -->
                        </div>
                    </div>
                </div>
                
                <!-- Recent Trades -->
                <div class="card">
                    <div class="card-header">
                        <h3 style="margin: 0;">Recent Trades</h3>
                    </div>
                    <div class="card-body">
                        <div id="recent-trades">
                            <!-- Trades will be populated here -->
                        </div>
                    </div>
                </div>
                
                <!-- Market Stats -->
                <div class="card">
                    <div class="card-header">
                        <h3 style="margin: 0;">Market Stats</h3>
                    </div>
                    <div class="card-body">
                        <div id="market-stats">
                            <!-- Stats will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Connection Status -->
    <div id="connection-status" class="connection-status" style="display: none;">
        Connecting to WebSocket...
    </div>
    
    <!-- Load JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    
    <!-- Core Scripts -->
    <script src="js/ui/websocket-handler.js"></script>
    <script src="js/ui/signal-lights-fix.js"></script>
    <script src="js/ui/menu-fix.js"></script>
    <script src="js/ui/market-trend-engine.js"></script>
    <script src="js/init-fixes.js"></script>
    
    <!-- Initialize App -->
    <script>
        // Wait for everything to load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('StarCrypt initialized!');
            
            // Initialize all fixes
            if (window.initializeStarCryptFixes) {
                window.initializeStarCryptFixes();
            }
            
            // Example: Update connection status
            setTimeout(() => {
                updateConnectionStatus(true);
            }, 2000);
            
            // Example: Add some signal lights (replace with real data)
            setTimeout(() => {
                const signals = [
                    { id: 'btc-1h', indicator: 'RSI', value: 65, trend: 'up' },
                    { id: 'eth-1h', indicator: 'MACD', value: 0.0025, trend: 'up' },
                    { id: 'sol-1h', indicator: 'BB', value: 0.8, trend: 'down' },
                    { id: 'btc-4h', indicator: 'RSI', value: 45, trend: 'neutral' },
                    { id: 'eth-4h', indicator: 'MACD', value: -0.0012, trend: 'down' },
                    { id: 'sol-4h', indicator: 'BB', value: 0.3, trend: 'up' },
                ];
                
                const matrix = document.querySelector('#signal-matrix > div');
                if (matrix) {
                    signals.forEach(signal => {
                        const el = document.createElement('div');
                        el.className = `signal-light ${signal.trend}`;
                        el.setAttribute('data-signal-id', signal.id);
                        el.setAttribute('title', `${signal.indicator}: ${signal.value}`);
                        matrix.appendChild(el);
                    });
                }
            }, 1000);
        });
    </script>
</body>
</html>
