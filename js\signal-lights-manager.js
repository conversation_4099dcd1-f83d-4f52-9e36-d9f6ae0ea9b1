// signal-lights-manager.js
class SignalLightsManager {
  constructor() {
    // Initialize state
    this.lastUpdateTime = 0
    this.updateInterval = 1000 // 1 second minimum between updates
    this.isUpdating = false
    this.pendingUpdate = false
    this.signalCache = new Map()
    this.updateQueue = []
    this.isProcessingQueue = false
    this.errorCount = 0
    this.maxErrors = 5
    this.updateInProgress = new Set() // Track which signals are being updated
    this.lastUpdateAttempt = 0
    this.updateCooldown = 500 // 500ms cooldown between updates
    this.currentTimeframe = '1h' // Default timeframe

    // Listen for signal updates and timeframe changes
    this.setupSignalListeners()
    this.setupTimeframeListener()
  }

  setupSignalListeners() {
    // Only set up listeners if SignalManager exists
    if (window.StarCrypt && window.StarCrypt.SignalManager) {
      const signalManager = window.StarCrypt.SignalManager

      // Add event listener with debouncing
      signalManager.addEventListener('update', (data) => {
        if (!this.canUpdate()) return

        // Queue update with delay to prevent recursion
        setTimeout(() => {
          try {
            this.handleSignalUpdate(data)
          } catch (error) {
            console.error('[SignalLightsManager] Error handling signal update:', error)
            this.handleError(error)
          }
        }, 10)
      })
    }
  }

  /**
     * Set up listener for timeframe changes
     */
  setupTimeframeListener() {
    document.addEventListener('timeframeChanged', (event) => {
      try {
        const { timeframe } = event.detail || {}
        if (!timeframe) return

        console.log(`[SignalLightsManager] Timeframe changed to ${timeframe}`)
        this.currentTimeframe = timeframe

        // Clear cache for the previous timeframe
        this.signalCache.clear()

        // Force update all signals for the new timeframe
        this.updateAllSignalLights()
      } catch (error) {
        console.error('[SignalLightsManager] Error handling timeframe change:', error)
      }
    })
  }

  /**
     * Get the current active timeframe
     * @returns {string} Current timeframe
     */
  getCurrentTimeframe() {
    return this.currentTimeframe
  }

  canUpdate() {
    const now = Date.now()
    const isCooldown = now - this.lastUpdateAttempt < this.updateCooldown
    const isUpdating = this.isUpdating || this.isProcessingQueue

    if (isCooldown || isUpdating) {
      return false
    }

    this.lastUpdateAttempt = now
    return true
  }

  /**
     * Update all signal lights on the page
     * @param {boolean} force - If true, bypass rate limiting
     */
  updateAllSignalLights(force = false) {
    // Check for too many errors
    if (this.errorCount >= this.maxErrors) {
      console.warn('Signal updates temporarily disabled due to too many errors')
      return
    }

    const now = Date.now()

    // Rate limiting
    if (!force && now - this.lastUpdateTime < this.updateInterval) {
      this.pendingUpdate = true
      return
    }

    // Only proceed if not already updating
    if (this.isUpdating || this.isProcessingQueue) {
      this.pendingUpdate = true
      return
    }

    this.isUpdating = true
    this.lastUpdateTime = now

    try {
      // Get all signal elements that need updating
      const signals = Array.from(document.querySelectorAll('.signal-light'))

      // Process updates in small batches
      const batchSize = 5
      const totalSignals = signals.length

      // Use Promise.all to process all batches in parallel
      const batchPromises = []

      for (let i = 0; i < totalSignals; i += batchSize) {
        const batch = signals.slice(i, i + batchSize)
        batchPromises.push(new Promise((resolve, reject) => {
          setTimeout(() => {
            try {
              this.updateQueue = batch
              this.processQueue()
                .then(resolve)
                .catch(reject)
            } catch (error) {
              reject(error)
            }
          }, i * 10)
        }))
      }

      // Wait for all batches to complete
      Promise.all(batchPromises)
        .then(() => {
          this.resetUpdateState()
        })
        .catch((error) => {
          console.error('Error processing batches:', error)
          this.handleError(error)
        })
    } catch (error) {
      console.error('Error in signal light update:', error)
      this.handleError(error)
    }
  }

  /**
     * Reset the update state and handle errors
     * @param {Error} [error] - Optional error that triggered the reset
     */
  handleError(error) {
    this.errorCount++
    this.lastErrorTime = Date.now()

    if (error) {
      console.error('[SignalLightsManager] Error:', error)
    }

    if (this.errorCount >= this.maxErrors) {
      console.warn(`[SignalLightsManager] Too many errors (${this.errorCount}/${this.maxErrors}), entering cooldown`)
    }

    this.resetUpdateState()
  }

  /**
     * Reset the update state
     */
  resetUpdateState() {
    this.isUpdating = false
    this.isProcessingQueue = false

    // Only reschedule update if we're not already in an update cycle
    if (this.pendingUpdate && !this.updateQueue.length) {
      this.pendingUpdate = false
      // Add small delay to break potential recursion
      setTimeout(() => this.updateAllSignalLights(true), 10)
    } else {
      this.pendingUpdate = false
    }
  }

  /**
     * Process the update queue in batches
     */
  async processQueue() {
    if (this.isProcessingQueue || this.isUpdating) return Promise.resolve()

    this.isProcessingQueue = true

    try {
      const batchSize = 5 // Process 5 signals at a time
      const batch = this.updateQueue.splice(0, batchSize)

      // Process each signal with a small delay to prevent recursion
      const signalPromises = batch.map(signal => {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            try {
              if (!signal || !signal.id) {
                resolve()
                return
              }

              // Skip if already updating this signal
              if (this.updateInProgress.has(signal.id)) {
                resolve()
                return
              }

              this.updateInProgress.add(signal.id)

              // Get the signal data from the element or cache
              const signalData = this.getSignalData(signal)
              if (!signalData) {
                this.updateInProgress.delete(signal.id)
                resolve()
                return
              }

              // Update the signal
              this.updateSignalElement(signal, signalData)

              // Update cache
              this.signalCache.set(signal.id, {
                ...signalData,
                lastUpdated: Date.now(),
              })

              this.updateInProgress.delete(signal.id)
              resolve()
            } catch (error) {
              console.error(`Error updating signal ${signal.id}:`, error)
              this.handleError(error)
              this.updateInProgress.delete(signal.id)
              resolve() // Continue processing other signals
            }
          }, 10)
        })
      })

      // Wait for all signals in this batch to complete
      await Promise.all(signalPromises)

      // Process remaining queue
      if (this.updateQueue.length > 0) {
        await this.processQueue()
      }
    } catch (error) {
      console.error('Error processing signal queue:', error)
      this.handleError(error)
    } finally {
      this.isProcessingQueue = false
    }
  }

  getSignalData(element) {
    if (!element || !element.getAttribute) return null

    const signalId = element.getAttribute('data-signal-id')
    if (!signalId) return null

    return {
      id: signalId,
      indicator: element.getAttribute('data-indicator'),
      timeframe: element.getAttribute('data-timeframe'),
      signal: element.getAttribute('data-signal') || 'neutral',
      strength: parseFloat(element.getAttribute('data-strength') || '0.5'),
      timestamp: Date.now(),
    }
  }

  updateSignalElement(element, data) {
    if (!element || !data) return

    try {
      // Update signal class
      const signalClass = `signal-${data.signal}`
      element.className = `signal-light ${signalClass}`

      // Update data attributes
      if (data.indicator) element.setAttribute('data-indicator', data.indicator)
      if (data.timeframe) element.setAttribute('data-timeframe', data.timeframe)
      if (data.signal) element.setAttribute('data-signal', data.signal)
      if (data.strength !== undefined) element.setAttribute('data-strength', data.strength)
    } catch (error) {
      console.error('Error updating signal element:', error)
    }
  }
}

// Create and expose singleton instance globally
const signalLightsManager = new SignalLightsManager()
window.SignalLightsManager = signalLightsManager

// For backward compatibility
if (typeof module !== 'undefined' && module.exports) {
  module.exports = signalLightsManager
}
