/**
 * @module LogicManager
 * @description Manages trading logic and signal processing
 */

import Logger from '../utils/logger.js'

export class LogicManager {
  constructor() {
    this.logger = Logger.getInstance()
    this.logger.setContext('LogicManager')
    this.rules = new Map()
    this.signals = new Map()
    this.activeRules = new Set()
    this.lastSignalTime = new Map()
    this.cooldownPeriod = 30000 // 30 seconds default cooldown
  }

  /**
     * Add a trading rule
     * @param {string} ruleId - Unique identifier for the rule
     * @param {Object} rule - Rule configuration
     * @returns {boolean} True if rule was added successfully
     */
  addRule(ruleId, rule) {
    try {
      if (!ruleId || !rule) {
        this.logger.error('Invalid rule parameters')
        return false
      }

      this.rules.set(ruleId, rule)
      this.logger.info(`Added rule: ${ruleId}`)
      return true
    } catch (error) {
      this.logger.error(`Error adding rule: ${error.message}`)
      return false
    }
  }

  /**
     * Remove a trading rule
     * @param {string} ruleId - The rule identifier
     * @returns {boolean} True if rule was removed successfully
     */
  removeRule(ruleId) {
    try {
      if (this.rules.delete(ruleId)) {
        this.activeRules.delete(ruleId)
        this.logger.info(`Removed rule: ${ruleId}`)
        return true
      }
      return false
    } catch (error) {
      this.logger.error(`Error removing rule: ${error.message}`)
      return false
    }
  }

  /**
     * Evaluate trading rules against current signals
     * @param {Object} signals - Current market signals
     * @returns {Array} Array of triggered rules
     */
  evaluateRules(signals) {
    const triggeredRules = []
    try {
      this.rules.forEach((rule, ruleId) => {
        if (this.activeRules.has(ruleId) && this.checkCooldown(ruleId)) {
          const result = this.evaluateRule(rule, signals)
          if (result) {
            triggeredRules.push({
              ruleId,
              result,
              timestamp: Date.now(),
            })
            this.lastSignalTime.set(ruleId, Date.now())
          }
        }
      })
      return triggeredRules
    } catch (error) {
      this.logger.error(`Error evaluating rules: ${error.message}`)
      return []
    }
  }

  /**
     * Evaluate a single rule
     * @param {Object} rule - The rule configuration
     * @param {Object} signals - Current market signals
     * @returns {Object|null} Rule evaluation result or null
     */
  evaluateRule(rule, signals) {
    try {
      const conditions = rule.conditions || []
      const result = conditions.every(condition => {
        const signalValue = signals[condition.indicator]
        if (signalValue === undefined) return false

        switch (condition.operator) {
          case '>':
            return signalValue > condition.value
          case '<':
            return signalValue < condition.value
          case '==':
            return signalValue === condition.value
          case '!=':
            return signalValue !== condition.value
          default:
            return false
        }
      })

      if (result && rule.action) {
        return {
          action: rule.action,
          confidence: rule.confidence || 1.0,
          indicators: conditions.map(c => c.indicator),
        }
      }
      return null
    } catch (error) {
      this.logger.error(`Error evaluating rule: ${error.message}`)
      return null
    }
  }

  /**
     * Check if a rule is in cooldown
     * @param {string} ruleId - The rule identifier
     * @returns {boolean} True if rule is not in cooldown
     */
  checkCooldown(ruleId) {
    const lastTime = this.lastSignalTime.get(ruleId)
    if (!lastTime) return true
    return Date.now() - lastTime >= this.cooldownPeriod
  }

  /**
     * Set cooldown period for rules
     * @param {number} period - Cooldown period in milliseconds
     */
  setCooldownPeriod(period) {
    if (typeof period === 'number' && period > 0) {
      this.cooldownPeriod = period
      this.logger.info(`Set cooldown period to: ${period}ms`)
    }
  }

  /**
     * Get all active rules
     * @returns {Array} Array of active rule IDs
     */
  getActiveRules() {
    return Array.from(this.activeRules)
  }

  /**
     * Toggle rule activation
     * @param {string} ruleId - The rule identifier
     * @returns {boolean} True if rule activation was toggled
     */
  toggleRule(ruleId) {
    try {
      const isActive = this.activeRules.has(ruleId)
      if (isActive) {
        this.activeRules.delete(ruleId)
      } else {
        this.activeRules.add(ruleId)
      }
      this.logger.info(`Rule ${ruleId} ${isActive ? 'deactivated' : 'activated'}`)
      return !isActive
    } catch (error) {
      this.logger.error(`Error toggling rule: ${error.message}`)
      return false
    }
  }

  /**
     * Reset all rules to default state
     */
  resetRules() {
    try {
      this.activeRules.clear()
      this.lastSignalTime.clear()
      this.logger.info('Reset all rules to default state')
    } catch (error) {
      this.logger.error(`Error resetting rules: ${error.message}`)
    }
  }
}

// Export singleton instance
export const logicManager = new LogicManager()
