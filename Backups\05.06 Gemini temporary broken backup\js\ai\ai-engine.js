const tf = require('@tensorflow/tfjs-node')
const TechnicalIndicators = require('technicalindicators')

class AIEngine {
  constructor() {
    this.models = new Map()
    this.isInitialized = false
    this.modelConfig = {
      // Model architecture configuration
      lookbackPeriod: 50, // Number of previous time steps to consider
      predictionHorizon: 5, // Predict 5 candles ahead
      trainingEpochs: 100,
      batchSize: 32,
      validationSplit: 0.2,
      // Feature engineering parameters
      featureParams: {
        rsiPeriod: 14,
        macdFast: 12,
        macdSlow: 26,
        macdSignal: 9,
        bbPeriod: 20,
        bbStdDev: 2,
        atrPeriod: 14,
        adxPeriod: 14,
        volumePeriod: 14,
      },
      // Model hyperparameters
      modelParams: {
        lstmUnits: 64,
        denseUnits: 32,
        dropoutRate: 0.2,
        learningRate: 0.001,
      },
    }
  }

  async initialize() {
    if (this.isInitialized) return

    try {
      // Enable TF.js for Node.js
      await tf.setBackend('tensorflow')
      console.log('AI Engine: TensorFlow.js backend set to', tf.getBackend())

      // Warm up TensorFlow
      tf.tensor([1, 2, 3]).dispose()

      this.isInitialized = true
      console.log('AI Engine: Initialized successfully')
    } catch (error) {
      console.error('AI Engine: Initialization failed:', error)
      throw error
    }
  }

  // Feature Engineering
  prepareFeatures(ohlcvData) {
    const { highs, lows, closes, volumes } = ohlcvData
    const { featureParams } = this.modelConfig

    const features = []
    const targets = []
    const timestamps = []

    // Calculate technical indicators
    const rsi = TechnicalIndicators.RSI.calculate({
      values: closes,
      period: featureParams.rsiPeriod,
    })

    const macd = TechnicalIndicators.MACD.calculate({
      values: closes,
      fastPeriod: featureParams.macdFast,
      slowPeriod: featureParams.macdSlow,
      signalPeriod: featureParams.macdSignal,
      SimpleMAOscillator: false,
      SimpleMASignal: false,
    })

    const bb = TechnicalIndicators.BollingerBands.calculate({
      values: closes,
      period: featureParams.bbPeriod,
      stdDev: featureParams.bbStdDev,
    })

    const atr = TechnicalIndicators.ATR.calculate({
      high: highs,
      low: lows,
      close: closes,
      period: featureParams.atrPeriod,
    })

    // Calculate price changes and volatilities
    const priceChanges = []
    const volatilities = []

    for (let i = 1; i < closes.length; i++) {
      priceChanges.push((closes[i] - closes[i - 1]) / closes[i - 1])
      if (i >= featureParams.volumePeriod) {
        const volSlice = volumes.slice(i - featureParams.volumePeriod, i)
        const avgVol = volSlice.reduce((a, b) => a + b, 0) / volSlice.length
        volatilities.push(avgVol)
      }
    }

    // Create feature vectors
    const lookback = this.modelConfig.lookbackPeriod
    for (let i = lookback; i < closes.length - this.modelConfig.predictionHorizon; i++) {
      const featureVector = []

      // Add normalized price features
      for (let j = 0; j < lookback; j++) {
        const idx = i - lookback + j
        featureVector.push(
          (closes[idx] - closes[i - lookback]) / closes[i - lookback],
          (highs[idx] - closes[i - lookback]) / closes[i - lookback],
          (lows[idx] - closes[i - lookback]) / closes[i - lookback],
        )
      }

      // Add technical indicators
      if (rsi[i]) featureVector.push(rsi[i] / 100) // Normalize RSI to [0,1]
      if (macd[i]) {
        featureVector.push(
          macd[i].MACD,
          macd[i].signal,
          macd[i].histogram,
        )
      }
      if (bb[i]) {
        featureVector.push(
          (closes[i] - bb[i].middle) / bb[i].middle,
          (bb[i].upper - bb[i].lower) / bb[i].middle,
        )
      }
      if (atr[i]) featureVector.push(atr[i] / closes[i])

      // Add volume features
      if (i > featureParams.volumePeriod) {
        const volSlice = volumes.slice(i - featureParams.volumePeriod, i)
        const avgVol = volSlice.reduce((a, b) => a + b, 0) / volSlice.length
        featureVector.push(volumes[i] / (avgVol || 1))
      }

      // Add time-based features
      const date = new Date(ohlcvData.timestamps[i])
      featureVector.push(
        date.getUTCHours() / 24,
        date.getUTCDay() / 7,
        date.getUTCMonth() / 12,
      )

      // Calculate target (future price movement)
      const futurePrice = closes[i + this.modelConfig.predictionHorizon]
      const priceChange = (futurePrice - closes[i]) / closes[i]

      features.push(featureVector)
      targets.push(priceChange > 0 ? 1 : 0) // Binary classification: 1 for up, 0 for down
      timestamps.push(ohlcvData.timestamps[i])
    }

    return { features, targets, timestamps }
  }

  // Model Architecture
  createModel(inputShape) {
    const model = tf.sequential()

    // Input layer
    model.add(tf.layers.lstm({
      units: this.modelConfig.modelParams.lstmUnits,
      inputShape: [inputShape[0], inputShape[1]],
      returnSequences: false,
      kernelInitializer: 'glorotNormal',
      recurrentInitializer: 'orthogonal',
    }))

    // Hidden layers
    model.add(tf.layers.dropout({
      rate: this.modelConfig.modelParams.dropoutRate,
    }))

    model.add(tf.layers.dense({
      units: this.modelConfig.modelParams.denseUnits,
      activation: 'relu',
      kernelInitializer: 'heNormal',
    }))

    // Output layer
    model.add(tf.layers.dense({
      units: 1,
      activation: 'sigmoid',
    }))

    // Compile model
    model.compile({
      optimizer: tf.train.adam(this.modelConfig.modelParams.learningRate),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy', tf.metrics.precision(), tf.metrics.recall()],
    })

    return model
  }

  // Training
  async trainModel(ohlcvData, callbacks = {}) {
    try {
      // Prepare features and targets
      const { features, targets } = this.prepareFeatures(ohlcvData)

      // Convert to tensors
      const xs = tf.tensor3d(features)
      const ys = tf.tensor2d(targets, [targets.length, 1])

      // Create and train model
      const model = this.createModel([features.length, features[0].length])

      const history = await model.fit(xs, ys, {
        epochs: this.modelConfig.trainingEpochs,
        batchSize: this.modelConfig.batchSize,
        validationSplit: this.modelConfig.validationSplit,
        callbacks: {
          onEpochEnd: async (epoch, logs) => {
            console.log(`Epoch ${epoch + 1}: loss = ${logs.loss.toFixed(4)}, acc = ${logs.acc.toFixed(4)}`)
            if (callbacks.onEpochEnd) {
              await callbacks.onEpochEnd(epoch, logs)
            }
          },
          onTrainBegin: callbacks.onTrainBegin,
          onTrainEnd: callbacks.onTrainEnd,
        },
      })

      // Cleanup
      xs.dispose()
      ys.dispose()

      return { model, history }
    } catch (error) {
      console.error('Error training model:', error)
      throw error
    }
  }

  // Prediction
  async predict(model, ohlcvData) {
    try {
      const { features, timestamps } = this.prepareFeatures(ohlcvData)
      const xs = tf.tensor3d([features[features.length - 1]])

      const prediction = await model.predict(xs).data()
      xs.dispose()

      return {
        prediction: prediction[0],
        confidence: Math.abs(prediction[0] - 0.5) * 2, // Convert to 0-1 range
        timestamp: timestamps[timestamps.length - 1],
      }
    } catch (error) {
      console.error('Error making prediction:', error)
      throw error
    }
  }

  // Ensemble Prediction (combines multiple models)
  async ensemblePredict(ohlcvData, models) {
    const predictions = []

    for (const [name, model] of models) {
      try {
        const prediction = await this.predict(model, ohlcvData)
        predictions.push({
          name,
          ...prediction,
        })
      } catch (error) {
        console.error(`Error in model ${name}:`, error)
      }
    }

    // Simple average of predictions
    if (predictions.length > 0) {
      const avgPrediction = predictions.reduce((sum, p) => sum + p.prediction, 0) / predictions.length
      const avgConfidence = predictions.reduce((sum, p) => sum + p.confidence, 0) / predictions.length

      return {
        prediction: avgPrediction,
        confidence: avgConfidence,
        timestamp: predictions[0].timestamp,
        models: predictions,
      }
    }

    throw new Error('No valid predictions from any model')
  }

  // Feature Importance Analysis
  async analyzeFeatureImportance(model, featureNames) {
    // Implementation for SHAP or permutation importance
    // This is a placeholder - actual implementation would depend on the model type
    return featureNames.map((name, i) => ({
      feature: name,
      importance: Math.random(), // Replace with actual importance calculation
    })).sort((a, b) => b.importance - a.importance)
  }

  // Save model to disk
  async saveModel(model, path) {
    await model.save(`file://${path}`)
  }

  // Load model from disk
  async loadModel(path) {
    return await tf.loadLayersModel(`file://${path}/model.json`)
  }
}

module.exports = new AIEngine()
