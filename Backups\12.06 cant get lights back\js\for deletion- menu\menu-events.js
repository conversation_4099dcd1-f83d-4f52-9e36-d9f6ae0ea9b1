/**
 * @module MenuEvents
 * @description Event handling system for menu interactions
 */

import { EventDispatcher } from '../utils/event-dispatcher.js'

export class MenuEvents extends EventDispatcher {
  constructor() {
    super()

    // Event types
    this.eventTypes = {
      'menu-open': 'menu-open',
      'menu-close': 'menu-close',
      'menu-close-all': 'menu-close-all',
      'menu-click': 'menu-click',
      'menu-keydown': 'menu-keydown',
      'menu-focus': 'menu-focus',
      'menu-blur': 'menu-blur',
      'state-change': 'state-change',
      error: 'error',
      initialized: 'initialized',
    }

    // Error handling
    this.errorHandler = new ErrorHandler({
      maxErrors: 5,
      recoveryDelay: 2000,
      errorThreshold: 0.1,
    })

    // Performance monitoring
    this.performance = new PerformanceMonitor({
      targetFPS: 60,
      maxLatency: 100,
      batchInterval: 100,
    })

    // Setup
    this.setupEventListeners()
  }

  /**
     * Setup event listeners
     * @private
     */
  setupEventListeners() {
    // Listen for document events
    document.addEventListener('click', (e) => this.handleDocumentClick(e))
    document.addEventListener('keydown', (e) => this.handleDocumentKeydown(e))

    // Listen for focus events
    document.addEventListener('focusin', (e) => this.handleFocusIn(e))
    document.addEventListener('focusout', (e) => this.handleFocusOut(e))
  }

  /**
     * Handle document click events
     * @param {Event} event - The click event
     * @private
     */
  handleDocumentClick(event) {
    try {
      // Track interaction
      this.trackInteraction('click', event.target)

      // Dispatch event
      this.dispatch(this.eventTypes['menu-click'], {
        target: event.target,
        timestamp: Date.now(),
        context: this.getContext(),
      })
    } catch (error) {
      this.handleError(error, 'click-handler')
    }
  }

  /**
     * Handle document keydown events
     * @param {Event} event - The keydown event
     * @private
     */
  handleDocumentKeydown(event) {
    try {
      // Track interaction
      this.trackInteraction('keydown', event.target, event.key)

      // Dispatch event
      this.dispatch(this.eventTypes['menu-keydown'], {
        target: event.target,
        key: event.key,
        timestamp: Date.now(),
        context: this.getContext(),
      })
    } catch (error) {
      this.handleError(error, 'keydown-handler')
    }
  }

  /**
     * Handle focus in events
     * @param {Event} event - The focusin event
     * @private
     */
  handleFocusIn(event) {
    try {
      // Track focus
      this.trackFocus(event.target)

      // Dispatch event
      this.dispatch(this.eventTypes['menu-focus'], {
        target: event.target,
        timestamp: Date.now(),
        context: this.getContext(),
      })
    } catch (error) {
      this.handleError(error, 'focusin-handler')
    }
  }

  /**
     * Handle focus out events
     * @param {Event} event - The focusout event
     * @private
     */
  handleFocusOut(event) {
    try {
      // Track blur
      this.trackBlur(event.target)

      // Dispatch event
      this.dispatch(this.eventTypes['menu-blur'], {
        target: event.target,
        timestamp: Date.now(),
        context: this.getContext(),
      })
    } catch (error) {
      this.handleError(error, 'focusout-handler')
    }
  }

  /**
     * Track menu interactions
     * @param {string} type - Interaction type
     * @param {HTMLElement} target - Target element
     * @param {string} [key] - Key pressed (for keyboard events)
     * @private
     */
  trackInteraction(type, target, key) {
    try {
      const interaction = {
        type,
        timestamp: Date.now(),
        targetId: target.id,
        targetClass: target.className,
        key,
        context: this.getContext(),
      }

      // Send to analytics
      this.utils.trackAnalytics('menu-interaction', interaction)
    } catch (error) {
      console.error('[MenuEvents] Error tracking interaction:', error)
    }
  }

  /**
     * Track focus events
     * @param {HTMLElement} target - Target element
     * @private
     */
  trackFocus(target) {
    try {
      const focusEvent = {
        timestamp: Date.now(),
        targetId: target.id,
        targetClass: target.className,
        context: this.getContext(),
      }

      // Send to analytics
      this.utils.trackAnalytics('menu-focus', focusEvent)
    } catch (error) {
      console.error('[MenuEvents] Error tracking focus:', error)
    }
  }

  /**
     * Track blur events
     * @param {HTMLElement} target - Target element
     * @private
     */
  trackBlur(target) {
    try {
      const blurEvent = {
        timestamp: Date.now(),
        targetId: target.id,
        targetClass: target.className,
        context: this.getContext(),
      }

      // Send to analytics
      this.utils.trackAnalytics('menu-blur', blurEvent)
    } catch (error) {
      console.error('[MenuEvents] Error tracking blur:', error)
    }
  }

  /**
     * Get current context for events
     * @returns {Object} Current context
     * @private
     */
  getContext() {
    return {
      timestamp: Date.now(),
      performance: this.performance.getStats(),
      errorCount: this.errorHandler.errorCount,
      activeMenu: this.getState('activeMenu'),
    }
  }

  /**
     * Handle errors
     * @param {Error} error - Error object
     * @param {string} context - Context where error occurred
     * @private
     */
  handleError(error, context) {
    try {
      // Log error
      console.error(`[MenuEvents] Error in ${context}:`, error)

      // Track error
      this.errorHandler.track(error, context)

      // Dispatch error event
      this.dispatch(this.eventTypes.error, {
        error,
        context,
        timestamp: Date.now(),
      })
    } catch (error) {
      console.error('[MenuEvents] Error handling failed:', error)
    }
  }

  /**
     * Dispatch event with error handling
     * @param {string} type - Event type
     * @param {Object} data - Event data
     */
  dispatch(type, data) {
    try {
      // Validate event type
      if (!this.eventTypes[type]) {
        throw new Error(`Invalid event type: ${type}`)
      }

      // Add performance metrics
      data.performance = this.performance.getStats()

      // Dispatch event
      super.dispatch(type, data)
    } catch (error) {
      this.handleError(error, 'dispatch')
    }
  }

  /**
     * Add event listener with error handling
     * @param {string} type - Event type
     * @param {Function} callback - Event handler
     */
  on(type, callback) {
    try {
      // Validate event type
      if (!this.eventTypes[type]) {
        throw new Error(`Invalid event type: ${type}`)
      }

      // Add listener
      super.on(type, callback)
    } catch (error) {
      this.handleError(error, 'add-listener')
    }
  }

  /**
     * Remove event listener
     * @param {string} type - Event type
     * @param {Function} callback - Event handler
     */
  off(type, callback) {
    try {
      // Validate event type
      if (!this.eventTypes[type]) {
        throw new Error(`Invalid event type: ${type}`)
      }

      // Remove listener
      super.off(type, callback)
    } catch (error) {
      this.handleError(error, 'remove-listener')
    }
  }

  /**
     * Clean up event listeners
     */
  cleanup() {
    try {
      // Remove document listeners
      document.removeEventListener('click', this.handleDocumentClick)
      document.removeEventListener('keydown', this.handleDocumentKeydown)
      document.removeEventListener('focusin', this.handleFocusIn)
      document.removeEventListener('focusout', this.handleFocusOut)

      // Remove all listeners
      this.removeAllListeners()

      // Reset error handler
      this.errorHandler.reset()

      // Reset performance monitor
      this.performance.reset()
    } catch (error) {
      console.error('[MenuEvents] Cleanup failed:', error)
    }
  }

  /**
     * Destroy event system
     */
  destroy() {
    try {
      // Cleanup
      this.cleanup()

      // Reset state
      this.eventTypes = null
      this.errorHandler = null
      this.performance = null
    } catch (error) {
      console.error('[MenuEvents] Destruction failed:', error)
    }
  }
}

// Export singleton instance
const menuEvents = new MenuEvents()
export default menuEvents
