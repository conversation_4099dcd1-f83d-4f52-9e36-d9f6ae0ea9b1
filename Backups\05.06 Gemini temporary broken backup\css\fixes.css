/* fixes.css - Global fixes for rendering and styling issues */

/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Fix for signal light tooltips */
.signal-light {
    position: relative;
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
}

/* Custom tooltip styles */
.custom-tooltip {
    position: absolute;
    background: #2d3748;
    color: #fff;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-line;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    max-width: 250px;
    transform: translate(-50%, -100%);
    margin-top: -8px;
}

/* Remove browser default tooltips */
[title] {
    position: relative;
}

[title]:hover::before,
[title]:hover::after {
    display: none !important;
}

/* Fix for menu system */
.menu {
    display: none;
    position: absolute;
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 1000;
    min-width: 200px;
}

.menu.active {
    display: block;
    animation: fadeIn 0.15s ease-out;
}

/* Menu animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Fix for signal light colors */
.signal-light.overbought {
    background-color: #f56565; /* Red */
    box-shadow: 0 0 8px rgba(245, 101, 101, 0.7);
}

.signal-light.oversold {
    background-color: #48bb78; /* Green */
    box-shadow: 0 0 8px rgba(72, 187, 120, 0.7);
}

.signal-light.neutral {
    background-color: #a0aec0; /* Gray */
}

.signal-light.bullish {
    background-color: #68d391; /* Light green */
    box-shadow: 0 0 8px rgba(104, 211, 145, 0.7);
}

.signal-light.bearish {
    background-color: #fc8181; /* Light red */
    box-shadow: 0 0 8px rgba(252, 129, 129, 0.7);
}

/* Fix for z-index issues */
.menu-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.2s ease;
}

/* Ensure proper stacking context */
body {
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Fix for WebSocket connection status */
.connection-status {
    position: fixed;
    bottom: 10px;
    right: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background-color: #48bb78;
    color: white;
}

.connection-status.disconnected {
    background-color: #f56565;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

/* Fix for scrollbars */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white;
        color: black;
    }
}
