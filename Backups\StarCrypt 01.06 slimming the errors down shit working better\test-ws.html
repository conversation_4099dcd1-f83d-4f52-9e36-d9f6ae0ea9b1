<!DOCTYPE html>
<html>
<head>
  <title>WebSocket Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    #status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
      font-weight: bold;
    }
    .connected { background-color: #d4edda; color: #155724; }
    .disconnected { background-color: #f8d7da; color: #721c24; }
    #log {
      height: 300px;
      overflow-y: auto;
      border: 1px solid #ddd;
      padding: 10px;
      font-family: monospace;
      background-color: #f8f9fa;
    }
  </style>
</head>
<body>
  <h1>WebSocket Connection Test</h1>
  <div>
    Connection Status: <span id="status" class="disconnected">Disconnected</span>
  </div>
  <div>
    <button id="connectBtn">Connect</button>
    <button id="disconnectBtn" disabled>Disconnect</button>
    <button id="sendPing">Send Ping</button>
  </div>
  <h3>Log:</h3>
  <div id="log"></div>

  <script>
    const statusEl = document.getElementById('status');
    const logEl = document.getElementById('log');
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const sendPingBtn = document.getElementById('sendPing');
    
    let socket = null;
    
    function log(message) {
      const time = new Date().toLocaleTimeString();
      logEl.innerHTML += `[${time}] ${message}<br>`;
      logEl.scrollTop = logEl.scrollHeight;
    }
    
    function updateStatus(connected) {
      if (connected) {
        statusEl.textContent = 'Connected';
        statusEl.className = 'connected';
        connectBtn.disabled = true;
        disconnectBtn.disabled = false;
        sendPingBtn.disabled = false;
      } else {
        statusEl.textContent = 'Disconnected';
        statusEl.className = 'disconnected';
        connectBtn.disabled = false;
        disconnectBtn.disabled = true;
        sendPingBtn.disabled = true;
      }
    }
    
    function connectWebSocket() {
      if (socket && (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING)) {
        log('WebSocket already connecting/connected');
        return;
      }
      
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.hostname}:3000/ws`;
      
      log(`Connecting to ${wsUrl}...`);
      
      try {
        socket = new WebSocket(wsUrl);
        
        socket.onopen = () => {
          log('WebSocket connection established');
          updateStatus(true);
        };
        
        socket.onclose = (event) => {
          log(`WebSocket connection closed: ${event.code} ${event.reason || 'No reason provided'}`);
          updateStatus(false);
          socket = null;
        };
        
        socket.onerror = (error) => {
          log(`WebSocket error: ${error.message || 'Unknown error'}`);
          updateStatus(false);
        };
        
        socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            log(`Received: ${JSON.stringify(data, null, 2)}`);
          } catch (e) {
            log(`Received (raw): ${event.data}`);
          }
        };
        
      } catch (error) {
        log(`Error creating WebSocket: ${error.message}`);
        updateStatus(false);
      }
    }
    
    function disconnectWebSocket() {
      if (socket) {
        log('Closing WebSocket connection...');
        socket.close();
      } else {
        log('No active WebSocket connection to close');
      }
    }
    
    function sendPing() {
      if (socket && socket.readyState === WebSocket.OPEN) {
        const message = JSON.stringify({ type: 'ping', timestamp: Date.now() });
        socket.send(message);
        log(`Sent: ${message}`);
      } else {
        log('Cannot send ping: WebSocket not connected');
      }
    }
    
    // Event listeners
    connectBtn.addEventListener('click', connectWebSocket);
    disconnectBtn.addEventListener('click', disconnectWebSocket);
    sendPingBtn.addEventListener('click', sendPing);
    
    // Initial log
    log('Page loaded. Click "Connect" to start WebSocket connection.');
  </script>
</body>
</html>
