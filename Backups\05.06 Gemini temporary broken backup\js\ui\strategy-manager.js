(function () {
/**
 * StarCrypt Strategy Manager
 *
 * This module provides a unified interface for managing trading strategies,
 * including strategy selection, UI updates, and indicator management.
 * It combines functionality from strategy-selector.js and strategy-logic.js
 * to provide a single source of truth for strategy-related operations.
 */

  // Use the global TRADING_STRATEGIES defined in global-variables.js
  if (!window.TRADING_STRATEGIES) {
    console.error('Error: TRADING_STRATEGIES not defined in global-variables.js')
    window.TRADING_STRATEGIES = {} // Prevent further errors
  }

  // Default strategy if none is selected
  const DEFAULT_STRATEGY = 'admiral_toa'

  // Strategy State Management
  const strategyState = {
    currentStrategy: null,
    isInitialized: false,
    eventListeners: new Map(),

    /**
   * Initialize the strategy state
   */
    init() {
      if (this.isInitialized) return

      // Load saved strategy or use default
      this.currentStrategy = localStorage.getItem('currentStrategy') || DEFAULT_STRATEGY

      // Validate the loaded strategy exists
      if (!TRADING_STRATEGIES[this.currentStrategy]) {
        console.warn(`Saved strategy ${this.currentStrategy} not found, using default`)
        this.currentStrategy = DEFAULT_STRATEGY
      }

      this.isInitialized = true
      console.log('[Strategy] Initialized with strategy:', this.currentStrategy)

      // Notify listeners that strategy is ready
      this.notifyStrategyChange(this.currentStrategy)
    },

    /**
   * Get the current strategy
   * @returns {string} The current strategy ID
   */
    getCurrentStrategy() {
      return this.currentStrategy
    },

    /**
   * Set the current strategy
   * @param {string} strategyId - The ID of the strategy to set
   * @returns {boolean} True if the strategy was changed, false otherwise
   */
    setStrategy(strategyId) {
      if (!strategyId || !TRADING_STRATEGIES[strategyId]) {
        console.error(`Invalid strategy ID: ${strategyId}`)
        return false
      }

      const oldStrategy = this.currentStrategy
      if (strategyId === oldStrategy) {
        return false // No change
      }

      console.log(`[Strategy] Changing strategy from ${oldStrategy} to ${strategyId}`)
      this.currentStrategy = strategyId
      localStorage.setItem('currentStrategy', strategyId)

      // Notify listeners of the change
      this.notifyStrategyChange(strategyId, oldStrategy)

      return true
    },

    /**
   * Add an event listener for strategy changes
   * @param {string} event - The event name
   * @param {Function} callback - The callback function
   */
    on(event, callback) {
      if (!this.eventListeners.has(event)) {
        this.eventListeners.set(event, new Set())
      }
      this.eventListeners.get(event).add(callback)
    },

    /**
   * Remove an event listener
   * @param {string} event - The event name
   * @param {Function} callback - The callback function to remove
   */
    off(event, callback) {
      if (this.eventListeners.has(event)) {
        const callbacks = this.eventListeners.get(event)
        callbacks.delete(callback)

        if (callbacks.size === 0) {
          this.eventListeners.delete(event)
        }
      }
    },

    /**
   * Notify all listeners of a strategy change
   * @param {string} newStrategy - The new strategy ID
   * @param {string} [oldStrategy] - The previous strategy ID
   * @private
   */
    notifyStrategyChange(newStrategy, oldStrategy) {
      if (!this.eventListeners.has('change')) return

      const event = {
        type: 'change',
        strategy: newStrategy,
        previousStrategy: oldStrategy,
        timestamp: new Date().toISOString(),
        data: TRADING_STRATEGIES[newStrategy] || {},
      }

      // Call all registered callbacks
      this.eventListeners.get('change').forEach(callback => {
        try {
          callback(event)
        } catch (error) {
          console.error('Error in strategy change callback:', error)
        }
      })
    },
  }

  /**
 * Strategy Selector UI Component
 * Handles rendering and interaction for the strategy selection UI
 */
  class StrategySelectorUI {
    constructor() {
    // DOM Elements
      this.strategyMenu = document.getElementById('strategyMenu')
      this.strategyButton = document.getElementById('strategyButton')
      this.strategyList = this.strategyMenu?.querySelector('.strategy-list')
      this.strategyTitle = this.strategyButton?.querySelector('.strategy-title')
      this.strategyDesc = this.strategyMenu?.querySelector('.strategy-description')
      this.strategyHelper = this.strategyMenu?.querySelector('.strategy-helper')
      this.applyButton = this.strategyMenu?.querySelector('.apply-strategy')

      // State
      this.selectedStrategy = null
      this.isInitialized = false

      // Bind methods
      this.handleDocumentClick = this.handleDocumentClick.bind(this)
      this.handleStrategySelect = this.handleStrategySelect.bind(this)
      this.handleApplyClick = this.handleApplyClick.bind(this)
      this.onStrategyChanged = this.onStrategyChanged.bind(this)

      // Initialize
      this.initialize()
    }

    /**
   * Initialize the strategy selector UI
   */
    initialize() {
      if (this.isInitialized || !this.strategyMenu || !this.strategyButton) {
        console.warn('Strategy UI elements not found, skipping initialization')
        return
      }

      // Set initial state
      this.selectedStrategy = strategyState.getCurrentStrategy()

      // Render the strategy list
      this.renderStrategyList()

      // Set up event listeners
      this.strategyButton.addEventListener('click', (e) => {
        e.stopPropagation()
        this.strategyMenu.classList.toggle('show')
      })

      if (this.applyButton) {
        this.applyButton.addEventListener('click', this.handleApplyClick)
      }

      // Close menu when clicking outside
      document.addEventListener('click', this.handleDocumentClick)

      // Listen for strategy changes from other components
      strategyState.on('change', this.onStrategyChanged)

      // Update UI to reflect current strategy
      this.updateSelectedStrategyUI()

      this.isInitialized = true
      console.log('[StrategyUI] Initialized')
    }

    /**
   * Render the list of available strategies
   * @private
   */
    renderStrategyList() {
      if (!this.strategyList) return

      this.strategyList.innerHTML = ''

      Object.entries(TRADING_STRATEGIES).forEach(([id, strategy]) => {
        const item = document.createElement('div')
        item.className = 'strategy-item'
        item.dataset.strategy = id
        item.textContent = strategy.name
        item.addEventListener('click', this.handleStrategySelect)

        // Highlight current strategy
        if (id === this.selectedStrategy) {
          item.classList.add('active')
        }

        this.strategyList.appendChild(item)
      })
    }

    /**
   * Handle strategy selection from the UI
   * @param {Event} event - The click event
   * @private
   */
    handleStrategySelect(event) {
      const strategyItem = event.currentTarget
      const strategyId = strategyItem.dataset.strategy

      if (!strategyId || !TRADING_STRATEGIES[strategyId]) {
        console.error('Invalid strategy selected:', strategyId)
        return
      }

      // Update selected strategy
      this.selectedStrategy = strategyId

      // Update UI
      this.strategyList.querySelectorAll('.strategy-item').forEach(item => {
        item.classList.toggle('active', item === strategyItem)
      })

      // Update preview
      this.updateStrategyPreview(strategyId)
    }

    /**
   * Update the strategy preview in the UI
   * @param {string} strategyId - The ID of the strategy to preview
   * @private
   */
    updateStrategyPreview(strategyId) {
      const strategy = TRADING_STRATEGIES[strategyId]
      if (!strategy) return

      // Update description
      if (this.strategyDesc) {
        this.strategyDesc.innerHTML = `<p>${strategy.description || 'No description available.'}</p>`
      }

      // Update helper text if available
      if (this.strategyHelper && strategy.helperText) {
        this.strategyHelper.innerHTML = strategy.helperText
      }

      // Update indicators list if available
      const indicatorsList = this.strategyMenu.querySelector('.strategy-indicators')
      if (indicatorsList && Array.isArray(strategy.indicators)) {
        indicatorsList.innerHTML = strategy.indicators
          .map(ind => `<span class="indicator-tag">${ind}</span>`)
          .join('')
      }
    }

    /**
   * Handle apply button click
   * @private
   */
    handleApplyClick() {
      if (!this.selectedStrategy) return

      const success = strategyState.setStrategy(this.selectedStrategy)
      if (success) {
        this.showFeedback('Strategy applied successfully!')
        this.strategyMenu.classList.remove('show')

        // Update the main UI
        updateStrategyInfoPanel(this.selectedStrategy)
      } else {
        this.showFeedback('Failed to apply strategy', 'error')
      }
    }

    /**
   * Update the UI to reflect the selected strategy
   * @private
   */
    updateSelectedStrategyUI() {
      const currentStrategy = strategyState.getCurrentStrategy()
      const strategy = TRADING_STRATEGIES[currentStrategy]

      if (!strategy) return

      // Update button text
      if (this.strategyTitle) {
        this.strategyTitle.textContent = strategy.name
      }

      // Update selected state in the list
      if (this.strategyList) {
        this.strategyList.querySelectorAll('.strategy-item').forEach(item => {
          item.classList.toggle('active', item.dataset.strategy === currentStrategy)
        })
      }

      // Update preview
      this.updateStrategyPreview(currentStrategy)
    }

    /**
   * Handle strategy changes from other components
   * @param {Object} event - The strategy change event
   * @private
   */
    onStrategyChanged(event) {
      if (event.strategy !== this.selectedStrategy) {
        this.selectedStrategy = event.strategy
        this.updateSelectedStrategyUI()
      }
    }

    /**
   * Show feedback message
   * @param {string} message - The message to show
   * @param {string} [type='success'] - The type of feedback (success, error, warning)
   * @private
   */
    showFeedback(message, type = 'success') {
    // Implementation for showing feedback (toast, alert, etc.)
      console.log(`[${type.toUpperCase()}] ${message}`)
    // You can implement a toast notification system here
    }

    /**
   * Handle clicks outside the strategy menu to close it
   * @param {MouseEvent} event - The click event
   * @private
   */
    handleDocumentClick(event) {
      if (this.strategyMenu && !this.strategyMenu.contains(event.target) &&
        this.strategyButton && !this.strategyButton.contains(event.target)) {
        this.strategyMenu.classList.remove('show')
      }
    }

    /**
   * Clean up event listeners and resources
   */
    cleanup() {
      document.removeEventListener('click', this.handleDocumentClick)
      strategyState.off('change', this.onStrategyChanged)

      if (this.strategyButton) {
        const newButton = this.strategyButton.cloneNode(true)
        this.strategyButton.parentNode.replaceChild(newButton, this.strategyButton)
      }

      if (this.applyButton) {
        this.applyButton.removeEventListener('click', this.handleApplyClick)
      }

      this.isInitialized = false
    }
  }

  // Track if the strategy system is ready
  let isSystemReady = false
  const readyCallbacks = []

  /**
 * Initialize the strategy system when the DOM is ready
 * @returns {Promise<Object>} A promise that resolves when the system is ready
 */
  function initializeStrategySystem() {
    if (isSystemReady) {
      return Promise.resolve(window.StarCrypt.StrategyManager)
    }

    return new Promise((resolve) => {
      const init = () => {
        try {
        // Initialize the state first
          strategyState.init()

          // Then initialize the UI
          const strategyUI = new StrategySelectorUI()

          // Export the public API
          window.StarCrypt = window.StarCrypt || {}
          window.StarCrypt.StrategyManager = {
            getCurrentStrategy: () => strategyState.getCurrentStrategy(),
            setStrategy: (strategyId) => strategyState.setStrategy(strategyId),
            on: (event, callback) => strategyState.on(event, callback),
            off: (event, callback) => strategyState.off(event, callback),
            ready: (callback) => {
              if (isSystemReady) {
                callback()
              } else {
                readyCallbacks.push(callback)
              }
            },
            ui: strategyUI,
          }

          // Mark as ready and notify callbacks
          isSystemReady = true
          console.log('[Strategy] System initialized')

          // Notify any pending callbacks
          while (readyCallbacks.length) {
            const callback = readyCallbacks.shift()
            try {
              callback()
            } catch (e) {
              console.error('Error in ready callback:', e)
            }
          }

          resolve(window.StarCrypt.StrategyManager)
        } catch (error) {
          console.error('Failed to initialize strategy system:', error)
          // Still resolve to prevent hanging, but system may not be fully functional
          resolve(null)
        }
      }

      // If DOM is already loaded, initialize immediately
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init)
      } else {
      // Use setTimeout to ensure this runs after other DOMContentLoaded handlers
        setTimeout(init, 0)
      }
    })
  }

  // Clean up any existing globals
  function cleanupGlobals() {
    const oldGlobals = [
      'strategySelector',
      'updateStrategyMenu',
      'handleStrategySelect',
      'applyStrategy',
      'updateStrategyIndicators',
      'updateStrategyInfoPanel',
      'getStrategyColor',
      'getIndicatorColor',
    ]

    oldGlobals.forEach(global => {
      try {
        delete window[global]
      } catch (e) {
      // Ignore errors
      }
    })
  }

  // Clean up old globals when the script loads
  cleanupGlobals()

  // Initialize when the DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeStrategySystem)
  } else {
    initializeStrategySystem()
  }

  // Export for CommonJS/Node.js
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
      StrategyManager: window.StarCrypt?.StrategyManager,
      initializeStrategySystem,
    }
  }
})()
