/**
 * StarCrypt Signal Matrix Module
 *
 * Handles rendering and updating the signal matrix/grid, including the dedicated volume signal light row.
 * This module is responsible for displaying the current state of all indicators across different timeframes.
 * It works in conjunction with the strategy selector to show relevant indicators for the selected strategy.
 */

if (!window.hasInitializedSignalMatrixModuleScript) {
  window.hasInitializedSignalMatrixModuleScript = true;

  (function () {
    'use strict';

    // Ensure signalMatrixState is declared only once on the window object
    if (typeof window.signalMatrixState === 'undefined') {
      window.signalMatrixState = {
        isInitialized: false,
        container: null,
        indicators: [],
        timeframes: [],
        strategy: null,
        lastUpdateTime: 0, // This is a property, not a separate variable declaration
      };
    }

    // Create a local reference to signalMatrixState for easier access
    const signalMatrixState = window.signalMatrixState;

    // Default configuration
    const DEFAULT_TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d'];
    const DEFAULT_INDICATORS = [
      { name: 'RSI', timeframes: DEFAULT_TIMEFRAMES },
      { name: 'MACD', timeframes: DEFAULT_TIMEFRAMES },
      { name: 'BB', timeframes: DEFAULT_TIMEFRAMES },
      { name: 'Volume', timeframes: DEFAULT_TIMEFRAMES, isVolume: true },
    ];
    const DEFAULT_STRATEGY = 'admiral_toa'; // Default strategy ID
    let isCurrentlyExecutingUpdateSignalMatrixImpl = false; // Re-entrancy guard for updateSignalMatrixImpl
    const UPDATE_THROTTLE_MS = 1000; // Throttle updates to once per second

    // Performance optimization: Cache DOM elements
    const signalElementsCache = new Map();
    const indicatorUpdateTimes = new Map();

    // Error handling
    class SignalMatrixError extends Error {
      constructor(message, code = 'SIGNAL_MATRIX_ERROR') {
        super(message);
        this.name = 'SignalMatrixError';
        this.code = code;
      }
    }

    /**
     * Creates and initializes the signal matrix in the specified container.
     * @param {string} containerId - The ID of the container element
     * @param {string[]} [timeframes] - Array of timeframe strings (e.g., ['1m', '5m', '1h'])
     * @param {string[]} [enabledIndicators] - Array of enabled indicator names
     * @returns {boolean} True if successful, false otherwise
     */
    function createSignalMatrix(containerId, timeframes = DEFAULT_TIMEFRAMES, enabledIndicators = []) {
      try {
        console.log('[SignalMatrix] Creating signal matrix...');

        // Validate inputs
        if (!containerId) {
          throw new SignalMatrixError('Container ID is required', 'INVALID_CONTAINER');
        }

        // Ensure MATRIX_INDICATORS is available first
        if (typeof window.MATRIX_INDICATORS === 'undefined') {
          console.warn('[SignalMatrix] MATRIX_INDICATORS not found, using default indicators');
          window.MATRIX_INDICATORS = [...DEFAULT_INDICATORS];
        }

        // Get default timeframes from MATRIX_INDICATORS if not provided
        const defaultTimeframes = window.MATRIX_INDICATORS[0]?.timeframes || DEFAULT_TIMEFRAMES;

        // Store current timeframes
        window.signalMatrixState.timeframes = Array.isArray(timeframes) ? [...timeframes] : [...defaultTimeframes];

        // Validate timeframes
        if (!window.signalMatrixState.timeframes.length) {
          console.error('[SignalMatrix] No valid timeframes found, using default');
          window.signalMatrixState.timeframes = [...DEFAULT_TIMEFRAMES];
        }

        // Get or create container
        window.signalMatrixState.container = document.getElementById(containerId);
        if (!window.signalMatrixState.container) {
          console.warn(`[SignalMatrix] Container not found: ${containerId}, creating one...`);
          window.signalMatrixState.container = document.createElement('div');
          window.signalMatrixState.container.id = containerId;
          document.body.appendChild(window.signalMatrixState.container);
        }

        // Log signal matrix creation attempt
        console.log('Attempting to create signal matrix. Initialization state:', window.signalMatrixState);

        // Only initialize once
        if (window.signalMatrixState.isInitialized) {
          console.log('[SignalMatrix] Already initialized, skipping creation');
          return true;
        }

        window.signalMatrixState.isInitialized = true;

        // DO NOT Clear existing content
        // window.signalMatrixState.container.innerHTML = '';
        // window.signalMatrixState.container.className = 'signal-matrix'; // Assuming class is already on #momentum-indicators or not needed
        // window.signalMatrixState.container.setAttribute('data-initialized', 'true'); // Use a different attribute to show script is attached if needed

        // DO NOT Add loading state
        // window.signalMatrixState.container.innerHTML = '<div class="loading">Initializing signal matrix...</div>';

        // DO NOT Clear existing content
        // signalMatrixState.container.innerHTML = '';
        // signalMatrixState.container.className = 'signal-matrix'; // Assuming class is already on #momentum-indicators or not needed
        // signalMatrixState.container.setAttribute('data-initialized', 'true'); // Use a different attribute to show script is attached if needed

        // DO NOT Add loading state
        // signalMatrixState.container.innerHTML = '<div class="loading">Initializing signal matrix...</div>';

        // DO NOT Create the matrix structure here - it should already exist
        // const matrixWrapper = document.createElement('div');
        // matrixWrapper.className = 'signal-matrix-wrapper';

        // DO NOT Create header row - it should already exist if part of the matrix
        // const headerRow = document.createElement('div');
        // headerRow.className = 'signal-row header';

        // DO NOT Add a title for the matrix - it should already exist
        // const titleRow = document.createElement('div');
        // titleRow.className = 'signal-matrix-title';
        // titleRow.textContent = 'Signal Matrix';
        // titleRow.title = 'Shows the current state of indicators across different timeframes';

        // DO NOT Add refresh button or append to non-existent titleRow/matrixWrapper
        // const refreshButton = document.createElement('button');
        // refreshButton.className = 'refresh-button';
        // refreshButton.innerHTML = '🔄';
        // refreshButton.title = 'Refresh signal matrix';
        // refreshButton.onclick = () => updateSignalMatrix();
        // titleRow.appendChild(refreshButton);
        // matrixWrapper.appendChild(titleRow);

        // DO NOT Add indicator label cell or append to non-existent headerRow
        // const labelCell = document.createElement('div');
        // labelCell.className = 'signal-label';
        // labelCell.textContent = 'Indicators';
        // labelCell.title = 'Indicator names';
        // headerRow.appendChild(labelCell);

        // DO NOT Add help text
        // const helpText = document.createElement('div');
        // helpText.className = 'help-text';
        // helpText.textContent = 'Hover over cells for details. Colors indicate signal strength.';
        // helpText.style.gridColumn = `1 / ${signalMatrixState.timeframes.length + 1}`; // Span all columns

        // DO NOT Add timeframe cells or append to non-existent headerRow
        // signalMatrixState.timeframes.forEach((tf, index) => {
        //   const cell = document.createElement('div');
        //   cell.className = 'signal-cell timeframe';
        //   cell.textContent = tf;
        //   cell.dataset.timeframe = tf;
        //   cell.title = `Timeframe: ${tf}`;
        //   cell.style.gridColumn = index + 2; // +1 for the label column
        //   headerRow.appendChild(cell);
        // });

        // DO NOT Add header row or help text to non-existent matrixWrapper
        // matrixWrapper.appendChild(headerRow);
        // matrixWrapper.appendChild(helpText);

        // DO NOT Add the matrix structure to the container
        // signalMatrixState.container.appendChild(matrixWrapper);

        // DO NOT Create a container for indicator rows or append to non-existent matrixWrapper
        // const rowsContainer = document.createElement('div');
        // rowsContainer.className = 'signal-rows-container';
        // matrixWrapper.appendChild(rowsContainer);

        // DO NOT Store reference to a newly created rows container
        // signalMatrixState.container.rowsContainer = rowsContainer;
        // Instead, updateIndicatorRows should find existing rows within signalMatrixState.container

        // Create rows for each indicator
        updateIndicatorRows();

        // Initial update of the matrix
        updateSignalMatrix();

        // Set up auto-refresh
        setupAutoRefresh();

        console.log('[SignalMatrix] Initialized successfully');

        // Mark as initialized
        signalMatrixState.isInitialized = true;
        return true;
      } catch (error) {
        console.error('[SignalMatrix] Initialization error:', error);
        // Don't show error UI, just log to console
        if (signalMatrixState.container) {
          signalMatrixState.container.innerHTML = ''; // Clear any existing content
          signalMatrixState.container.style.display = 'none'; // Hide the container
        }
        return false;
      }
    }

    /**
 * Creates a row for the signal matrix
 * @param {string} indicator - The indicator name
 * @param {string[]} timeframes - Array of timeframes
 * @param {boolean} [isVolumeRow=false] - Whether this is a volume row
 * @returns {HTMLElement|null} The created row element or null if invalid
 */
    function createSignalRow(indicator, timeframes, isVolumeRow = false) {
      try {
        if (!indicator || !timeframes?.length) {
          console.error('Invalid arguments for createSignalRow:', { indicator, timeframes });
          return null;
        }

        const row = document.createElement('div');
        row.className = `signal-row ${isVolumeRow ? 'volume-row' : ''}`;
        row.dataset.indicator = indicator;

        try {
          // Add indicator label
          const labelCell = document.createElement('div');
          labelCell.className = 'signal-label';
          labelCell.textContent = typeof indicator === 'string' ?
            indicator.charAt(0).toUpperCase() + indicator.slice(1) :
            String(indicator);
          row.appendChild(labelCell);

          // Add signal cells for each timeframe
          timeframes.forEach(tf => {
            try {
              const cell = document.createElement('div');
              cell.className = 'signal-cell';
              cell.dataset.timeframe = tf;

              const signal = document.createElement('div');
              signal.className = 'signal-circle';
              signal.dataset.indicator = indicator;
              signal.dataset.timeframe = tf;
              signal.dataset.state = 'neutral';
              signal.dataset.id = `signal-${indicator}-${tf}`.toLowerCase();

              // Set a unique ID for the signal element
              signal.id = signal.dataset.id;

              // Add tooltip
              signal.title = `${indicator} (${tf}) - No signal`;

              // Add ARIA attributes for accessibility
              signal.setAttribute('role', 'status');
              signal.setAttribute('aria-live', 'polite');
              signal.setAttribute('aria-atomic', 'true');

              cell.appendChild(signal);
              row.appendChild(cell);

              // Register the signal element immediately if signalInitializer is available
              if (window.signalInitializer) {
                window.signalInitializer.registerSignalElement(signal);
              }
            } catch (cellError) {
              console.error(`Error creating signal cell for ${indicator} (${tf}):`, cellError);
            }
          });

          return row;
        } catch (rowError) {
          console.error(`Error creating signal row for ${indicator}:`, rowError);
          return null;
        }
      } catch (error) {
        console.error('Unexpected error in createSignalRow:', error);
        return null;
      }
    }

    /**
 * --- SIGNAL MATRIX UPDATE ---
 *
 * This section handles updating the signal matrix based on the current state of indicators
 * and the selected strategy. It ensures that only relevant indicators are shown and that
 * their states are accurately reflected in the UI.
 */

    /**
 * Updates the signal matrix for a specific strategy
 * @param {string} strategyId - The ID of the strategy to update for
 * @param {boolean} [force=false] - Whether to force an update even if the strategy hasn't changed
 */
    function updateForStrategy(strategyId, force = false) {
      try {
        if (!strategyId) {
          console.warn('[SignalMatrix] No strategy ID provided, using default');
          strategyId = DEFAULT_STRATEGY;
        }

        const now = Date.now();
        // Use signalMatrixState for currentStrategy and lastUpdateTime
        if (!force && signalMatrixState.strategy === strategyId && (now - (signalMatrixState.lastUpdateTime || 0)) < UPDATE_THROTTLE_MS) {
          console.log(`[SignalMatrix] Throttling update for ${strategyId}. Last update: ${now - (signalMatrixState.lastUpdateTime || 0)}ms ago.`);
          return;
        }

        console.log(`[SignalMatrix] Attempting to update for strategy: ${strategyId}${force ? ' (forced)' : ''}`);

        // Only update if the strategy has changed or if forced
        if (!force && signalMatrixState.strategy === strategyId) {
          console.log('[SignalMatrix] Strategy unchanged, skipping full update. Forcing a view refresh.');
          updateSignalMatrix(); // Still call to refresh view in case data changed
          return;
        }

        // Strategy is changing or update is forced
        const previousStrategy = signalMatrixState.strategy;
        // signalMatrixState.strategy will be updated by updateIndicatorRows
        signalMatrixState.lastUpdateTime = now;

        console.log(`[SignalMatrix] Strategy changed from ${previousStrategy} to ${strategyId} or update forced.`);

        // Update the active indicators list based on the new strategy
        updateIndicatorRows(); // This will set signalMatrixState.strategy and signalMatrixState.indicators

        // Now update the DOM based on the new state
        updateSignalMatrix(); // This calls the debounced updateSignalMatrixImpl

        console.log(`[SignalMatrix] Update process initiated for strategy: ${strategyId} with ${signalMatrixState.indicators?.length || 0} indicators.`);
      } catch (error) {
        console.error('[SignalMatrix] Error updating for strategy:', error);
        // Try to recover by falling back to default strategy
        if (strategyId !== DEFAULT_STRATEGY) {
          console.warn('[SignalMatrix] Falling back to default strategy due to error.');
          updateForStrategy(DEFAULT_STRATEGY, true); // Force update to default
        } else {
          // If we're already on the default strategy and it failed, show error
          showError('Failed to update signal matrix even on default strategy. Please refresh the page.');
        }
      }
    }

    /**
 * Updates the signal matrix implementation
 * @private
 */
    function updateSignalMatrixImpl() {
      if (isCurrentlyExecutingUpdateSignalMatrixImpl) {
        console.warn('[SignalMatrix] updateSignalMatrixImpl called re-entrantly. Skipping this call.');
        return;
      }
      isCurrentlyExecutingUpdateSignalMatrixImpl = true;
      try {
        if (!signalMatrixState.container) {
          console.error('[SignalMatrix] Main container (#momentum-indicators) not found in state. Cannot update matrix.');
          return;
        }

        const activeIndicators = signalMatrixState.indicators || [];
        const timeframes = signalMatrixState.timeframes || [];

        // Select all potential indicator rows within the #momentum-indicators container
        // This assumes your rows are TR elements with IDs like 'indicator-row-rsi', 'indicator-row-macd', etc.
        const allIndicatorRows = signalMatrixState.container.querySelectorAll('tr[id^="indicator-row-"]');

        allIndicatorRows.forEach(rowElement => {
          const rowId = rowElement.id;
          // Extract indicator name from ID, e.g., 'rsi' from 'indicator-row-rsi'
          const indicatorNameFromId = rowId.replace('indicator-row-', '');

          if (activeIndicators.includes(indicatorNameFromId)) {
            // This indicator is active for the current strategy, ensure row is visible
            rowElement.style.display = ''; // Or remove a 'hidden' class if you use one

            timeframes.forEach(timeframe => {
              const signalCircleId = `signal-${indicatorNameFromId}-${timeframe.toLowerCase()}`;
              const signalElement = document.getElementById(signalCircleId);

              if (signalElement) {
                // --- Placeholder for fetching live data and applying 5-color logic ---
                // Example: const signalData = getLatestSignalData(indicatorNameFromId, timeframe);
                // Example: const signalState = determineSignalState(signalData); // e.g., 'buy', 'sell', 'neutral', 'strong-buy', 'strong-sell'
                const signalState = 'neutral'; // Default to neutral for now

                // Update visual state (CSS classes)
                signalElement.className = 'signal-circle'; // Reset classes
                signalElement.classList.add(`signal-${signalState}`); // Add state-specific class, e.g., 'signal-neutral', 'signal-buy'

                // Update data attribute for tooltip or other logic
                signalElement.dataset.state = signalState;
                signalElement.title = `${indicatorNameFromId.toUpperCase()} (${timeframe}) - ${signalState}`;
                // --- End Placeholder ---
              } else {
                // console.warn(`[SignalMatrix] Signal element not found: ${signalCircleId} for row ${rowId}`);
              }
            });
          } else {
            // This indicator is NOT active for the current strategy, hide the row
            rowElement.style.display = 'none'; // Or add a 'hidden' class
          }
        });

        // If no active indicators, you might want to show a message in #momentum-indicators
        if (activeIndicators.length === 0 && allIndicatorRows.length > 0) {
          // Check if a 'no indicators' message element exists, if not, create and append it (carefully, only if it's truly not there)
          const noIndicatorsMsg = signalMatrixState.container.querySelector('.no-indicators-message');
          if (!noIndicatorsMsg) {
            // This is an exception to 'no DOM creation' as it's a status message within the container
            // Ensure this is acceptable or handle it purely with CSS on existing elements.
            // console.warn('[SignalMatrix] No active indicators. Consider adding a placeholder message if appropriate.');
          }
        } else {
          const noIndicatorsMsg = signalMatrixState.container.querySelector('.no-indicators-message');
          if (noIndicatorsMsg) {
            // noIndicatorsMsg.remove(); // Remove if indicators are now active
          }
        }
        // console.log('[SignalMatrix] Matrix display updated.');
      } catch (error) {
        console.error('[SignalMatrix] Critical error during updateSignalMatrixImpl execution:', error);
        // Depending on the desired behavior, you might want to re-throw or handle more gracefully.
      } finally {
        isCurrentlyExecutingUpdateSignalMatrixImpl = false;
      }
    }

    // Debounced version of updateSignalMatrix
    const debouncedUpdateSignalMatrix = debounce(updateSignalMatrixImpl, 100);

    // Public function to update the signal matrix
    function updateSignalMatrix() {
      debouncedUpdateSignalMatrix();
    }

    /**
 * Updates the signal matrix with the given indicators
 * @param {string[]} indicators - Array of indicator names to display in the matrix
 */
    function updateMatrix(indicators) {
      console.warn('[SignalMatrix] updateMatrix function was called. This function is deprecated and should not be performing DOM updates. DOM updates are now handled by updateSignalMatrixImpl. Indicators received:', indicators);
      // The original body of this function, which performed DOM manipulation, has been commented out
      // to prevent conflicts with the new updateSignalMatrixImpl logic.
      /*
  try {
    if (!signalMatrixContainer) { // Should be signalMatrixState.container
      console.warn('[SignalMatrix] Container not initialized');
      return;
    }

    // Clear existing rows
    // This DOM manipulation is problematic and conflicts with updateSignalMatrixImpl
    if (signalMatrixContainer.rowsContainer) { // rowsContainer concept might be outdated
      signalMatrixContainer.rowsContainer.innerHTML = '';
    } else {
      console.warn('[SignalMatrix] Rows container not found');
      return;
    }

    // Create rows for each indicator
    // This DOM creation is problematic
    indicators.forEach(indicator => {
      try {
        const row = createSignalRow(indicator, currentTimeframes, indicator === 'volume'); // currentTimeframes is undefined
        if (row && signalMatrixContainer.rowsContainer) {
          signalMatrixContainer.rowsContainer.appendChild(row);
        }
      } catch (e) {
        console.error(`[SignalMatrix] Error creating row for ${indicator}:`, e);
      }
    });

    console.log(`[SignalMatrix] Matrix updated with ${indicators.length} indicators`);

  } catch (error) {
    console.error('[SignalMatrix] Error in updateMatrix:', error);
    showError(`Failed to update matrix: ${error.message}`);
  }
  */
    }

    /**
 * Simple debounce implementation
 * @param {Function} func - Function to debounce
 * @param {number} wait - Debounce delay in ms
 * @returns {Function} Debounced function
 */
    function debounce(func, wait) {
      let timeout;
      return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
      };
    }

    /**
 * Logs error messages to console
 * @param {string} message - Error message to log
 */
    function showError(message) {
      console.error('[SignalMatrix]', message);
      // No UI updates, just log to console
    }

    /**
 * Gets indicators specific to a strategy
 * @param {string} strategyId - Strategy ID
 * @returns {string[]} Array of indicator names
 */
    function getStrategySpecificIndicators(strategyId) {
      const strategy = window.TRADING_STRATEGIES?.[strategyId];
      if (!strategy || !strategy.indicators) {
        return [];
      }

      // Return a copy of the indicators array
      return [...(strategy.indicators || [])];
    }

    /**
 * Initialize the signal matrix when the DOM is ready
 */
    function initializeSignalMatrix() {
      if (signalMatrixState.isInitialized) {
        console.log('[SignalMatrix] Already initialized');
        return;
      }

      console.log('[SignalMatrix] Initializing signal matrix...');

      // Default timeframes if not provided
      const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d'];

      // Create the signal matrix
      createSignalMatrix('momentum-indicators', timeframes, []);

      // Update for the current strategy
      const strategyId = window.currentStrategy || 'admiral_toa';
      updateForStrategy(strategyId);

      console.log('[SignalMatrix] Signal matrix initialized');
    }

    /**
 * Initialize the signal matrix module
 */
    function initializeModule() {
      try {
        // Only initialize once
        if (window.signalMatrixInitialized) {
          console.log('[SignalMatrix] Module already initialized');
          return;
        }

        console.log('[SignalMatrix] Initializing module...');

        // Create debounced versions of update functions
        const updateSignalMatrixDebounced = debounce(updateSignalMatrix, 500);

        // Make functions globally available
        window.updateSignalMatrixDebounced = updateSignalMatrixDebounced;
        window.updateSignalMatrix = updateSignalMatrix;
        window.initializeSignalMatrix = initializeSignalMatrix;
        window.updateForStrategy = updateForStrategy;

        // Mark as initialized
        window.signalMatrixInitialized = true;

        console.log('[SignalMatrix] Module initialized');

        // Initialize when the DOM is ready
        const init = () => {
          try {
            console.log('[SignalMatrix] DOM ready, initializing UI...');
            initializeSignalMatrix();

            // Listen for strategy changes
            window.addEventListener('strategyChanged', (event) => {
              if (event.detail?.strategyId) {
                console.log('[SignalMatrix] Detected strategy change to:', event.detail.strategyId);
                updateForStrategy(event.detail.strategyId);
              }
            });

            console.log('[SignalMatrix] UI initialization complete');
          } catch (error) {
            console.error('[SignalMatrix] Error during UI initialization:', error);
          }
        };

        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', init);
        } else {
          // DOM already loaded, initialize with a small delay
          setTimeout(init, 100);
        }
      } catch (error) {
        console.error('[SignalMatrix] Failed to initialize module:', error);
      }
    }

    /**
 * Set up automatic refresh of the signal matrix
 */
    /**
 * Updates the indicator rows in the matrix based on the current strategy
 */
    function updateIndicatorRows() {
      // This function should now only determine which indicators are active for the current strategy
      // and update signalMatrixState.indicators. It should NOT modify the DOM.

      if (!signalMatrixState.container) {
        console.error('[SignalMatrix] Main container (#momentum-indicators) not found in state. Cannot determine active indicators.');
        return;
      }

      try {
        const strategyId = window.currentStrategy || DEFAULT_STRATEGY;
        const indicatorsForStrategy = getStrategySpecificIndicators(strategyId);

        if (!indicatorsForStrategy?.length) {
          console.warn('[SignalMatrix] No indicators found for strategy:', strategyId);
          signalMatrixState.indicators = []; // Clear active indicators
          signalMatrixState.strategy = strategyId;
          // updateSignalMatrixImpl will handle hiding all rows or showing a 'no indicators' message if necessary
          return;
        }

        // Store current indicators based on strategy
        // Ensure MATRIX_INDICATORS is defined or handled if used as a fallback
        let defaultIndicators = [];
        if (typeof window.MATRIX_INDICATORS !== 'undefined' && Array.isArray(window.MATRIX_INDICATORS)) {
          defaultIndicators = [...window.MATRIX_INDICATORS];
        }
        signalMatrixState.indicators = Array.isArray(indicatorsForStrategy) ? [...indicatorsForStrategy] : defaultIndicators;
        signalMatrixState.strategy = strategyId;

        console.log(`[SignalMatrix] Active indicators for strategy '${strategyId}':`, signalMatrixState.indicators);
      } catch (error) {
        console.error('[SignalMatrix] Error determining active indicators:', error);
        // Do not throw, allow updateSignalMatrixImpl to proceed with potentially empty/stale indicators
      }
    }

    /**
 * Set up automatic refresh of the signal matrix
 */
    function setupAutoRefresh() {
      try {
        // Clear any existing interval
        if (window.signalMatrixRefreshInterval) {
          clearInterval(window.signalMatrixRefreshInterval);
        }

        // Instead of auto-refresh, listen for SignalManager updates
        if (window.StarCrypt && window.StarCrypt.SignalManager) {
          const signalManager = window.StarCrypt.SignalManager;
          if (typeof signalManager.updateSignal === 'function') {
            // Listen for signal updates
            console.warn('[SignalMatrix] Temporarily bypassing SignalManager event listener in setupAutoRefresh for debugging recursion.');
            /*
        signalManager.addEventListener('update', () => {
          try {
            updateSignalMatrix();
          } catch (error) {
            console.error('[SignalMatrix] Error updating from SignalManager:', error);
          }
        });
        */
          } // Closes: if (typeof signalManager.updateSignal === 'function')
        } // Closes: if (window.StarCrypt && window.StarCrypt.SignalManager)
      } catch (error) { // This is the catch for the main try block in setupAutoRefresh
        console.error('[SignalMatrix] Error setting up auto-refresh:', error);
      }
    }

    // Make the initializeSignalMatrix function globally available
    window.initializeSignalMatrix = function() {
      try {
        if (!signalMatrixState.isInitialized) {
          return initializeModule();
        }
        return true;
      } catch (error) {
        console.error('[SignalMatrix] Error in initializeSignalMatrix:', error);
        return false;
      }
    };
    
    // Mark this script as loaded
    window.hasInitializedSignalMatrixModuleScript = true;
    
    // Auto-initialize the module when the DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        if (!signalMatrixState.isInitialized) {
          initializeModule();
        }
      });
    } else if (!signalMatrixState.isInitialized) {
      // If DOM is already loaded, initialize immediately
      setTimeout(initializeModule, 100);
    }
  })(); // IIFE Closer
}
