// Extracted from index.html <script> block and inline JS
// <PERSON>les strategy logic, initialization, and helper functions for StarCrypt

// StarCrypt Strategy Logic Module
// Extracted and modularized from index.html

// --- GLOBALS ---
window.TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
window.LOW_TIMEFRAMES = ['1s', '5s', '15s', '1m', '4m', '24m', '168m'];
window.useLowTimeframes = false;
window.TIMEFRAME_SECONDS = {
  '1s': 1, '5s': 5, '15s': 15, '1m': 60, '4m': 240, '24m': 1440, '168m': 10080,
  '5m': 300, '15m': 900, '1h': 3600, '4h': 14400, '1d': 86400, '1w': 604800
};
window.currentTf = '1h';
window.INDICATORS = {
  momentum: ['rsi', 'stochRsi', 'williamsR', 'ultimateOscillator', 'mfi'],
  trend: ['macd', 'bollingerBands', 'adx', 'atr', 'vwap', 'fractal'],
  volume: ['volume'],
  ml: ['ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly']
};
window.helperOrder = ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx'];
window.currentStrategy = 'admiral_toa';
window.currentPair = 'xbtusdt';

// --- STRATEGY DEFINITIONS (FULL) ---
window.TRADING_STRATEGIES = {
  admiral_toa: {
    name: 'Admiral T.O.A. Convergence',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'The flagship strategy that combines momentum indicators (RSI, Stoch RSI) with trend confirmation (MACD), price position (Bollinger Bands), trend strength (ADX), volume analysis, and ML confirmation for high-confidence signals.',
    helperText: `<p><strong>Step 1: Check Momentum (<span class="indicator">RSI14</span> & <span class="indicator">Stoch RSI</span>)</strong></p>
<p>- Look for <span class="indicator">RSI14</span> > <span class="condition-sell">70 (overbought)</span> or < <span class="condition-buy">30 (oversold)</span>. Confidence: ~65% for reversals.</p>
<p>- Confirm with <span class="indicator">Stoch RSI</span> > <span class="condition-sell">80 (overbought)</span> or < <span class="condition-buy">20 (oversold)</span>. Combined Confidence: ~70%.</p>
<p><strong>Step 2: Confirm with <span class="indicator">Bollinger Bands</span> & <span class="indicator">ATR</span></strong></p>
<p>- Price near the upper band (overbought) or lower band (oversold) with high <span class="indicator">ATR</span> (>1% of price) increases reversal likelihood. Confidence: ~75% when combined with RSI/Stoch RSI.</p>
<p><strong>Step 3: Look for <span class="indicator">MACD</span> Convergence (Shield Logic)</strong></p>
<p>- <span class="indicator">MACD</span> crossing above signal line (bullish) or below (bearish) with RSI/Stoch RSI confirmation. (Shield) blocks trades against trend. Confidence: ~80% for strong signals.</p>
<p><strong>Step 4: Additional Confirmation (<span class="indicator">Williams %R</span>, <span class="indicator">UltOscillator</span>, <span class="indicator">MFI</span>)</strong></p>
<p>- <span class="indicator">Williams %R</span>, <span class="indicator">UltOscillator</span>, and <span class="indicator">MFI</span> aligning with RSI/Stoch RSI (e.g., all overbought) boosts confidence. Confidence: ~85% with 4+ indicators.</p>
<p><strong>Step 5: Check Trend Strength (<span class="indicator">ADX</span>)</strong></p>
<p>- <span class="indicator">ADX</span> > 25 confirms a strong trend, increasing trade reliability. Final convergence confidence: ~90% with all steps aligned.</p>
<p><strong>Step 6: Advanced Trend Confirmation (<span class="indicator">VWAP</span> & <span class="indicator">Fractal</span>)</strong></p>
<p>- <span class="indicator">VWAP</span> position relative to price and <span class="indicator">Fractal</span> patterns provide additional trend confirmation. Confidence: ~90% when aligned with other indicators.</p>
<p><strong>Step 7: Volume & <span class="indicator">ML</span> Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> spike (>1.5x 14-period avg) and <span class="indicator">ML</span> prediction alignment (confidence >70%) add final confirmation. Confidence: ~92% with volume and ML support.</p>
<p><strong>Step 8: Advanced ML Analysis (<span class="indicator">Sentiment</span>, <span class="indicator">Entropy</span>, <span class="indicator">Correlation</span>, <span class="indicator">Time Anomaly</span>)</strong></p>
<p>- Advanced ML indicators provide deep market insights beyond traditional technical analysis. When all ML indicators align, confidence reaches ~95%.</p>
<p><strong>Tip:</strong> Wait for at least 4 indicators to align (e.g., RSI, Stoch RSI, Bollinger Bands, MACD) for a high-probability trade. The Admiral T.O.A. chat will alert you to strong convergences with confidence percentages!</p>`,
    color: '#00FFFF'
  },
    neural_network_navigator: {
    name: 'AI Neural Network Navigator',
    indicators: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'Advanced AI strategy that uses neural networks to identify complex market patterns and predict price movements with high accuracy by combining traditional indicators with machine learning signals.',
    helperText: `<p><strong>Step 1: AI Pattern Recognition (<span class="indicator">ML</span>)</strong></p>
<p>- <span class="indicator">ML</span> model analyzes historical price patterns to identify high-probability setups with <span class="condition-trend">confidence score >75%</span>. Base confidence: ~80%.</p>
<p><strong>Step 2: Sentiment Analysis (<span class="indicator">Sentiment</span>)</strong></p>
<p>- <span class="indicator">Sentiment</span> indicator measures market mood from social media and news sources. <span class="condition-buy">Positive sentiment >65%</span> or <span class="condition-sell">negative sentiment >65%</span> confirms direction. Confidence: ~85% when aligned with ML.</p>
<p><strong>Step 3: Technical Confirmation (<span class="indicator">RSI</span> & <span class="indicator">MACD</span>)</strong></p>
<p>- <span class="indicator">RSI</span> and <span class="indicator">MACD</span> must align with AI predictions to filter false signals. Confidence: ~88% with technical confirmation.</p>
<p><strong>Step 4: Volatility Assessment (<span class="indicator">Bollinger Bands</span>)</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> width indicates market volatility. Narrow bands before expansion signal potential breakouts. Confidence: ~90% with volatility confirmation.</p>
<p><strong>Step 5: Advanced AI Metrics (<span class="indicator">Entropy</span> & <span class="indicator">Correlation</span>)</strong></p>
<p>- <span class="indicator">Entropy</span> measures market randomness while <span class="indicator">Correlation</span> tracks relationships with other assets. Low entropy and diverging correlations signal high-confidence setups. Confidence: ~92%.</p>
<p><strong>Step 6: Time Series Analysis (<span class="indicator">Time Anomaly</span>)</strong></p>
<p>- <span class="indicator">Time Anomaly</span> detector identifies unusual market behavior across multiple timeframes. When anomalies align with predictions, confidence reaches ~95%.</p>
<p><strong>Tip:</strong> Neural Network Navigator works best when at least 3 AI indicators (ML, Sentiment, Entropy, Correlation, Time Anomaly) align with traditional technical indicators. The AI confidence score in the helper panel shows overall signal strength!</p>`,
    color: '#8A2BE2'
  },
  deep_learning_diver: {
    name: 'AI Deep Learning Diver',
    indicators: ['rsi', 'stochRsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation'],
    description: 'Leverages deep learning algorithms to analyze market microstructure and order flow patterns, identifying hidden support/resistance levels and liquidity zones for precise entries and exits.',
    helperText: `<p><strong>Step 1: Order Flow Analysis (<span class="indicator">ML</span> & <span class="indicator">Volume</span>)</strong></p>
<p>- <span class="indicator">ML</span> model analyzes order flow patterns to identify institutional buying/selling pressure. <span class="indicator">Volume</span> spikes >2x average confirm significant moves. Confidence: ~82%.</p>
<p><strong>Step 2: Liquidity Detection (<span class="indicator">ML</span>)</strong></p>
<p>- AI identifies hidden liquidity zones where stop losses are clustered. <span class="condition-buy">Buy zones</span> and <span class="condition-sell">sell zones</span> marked on chart. Confidence: ~85% for trades targeting these zones.</p>
<p><strong>Step 3: Momentum Confirmation (<span class="indicator">RSI</span> & <span class="indicator">Stoch RSI</span>)</strong></p>
<p>- <span class="indicator">RSI</span> and <span class="indicator">Stoch RSI</span> must show early momentum shift in predicted direction. Confidence: ~88% with momentum alignment.</p>
<p><strong>Step 4: Trend Validation (<span class="indicator">MACD</span>)</strong></p>
<p>- <span class="indicator">MACD</span> histogram expansion confirms trend strength in predicted direction. Confidence: ~90% with trend confirmation.</p>
<p><strong>Step 5: Market Sentiment (<span class="indicator">Sentiment</span>)</strong></p>
<p>- <span class="indicator">Sentiment</span> analysis from news and social media provides context for AI predictions. Confidence: ~92% when sentiment aligns.</p>
<p><strong>Step 6: Cross-Asset Validation (<span class="indicator">Correlation</span>)</strong></p>
<p>- <span class="indicator">Correlation</span> analysis with related assets confirms or warns against potential trades. Confidence: ~95% with cross-asset confirmation.</p>
<p><strong>Tip:</strong> Deep Learning Diver excels at finding optimal entry and exit points within established trends. The AI liquidity detector highlights zones where price is likely to reverse or accelerate. Wait for at least 4 indicators to align before entering trades!</p>`,
    color: '#1E90FF'
  },
  ai_pattern_prophet: {
    name: 'AI Pattern Prophet',
    indicators: ['bollingerBands', 'macd', 'adx', 'ml', 'entropy', 'time_anomaly', 'fractal'],
    description: 'Uses advanced pattern recognition algorithms to identify complex chart patterns and fractals before they complete, providing early entries into emerging trends with precise risk management levels.',
    helperText: `<p><strong>Step 1: Pattern Identification (<span class="indicator">ML</span> & <span class="indicator">Fractal</span>)</strong></p>
<p>- <span class="indicator">ML</span> model recognizes over 50 chart patterns with <span class="condition-trend">75%+ accuracy</span>. <span class="indicator">Fractal</span> analysis confirms pattern validity. Confidence: ~80%.</p>
<p><strong>Step 2: Pattern Completion Probability (<span class="indicator">ML</span>)</strong></p>
<p>- AI calculates probability of pattern completion and projects price targets. Patterns with >80% completion probability have highest confidence. Confidence: ~85%.</p>
<p><strong>Step 3: Volatility Context (<span class="indicator">Bollinger Bands</span>)</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> provide volatility context for pattern development. Confidence: ~88% when pattern forms with appropriate volatility.</p>
<p><strong>Step 4: Trend Strength (<span class="indicator">ADX</span> & <span class="indicator">MACD</span>)</strong></p>
<p>- <span class="indicator">ADX</span> > 20 confirms trend strength while <span class="indicator">MACD</span> confirms momentum direction. Confidence: ~90% with strong trend.</p>
<p><strong>Step 5: Market Randomness (<span class="indicator">Entropy</span>)</strong></p>
<p>- <span class="indicator">Entropy</span> measures market randomness. Low entropy periods (<30%) indicate structured market behavior ideal for pattern trading. Confidence: ~92%.</p>
<p><strong>Step 6: Temporal Analysis (<span class="indicator">Time Anomaly</span>)</strong></p>
<p>- <span class="indicator">Time Anomaly</span> detector identifies optimal timing for pattern completion based on historical data. Confidence: ~95% when time anomalies align with pattern projections.</p>
<p><strong>Tip:</strong> Pattern Prophet excels at identifying high-probability reversal and continuation patterns before they become obvious to most traders. The AI pattern scanner highlights emerging patterns with completion probability percentages. Wait for pattern confirmation before entering trades!</p>`,
    color: '#FF4500'
  },
  machine_learning_momentum: {
    name: 'AI ML Momentum Master',
    indicators: ['rsi', 'macd', 'williamsR', 'ml', 'volume', 'sentiment', 'correlation'],
    description: 'Combines traditional momentum indicators with machine learning algorithms to identify the strongest trending assets with precise entry timing, optimized for riding medium to long-term trends.',
    helperText: `<p><strong>Step 1: Trend Strength Analysis (<span class="indicator">ML</span>)</strong></p>
<p>- <span class="indicator">ML</span> model analyzes over 30 trend metrics to identify assets with strongest momentum. Trend strength score >75 indicates high-confidence setup. Confidence: ~80%.</p>
<p><strong>Step 2: Entry Timing (<span class="indicator">RSI</span> & <span class="indicator">Williams %R</span>)</strong></p>
<p>- <span class="indicator">RSI</span> and <span class="indicator">Williams %R</span> identify optimal entry points within strong trends. Look for <span class="condition-buy">RSI crossing above 40</span> in uptrends or <span class="condition-sell">below 60</span> in downtrends. Confidence: ~85%.</p>
<p><strong>Step 3: Trend Confirmation (<span class="indicator">MACD</span>)</strong></p>
<p>- <span class="indicator">MACD</span> histogram expansion confirms increasing momentum in trend direction. Confidence: ~88% with strong MACD signal.</p>
<p><strong>Step 4: Volume Analysis (<span class="indicator">Volume</span>)</strong></p>
<p>- <span class="indicator">Volume</span> should increase in trend direction. Volume > 1.5x average confirms strong trend. Confidence: ~90% with volume confirmation.</p>
<p><strong>Step 5: Sentiment Validation (<span class="indicator">Sentiment</span>)</strong></p>
<p>- <span class="indicator">Sentiment</span> analysis confirms if public sentiment aligns with trend direction. Confidence: ~92% when sentiment supports trend.</p>
<p><strong>Step 6: Relative Strength (<span class="indicator">Correlation</span>)</strong></p>
<p>- <span class="indicator">Correlation</span> analysis identifies assets outperforming their correlated markets. Assets with positive divergence have highest trend potential. Confidence: ~95%.</p>
<p><strong>Tip:</strong> ML Momentum Master excels at identifying assets with the strongest trend potential and optimal entry points. The AI momentum scanner ranks assets by trend strength and highlights ideal entry zones. Focus on trades with at least 4 aligned indicators for highest probability setups!</p>`,
    color: '#32CD32'
  },
  sentiment_analysis_surfer: {
    name: 'AI Sentiment Analysis Surfer',
    indicators: ['rsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
    description: 'Harnesses natural language processing to analyze social media, news, and on-chain data to identify shifts in market sentiment before they impact price, perfect for anticipating major trend reversals.',
    helperText: `<p><strong>Step 1: Sentiment Analysis (<span class="indicator">Sentiment</span>)</strong></p>
<p>- <span class="indicator">Sentiment</span> AI analyzes millions of social media posts, news articles, and on-chain metrics to gauge market mood. Sentiment score >70 (bullish) or <30 (bearish) indicates strong bias. Confidence: ~80%.</p>
<p><strong>Step 2: Sentiment Divergence (<span class="indicator">Sentiment</span> & <span class="indicator">ML</span>)</strong></p>
<p>- <span class="indicator">ML</span> model identifies divergences between sentiment and price action. <span class="condition-buy">Bullish divergence</span> (negative sentiment + oversold price) or <span class="condition-sell">bearish divergence</span> (positive sentiment + overbought price) signals potential reversals. Confidence: ~85%.</p>
<p><strong>Step 3: Social Volume Analysis (<span class="indicator">Volume</span>)</strong></p>
<p>- Social mention <span class="indicator">Volume</span> spikes often precede major price moves. Volume >2x average indicates significant interest. Confidence: ~88% with volume confirmation.</p>
<p><strong>Step 4: Technical Confirmation (<span class="indicator">RSI</span> & <span class="indicator">MACD</span>)</strong></p>
<p>- <span class="indicator">RSI</span> and <span class="indicator">MACD</span> must show early signs of reversal that align with sentiment signals. Confidence: ~90% with technical confirmation.</p>
<p><strong>Step 5: Market Noise Filtering (<span class="indicator">Entropy</span>)</strong></p>
<p>- <span class="indicator">Entropy</span> measures signal-to-noise ratio in sentiment data. Low entropy (<40%) indicates clear sentiment consensus. Confidence: ~92% with low entropy.</p>
<p><strong>Step 6: Cross-Asset Sentiment (<span class="indicator">Correlation</span>)</strong></p>
<p>- <span class="indicator">Correlation</span> of sentiment across related assets confirms broad market shifts. Confidence: ~95% when sentiment aligns across correlated assets.</p>
<p><strong>Tip:</strong> Sentiment Analysis Surfer excels at identifying major market turning points before they appear in price action. The AI sentiment scanner highlights unusual sentiment patterns and divergences. Most powerful for contrarian trading at market extremes when at least 4 indicators align!</p>`,
    color: '#FF1493'
  },
  momentum_blast: {
    name: 'Momentum Blast',
    indicators: ['rsi', 'stochRsi', 'macd', 'mfi', 'volume'],
    description: 'A more aggressive strategy focusing on momentum indicators with looser thresholds for more frequent trading signals.',
    helperText: `<p><strong>Step 1:</strong> Look for RSI crossing above 60 (bullish) or below 40 (bearish)</p>
<p><strong>Step 2:</strong> Confirm with Stoch RSI crossing above 70 (bullish) or below 30 (bearish)</p>
<p><strong>Step 3:</strong> Check MACD for crossover or histogram direction change</p>
<p><strong>Step 4:</strong> Verify MFI aligns with other momentum indicators</p>
<p><strong>Step 5:</strong> Ensure volume is increasing in the direction of the move</p>
<p><strong>Note:</strong> This strategy generates more signals but with lower accuracy - use smaller position sizes!</p>`,
    color: '#FFD700'
  },
  top_bottom_feeder: {
    name: 'Top/Bottom Feeder',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'mfi', 'volume'],
    description: 'Specialized for catching market extremes, focusing on deeply oversold or overbought conditions across multiple indicators.',
    helperText: `<p><strong>Step 1:</strong> Wait for RSI below 20 (buy) or above 80 (sell)</p>
<p><strong>Step 2:</strong> Confirm with Stoch RSI below 10 (buy) or above 90 (sell)</p>
<p><strong>Step 3:</strong> Verify price is at or beyond Bollinger Bands (2.5 standard deviations)</p>
<p><strong>Step 4:</strong> Check MFI for extreme readings aligned with other indicators</p>
<p><strong>Step 5:</strong> Look for volume climax or exhaustion</p>
<p><strong>Step 6:</strong> Wait for the first sign of reversal before entering</p>
<p><strong>Note:</strong> This strategy has fewer signals but higher win rate when properly executed</p>`,
    color: '#00BFFF'
  },
  trend_rider: {
    name: 'Trend Rider',
    indicators: ['adx', 'macd', 'rsi', 'williamsR', 'volume'],
    description: 'Focuses on riding established trends by combining ADX for trend strength with momentum indicators for entry timing.',
    helperText: `<p><strong>Step 1:</strong> Confirm ADX is above 25 to ensure a strong trend exists</p>
<p><strong>Step 2:</strong> Check MACD alignment with the trend direction</p>
<p><strong>Step 3:</strong> Look for RSI above 50 in uptrends or below 50 in downtrends</p>
<p><strong>Step 4:</strong> Use Williams %R pullbacks to -50 in uptrends or -50 in downtrends as entry points</p>
<p><strong>Step 5:</strong> Verify volume is supporting the trend direction</p>
<p><strong>Note:</strong> This strategy works best in clearly trending markets, avoid ranging conditions</p>`,
    color: '#228B22'
  },
  time_warp_scalper: {
    name: 'Time Warp Scalper',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'macd', 'williamsR'],
    description: 'A fast-paced scalping strategy that exploits micro-trends and time frame convergence for rapid entries and exits.',
    helperText: `<p><strong>Step 1: Identify Time Frame Convergence (<span class="indicator">RSI</span> & <span class="indicator">Stoch RSI</span>)</strong></p>
<p>- Look for <span class="indicator">RSI</span> and <span class="indicator">Stoch RSI</span> alignment across multiple timeframes (1m, 5m, 15m).</p>
<p><strong>Step 2: Confirm with <span class="indicator">Bollinger Bands</span></strong></p>
<p>- Price touching or crossing <span class="condition-buy">lower band</span> or <span class="condition-sell">upper band</span> on multiple timeframes.</p>
<p><strong>Step 3: Check <span class="indicator">MACD</span> for Confirmation</strong></p>
<p>- <span class="indicator">MACD</span> histogram showing momentum shift in same direction across timeframes.</p>
<p><strong>Step 4: Final Validation (<span class="indicator">Williams %R</span>)</strong></p>
<p>- <span class="indicator">Williams %R</span> confirming oversold/overbought conditions across timeframes.</p>`,
    color: '#FF8C00'
  },
  fractal_visionary: {
    name: 'Fractal Visionary',
    indicators: ['fractal', 'bollingerBands', 'rsi', 'macd', 'adx'],
    description: 'Identifies repeating fractal patterns and volatility clusters for high-probability reversal and breakout trades.',
    helperText: `<p><strong>Step 1:</strong> Scan for repeating fractal structures on multiple timeframes.</p>
<p><strong>Step 2:</strong> Confirm with Bollinger Bands for volatility clusters.</p>
<p><strong>Step 3:</strong> Use RSI and MACD to validate direction of breakout or reversal.</p>
<p><strong>Step 4:</strong> ADX > 20 confirms trend strength for breakout trades.</p>
<p><strong>Tip:</strong> Fractal Visionary excels in choppy markets and can anticipate large moves before they start.</p>`,
    color: '#9932CC'
  },
  entropy_breaker: {
    name: 'Entropy Breaker',
    indicators: ['entropy', 'rsi', 'stochRsi', 'macd', 'volume'],
    description: 'Targets periods of low market entropy (randomness) to anticipate explosive directional moves.',
    helperText: `<p><strong>Step 1:</strong> Monitor entropy readings for periods below 35%.</p>
<p><strong>Step 2:</strong> Confirm with RSI and Stoch RSI for directional bias.</p>
<p><strong>Step 3:</strong> MACD crossover signals entry when entropy is low.</p>
<p><strong>Step 4:</strong> Volume spike confirms breakout is real.</p>
<p><strong>Tip:</strong> Entropy Breaker is best used during consolidation phases before major news or events.</p>`,
    color: '#B22222'
  },
  vwap_guardian: {
    name: 'VWAP Guardian',
    indicators: ['vwap', 'volume', 'rsi', 'macd', 'adx'],
    description: 'Uses VWAP and volume analysis to identify institutional trading zones and defend against false breakouts.',
    helperText: `<p><strong>Step 1:</strong> Watch for price crossing above or below VWAP with strong volume.</p>
<p><strong>Step 2:</strong> Confirm with RSI and MACD for trend direction.</p>
<p><strong>Step 3:</strong> ADX > 25 confirms strength of move.</p>
<p><strong>Tip:</strong> VWAP Guardian is especially effective during high-liquidity sessions and for intraday trading.</p>`,
    color: '#4682B4'
  },
  correlation_hunter: {
    name: 'Correlation Hunter',
    indicators: ['correlation', 'ml', 'entropy', 'rsi', 'macd'],
    description: 'Tracks cross-asset correlations and machine learning predictions to find unique trading opportunities.',
    helperText: `<p><strong>Step 1:</strong> Monitor correlation matrix for divergence from historical norms.</p>
<p><strong>Step 2:</strong> Confirm with ML predictions for directional edge.</p>
<p><strong>Step 3:</strong> Entropy below 40% signals clearer market structure.</p>
<p><strong>Step 4:</strong> RSI and MACD validate entry timing.</p>
<p><strong>Tip:</strong> Correlation Hunter is best for pairs trading and market-neutral strategies.</p>`,
    color: '#5F9EA0'
  },
  ultimate_oscillator_overlord: {
    name: 'Ultimate Oscillator Overlord',
    indicators: ['ultimateOscillator', 'rsi', 'macd', 'adx', 'volume'],
    description: 'Leverages the Ultimate Oscillator with trend and momentum indicators to catch early trend reversals.',
    helperText: `<p><strong>Step 1:</strong> Look for Ultimate Oscillator crossing above 60 (bullish) or below 40 (bearish).</p>
<p><strong>Step 2:</strong> Confirm with RSI and MACD for momentum alignment.</p>
<p><strong>Step 3:</strong> ADX > 22 signals trend strength.</p>
<p><strong>Step 4:</strong> Volume spike confirms trend reversal is real.</p>
<p><strong>Tip:</strong> Ultimate Oscillator Overlord is best for catching the very start of new trends.</p>`,
    color: '#FFD700'
  },
  quantum_liquidity_seeker: {
    name: 'Quantum Liquidity Seeker',
    indicators: ['volume', 'vwap', 'ml', 'entropy', 'rsi'],
    description: 'Seeks out hidden liquidity pools and quantum price anomalies for advanced scalping and arbitrage opportunities.',
    helperText: `<p><strong>Step 1:</strong> Identify volume spikes and anomalies with ML and entropy analysis.</p>
<p><strong>Step 2:</strong> Confirm price position relative to VWAP and recent liquidity zones.</p>
<p><strong>Step 3:</strong> Use RSI to validate overbought/oversold status.</p>
<p><strong>Tip:</strong> Quantum Liquidity Seeker is ideal for advanced users and high-frequency trading environments.</p>`,
    color: '#7FFFD4'
  },
};

// Helper functions and update logic (extracted and attached to window for global access)
window.showStrategyAnimation = function(strategy) {
  // Store the current state of mini charts only (not indicators)
  const miniChartState = {};
  Object.keys(window.INDICATORS).forEach(type => {
    window.INDICATORS[type].forEach(ind => {
      const canvas = document.getElementById(`mini-chart-canvas-${ind}`);
      if (canvas) {
        miniChartState[ind] = {
          width: canvas.width,
          height: canvas.height
        };
      }
    });
  });

  // Remove any existing animation containers first
  const existingContainer = document.querySelector('.strategy-switch-animation');
  if (existingContainer) {
    dismissStrategyAnimation(existingContainer);
  }

  // Create a new animation container
  const animationContainer = document.createElement('div');
  animationContainer.className = 'strategy-switch-animation';
  // Only store mini chart state, not indicator state
  animationContainer.setAttribute('data-mini-chart-state', JSON.stringify(miniChartState));
  document.body.appendChild(animationContainer);

  // Add click event listener to dismiss the animation
  animationContainer.addEventListener('click', () => {
    dismissStrategyAnimation(animationContainer);
  });

  // Get strategy details
  const strategyDetails = window.TRADING_STRATEGIES[strategy];
  if (!strategyDetails) return;

  // Create animation content
  const content = document.createElement('div');
  content.className = 'strategy-switch-content';

  // Add strategy name with dynamic color
  const nameElement = document.createElement('div');
  nameElement.className = 'strategy-switch-name';
  nameElement.textContent = strategyDetails.name;
  nameElement.style.color = getStrategyColor(strategy);
  content.appendChild(nameElement);

  // Add strategy description with fade-in effect
  const descriptionElement = document.createElement('div');
  descriptionElement.className = 'strategy-switch-description';
  descriptionElement.textContent = strategyDetails.description || '';
  descriptionElement.style.color = getStrategyColor(strategy);
  descriptionElement.style.opacity = '0';
  content.appendChild(descriptionElement);

  // Add click instruction
  const clickInstruction = document.createElement('div');
  clickInstruction.className = 'click-instruction';
  clickInstruction.textContent = 'Click anywhere to dismiss';
  clickInstruction.style.opacity = '0';
  content.appendChild(clickInstruction);

  // Add indicator dots
  const indicatorsContainer = document.createElement('div');
  indicatorsContainer.className = 'strategy-switch-indicators';

  // Add indicator dots with staggered animation and chevron animations
  strategyDetails.indicators.forEach((ind, index) => {
    const dotContainer = document.createElement('div');
    dotContainer.className = 'indicator-dot-container';

    // Create the indicator dot
    const dot = document.createElement('div');
    dot.className = 'strategy-indicator-dot';
    dot.style.backgroundColor = getIndicatorColor(ind);
    dot.style.animationDelay = `${index * 0.1}s`;
    dot.setAttribute('data-indicator', ind);

    // Add indicator name
    const indName = document.createElement('span');
    indName.className = 'indicator-name';
    indName.textContent = ind.toUpperCase();
    indName.style.color = getIndicatorColor(ind);

    // Add chevron animation
    const chevron = document.createElement('div');
    chevron.className = 'indicator-chevron';
    chevron.style.borderColor = getIndicatorColor(ind);
    chevron.style.animationDelay = `${index * 0.1}s`;

    dotContainer.appendChild(dot);
    dotContainer.appendChild(indName);
    dotContainer.appendChild(chevron);
    indicatorsContainer.appendChild(dotContainer);
  });

  content.appendChild(indicatorsContainer);
  animationContainer.innerHTML = '';
  animationContainer.appendChild(content);

  // Show animation
  animationContainer.classList.add('active');

  // Fade in description and click instruction after a short delay
  setTimeout(() => {
    descriptionElement.style.transition = 'opacity 0.5s ease';
    descriptionElement.style.opacity = '1';
    setTimeout(() => {
      clickInstruction.style.transition = 'opacity 0.5s ease';
      clickInstruction.style.opacity = '0.7';
    }, 500);
  }, 500);

  // Add pulsing glow effect to the container
  animationContainer.style.boxShadow = `0 0 20px ${getStrategyColor(strategy)}`;
  animationContainer.style.transition = 'box-shadow 1s ease';

  // Hide animation after delay (auto-dismiss after 5 seconds)
  animationContainer.autoHideTimeout = setTimeout(() => {
    dismissStrategyAnimation(animationContainer);
  }, 5000);
};
window.dismissStrategyAnimation = function(container) {
  // Clear any existing timeout to prevent multiple dismissals
  if (container.autoHideTimeout) {
    clearTimeout(container.autoHideTimeout);
    container.autoHideTimeout = null;
  }

  // Fade out
  container.style.opacity = '0';
  container.style.transition = 'opacity 0.5s ease, box-shadow 0.5s ease';
  container.style.boxShadow = '0 0 0 transparent';
  container.style.pointerEvents = 'none';

  // Remove after fade out
  setTimeout(() => {
    container.classList.remove('active');
    container.style.opacity = '1';

    // Remove the container from the DOM completely
    if (container.parentNode) {
      container.parentNode.removeChild(container);
    }

    // Update signal lights to ensure all indicators are properly displayed
    if (typeof updateAllSignalLights === 'function') updateAllSignalLights();

    // Restore mini chart dimensions if needed
    try {
      const savedMiniChartState = container.getAttribute('data-mini-chart-state');
      if (savedMiniChartState) {
        const miniChartState = JSON.parse(savedMiniChartState);
        Object.keys(miniChartState).forEach(ind => {
          const canvas = document.getElementById(`mini-chart-canvas-${ind}`);
          if (canvas) {
            canvas.width = miniChartState[ind].width;
            canvas.height = miniChartState[ind].height;
          }
        });
        // Force update mini charts
        if (typeof updateMiniCharts === 'function') updateMiniCharts();
      }
    } catch (e) {
      if (typeof logMessages !== 'undefined') {
        logMessages.push(`[${new Date().toLocaleString()}] Error restoring mini chart state: ${e.message}`);
        if (typeof updateLogger === 'function') updateLogger();
      }
    }
  }, 500);
};
window.getStrategyColor = function(strategy) {
  const colors = {
    admiral_toa: '#E91E63',
    momentum_blast: '#FF5722',
    tight_convergence: '#4CAF50',
    top_bottom_feeder: '#FF9800',
    scalping_sniper: '#9C27B0',
    trend_rider: '#2196F3',
    fractal_surge: '#4CAF50',
    x_sentiment_blaster: '#FF9800',
    quantum_entropy: '#9C27B0',
    'cross-asset_nebula': '#2196F3',
    time_warp_scalper: '#00BCD4',
    neural_network_navigator: '#3F51B5',
    deep_learning_diver: '#673AB7',
    ai_pattern_prophet: '#009688',
    machine_learning_momentum: '#8BC34A',
    sentiment_analysis_surfer: '#FFEB3B'
  };
  return colors[strategy] || '#00FFFF';
};
window.getIndicatorColor = function(indicator) {
  const colors = {
    rsi: '#FF5722',
    stochRsi: '#FF9800',
    macd: '#4CAF50',
    bollingerBands: '#2196F3',
    adx: '#9C27B0',
    williamsR: '#00BCD4',
    ultimateOscillator: '#3F51B5',
    mfi: '#E91E63',
    vwap: '#009688',
    volume: '#607D8B',
    fractal: '#FFEB3B',
    ml: '#8BC34A',
    sentiment: '#FF4081',
    entropy: '#7C4DFF',
    correlation: '#00E5FF',
    time_anomaly: '#FFD600',
    atr: '#795548'
  };
  return colors[indicator] || '#FFFFFF';
};
window.updateStrategyMenu = function() {
  try {
    const mainStrategySelector = document.getElementById('mainStrategySelector');
    if (mainStrategySelector) {
      mainStrategySelector.value = currentStrategy;
    }
    const mainStrategyInfoContent = document.getElementById('mainStrategyInfoContent');
    const mainStrategyIndicatorsContent = document.getElementById('mainStrategyIndicatorsContent');
    if (mainStrategyInfoContent && TRADING_STRATEGIES[currentStrategy]) {
      mainStrategyInfoContent.innerHTML = `<p>${TRADING_STRATEGIES[currentStrategy].description || 'No description available.'}</p>`;
    }
    if (mainStrategyIndicatorsContent && TRADING_STRATEGIES[currentStrategy] && TRADING_STRATEGIES[currentStrategy].indicators) {
      mainStrategyIndicatorsContent.innerHTML = '';
      TRADING_STRATEGIES[currentStrategy].indicators.forEach(indicator => {
        const span = document.createElement('span');
        span.className = 'indicator-tag';
        span.textContent = window.INDICATOR_DISPLAY_NAMES && window.INDICATOR_DISPLAY_NAMES[indicator] ? window.INDICATOR_DISPLAY_NAMES[indicator] : indicator;
        span.style.backgroundColor = getIndicatorColor(indicator);
        span.style.color = '#FFFFFF';
        span.style.padding = '0.2em 0.5em';
        span.style.margin = '0.2em';
        span.style.borderRadius = '0.25em';
        span.style.display = 'inline-block';
        span.style.fontSize = '0.9em';
        mainStrategyIndicatorsContent.appendChild(span);
      });
    }
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Strategy menu updated for ${TRADING_STRATEGIES[currentStrategy].name}`);
      if (typeof updateLogger === 'function') updateLogger();
    }
  } catch (e) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] updateStrategyMenu error: ${e.message}`);
      if (typeof updateLogger === 'function') updateLogger();
    }
  }
};
window.renderLogicMenu = function() {
  try {
    const container = document.getElementById('logicControls');
    if (!container) {
      if (typeof logMessages !== 'undefined') {
        logMessages.push(`[${new Date().toLocaleString()}] Logic controls container not found`);
        if (typeof updateLogger === 'function') updateLogger();
      }
      return;
    }
    const currentStrategyName = document.getElementById('currentStrategyName');
    if (currentStrategyName) {
      currentStrategyName.textContent = TRADING_STRATEGIES[currentStrategy].name;
    }
    updateStrategyInfoPanel(currentStrategy);
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Rendered signal logic menu`);
      if (typeof updateLogger === 'function') updateLogger();
    }
  } catch (e) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] renderLogicMenu error: ${e.message}`);
      if (typeof updateLogger === 'function') updateLogger();
    }
  }
};
window.updateStrategyInfoPanel = function(strategy, infoContentId = 'strategyInfoContent', indicatorsContentId = 'strategyIndicatorsContent') {
  try {
    const strategyInfoContent = document.getElementById(infoContentId);
    const strategyIndicatorsContent = document.getElementById(indicatorsContentId);
    if (strategyInfoContent && TRADING_STRATEGIES[strategy]) {
      strategyInfoContent.innerHTML = `<p>${TRADING_STRATEGIES[strategy].description || 'No description available.'}</p>`;
    }
    if (strategyIndicatorsContent && TRADING_STRATEGIES[strategy] && TRADING_STRATEGIES[strategy].indicators) {
      strategyIndicatorsContent.innerHTML = '';
      TRADING_STRATEGIES[strategy].indicators.forEach(indicator => {
        const span = document.createElement('span');
        span.className = 'indicator-tag';
        span.textContent = window.INDICATOR_DISPLAY_NAMES && window.INDICATOR_DISPLAY_NAMES[indicator] ? window.INDICATOR_DISPLAY_NAMES[indicator] : indicator;
        span.style.backgroundColor = getIndicatorColor(indicator);
        span.style.color = '#FFFFFF';
        span.style.padding = '0.2em 0.5em';
        span.style.margin = '0.2em';
        span.style.borderRadius = '0.25em';
        span.style.display = 'inline-block';
        span.style.fontSize = '0.9em';
        strategyIndicatorsContent.appendChild(span);
      });
    }
  } catch (e) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] updateStrategyInfoPanel error: ${e.message}`);
      if (typeof updateLogger === 'function') updateLogger();
    }
  }
};
window.updateIndicatorsForStrategy = function(strategy) {
  if (!TRADING_STRATEGIES[strategy]) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Error: Strategy ${strategy} not found`);
      if (typeof updateLogger === 'function') updateLogger();
    }
    return;
  }
  const previousStrategy = currentStrategy;
  if (previousStrategy !== strategy) {
    showStrategyAnimation(strategy);
  }
  currentStrategy = strategy;
  if (TRADING_STRATEGIES[strategy].helperText) {
    const helperContainer = document.getElementById('helper-container');
    if (helperContainer) {
      const helperContent = helperContainer.querySelector('.helper-content');
      if (helperContent) {
        helperContent.innerHTML = TRADING_STRATEGIES[strategy].helperText;
      }
    }
  }
};
window.updateSignalMatrix = function() {
  try {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Updating signal matrix for ${currentStrategy} strategy`);
    }
    const momentumTable = document.getElementById('momentum-table');
    if (!momentumTable) {
      if (typeof logMessages !== 'undefined') {
        logMessages.push(`[${new Date().toLocaleString()}] updateSignalMatrix: Momentum table not found`);
        if (typeof updateLogger === 'function') updateLogger();
      }
      return;
    }
    const strategyIndicators = TRADING_STRATEGIES[currentStrategy]?.indicators || [];
    const activeTimeframes = useLowTimeframes ? LOW_TIMEFRAMES : TIMEFRAMES;
    const allEnabledIndicators = [...INDICATORS.momentum, ...INDICATORS.trend, ...INDICATORS.volume, ...INDICATORS.ml].filter(ind => {
      const indSettings = enabledIndicators.find(i => i.name === ind);
      if (currentStrategy === 'admiral_toa') {
        return indSettings && indSettings.enabled;
      } else {
        return indSettings && indSettings.enabled && strategyIndicators.includes(ind);
      }
    });
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Signal matrix updated with ${allEnabledIndicators.length} indicators for ${TRADING_STRATEGIES[currentStrategy].name} strategy`);
      if (typeof updateLogger === 'function') updateLogger();
    }
    if (typeof renderIndicatorTables === 'function') renderIndicatorTables();
    return true;
  } catch (e) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] updateSignalMatrix error: ${e.message}`);
      if (typeof updateLogger === 'function') updateLogger();
    }
    return false;
  }
};
// Add any other extracted logic or initialization as needed.


// Helper functions and update logic (extracted and attached to window for global access)
window.showStrategyAnimation = function(strategy) { /* ...full function from index.html... */ };
window.dismissStrategyAnimation = function(container) { /* ...full function from index.html... */ };
window.getStrategyColor = function(strategy) { /* ...full function from index.html... */ };
window.getIndicatorColor = function(indicator) { /* ...full function from index.html... */ };
window.updateStrategyMenu = function() { /* ...full function from index.html... */ };
window.renderLogicMenu = function() { /* ...full function from index.html... */ };
window.updateStrategyInfoPanel = function(strategy, infoContentId = 'strategyInfoContent', indicatorsContentId = 'strategyIndicatorsContent') { /* ...full function from index.html... */ };
window.updateIndicatorsForStrategy = function(strategy) { /* ...full function from index.html... */ };
window.updateSignalMatrix = function() { /* ...full function from index.html... */ };
// Add any other extracted logic or initialization as needed.

