/* Signal Lights Fixes */
.signal-circle {
    /* Fixed dimensions */
    width: 36px !important;
    height: 36px !important;
    min-width: 36px !important;
    min-height: 36px !important;
    max-width: 36px !important;
    max-height: 36px !important;
    
    /* Visual styling */
    border-radius: 50% !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 2px !important;
    flex-shrink: 0 !important;
    box-sizing: border-box !important;
    position: relative !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3) !important;
    
        /* Ensure proper stacking context */
    z-index: 1 !important;
    
    /* Improve visibility */
    opacity: 1 !important;
    visibility: visible !important;
    
    /* Ensure proper cursor */
    cursor: pointer;
    
    /* Add subtle hover effect */
    transition: all 0.2s ease-in-out !important;
}

/* Hover effects */
.signal-circle:hover {
    transform: scale(1.1) !important;
    z-index: 10 !important;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3) !important;
}

/* Active/clicked state */
.signal-circle:active {
    transform: scale(0.95) !important;
}

/* Focus state for accessibility */
.signal-circle:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5) !important;
    outline-offset: 2px !important;
}

/* Disabled state */
.signal-circle.disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    filter: grayscale(70%) !important;
}

/* Prevent any parent from overriding the size */
.signal-circle * {
    box-sizing: border-box !important;
}

/* Ensure all signal lights are visible */
#signal-matrix .signal-circle,
#indicator-signals .signal-circle,
#signals-container .signal-circle,
#indicator-grid .signal-circle,
.signal-container .signal-circle,
.signal-cell .signal-circle {
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-flex !important;
}

/* Fix for any flex/grid layouts that might be affecting the signals */
#signals-container,
#indicator-grid,
#signal-matrix,
#indicator-signals {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 4px !important;
    align-items: center !important;
}

/* Ensure tooltips are visible and properly positioned */
.signal-circle[title] {
    position: relative;
    cursor: help;
}

.signal-circle[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    margin-bottom: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    margin-bottom: 5px;
}

/* Animation for signal updates */
@keyframes signal-update {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.signal-updated {
    animation: signal-update 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .signal-circle {
        width: 32px !important;
        height: 32px !important;
        min-width: 32px !important;
        min-height: 32px !important;
        max-width: 32px !important;
        max-height: 32px !important;
    }
}

@media (max-width: 768px) {
    .signal-circle {
        width: 28px !important;
        height: 28px !important;
        min-width: 28px !important;
        min-height: 28px !important;
        max-width: 28px !important;
        max-height: 28px !important;
    }
    
    /* Make signal containers scrollable on small screens */
    #signals-container,
    #indicator-grid,
    #signal-matrix,
    #indicator-signals {
        flex-wrap: nowrap !important;
        overflow-x: auto !important;
        padding-bottom: 8px !important;
        scrollbar-width: thin !important;
        scrollbar-color: rgba(255, 255, 255, 0.2) transparent !important;
    }
    
    /* Hide scrollbar on WebKit browsers */
    #signals-container::-webkit-scrollbar,
    #indicator-grid::-webkit-scrollbar,
    #signal-matrix::-webkit-scrollbar,
    #indicator-signals::-webkit-scrollbar {
        height: 4px !important;
    }
    
    #signals-container::-webkit-scrollbar-thumb,
    #indicator-grid::-webkit-scrollbar-thumb,
    #signal-matrix::-webkit-scrollbar-thumb,
    #indicator-signals::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.2) !important;
        border-radius: 2px !important;
    }
}

/* Dark theme by default */
:root {
    --signal-border: rgba(255, 255, 255, 0.1);
    --signal-shadow: rgba(0, 0, 0, 0.3);
    --signal-hover-shadow: rgba(255, 255, 255, 0.3);
    --tooltip-bg: rgba(0, 0, 0, 0.8);
    --tooltip-text: #fff;
}

/* Light theme support */
[data-theme="light"] {
    --signal-border: rgba(0, 0, 0, 0.1);
    --signal-shadow: rgba(0, 0, 0, 0.1);
    --signal-hover-shadow: rgba(0, 0, 0, 0.2);
    --tooltip-bg: rgba(255, 255, 255, 0.95);
    --tooltip-text: #333;
}

/* Update signal circle styles to use theme variables */
.signal-circle {
    border-color: var(--signal-border) !important;
    box-shadow: 0 0 5px var(--signal-shadow) !important;
}

.signal-circle:hover {
    box-shadow: 0 0 10px var(--signal-hover-shadow) !important;
}

.signal-circle[title]:hover::after {
    background: var(--tooltip-bg) !important;
    color: var(--tooltip-text) !important;
}

/* Ensure all signal states are properly colored */
.signal-circle.strong-buy {
    background-color: #00FF00 !important;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5) !important;
}

.signal-circle.mild-buy {
    background-color: #00AAFF !important;
    box-shadow: 0 0 8px rgba(0, 170, 255, 0.4) !important;
}

.signal-circle.neutral {
    background-color: #808080 !important;
    box-shadow: 0 0 5px rgba(128, 128, 128, 0.3) !important;
}

.signal-circle.mild-sell {
    background-color: #FFA500 !important;
    box-shadow: 0 0 8px rgba(255, 165, 0, 0.4) !important;
}

.signal-circle.strong-sell {
    background-color: #FF0000 !important;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.5) !important;
}

.signal-circle.error {
    background-color: #FF00FF !important;
    animation: errorPulse 1.5s infinite alternate !important;
    box-shadow: 0 0 10px rgba(255, 0, 255, 0.5) !important;
}

/* Pulse animation for strong signals */
.signal-circle.pulse {
    animation: pulse 2s infinite alternate !important;
}

@keyframes pulse {
    from {
        box-shadow: 0 0 5px currentColor;
    }
    to {
        box-shadow: 0 0 20px currentColor;
    }
}

@keyframes errorPulse {
    from {
        opacity: 0.5;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1.05);
    }
}
