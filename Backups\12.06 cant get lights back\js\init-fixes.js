// init-fixes.js - Initialize all fixes and enhancements

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing StarCrypt fixes...');
    
    // Load CSS fixes
    loadCSS('css/fixes.css');
    
    // Initialize menu system
    if (window.MenuSystem) {
        window.MenuSystem.initialize();
    }
    
    // Initialize signal lights
    if (window.updateSignalLight) {
        console.log('Signal light system already loaded');
    } else if (typeof SignalLights !== 'undefined') {
        SignalLights.initialize();
    }
    
    // Fix WebSocket handler if needed
    if (window.WebSocketHandler) {
        console.log('WebSocket handler already loaded');
    } else {
        // Initialize WebSocket handler if not already done
        initializeWebSocketHandler();
    }
    
    // Add connection status indicator
    addConnectionStatus();
    
    console.log('All fixes initialized');
});

// Load CSS dynamically
function loadCSS(href) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.type = 'text/css';
    document.head.appendChild(link);
}

// Initialize WebSocket handler
function initializeWebSocketHandler() {
    // Your WebSocket initialization code here
    console.log('Initializing WebSocket handler...');
    
    // Example WebSocket connection
    const ws = new WebSocket('wss://your-websocket-endpoint');
    
    ws.onopen = () => {
        console.log('WebSocket connected');
        updateConnectionStatus(true);
    };
    
    ws.onclose = () => {
        console.log('WebSocket disconnected');
        updateConnectionStatus(false);
        
        // Attempt to reconnect
        setTimeout(initializeWebSocketHandler, 5000);
    };
    
    ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        updateConnectionStatus(false);
    };
    
    // Store WebSocket instance globally if needed
    window.appWebSocket = ws;
}

// Add connection status indicator
function addConnectionStatus() {
    // Remove existing status if any
    const existingStatus = document.getElementById('connection-status');
    if (existingStatus) {
        existingStatus.remove();
    }
    
    const status = document.createElement('div');
    status.id = 'connection-status';
    status.className = 'connection-status';
    status.textContent = 'Connecting...';
    
    document.body.appendChild(status);
}

// Update connection status indicator
function updateConnectionStatus(connected) {
    const status = document.getElementById('connection-status');
    if (!status) return;
    
    if (connected) {
        status.textContent = 'Connected';
        status.className = 'connection-status connected';
    } else {
        status.textContent = 'Disconnected - Reconnecting...';
        status.className = 'connection-status disconnected';
    }
}

// Export initialization function
window.initializeStarCryptFixes = function() {
    // This function can be called manually if needed
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeAll);
    } else {
        initializeAll();
    }
};

// Initialize everything
function initializeAll() {
    // This function will be called by the event listener
    console.log('StarCrypt fixes initialized');
}

// Auto-initialize
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAll);
} else {
    setTimeout(initializeAll, 0);
}
