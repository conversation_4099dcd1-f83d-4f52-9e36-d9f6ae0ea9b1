/**
 * @module MenuUtils
 * @description Utility functions for menu operations
 */

import { DOMUtils } from '../utils/dom-utils.js'
import { AnimationUtils } from '../utils/animation-utils.js'
import { PerformanceMonitor } from '../utils/performance-monitor.js'
import { ErrorHandler } from '../utils/error-handler.js'

export class MenuUtils {
  constructor() {
    // DOM utilities
    this.dom = new DOMUtils()

    // Animation utilities
    this.animation = new AnimationUtils()

    // Performance monitoring
    this.performance = new PerformanceMonitor({
      targetFPS: 60,
      maxLatency: 100,
      batchInterval: 100,
    })

    // Error handling
    this.errorHandler = new ErrorHandler({
      maxErrors: 5,
      recoveryDelay: 2000,
      errorThreshold: 0.1,
    })

    // Configuration
    this.config = {
      animationDuration: 300,
      transitionDelay: 100,
      maxMenuDepth: 3,
      keyboardNavigation: true,
      ariaSupport: true,
    }
  }

  /**
     * Initialize menu utilities
     */
  init() {
    try {
      // Setup DOM utilities
      this.dom.setup()

      // Setup animation utilities
      this.animation.setup()

      // Setup performance monitoring
      this.performance.start()

      // Setup error handling
      this.errorHandler.setup()
    } catch (error) {
      this.handleError(error, 'init')
    }
  }

  /**
     * Get menu element by ID
     * @param {string} menuId - Menu ID
     * @returns {HTMLElement|null} Menu element
     */
  getMenuElement(menuId) {
    try {
      // Validate ID
      if (!menuId || typeof menuId !== 'string') {
        throw new Error('Invalid menu ID')
      }

      // Get element
      const element = document.getElementById(menuId)

      // Validate element
      if (!element) {
        throw new Error(`Menu element not found: ${menuId}`)
      }

      return element
    } catch (error) {
      this.handleError(error, 'get-menu-element')
      return null
    }
  }

  /**
     * Get menu button by target ID
     * @param {string} targetId - Target menu ID
     * @returns {HTMLElement|null} Menu button element
     */
  getMenuButton(targetId) {
    try {
      // Validate ID
      if (!targetId || typeof targetId !== 'string') {
        throw new Error('Invalid target ID')
      }

      // Query button
      const button = document.querySelector(`[data-target="${targetId}"]`)

      // Validate button
      if (!button) {
        throw new Error(`Menu button not found for target: ${targetId}`)
      }

      return button
    } catch (error) {
      this.handleError(error, 'get-menu-button')
      return null
    }
  }

  /**
     * Get menu items for a menu
     * @param {string} menuId - Menu ID
     * @returns {HTMLElement[]} Menu items
     */
  getMenuItems(menuId) {
    try {
      // Get menu element
      const menu = this.getMenuElement(menuId)
      if (!menu) return []

      // Get items
      const items = Array.from(menu.querySelectorAll('.menu-item'))

      // Validate items
      if (!items.length) {
        console.warn(`No menu items found for menu: ${menuId}`)
      }

      return items
    } catch (error) {
      this.handleError(error, 'get-menu-items')
      return []
    }
  }

  /**
     * Add menu item
     * @param {string} menuId - Menu ID
     * @param {Object} item - Menu item configuration
     * @returns {HTMLElement} Created menu item
     */
  addMenuItem(menuId, item) {
    try {
      // Validate item
      if (!item || typeof item !== 'object') {
        throw new Error('Invalid menu item configuration')
      }

      // Get menu element
      const menu = this.getMenuElement(menuId)
      if (!menu) return null

      // Create item element
      const itemElement = this.dom.createElement('div', {
        className: 'menu-item',
        attributes: {
          'data-id': item.id,
          'data-type': item.type,
          'aria-label': item.label || item.text,
        },
      })

      // Add content
      itemElement.innerHTML = item.html || item.text

      // Add styles
      if (item.style) {
        Object.assign(itemElement.style, item.style)
      }

      // Add event listeners
      if (item.events) {
        this.setupItemEvents(itemElement, item.events)
      }

      // Add to menu
      menu.appendChild(itemElement)

      // Animate if enabled
      if (this.config.animation) {
        this.animation.fadeIn(itemElement)
      }

      return itemElement
    } catch (error) {
      this.handleError(error, 'add-menu-item')
      return null
    }
  }

  /**
     * Setup menu item events
     * @param {HTMLElement} item - Menu item element
     * @param {Object} events - Event configuration
     * @private
     */
  setupItemEvents(item, events) {
    try {
      // Add click event
      if (events.click) {
        item.addEventListener('click', events.click)
      }

      // Add keyboard events
      if (events.keydown) {
        item.addEventListener('keydown', events.keydown)
      }

      // Add focus events
      if (events.focus) {
        item.addEventListener('focus', events.focus)
      }

      // Add blur events
      if (events.blur) {
        item.addEventListener('blur', events.blur)
      }
    } catch (error) {
      console.error('[MenuUtils] Failed to setup item events:', error)
    }
  }

  /**
     * Remove menu item
     * @param {string} menuId - Menu ID
     * @param {string} itemId - Item ID
     */
  removeMenuItem(menuId, itemId) {
    try {
      // Get menu element
      const menu = this.getMenuElement(menuId)
      if (!menu) return

      // Find item
      const item = menu.querySelector(`[data-id="${itemId}"]`)
      if (!item) return

      // Animate removal if enabled
      if (this.config.animation) {
        this.animation.fadeOut(item, () => item.remove())
      } else {
        item.remove()
      }
    } catch (error) {
      this.handleError(error, 'remove-menu-item')
    }
  }

  /**
     * Update menu item
     * @param {string} menuId - Menu ID
     * @param {string} itemId - Item ID
     * @param {Object} updates - Update configuration
     */
  updateMenuItem(menuId, itemId, updates) {
    try {
      // Get item
      const item = this.getMenuItem(menuId, itemId)
      if (!item) return

      // Update properties
      if (updates.text) {
        item.textContent = updates.text
      }

      if (updates.html) {
        item.innerHTML = updates.html
      }

      if (updates.style) {
        Object.assign(item.style, updates.style)
      }

      if (updates.className) {
        item.className = `menu-item ${updates.className}`
      }

      if (updates.events) {
        this.setupItemEvents(item, updates.events)
      }
    } catch (error) {
      this.handleError(error, 'update-menu-item')
    }
  }

  /**
     * Get menu item by ID
     * @param {string} menuId - Menu ID
     * @param {string} itemId - Item ID
     * @returns {HTMLElement|null} Menu item element
     */
  getMenuItem(menuId, itemId) {
    try {
      // Get menu element
      const menu = this.getMenuElement(menuId)
      if (!menu) return null

      // Find item
      return menu.querySelector(`[data-id="${itemId}"]`)
    } catch (error) {
      this.handleError(error, 'get-menu-item')
      return null
    }
  }

  /**
     * Toggle menu visibility
     * @param {string} menuId - Menu ID
     * @param {boolean} [show] - Show menu (optional)
     */
  toggleMenu(menuId, show) {
    try {
      // Get menu element
      const menu = this.getMenuElement(menuId)
      if (!menu) return

      // Toggle class
      if (show !== undefined) {
        if (show) {
          menu.classList.add('active')
        } else {
          menu.classList.remove('active')
        }
      } else {
        menu.classList.toggle('active')
      }

      // Animate if enabled
      if (this.config.animation) {
        this.animation.toggle(menu, show)
      }
    } catch (error) {
      this.handleError(error, 'toggle-menu')
    }
  }

  /**
     * Handle errors
     * @param {Error} error - Error object
     * @param {string} context - Context where error occurred
     * @private
     */
  handleError(error, context) {
    try {
      // Log error
      console.error(`[MenuUtils] Error in ${context}:`, error)

      // Track error
      this.errorHandler.track(error, context)

      // Dispatch error event
      this.dispatch('error', {
        error,
        context,
        timestamp: Date.now(),
      })
    } catch (error) {
      console.error('[MenuUtils] Error handling failed:', error)
    }
  }

  /**
     * Clean up resources
     */
  cleanup() {
    try {
      // Cleanup DOM utilities
      this.dom.cleanup()

      // Cleanup animation utilities
      this.animation.cleanup()

      // Reset performance monitoring
      this.performance.reset()

      // Reset error handler
      this.errorHandler.reset()
    } catch (error) {
      console.error('[MenuUtils] Cleanup failed:', error)
    }
  }

  /**
     * Destroy menu utilities
     */
  destroy() {
    try {
      // Cleanup
      this.cleanup()

      // Reset state
      this.dom = null
      this.animation = null
      this.performance = null
      this.errorHandler = null
    } catch (error) {
      console.error('[MenuUtils] Destruction failed:', error)
    }
  }
}

// Export singleton instance
const menuUtils = new MenuUtils()
export default menuUtils
