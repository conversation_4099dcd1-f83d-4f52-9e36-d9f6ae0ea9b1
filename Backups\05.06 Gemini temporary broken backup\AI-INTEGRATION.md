# AI Integration for StarCrypt

This document outlines the AI integration in the StarCrypt trading platform, including features, architecture, and usage.

## Features

1. **Market Analysis**
   - Real-time technical analysis
   - Pattern recognition
   - Market regime detection
   - Sentiment analysis (optional)

2. **Trading Signals**
   - AI-generated buy/sell signals
   - Confidence scoring
   - Multiple timeframe analysis
   - Strategy-specific signals

3. **Backtesting**
   - Historical strategy testing
   - Performance metrics
   - Risk analysis
   - Monte Carlo simulations

4. **Risk Management**
   - Position sizing
   - Stop-loss optimization
   - Portfolio allocation
   - Drawdown control

## Architecture

```
┌───────────────────────────────────────────────────────────────┐
│                       Client Application                      │
├───────────────┬───────────────────────┬───────────────────────┤
│               │                       │                       │
│    UI Layer   │     Data Processing    │      AI Services      │
│               │                       │                       │
├───────────────┴───────────────────────┴───────────────────────┤
│                                                               │
│                      Backend Server                          │
│                                                               │
├───────────────────────────────────────────────────────────────┤
│                                                               │
│                       Data Sources                           │
│                                                               │
└───────────────────────────────────────────────────────────────┘
```

## Getting Started

### Prerequisites

- Node.js 16+
- npm 8+
- Python 3.8+ (for some ML models)
- TensorFlow.js

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Copy `.env.example` to `.env` and configure your environment variables
4. Start the server:
   ```bash
   npm start
   ```

## API Endpoints

### Market Analysis

```http
GET /api/ai/market/analysis?symbol=BTCUSDT&timeframe=1h
```

### Trading Signals

```http
GET /api/ai/signals?symbol=BTCUSDT&timeframe=1h&strategy=momentum
```

### Backtesting

```http
POST /api/ai/backtest
{
  "strategy": "mean_reversion",
  "symbol": "BTCUSDT",
  "timeframe": "1h",
  "startDate": "2023-01-01",
  "endDate": "2023-12-31",
  "initialBalance": 10000
}
```

## Configuration

Edit `config/ai-config.js` to customize AI behavior:

```javascript
module.exports = {
  // Model training parameters
  training: {
    epochs: 100,
    batchSize: 32,
    learningRate: 0.001
  },
  
  // Feature engineering
  features: {
    lookbackPeriod: 50,
    predictionHorizon: 5
  },
  
  // Strategy parameters
  strategies: {
    defaultWeight: 0.5,
    aiWeight: 0.7,
    minConfidence: 0.6
  }
};
```

## Usage Examples

### Getting Market Analysis

```javascript
const ai = require('./js/ai');

async function analyzeMarket() {
  try {
    const analysis = await ai.getMarketAnalysis('BTCUSDT', '1h');
    console.log('Market Analysis:', analysis);
  } catch (error) {
    console.error('Error:', error);
  }
}
```

### Generating Trading Signals

```javascript
async function getSignal() {
  try {
    const signal = await ai.getTradingSignal('BTCUSDT', '1h', 'momentum');
    console.log('Trading Signal:', signal);
  } catch (error) {
    console.error('Error:', error);
  }
}
```

### Running a Backtest

```javascript
async function runBacktest() {
  const results = await ai.runBacktest(
    'mean_reversion',
    'BTCUSDT',
    '1h',
    '2023-01-01',
    '2023-12-31',
    10000
  );
  console.log('Backtest Results:', results);
}
```

## Performance Monitoring

Monitor AI performance metrics at:

```http
GET /api/ai/performance
```

## Troubleshooting

### Common Issues

1. **Model Loading Errors**
   - Ensure model files exist in the correct directory
   - Check file permissions
   - Verify model compatibility with TensorFlow.js version

2. **Performance Issues**
   - Reduce batch size
   - Enable GPU acceleration
   - Optimize feature engineering

3. **API Connection Problems**
   - Verify API keys
   - Check network connectivity
   - Review rate limits

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request
