// js/ui/menu-handler.js
/**
 * StarCrypt Menu Handler
 *
 * Centralized menu management for all UI menus
 */

class MenuHandler {
  constructor() {
    this.activeMenu = null
    this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu']
    this.initialize()
  }

  initialize() {
    // Initialize all menus
    this.setupMenuButtons()
    this.setupClickOutsideHandler()
    console.log('[MenuHandler] Initialized menu handler')
  }

  setupMenuButtons() {
    // Strategy menu button
    document.addEventListener('click', (e) => {
      try {
        // Strategy menu
        if (e.target.matches('#strategyButton, #strategyButton *')) {
          e.preventDefault()
          e.stopPropagation()
          this.toggleMenu('strategyMenu')
          return
        }

        // Indicator menu
        if (e.target.matches('#toggleMenuButton, #toggleMenuButton *')) {
          e.preventDefault()
          e.stopPropagation()
          this.toggleMenu('indicatorMenu')
          return
        }

        // Thresholds menu
        if (e.target.matches('#toggleThresholdsButton, #toggleThresholdsButton *')) {
          e.preventDefault()
          e.stopPropagation()
          this.toggleMenu('thresholdsMenu')
          return
        }

        // Logic menu
        if (e.target.matches('#toggleLogicButton, #toggleLogicButton *')) {
          e.preventDefault()
          e.stopPropagation()
          this.toggleMenu('logicMenu')
        }
      } catch (error) {
        console.error('[MenuHandler] Error in menu button handler:', error)
      }
    })
  }

  toggleMenu(menuId) {
    try {
      const menu = document.getElementById(menuId)
      if (!menu) {
        console.warn(`[MenuHandler] Menu not found: ${menuId}`)
        return
      }

      // Close all other menus
      this.menus.forEach(id => {
        if (id !== menuId) {
          const otherMenu = document.getElementById(id)
          if (otherMenu) {
            otherMenu.classList.remove('active')
          }
        }
      })

      // Toggle the clicked menu
      menu.classList.toggle('active')

      // Update active menu reference
      if (menu.classList.contains('active')) {
        this.activeMenu = menuId
      } else {
        this.activeMenu = null
      }

      console.log(`[MenuHandler] Toggled menu: ${menuId}, active: ${menu.classList.contains('active')}`)
    } catch (error) {
      console.error(`[MenuHandler] Error toggling menu ${menuId}:`, error)
    }
  }

  closeAllMenus() {
    this.menus.forEach(menuId => {
      const menu = document.getElementById(menuId)
      if (menu) {
        menu.classList.remove('active')
      }
    })
    this.activeMenu = null
  }

  setupClickOutsideHandler() {
    document.addEventListener('click', (e) => {
      try {
        // If clicking inside a menu or on a menu button, don't close
        if (e.target.closest('.menu-content') || e.target.closest('.menu-button')) {
          return
        }

        // Close all menus when clicking outside
        this.closeAllMenus()
      } catch (error) {
        console.error('[MenuHandler] Error in click outside handler:', error)
      }
    })
  }
}

// Initialize the menu handler when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.menuHandler = new MenuHandler()
})

// Make the MenuHandler available globally
window.MenuHandler = MenuHandler
