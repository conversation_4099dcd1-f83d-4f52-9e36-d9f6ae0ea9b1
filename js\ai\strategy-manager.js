const AIEngine = require('./ai-engine')
const { TRADING_STRATEGIES } = require('../../server')

class StrategyManager {
  constructor() {
    this.aiEngine = AIEngine
    this.strategies = new Map()
    this.modelCache = new Map()
    this.initialized = false
  }

  async initialize() {
    if (this.initialized) return

    try {
      await this.aiEngine.initialize()
      this.initialized = true
      console.log('Strategy Manager: Initialized successfully')
    } catch (error) {
      console.error('Strategy Manager: Initialization failed:', error)
      throw error
    }
  }

  // Load and cache a specific strategy
  async loadStrategy(strategyId) {
    if (this.strategies.has(strategyId)) {
      return this.strategies.get(strategyId)
    }

    const strategy = TRADING_STRATEGIES[strategyId]
    if (!strategy) {
      throw new Error(`Strategy ${strategyId} not found`)
    }

    // Initialize AI models for this strategy if needed
    if (strategy.aiEnabled) {
      await this.initializeAIModels(strategy)
    }

    this.strategies.set(strategyId, strategy)
    return strategy
  }

  // Initialize AI models for a strategy
  async initializeAIModels(strategy) {
    const modelPromises = []

    // Load base model
    if (strategy.aiModelPath) {
      modelPromises.push(
        this.aiEngine.loadModel(strategy.aiModelPath)
          .then(model => {
            this.modelCache.set(strategy.id, model)
            console.log(`Loaded AI model for strategy: ${strategy.name}`)
          })
          .catch(error => {
            console.error(`Failed to load AI model for ${strategy.name}:`, error)
          }),
      )
    }

    // Load any additional models
    if (strategy.additionalModels) {
      Object.entries(strategy.additionalModels).forEach(([name, path]) => {
        modelPromises.push(
          this.aiEngine.loadModel(path)
            .then(model => {
              this.modelCache.set(`${strategy.id}_${name}`, model)
              console.log(`Loaded additional model ${name} for strategy: ${strategy.name}`)
            })
            .catch(error => {
              console.error(`Failed to load additional model ${name} for ${strategy.name}:`, error)
            }),
        )
      })
    }

    await Promise.all(modelPromises)
  }

  // Get a signal from a strategy
  async getStrategySignal(strategyId, indicators) {
    const strategy = await this.loadStrategy(strategyId)

    try {
      // Get base signal from strategy logic
      const baseSignal = strategy.logic(indicators)

      // If AI is enabled for this strategy, enhance the signal
      if (strategy.aiEnabled && this.modelCache.has(strategyId)) {
        const model = this.modelCache.get(strategyId)
        const aiPrediction = await this.aiEngine.predict(model, indicators)

        // Combine AI prediction with base signal
        return this.combineSignals(baseSignal, aiPrediction, strategy)
      }

      return baseSignal
    } catch (error) {
      console.error(`Error getting signal from strategy ${strategyId}:`, error)
      // Fall back to base signal if AI prediction fails
      return strategy.logic(indicators)
    }
  }

  // Combine AI prediction with base strategy signal
  combineSignals(baseSignal, aiPrediction, strategy) {
    // Simple weighted average for now
    const aiWeight = strategy.aiWeight || 0.7 // Default to 70% AI, 30% base
    const baseWeight = 1 - aiWeight

    // Calculate combined score
    const combinedScore = (
      (baseSignal.score * baseWeight) +
      (aiPrediction.prediction * aiWeight)
    )

    // Calculate combined confidence
    const combinedConfidence = Math.min(
      (baseSignal.confidence * baseWeight) +
      (aiPrediction.confidence * aiWeight),
      1.0,
    )

    // Determine direction based on combined score
    const direction = combinedScore >= 0.5 ? 'LONG' : 'SHORT'

    // Generate rationale
    const rationale = [
      `Base Strategy: ${baseSignal.rationale || 'No rationale provided'}`,
      `AI Prediction: ${(aiPrediction.prediction * 100).toFixed(2)}% confidence`,
      `Combined Score: ${(combinedScore * 100).toFixed(2)}%`,
    ].join(' | ')

    return {
      direction,
      score: combinedScore,
      confidence: combinedConfidence,
      rationale,
      components: {
        base: baseSignal,
        ai: aiPrediction,
      },
      timestamp: new Date().toISOString(),
    }
  }

  // Get signals from all strategies
  async getAllSignals(indicators) {
    const signals = {}
    const strategyIds = Object.keys(TRADING_STRATEGIES)

    await Promise.all(strategyIds.map(async (strategyId) => {
      try {
        const signal = await this.getStrategySignal(strategyId, indicators)
        signals[strategyId] = signal
      } catch (error) {
        console.error(`Error getting signal for ${strategyId}:`, error)
        signals[strategyId] = {
          error: error.message,
          timestamp: new Date().toISOString(),
        }
      }
    }))

    return signals
  }

  // Get consensus signal from all strategies
  async getConsensusSignal(indicators) {
    const signals = await this.getAllSignals(indicators)
    const validSignals = Object.values(signals).filter(s => !s.error)

    if (validSignals.length === 0) {
      return {
        direction: 'NEUTRAL',
        confidence: 0,
        rationale: 'No valid signals from any strategy',
        timestamp: new Date().toISOString(),
      }
    }

    // Calculate weighted average of signals
    let longScore = 0
    let totalWeight = 0

    validSignals.forEach(signal => {
      const weight = signal.confidence || 0.5 // Default to 0.5 confidence if not specified
      if (signal.direction === 'LONG') {
        longScore += weight
      } else if (signal.direction === 'SHORT') {
        longScore -= weight
      }
      totalWeight += weight
    })

    // Normalize score to [0, 1] range
    const normalizedScore = (longScore / totalWeight + 1) / 2

    // Calculate average confidence
    const avgConfidence = validSignals.reduce((sum, s) => sum + (s.confidence || 0), 0) / validSignals.length

    // Determine direction
    let direction
    if (normalizedScore > 0.6) {
      direction = 'STRONG_LONG'
    } else if (normalizedScore > 0.55) {
      direction = 'LONG'
    } else if (normalizedScore < 0.4) {
      direction = 'STRONG_SHORT'
    } else if (normalizedScore < 0.45) {
      direction = 'SHORT'
    } else {
      direction = 'NEUTRAL'
    }

    // Generate rationale
    const longCount = validSignals.filter(s => s.direction === 'LONG' || s.direction === 'STRONG_LONG').length
    const shortCount = validSignals.filter(s => s.direction === 'SHORT' || s.direction === 'STRONG_SHORT').length
    const neutralCount = validSignals.length - longCount - shortCount

    const rationale = [
      `Consensus: ${direction.replace('_', ' ')}`,
      `Strength: ${(Math.abs(normalizedScore - 0.5) * 200).toFixed(1)}%`,
      `Votes: ${longCount} Long | ${shortCount} Short | ${neutralCount} Neutral`,
    ].join(' | ')

    return {
      direction,
      score: normalizedScore,
      confidence: avgConfidence,
      rationale,
      signals,
      timestamp: new Date().toISOString(),
    }
  }

  // Train AI models for all strategies
  async trainAllStrategies(trainingData) {
    const results = {}

    for (const [strategyId, strategy] of Object.entries(TRADING_STRATEGIES)) {
      if (strategy.aiEnabled) {
        try {
          console.log(`Training AI model for strategy: ${strategy.name}`)
          const { model, history } = await this.aiEngine.trainModel(trainingData)

          // Save the trained model
          this.modelCache.set(strategyId, model)

          // Store training results
          results[strategyId] = {
            status: 'success',
            epochs: history.epoch.length,
            finalLoss: history.loss[history.loss.length - 1],
            finalAccuracy: history.acc ? history.acc[history.acc.length - 1] : null,
            history,
          }

          console.log(`Successfully trained ${strategy.name}:`, results[strategyId])
        } catch (error) {
          console.error(`Error training ${strategy.name}:`, error)
          results[strategyId] = {
            status: 'error',
            error: error.message,
          }
        }
      }
    }

    return results
  }
}

// Export singleton instance
module.exports = new StrategyManager()
