import { marketDataService } from './MarketDataService.js'
import { aiAnalysisService } from './AIAnalysisService.js'
import AIDashboard from '../components/AIDashboard.js'

class AIIntegrationService {
  constructor() {
    this.dashboard = null
    this.isInitialized = false
    this.marketDataBuffer = []
    this.bufferSize = 100
    this.analysisInterval = null
    this.analysisFrequency = 30000 // 30 seconds
  }

  async initialize(containerId = 'ai-dashboard-container') {
    if (this.isInitialized) return

    try {
      // Create dashboard container if it doesn't exist
      let container = document.getElementById(containerId)
      if (!container) {
        container = document.createElement('div')
        container.id = containerId
        document.body.appendChild(container)
      }

      // Initialize dashboard
      this.dashboard = new AIDashboard(containerId)
      await this.dashboard.initialize()

      // Initialize market data service
      marketDataService.connect()

      // Subscribe to market data
      marketDataService.subscribeKline('BTCUSDT', '1m', this.handleMarketData.bind(this))

      // Set up periodic analysis
      this.setupAnalysisInterval()

      this.isInitialized = true
      console.log('AI Integration Service initialized')
    } catch (error) {
      console.error('Failed to initialize AI Integration Service:', error)
      throw error
    }
  }

  handleMarketData(data) {
    if (!data || !data.data) return

    // Add to buffer
    this.marketDataBuffer.push(data.data)

    // Keep buffer size limited
    if (this.marketDataBuffer.length > this.bufferSize) {
      this.marketDataBuffer.shift()
    }

    // Update dashboard with latest data
    if (this.dashboard && data.data.close) {
      this.dashboard.update({
        open: data.data.open,
        high: data.data.high,
        low: data.data.low,
        close: data.data.close,
        volume: data.data.volume,
        timestamp: data.data.time,
      })
    }
  }

  setupAnalysisInterval() {
    // Clear existing interval if any
    if (this.analysisInterval) {
      clearInterval(this.analysisInterval)
    }

    // Perform initial analysis
    this.performAnalysis()

    // Set up periodic analysis
    this.analysisInterval = setInterval(() => {
      this.performAnalysis()
    }, this.analysisFrequency)
  }

  async performAnalysis() {
    if (this.marketDataBuffer.length === 0) return

    try {
      // Get the latest market data
      const latestData = this.marketDataBuffer[this.marketDataBuffer.length - 1]

      // Perform analysis
      const analysis = await aiAnalysisService.analyzeMarket({
        open: latestData.open,
        high: latestData.high,
        low: latestData.low,
        close: latestData.close,
        volume: latestData.volume,
        timestamp: latestData.time,
      })

      // Process the analysis results
      this.processAnalysisResults(analysis)

      return analysis
    } catch (error) {
      console.error('Error performing analysis:', error)
      return null
    }
  }

  processAnalysisResults(analysis) {
    if (!analysis) return

    // Here you can implement custom logic to handle the analysis results
    // For example, trigger alerts, update UI, or execute trades

    const { actionRecommendation, pricePrediction, trendAnalysis } = analysis

    console.log('Analysis Results:', {
      action: actionRecommendation.action,
      confidence: actionRecommendation.confidence,
      predictedPrice: pricePrediction.prediction,
      trend: trendAnalysis.primaryTrend,
      trendStrength: trendAnalysis.strength,
    })

    // Example: Trigger a custom event that other parts of the app can listen to
    const event = new CustomEvent('aiAnalysisUpdate', {
      detail: analysis,
    })

    window.dispatchEvent(event)
  }

  // Clean up resources
  destroy() {
    if (this.analysisInterval) {
      clearInterval(this.analysisInterval)
      this.analysisInterval = null
    }

    marketDataService.disconnect()

    if (this.dashboard) {
      // If the dashboard has a destroy method, call it
      if (typeof this.dashboard.destroy === 'function') {
        this.dashboard.destroy()
      }
      this.dashboard = null
    }

    this.isInitialized = false
  }
}

// Export as singleton
export const aiIntegrationService = new AIIntegrationService()

// Auto-initialize when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    aiIntegrationService.initialize().catch(console.error)
  })
} else {
  aiIntegrationService.initialize().catch(console.error)
}
