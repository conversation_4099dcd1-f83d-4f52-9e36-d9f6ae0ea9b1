// StarCrypt Enterprise - Main Application Controller
/**
 * StarCrypt Enterprise - Main Application Initialization
 * Coordinates all UI components, data flow, and strategy management
 */

// Configure WebSocket URL
if (!window.wsUrl) {
  const httpPort = window.location.port
  const wsPort = (httpPort === '3001') ? '8081' : '8080' // If HTTP is on 3001 (Express fallback), WS is on 8081.
  window.wsUrl = window.location.hostname === 'localhost' ?
    `ws://localhost:${wsPort}` :
    `wss://${window.location.host}` // For deployed, wss typically doesn't specify port if it's standard 443
  console.log('WebSocket URL:', window.wsUrl)
}

// Global error handler for uncaught exceptions
window.onerror = function (message, source, lineno, colno, error) {
  console.error(`Global error: ${message} at ${source}:${lineno}:${colno}`, error)
  return true // Prevent default browser error handling
}

// Global error event listener
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error || event.message, event)
  return false
})

// WebSocket error handler
if (window.ws) {
  window.ws.onerror = function (error) {
    console.error('WebSocket error:', error)
  }
}

/**
 * Main initialization function - runs when DOM is fully loaded
 */
document.addEventListener('DOMContentLoaded', async () => {
  console.log('StarCrypt Enterprise Initializing...')

  try {
    // Verify required global variables are available
    if (!window.TRADING_STRATEGIES) {
      console.error('Error: TRADING_STRATEGIES not defined - check that global-variables.js is loaded')
    }

    // Initialize global state
    initGlobalState()

    // Initialize basic UI components
    initializeBasicUI()

    // Initialize strategy system
    await initializeStrategySystem()

    // Initialize remaining application components
    initializeRemainingComponents()

    // Complete initialization
    completeInitialization()
  } catch (error) {
    console.error('Fatal error during initialization:', error)
    showErrorOverlay('Initialization failed. Please check console for details.')
  }
})

/**
 * Initialize global application state
 */
function initGlobalState() {
  // Core configuration
  window.currentPair = window.currentPair || 'xbtusdt'
  window.currentTf = window.currentTf || '1h'
  window.currentStrategy = window.currentStrategy || 'admiral_toa'
  window.logMessages = window.logMessages || []

  // Initialize enabled indicators with default set
  window.enabledIndicators = [
    'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'adx',
    'williamsR', 'ultimateOscillator', 'mfi', 'vwap', 'fractal',
    'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly',
  ]
}

/**
 * Initialize the strategy system
 */
async function initializeStrategySystem() {
  try {
    const strategyManager = await waitForStrategyManager()
    if (strategyManager) {
      initializeStrategyDependentComponents(strategyManager)
    } else {
      console.warn('Strategy Manager not available, using default strategy')
      initializeFallbackStrategy()
    }
  } catch (error) {
    console.error('Error initializing strategy system:', error)
    initializeFallbackStrategy()
  }
}

/**
 * Complete the initialization process
 */
function completeInitialization() {
  console.log('StarCrypt Enterprise Initialized Successfully')

  // Hide loading overlay
  const loadingOverlay = document.getElementById('loading-overlay')
  if (loadingOverlay) {
    setTimeout(() => {
      loadingOverlay.style.opacity = '0'
      setTimeout(() => {
        loadingOverlay.style.display = 'none'
      }, 1000)
    }, 500)
  }
}

/**
 * Display an error overlay with the given message
 * @param {string} message - The error message to display
 */
function showErrorOverlay(message) {
  const errorOverlay = document.createElement('div')
  errorOverlay.style.position = 'fixed'
  errorOverlay.style.top = '0'
  errorOverlay.style.left = '0'
  errorOverlay.style.width = '100%'
  errorOverlay.style.padding = '20px'
  errorOverlay.style.backgroundColor = 'rgba(255, 0, 0, 0.8)'
  errorOverlay.style.color = 'white'
  errorOverlay.style.textAlign = 'center'
  errorOverlay.style.zIndex = '9999'
  errorOverlay.textContent = message
  document.body.prepend(errorOverlay)
}

/**
 * Wait for the strategy manager to be ready
 * @returns {Promise<Object|null>} The strategy manager or null if not available
 */
async function waitForStrategyManager() {
  // If strategy manager is already available, use it
  if (window.StarCrypt?.StrategyManager) {
    if (window.StarCrypt.StrategyManager.ready) {
      await new Promise(resolve => window.StarCrypt.StrategyManager.ready(resolve))
    }
    return window.StarCrypt.StrategyManager
  }

  // If not available yet, wait a bit and try again
  return new Promise((resolve) => {
    const checkInterval = setInterval(() => {
      if (window.StarCrypt?.StrategyManager) {
        clearInterval(checkInterval)
        if (window.StarCrypt.StrategyManager.ready) {
          window.StarCrypt.StrategyManager.ready(() => resolve(window.StarCrypt.StrategyManager))
        } else {
          resolve(window.StarCrypt.StrategyManager)
        }
      }
    }, 100)

    // Timeout after 5 seconds
    setTimeout(() => {
      clearInterval(checkInterval)
      console.warn('Timeout waiting for strategy manager')
      resolve(null)
    }, 5000)
  })
}

/**
 * Initialize components that depend on the strategy manager
 * @param {Object} strategyManager - The strategy manager instance
 */
function initializeStrategyDependentComponents(strategyManager) {
  console.log('Strategy Manager initialized')

  // Get current strategy from manager
  const currentStrategy = strategyManager.getCurrentStrategy()
  if (currentStrategy && window.TRADING_STRATEGIES?.[currentStrategy]) {
    window.currentStrategy = currentStrategy
    console.log('Current strategy set to:', currentStrategy)
  } else {
    console.warn('Invalid current strategy, using default')
    window.currentStrategy = 'admiral_toa'
    strategyManager.setStrategy('admiral_toa')
  }

  // Listen for strategy changes
  strategyManager.on('change', (event) => {
    console.log('Strategy changed to:', event.strategy)
    window.currentStrategy = event.strategy

    // Update UI components that depend on strategy
    if (typeof updateStrategyInfoPanel === 'function') {
      updateStrategyInfoPanel(event.strategy)
    }

    // Update any other components that depend on strategy
    if (window.SignalManager) {
      window.SignalManager.onStrategyChange(event.strategy)
    }
  })

  // Initialize strategy UI components
  initializeStrategyUI(strategyManager)
}

/**
 * Initialize basic UI components that don't depend on the strategy
 */
function initializeBasicUI() {
  // Add any basic UI initialization here
  console.log('Initializing basic UI components')
}

/**
 * Initialize fallback strategy when strategy manager is not available
 */
function initializeFallbackStrategy() {
  console.warn('Using fallback strategy initialization')
  // Initialize any fallback UI components
  initializeStrategyUI(null)
}

/**
 * Initialize the rest of the application components
 */
function initializeRemainingComponents() {
  if (window.initializeThresholdsMenu) {
    window.initializeThresholdsMenu()
  } else {
    console.warn('initializeThresholdsMenu function not found.')
  }
  console.log('Initializing remaining components')
  // Add any remaining initialization code here
}

/**
 * Initialize strategy-related UI components
 * @param {Object|null} strategyManager - The strategy manager or null if not available
 */
function initializeStrategyUI(strategyManager) {
  // Add strategy UI initialization code here
  console.log('Initializing strategy UI')

  // Set enabled indicators based on current strategy
  window.enabledIndicators = [
    'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'adx',
    'williamsR', 'ultimateOscillator', 'mfi', 'vwap', 'fractal',
    'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly',
  ]

  // Initialize strategy selection UI
  initializeStrategySelection(strategyManager)
}

/**
 * Initialize strategy selection UI components
 * @param {Object|null} strategyManager - The strategy manager or null if not available
 */
function initializeStrategySelection(strategyManager) {
  const strategySelect = document.getElementById('mainStrategySelector')
  const applyStrategyBtn = document.getElementById('applyStrategyBtn')

  if (!strategySelect || !applyStrategyBtn) {
    console.warn('Strategy selection elements not found')
    return
  }

  // Function to handle strategy application
  const applyStrategy = () => {
    if (!strategySelect) return

    const selectedStrategy = strategySelect.value
    if (!selectedStrategy || selectedStrategy === window.currentStrategy) {
      return // No change or invalid selection
    }

    console.log('Applying strategy:', selectedStrategy)

    try {
      // Use strategy manager if available, otherwise fallback to direct assignment
      if (strategyManager) {
        // The strategy manager will handle all the updates
        strategyManager.setStrategy(selectedStrategy)
      } else {
        // Fallback implementation if strategy manager is not available
        const previousStrategy = window.currentStrategy
        window.currentStrategy = selectedStrategy

        // Save to localStorage
        try {
          localStorage.setItem('currentStrategy', selectedStrategy)
        } catch (e) {
          console.warn('Could not save strategy to localStorage:', e)
        }

        // Update enabled indicators based on the new strategy
        const strategy = window.TRADING_STRATEGIES[selectedStrategy]
        if (strategy && Array.isArray(strategy.indicators)) {
          window.enabledIndicators = [...strategy.indicators]

          // Update the signal matrix
          if (typeof window.updateSignalMatrix === 'function') {
            window.updateSignalMatrix()
          }

          // Update any other components that depend on the strategy
          if (typeof window.onStrategyChanged === 'function') {
            window.onStrategyChanged(selectedStrategy, previousStrategy)
          }
        }
      }

      // Show success message
      const statusEl = document.getElementById('strategyStatus')
      if (statusEl) {
        statusEl.textContent = `Strategy updated to: ${selectedStrategy}`
        statusEl.className = 'status success'
        setTimeout(() => {
          statusEl.textContent = ''
          statusEl.className = 'status'
        }, 3000)
      }
    } catch (error) {
      console.error('Error applying strategy:', error)

      // Revert the select to previous value
      if (strategySelect) {
        strategySelect.value = window.currentStrategy
      }

      // Show error message
      const statusEl = document.getElementById('strategyStatus')
      if (statusEl) {
        statusEl.textContent = `Failed to update strategy: ${error.message || 'Unknown error'}`
        statusEl.className = 'status error'
        setTimeout(() => {
          statusEl.textContent = ''
          statusEl.className = 'status'
        }, 5000)
      }
    }
  }

  // Set up event listeners
  if (strategySelect) {
    // Preview strategy on change
    strategySelect.addEventListener('change', (event) => {
      const selectedStrategy = event.target.value
      console.log('Strategy preview:', selectedStrategy)

      // Update strategy info panel with the selected strategy details
      if (typeof window.updateStrategyInfoPanel === 'function') {
        window.updateStrategyInfoPanel(selectedStrategy)
      }
    })

    // Apply strategy on button click or Enter key
    const handleApply = () => {
      applyStrategy()
      return false
    }

    if (applyStrategyBtn) {
      applyStrategyBtn.addEventListener('click', handleApply)
    }

    strategySelect.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        handleApply()
      }
    })

    // Set initial strategy from localStorage or default
    const savedStrategy = localStorage.getItem('currentStrategy')
    if (savedStrategy && window.TRADING_STRATEGIES[savedStrategy]) {
      window.currentStrategy = savedStrategy
      strategySelect.value = savedStrategy

      // Update UI to reflect the current strategy
      if (typeof window.updateStrategyInfoPanel === 'function') {
        window.updateStrategyInfoPanel(savedStrategy)
      }
    } else if (strategySelect.value !== window.currentStrategy) {
      strategySelect.value = window.currentStrategy
    }
  }

  // Initialize signal matrix
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
  if (typeof window.createSignalMatrix === 'function') {
    if (typeof window.isSignalMatrixInitialized === 'undefined') {
      window.isSignalMatrixInitialized = true
      window.createSignalMatrix('signalMatrixContainer', timeframes, window.enabledIndicators)
      console.log('Signal matrix initialized')
    } else {
      console.log('Signal matrix already initialized, skipping to avoid duplicates.')
    }
  } else {
    console.error('Signal matrix initialization function not found')
  }

  // Initialize strategy selector
  if (typeof window.initializeStrategySelector === 'function') {
    window.initializeStrategySelector()
    console.log('Strategy selector initialized')
  }

  // Initialize all event handlers
  if (typeof window.initializeEventHandlers === 'function') {
    window.initializeEventHandlers()
    console.log('Event handlers initialized')
  }

  // Initialize animations
  if (typeof window.initializeAnimations === 'function') {
    window.initializeAnimations()
    console.log('Animations initialized')
  }

  // Initialize charts
  if (typeof window.initializeCharts === 'function') {
    window.initializeCharts()
    console.log('Charts initialized')
  }

  // Initialize signal lights
  if (typeof window.updateAllSignalLights === 'function') {
    window.updateAllSignalLights()
    console.log('Signal lights initialized')
  }

  // Connect to WebSocket server
  // This happens automatically through websocket-init.js

  // Show initialization complete message
  console.log('StarCrypt Enterprise Initialized Successfully')

  // Hide loading overlay once everything is ready
  const loadingOverlay = document.getElementById('loading-overlay')
  if (loadingOverlay) {
    setTimeout(() => {
      loadingOverlay.style.opacity = '0'
      setTimeout(() => {
        loadingOverlay.style.display = 'none'
      }, 1000)
    }, 500)
  }
} // End of initializeRemainingComponents function
