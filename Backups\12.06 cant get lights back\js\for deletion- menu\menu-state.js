/**
 * @module MenuStateManager
 * @description Centralized state management for menu system
 */

import { StateManager } from '../utils/state-manager.js'
import { MenuEvents } from './menu-events.js'

export class MenuStateManager extends StateManager {
  constructor() {
    super({
      initialState: {
        activeMenu: null,
        menuStack: [],
        menuItems: new Map(),
        menuHistory: [],
        errorState: null,
        loadingState: false,
        preferences: {
          theme: 'light',
          keyboardNavigation: true,
          animation: true,
          lastUpdate: Date.now(),
        },
      },
      storageKey: 'menu-state',
    })

    // Initialize events
    this.events = new MenuEvents()

    // Setup event listeners
    this.setupEventListeners()
  }

  /**
     * Setup event listeners
     * @private
     */
  setupEventListeners() {
    // Listen for menu changes
    this.events.on('menu-change', (data) => {
      this.updateState({
        activeMenu: data.menuId,
        menuStack: [...this.state.menuStack, data.menuId],
      })
    })

    // Listen for preference changes
    this.events.on('preferences-change', (data) => {
      this.updatePreferences(data)
    })

    // Listen for error state
    this.events.on('error', (error) => {
      this.setErrorState(error)
    })

    // Listen for loading state
    this.events.on('loading', (isLoading) => {
      this.setLoadingState(isLoading)
    })
  }

  /**
     * Update menu state
     * @param {Object} updates - State updates
     * @private
     */
  updateState(updates) {
    try {
      // Validate updates
      this.validateStateUpdates(updates)

      // Update state
      this.setState(prevState => ({
        ...prevState,
        ...updates,
        lastUpdate: Date.now(),
      }))

      // Dispatch state change event
      this.events.dispatch('state-change', {
        state: this.state,
        updates,
        timestamp: Date.now(),
      })
    } catch (error) {
      this.handleError(error, 'state-update')
    }
  }

  /**
     * Validate state updates
     * @param {Object} updates - State updates to validate
     * @private
     */
  validateStateUpdates(updates) {
    if (!updates) {
      throw new Error('No state updates provided')
    }

    // Validate menu IDs
    if (updates.activeMenu && typeof updates.activeMenu !== 'string') {
      throw new Error('Invalid menu ID format')
    }

    // Validate menu stack
    if (updates.menuStack && !Array.isArray(updates.menuStack)) {
      throw new Error('Menu stack must be an array')
    }

    // Validate preferences
    if (updates.preferences) {
      this.validatePreferences(updates.preferences)
    }
  }

  /**
     * Validate preferences
     * @param {Object} preferences - Preferences to validate
     * @private
     */
  validatePreferences(preferences) {
    const validThemes = ['light', 'dark']

    if (typeof preferences.theme !== 'string' ||
            !validThemes.includes(preferences.theme)) {
      throw new Error(`Invalid theme: ${preferences.theme}`)
    }

    if (typeof preferences.keyboardNavigation !== 'boolean') {
      throw new Error('keyboardNavigation must be boolean')
    }

    if (typeof preferences.animation !== 'boolean') {
      throw new Error('animation must be boolean')
    }
  }

  /**
     * Update preferences
     * @param {Object} preferences - Preferences to update
     * @private
     */
  updatePreferences(preferences) {
    try {
      // Validate preferences
      this.validatePreferences(preferences)

      // Update state
      this.updateState({
        preferences: {
          ...this.state.preferences,
          ...preferences,
          lastUpdate: Date.now(),
        },
      })

      // Dispatch preferences change event
      this.events.dispatch('preferences-updated', {
        preferences: this.state.preferences,
        timestamp: Date.now(),
      })
    } catch (error) {
      this.handleError(error, 'preferences-update')
    }
  }

  /**
     * Set error state
     * @param {Error} error - Error object
     * @private
     */
  setErrorState(error) {
    try {
      // Update state
      this.updateState({
        errorState: {
          message: error.message,
          stack: error.stack,
          timestamp: Date.now(),
        },
      })

      // Dispatch error state change
      this.events.dispatch('error-state-change', {
        error: this.state.errorState,
        timestamp: Date.now(),
      })
    } catch (error) {
      console.error('[MenuStateManager] Error setting error state:', error)
    }
  }

  /**
     * Set loading state
     * @param {boolean} isLoading - Loading state
     * @private
     */
  setLoadingState(isLoading) {
    try {
      // Update state
      this.updateState({
        loadingState: isLoading,
      })

      // Dispatch loading state change
      this.events.dispatch('loading-state-change', {
        isLoading,
        timestamp: Date.now(),
      })
    } catch (error) {
      console.error('[MenuStateManager] Error setting loading state:', error)
    }
  }

  /**
     * Handle errors
     * @param {Error} error - Error object
     * @param {string} context - Context where error occurred
     * @private
     */
  handleError(error, context) {
    console.error(`[MenuStateManager] Error in ${context}:`, error)

    // Set error state
    this.setErrorState(error)

    // Dispatch error event
    this.events.dispatch('error', {
      error,
      context,
      timestamp: Date.now(),
    })
  }

  /**
     * Reset state to initial values
     * @private
     */
  resetState() {
    try {
      // Reset to initial state
      this.setState(this.config.initialState)

      // Dispatch reset event
      this.events.dispatch('state-reset', {
        timestamp: Date.now(),
      })
    } catch (error) {
      console.error('[MenuStateManager] Error resetting state:', error)
    }
  }

  /**
     * Get current state
     * @returns {Object} Current state
     */
  getState() {
    return { ...this.state }
  }

  /**
     * Get specific state property
     * @param {string} property - Property to get
     * @returns {*} Property value
     */
  getStateProperty(property) {
    return this.state[property]
  }

  /**
     * Destroy state manager
     */
  destroy() {
    try {
      // Remove event listeners
      this.events.removeAllListeners()

      // Reset state
      this.resetState()

      // Clear storage
      this.clearStorage()
    } catch (error) {
      console.error('[MenuStateManager] Error destroying:', error)
    }
  }
}

// Export singleton instance
const menuStateManager = new MenuStateManager()
export default menuStateManager
