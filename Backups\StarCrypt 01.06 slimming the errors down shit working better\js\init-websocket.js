// WebSocket initialization with error handling and reconnection
(function () {
  'use strict'

  // Configuration
  const CONFIG = {
    // WebSocket Manager settings
    wsManager: {
      maxReconnectAttempts: 10,
      reconnectInterval: 1000,
      maxReconnectInterval: 30000,
      connectionTimeout: 5000,
      pingInterval: 25000,
      pingTimeout: 10000,
      maxProcessingDepth: 20,
      maxConsecutiveErrors: 10,
      errorResetTime: 30000,
    },
    // WebSocket Processor settings
    wsProcessor: {
      maxBatchSize: 10,
      processDelay: 50,
      maxQueueSize: 1000,
      rateLimit: 1000,
      maxProcessedMessages: 5000,
      maxDepth: 20,
      maxConsecutiveErrors: 10,
      errorResetTime: 30000,
    },
    // Channels to subscribe to
    channels: ['prices', 'signals', 'heartbeat'],
  }

  // Wait for DOM to be fully loaded
  function init() {
    console.log('[WebSocket] Initializing...')

    // Check for required dependencies
    if (!window.WebSocketManager) {
      console.error('[WebSocket] WebSocketManager not found. Make sure websocket-manager.js is loaded first.')
      return
    }

    if (!window.WebSocketProcessor) {
      console.error('[WebSocket] WebSocketProcessor not found. Make sure websocket-processor-fixed.js is loaded.')
      return
    }

    initializeWebSocket()
    initializeSignalLightsManager()
  }

  /**
     * Initialize WebSocket connection and set up event handlers
     */
  function initializeWebSocket() {
    try {
      // Determine WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsUrl = `${protocol}//${window.location.hostname}:8080`

      console.log(`[WebSocket] Connecting to ${wsUrl}`)

      // Initialize WebSocket manager and processor
      const wsManager = new WebSocketManager(wsUrl, [], CONFIG.wsManager)
      const wsProcessor = new WebSocketProcessor(CONFIG.wsProcessor)

      // Set up message handlers
      setupMessageHandlers(wsProcessor)

      // Connect manager to processor
      wsManager.on('message', (message) => {
        try {
          const parsedMessage = typeof message === 'string' ? JSON.parse(message) : message
          wsProcessor.queueMessage(parsedMessage)
        } catch (error) {
          console.error('[WebSocket] Error parsing message:', error)
        }
      })

      // Handle connection events
      wsManager.on('open', () => handleConnectionOpen(wsManager, wsProcessor))
      wsManager.on('close', handleConnectionClose)
      wsManager.on('error', (error) => handleConnectionError(error, wsProcessor))
      wsManager.on('reconnect', handleReconnect)

      // Handle page visibility changes
      setupVisibilityHandlers(wsManager)

      // Add global error handler for WebSocket errors
      document.addEventListener('websocket:error', (event) => {
        console.error('WebSocket error:', event.detail)
      })

      // Export for debugging
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        window.wsManager = wsManager
        window.wsProcessor = wsProcessor
      }

      // Add cleanup on page unload
      window.addEventListener('beforeunload', () => {
        console.log('[WebSocket] Cleaning up...')
        if (wsManager) {
          wsManager.close()
        }
        if (wsProcessor) {
          wsProcessor.destroy()
        }
      })

      return { wsManager, wsProcessor }
    } catch (error) {
      console.error('[WebSocket] Error initializing WebSocket:', error)
      throw error // Re-throw to be caught by the global error handler
    }
  }

  /**
     * Initialize Signal Lights Manager if available
     */
  function initializeSignalLightsManager() {
    if (!window.SignalLightsManager) {
      console.error('SignalLightsManager not found!')
      return
    }

    try {
      window.signalLightsManager = new window.SignalLightsManager()
      console.log('SignalLightsManager initialized')
    } catch (error) {
      console.error('Failed to initialize SignalLightsManager:', error)
    }
  }

  /**
     * Handle WebSocket connection opened event
     */
  function handleConnectionOpen(wsManager, wsProcessor) {
    console.log('[WebSocket] Connection established')

    if (wsProcessor) {
      wsProcessor.consecutiveErrors = 0
    }

    // Subscribe to channels
    try {
      const subscribeMessage = {
        type: 'subscribe',
        channels: CONFIG.channels,
        timestamp: Date.now(),
      }

      wsManager.send(JSON.stringify(subscribeMessage))
      console.log('[WebSocket] Subscribed to channels:', CONFIG.channels)
    } catch (error) {
      console.error('[WebSocket] Error subscribing to channels:', error)
    }
  }

  /**
     * Handle WebSocket connection closed event
     */
  function handleConnectionClose(event) {
    console.log('[WebSocket] Connection closed', {
      code: event.code,
      reason: event.reason,
      wasClean: event.wasClean,
    })
  }

  /**
     * Handle WebSocket connection errors
     */
  function handleConnectionError(error, wsProcessor) {
    console.error('[WebSocket] Connection error:', error)
    if (wsProcessor) {
      wsProcessor.pauseProcessing(5000) // Pause processing on error
    }
  }

  /**
     * Handle reconnection attempts
     */
  function handleReconnect(attempt) {
    console.log(`[WebSocket] Reconnect attempt ${attempt}`)
  }

  /**
     * Set up message handlers for different message types
     */
  function setupMessageHandlers(processor) {
    if (!processor || typeof processor.addMessageHandler !== 'function') {
      console.error('[WebSocket] Invalid processor or addMessageHandler not available')
      return
    }

    // Price update handler
    processor.addMessageHandler('price', (data) => {
      try {
        const event = new CustomEvent('priceUpdate', {
          detail: data,
          bubbles: true,
          cancelable: true,
        })
        document.dispatchEvent(event)
      } catch (error) {
        console.error('[WebSocket] Error handling price update:', error)
      }
    })

    // Signal update handler
    processor.addMessageHandler('signal', (data) => {
      try {
        const event = new CustomEvent('signalUpdate', {
          detail: data,
          bubbles: true,
          cancelable: true,
        })
        document.dispatchEvent(event)
      } catch (error) {
        console.error('[WebSocket] Error handling signal update:', error)
      }
    })

    // Indicator update handler
    processor.addMessageHandler('indicator', (data) => {
      try {
        const event = new CustomEvent('indicatorUpdate', {
          detail: data,
          bubbles: true,
          cancelable: true,
        })
        document.dispatchEvent(event)
      } catch (error) {
        console.error('[WebSocket] Error handling indicator update:', error)
      }
    })

    // Heartbeat handler
    processor.addMessageHandler('heartbeat', (data) => {
      console.debug('[WebSocket] Heartbeat received', data)
    })

    // Error handler
    processor.addMessageHandler('error', (data) => {
      console.error('[WebSocket] Server error:', data)
      const event = new CustomEvent('websocketError', {
        detail: data,
        bubbles: true,
        cancelable: true,
      })
      document.dispatchEvent(event)
    })
  }

  /**
     * Set up page visibility change handlers
     */
  function setupVisibilityHandlers(wsManager) {
    if (!wsManager) return

    let hidden; let visibilityChange

    if (typeof document.hidden !== 'undefined') {
      hidden = 'hidden'
      visibilityChange = 'visibilitychange'
    } else if (typeof document.msHidden !== 'undefined') {
      hidden = 'msHidden'
      visibilityChange = 'msvisibilitychange'
    } else if (typeof document.webkitHidden !== 'undefined') {
      hidden = 'webkitHidden'
      visibilityChange = 'webkitvisibilitychange'
    } else {
      return // Page Visibility API not supported
    }

    if (typeof document[hidden] !== 'undefined') {
      document.addEventListener(visibilityChange, () => {
        if (!document[hidden] && wsManager.ws && wsManager.ws.readyState !== WebSocket.OPEN) {
          console.log('[WebSocket] Page visible, reconnecting...')
          wsManager.reconnect()
        }
      }, false)
    }
  }

  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    try {
      init()
    } catch (error) {
      console.error('[WebSocket] Initialization error:', error)
    }
  })

  // Also try to initialize if DOM is already loaded
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    init()
  }
})()
