/**
 * Signal Calculator
 * Handles signal calculations and prevents recursion
 */

class SignalCalculator {
  constructor() {
    this.recursionDepth = 0
    this.maxRecursionDepth = 5
    this.errorCount = 0
    this.maxErrors = 10
    this.lastCalculation = 0
    this.throttleTimeout = 100
  }

  async calculateSignal(data, indicator, timeframe) {
    try {
      // Break call stack
      await new Promise(resolve => setTimeout(resolve, 0))

      // Check recursion depth
      if (this.recursionDepth >= this.maxRecursionDepth) {
        throw new Error('Maximum recursion depth exceeded')
      }

      // Increment recursion depth
      this.recursionDepth++

      // Throttle calculations
      const now = Date.now()
      if (now - this.lastCalculation < this.throttleTimeout) {
        await new Promise(resolve => setTimeout(resolve, this.throttleTimeout))
      }
      this.lastCalculation = now

      // Get calculation function for this indicator
      const calcFunc = this.getCalculationFunction(indicator)
      if (!calcFunc) {
        throw new Error(`No calculation function for indicator: ${indicator}`)
      }

      // Calculate signal
      const signal = await calcFunc(data, timeframe)

      // Reset recursion depth
      this.recursionDepth = 0

      return signal
    } catch (error) {
      console.error(`[SignalCalculator] Error calculating signal for ${indicator} ${timeframe}:`, error)
      this.handleError(error)
      throw error
    }
  }

  getCalculationFunction(indicator) {
    // Map of indicator names to calculation functions
    const calcFunctions = {
      momentum: this.calculateMomentum,
      trend: this.calculateTrend,
      volume: this.calculateVolume,
      ml: this.calculateML,
    }

    return calcFunctions[indicator] || this.calculateDefault
  }

  calculateMomentum(data, timeframe) {
    // Implementation of momentum calculation
    return {
      value: data.momentum || 0,
      color: this.getColor(data.momentum),
      intensity: this.getIntensity(data.momentum),
    }
  }

  calculateTrend(data, timeframe) {
    // Implementation of trend calculation
    return {
      value: data.trend || 0,
      color: this.getColor(data.trend),
      intensity: this.getIntensity(data.trend),
    }
  }

  calculateVolume(data, timeframe) {
    // Implementation of volume calculation
    return {
      value: data.volume || 0,
      color: this.getColor(data.volume),
      intensity: this.getIntensity(data.volume),
    }
  }

  calculateML(data, timeframe) {
    // Implementation of ML calculation
    return {
      value: data.ml || 0,
      color: this.getColor(data.ml),
      intensity: this.getIntensity(data.ml),
    }
  }

  calculateDefault(data, timeframe) {
    // Default calculation if no specific function exists
    return {
      value: data.value || 0,
      color: this.getColor(data.value),
      intensity: this.getIntensity(data.value),
    }
  }

  getColor(value) {
    // Color mapping based on value
    if (value > 0) return '#00ff00'
    if (value < 0) return '#ff0000'
    return '#ffffff'
  }

  getIntensity(value) {
    // Intensity mapping based on value
    const absValue = Math.abs(value)
    return Math.min(1, absValue / 100)
  }

  handleError(error) {
    this.errorCount++
    if (this.errorCount > this.maxErrors) {
      console.error('[SignalCalculator] Too many errors, disabling calculations')
      this.disableCalculations()
    }
  }

  disableCalculations() {
    this.recursionDepth = this.maxRecursionDepth
    this.errorCount = this.maxErrors
  }

  static getInstance() {
    if (!window.signalCalculator) {
      window.signalCalculator = new SignalCalculator()
    }
    return window.signalCalculator
  }
}

// Initialize SignalCalculator when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    SignalCalculator.getInstance()
  })
} else {
  SignalCalculator.getInstance()
}

// Export for global access
window.SignalCalculator = SignalCalculator
