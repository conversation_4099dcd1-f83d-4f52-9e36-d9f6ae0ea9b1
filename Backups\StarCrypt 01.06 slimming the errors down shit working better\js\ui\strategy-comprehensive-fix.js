/**
 * StarCrypt Strategy Comprehensive Fix
 * This script fixes all strategy-related issues in a single file:
 * 1. Ensures all strategies display their correct indicators
 * 2. Adds proper signal lights (5-color logic) for all indicators
 * 3. Ensures helper text changes correctly for each strategy
 * 4. Prevents UI lockup after strategy switches
 * 5. Makes all indicators globally available and consistent
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('[StrategyFix] Initializing comprehensive strategy fix')

  // Fix Apply button functionality
  fixApplyStrategyButton()

  // Fix the menu item functionality
  fixMenuItemsFunctionality()

  // Fix signal light display
  fixSignalLightDisplay()

  // Ensure all indicators exist in Oracle Matrix
  ensureAllIndicatorsExist()

  console.log('[StrategyFix] Initialization complete')
})

// Fix the Apply Strategy button functionality
function fixApplyStrategyButton() {
  try {
    const applyButton = document.getElementById('applyStrategyButton')
    if (!applyButton) {
      console.error('[StrategyFix] Apply button not found')
      return
    }

    // Remove any existing listeners
    const newButton = applyButton.cloneNode(true)
    if (applyButton.parentNode) {
      applyButton.parentNode.replaceChild(newButton, applyButton)
    }

    // Add new listener
    newButton.addEventListener('click', (event) => {
      // Get selected strategy
      const strategySelector = document.getElementById('mainStrategySelector')
      if (!strategySelector) {
        console.error('[StrategyFix] Strategy selector not found')
        return
      }

      const selectedStrategy = strategySelector.value
      console.log('[StrategyFix] Applying strategy:', selectedStrategy)

      // Save to localStorage
      try {
        localStorage.setItem('currentStrategy', selectedStrategy)
      } catch (e) {
        console.error('[StrategyFix] Failed to save to localStorage:', e)
      }

      // Update global variable
      window.currentStrategy = selectedStrategy

      // Update helper text and steps
      updateHelperForStrategy(selectedStrategy)

      // Show animation
      if (typeof window.enhancedShowStrategyAnimation === 'function') {
        window.enhancedShowStrategyAnimation(selectedStrategy)
      } else if (typeof window.showStrategyAnimation === 'function') {
        window.showStrategyAnimation(selectedStrategy)
      }

      // Update strategy in UI
      updateStrategyUI(selectedStrategy)

      // Force update of all indicators
      if (typeof window.updateSignalMatrix === 'function') {
        window.updateSignalMatrix()
      }

      // Ensure all signal lights are visible
      ensureSignalLightsForAllIndicators(selectedStrategy)

      // Prevent default behavior
      event.preventDefault()
    })

    console.log('[StrategyFix] Apply button fixed')
  } catch (e) {
    console.error('[StrategyFix] Error fixing apply button:', e)
  }
}

// Make sure all menu items continue to work after strategy switch
function fixMenuItemsFunctionality() {
  try {
    // Fix menu functionality by reattaching event listeners
    const menuButtons = document.querySelectorAll('.menu-button')
    menuButtons.forEach(button => {
      // Clone and replace to remove old listeners
      const newButton = button.cloneNode(true)
      if (button.parentNode) {
        button.parentNode.replaceChild(newButton, button)
      }

      // Add new listener
      newButton.addEventListener('click', function (event) {
        const targetId = this.getAttribute('data-target')
        const targetMenu = document.getElementById(targetId)

        if (targetMenu) {
          // Hide all other menus
          document.querySelectorAll('.menu-content').forEach(menu => {
            if (menu.id !== targetId) {
              menu.style.display = 'none'
            }
          })

          // Toggle this menu
          targetMenu.style.display = targetMenu.style.display === 'block' ? 'none' : 'block'
        }

        event.preventDefault()
      })
    })

    console.log('[StrategyFix] Menu functionality fixed')
  } catch (e) {
    console.error('[StrategyFix] Error fixing menu functionality:', e)
  }
}

// Fix signal light display for all indicators
function fixSignalLightDisplay() {
  try {
    // Define 5-color logic
    window.getSignalColor = function (signal, strength = 0.5) {
      if (signal === 'buy') {
        return strength > 0.6 ? '#00FF00' : '#00AAFF' // Strong vs mild buy
      } else if (signal === 'sell') {
        return strength > 0.6 ? '#FF0000' : '#FFA500' // Strong vs mild sell
      }
      return '#808080' // Neutral - always gray, never blank
    }

    console.log('[StrategyFix] Signal color logic fixed')
  } catch (e) {
    console.error('[StrategyFix] Error fixing signal colors:', e)
  }
}

// Ensure all indicators exist in the Oracle Matrix
function ensureAllIndicatorsExist() {
  try {
    // Define all possible indicators
    const allIndicators = [
      'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'adx',
      'williamsR', 'ultimateOscillator', 'mfi', 'vwap', 'fractal',
      'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly',
    ]

    // Ensure all indicators exist in Oracle Matrix
    if (typeof window.ensureIndicatorRowsExist === 'function') {
      window.ensureIndicatorRowsExist(allIndicators)
    }

    console.log('[StrategyFix] All indicators added to Oracle Matrix')
  } catch (e) {
    console.error('[StrategyFix] Error adding indicators:', e)
  }
}

// Update helper text and steps for specific strategy
function updateHelperForStrategy(strategy) {
  try {
    if (!window.TRADING_STRATEGIES || !window.TRADING_STRATEGIES[strategy]) {
      console.error('[StrategyFix] Strategy not found:', strategy)
      return
    }

    const strategyDetails = window.TRADING_STRATEGIES[strategy]

    // Update helper text
    const helperContainer = document.getElementById('helper-container')
    if (helperContainer) {
      // Update helper title
      const helperTitle = helperContainer.querySelector('.section-header h3')
      if (helperTitle) {
        helperTitle.textContent = `Trading Helper: ${strategyDetails.name}`
      }

      // Update helper content
      const helperContent = helperContainer.querySelector('.helper-content')
      if (helperContent && strategyDetails.helperText) {
        helperContent.innerHTML = strategyDetails.helperText
      } else if (helperContent) {
        // Generate default helper text based on indicators
        helperContent.innerHTML = generateHelperText(strategy)
      }
    }

    console.log('[StrategyFix] Helper updated for strategy:', strategy)
  } catch (e) {
    console.error('[StrategyFix] Error updating helper:', e)
  }
}

// Generate helper text based on strategy indicators
function generateHelperText(strategy) {
  try {
    if (!window.TRADING_STRATEGIES || !window.TRADING_STRATEGIES[strategy]) {
      return 'Strategy information not available.'
    }

    const strategyDetails = window.TRADING_STRATEGIES[strategy]
    const indicators = strategyDetails.indicators || []

    let helperHTML = `<p><strong>${strategyDetails.name}</strong> - ${strategyDetails.description}</p>
                     <p>Follow these steps:</p>
                     <ol>`

    // Add steps based on indicators
    if (indicators.includes('rsi')) {
      helperHTML += `<li>Check if RSI is above 70 (overbought) or below 30 (oversold)</li>`
    }

    if (indicators.includes('macd')) {
      helperHTML += `<li>Confirm MACD line crossed signal line (bullish/bearish)</li>`
    }

    if (indicators.includes('bollingerBands')) {
      helperHTML += `<li>Verify price position relative to Bollinger Bands</li>`
    }

    if (indicators.includes('volume')) {
      helperHTML += `<li>Check for volume confirmation of price movement</li>`
    }

    if (indicators.includes('ml') || indicators.includes('sentiment')) {
      helperHTML += `<li>Verify AI signals align with technical indicators</li>`
    }

    // Add general guidance
    helperHTML += `<li>Look for convergence across multiple timeframes</li>
                  <li>Enter when ${indicators.length > 3 ? '3+' : 'all'} indicators align</li>
                </ol>`

    return helperHTML
  } catch (e) {
    console.error('[StrategyFix] Error generating helper text:', e)
    return 'Strategy information temporarily unavailable.'
  }
}

// Update UI elements to reflect selected strategy
function updateStrategyUI(strategy) {
  try {
    if (!window.TRADING_STRATEGIES || !window.TRADING_STRATEGIES[strategy]) {
      console.error('[StrategyFix] Strategy not found for UI update:', strategy)
      return
    }

    // Update strategy name in header
    const strategyNameElement = document.querySelector('.current-strategy')
    if (strategyNameElement) {
      strategyNameElement.textContent = window.TRADING_STRATEGIES[strategy].name
    }

    // Update strategy description
    const strategyDescription = document.getElementById('strategyDescription')
    if (strategyDescription && window.TRADING_STRATEGIES[strategy].description) {
      strategyDescription.innerHTML = window.TRADING_STRATEGIES[strategy].description
    }

    // Update strategy info panel if function exists
    if (typeof window.updateStrategyInfoPanel === 'function') {
      window.updateStrategyInfoPanel(strategy)
    }

    console.log('[StrategyFix] Strategy UI updated for:', strategy)
  } catch (e) {
    console.error('[StrategyFix] Error updating strategy UI:', e)
  }
}

// Ensure signal lights for all indicators in a strategy
function ensureSignalLightsForAllIndicators(strategy) {
  try {
    if (!window.TRADING_STRATEGIES || !window.TRADING_STRATEGIES[strategy]) {
      console.error('[StrategyFix] Strategy not found for signal lights:', strategy)
      return
    }

    const indicators = window.TRADING_STRATEGIES[strategy].indicators || []
    const timeframes = window.TIMEFRAMES || ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

    // Process each indicator
    indicators.forEach(indicator => {
      // Find the row for this indicator
      const row = document.querySelector(`tr[data-indicator="${indicator}"]`)
      if (!row) {
        console.warn(`[StrategyFix] Row not found for indicator: ${indicator}`)
        return
      }

      // Ensure the row is visible
      row.style.display = ''

      // Check if signal lights need to be created
      const signalLightsCells = row.querySelectorAll('.signal-light-cell')
      if (signalLightsCells.length === 0) {
        // Create signal cells for each timeframe
        timeframes.forEach(tf => {
          const signalCell = document.createElement('td')
          signalCell.setAttribute('data-timeframe', tf)
          signalCell.className = 'signal-light-cell'

          // Create signal light
          const light = document.createElement('div')
          light.className = 'signal-light'
          light.id = `${indicator}-${tf}-signal`
          light.style.backgroundColor = '#808080' // Default to neutral

          signalCell.appendChild(light)
          row.appendChild(signalCell)
        })
      }

      // Ensure name cell has correct color
      const nameCell = row.querySelector('.signal-name')
      if (nameCell) {
        // Color based on indicator type
        if (['ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'].includes(indicator)) {
          nameCell.style.color = '#00AAFF' // AI-based indicators in blue
        } else if (['volume'].includes(indicator)) {
          nameCell.style.color = '#FFA500' // Volume in orange
        } else {
          nameCell.style.color = '#FFFFFF' // Others in white
        }
      }
    })

    console.log('[StrategyFix] Signal lights ensured for all indicators')
  } catch (e) {
    console.error('[StrategyFix] Error ensuring signal lights:', e)
  }
}

// Initialize with current strategy
function initializeWithCurrentStrategy() {
  try {
    // Get current strategy from localStorage or global variable
    let strategy = null

    try {
      strategy = localStorage.getItem('currentStrategy')
    } catch (e) {
      console.warn('[StrategyFix] Could not read from localStorage:', e)
    }

    // Fallback to global variable or default
    if (!strategy) {
      strategy = window.currentStrategy || 'admiral_toa'
    }

    // Check if strategy exists
    if (!window.TRADING_STRATEGIES || !window.TRADING_STRATEGIES[strategy]) {
      console.warn(`[StrategyFix] Strategy not found: ${strategy}, defaulting to admiral_toa`)
      strategy = 'admiral_toa'
    }

    // Update global variable
    window.currentStrategy = strategy

    // Update UI
    const strategySelector = document.getElementById('mainStrategySelector')
    if (strategySelector) {
      strategySelector.value = strategy
    }

    // Update helper and UI
    updateHelperForStrategy(strategy)
    updateStrategyUI(strategy)

    // Ensure signal lights
    ensureSignalLightsForAllIndicators(strategy)

    console.log('[StrategyFix] Initialized with strategy:', strategy)
  } catch (e) {
    console.error('[StrategyFix] Error initializing with current strategy:', e)
  }
}

// Run initialization
initializeWithCurrentStrategy()

// Export functions to global scope
window.fixApplyStrategyButton = fixApplyStrategyButton
window.fixMenuItemsFunctionality = fixMenuItemsFunctionality
window.updateHelperForStrategy = updateHelperForStrategy
window.ensureSignalLightsForAllIndicators = ensureSignalLightsForAllIndicators
