/**
 * StarCrypt Strategy Fix
 * 
 * This is an emergency fix for strategy selection and switching issues.
 * It ensures that strategies are properly selected and applied globally.
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('Strategy fix module loaded');
  
  // Fix the Apply Strategy button
  fixApplyStrategyButton();
  
  // Ensure global strategy is correctly synchronized
  synchronizeGlobalStrategy();
});

// Fix the Apply Strategy button functionality
function fixApplyStrategyButton() {
  try {
    const applyButton = document.getElementById('applyStrategyButton');
    if (!applyButton) {
      console.error('Apply strategy button not found');
      return;
    }
    
    // Store original click handler if it exists
    const originalClickHandler = applyButton.onclick;
    
    // Replace with our fixed handler
    applyButton.onclick = function(event) {
      // Get selected strategy
      const strategySelector = document.getElementById('mainStrategySelector');
      if (!strategySelector) {
        console.error('Strategy selector not found');
        return;
      }
      
      const selectedStrategy = strategySelector.value;
      console.log('Applying strategy:', selectedStrategy);
      
      // Save to localStorage for persistence
      try {
        localStorage.setItem('currentStrategy', selectedStrategy);
        console.log('Strategy saved to localStorage');
      } catch (e) {
        console.error('Failed to save strategy to localStorage:', e);
      }
      
      // Update global strategy
      window.currentStrategy = selectedStrategy;
      
      // Show strategy animation
      if (typeof window.enhancedShowStrategyAnimation === 'function') {
        window.enhancedShowStrategyAnimation(selectedStrategy);
      } else if (typeof window.showStrategyAnimation === 'function') {
        window.showStrategyAnimation(selectedStrategy);
      }
      
      // Force an update of all UI components
      if (typeof window.switchStrategy === 'function') {
        window.switchStrategy(selectedStrategy);
      }
      
      // Update Oracle Matrix
      if (typeof window.updateSignalMatrix === 'function') {
        window.updateSignalMatrix();
      }
      
      // Log the change
      console.log('Strategy successfully applied:', selectedStrategy);
      
      // Prevent event propagation
      event.preventDefault();
      event.stopPropagation();
      
      // Call original handler if needed
      if (typeof originalClickHandler === 'function') {
        originalClickHandler.call(this, event);
      }
      
      return false;
    };
    
    console.log('Apply strategy button fixed');
  } catch (e) {
    console.error('Error fixing apply button:', e);
  }
}

// Ensure global strategy is correctly synchronized
function synchronizeGlobalStrategy() {
  try {
    // Try to load from localStorage
    let savedStrategy = null;
    try {
      savedStrategy = localStorage.getItem('currentStrategy');
    } catch (e) {
      console.error('Failed to load strategy from localStorage:', e);
    }
    
    // Get current DOM selected strategy
    const strategySelector = document.getElementById('mainStrategySelector');
    let selectorStrategy = null;
    if (strategySelector) {
      selectorStrategy = strategySelector.value;
    }
    
    // Determine correct strategy
    const currentStrategy = savedStrategy || selectorStrategy || window.currentStrategy || 'admiral_toa';
    
    // Update global
    window.currentStrategy = currentStrategy;
    
    // Update selector if needed
    if (strategySelector && strategySelector.value !== currentStrategy) {
      strategySelector.value = currentStrategy;
    }
    
    console.log('Global strategy synchronized:', currentStrategy);
  } catch (e) {
    console.error('Error synchronizing strategy:', e);
  }
}

// Make functions available globally
window.fixApplyStrategyButton = fixApplyStrategyButton;
window.synchronizeGlobalStrategy = synchronizeGlobalStrategy;
