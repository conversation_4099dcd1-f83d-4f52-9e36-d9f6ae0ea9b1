{"name": "starcrypt-enterprise", "version": "1.0.0", "description": "StarCrypt Enterprise Trading Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@tensorflow/tfjs-node": "^4.22.0", "@tensorflow/tfjs-node-gpu": "^4.22.0", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-zoom": "^2.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "node-fetch": "^2.7.0", "technicalindicators": "^3.1.0", "ws": "^8.18.1"}, "devDependencies": {"@eslint/js": "^9.25.1", "eslint": "^9.25.1", "globals": "^16.0.0", "nodemon": "^3.0.1", "stylelint-config-standard": "^38.0.0", "webpack-cli": "^6.0.1"}}