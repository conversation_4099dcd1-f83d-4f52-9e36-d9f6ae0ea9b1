/**
 * Signal Initialization and Auto-Fix Module
 * Ensures all signal lights are properly initialized and maintained
 */

// Configuration
const SIGNAL_CONFIG = {
  // Signal light states and colors
  STATES: {
    'strong-buy': '#00FF00',
    'mild-buy': '#00AAFF',
    neutral: '#808080',
    'mild-sell': '#FFA500',
    'strong-sell': '#FF0000',
    error: '#FF00FF',
  },

  // Update intervals
  UPDATE_INTERVAL: 1000, // 1 second

  // Maximum number of signal elements to process per batch
  BATCH_SIZE: 10,

  // Timeout for DOM queries
  QUERY_TIMEOUT: 5000,
}

class SignalInitializer {
  constructor() {
    // Signal element tracking
    this.signalElements = new Map()
    this.updateQueue = []
    this.isProcessing = false
    this.initializationAttempts = 0
    this.maxInitializationAttempts = 5

    // Update configuration
    this.updateInterval = 50 // ms between batch updates
    this.batchSize = 5 // Number of elements to update per batch
    this.initialized = false

    // Container IDs to monitor for signal elements
    this.containers = [
      'signal-matrix',
      'indicator-signals',
      'signals-container',
      'indicator-grid',
    ]

    // Theme support
    this.currentTheme = document.documentElement.getAttribute('data-theme') || 'dark'
    this.themeChangeHandlers = []

    // Bind methods
    this.init = this.init.bind(this)
    this.checkAndFixSignals = this.checkAndFixSignals.bind(this)
    this.processUpdateQueue = this.processUpdateQueue.bind(this)
    this.updateSignalElement = this.updateSignalElement.bind(this)
    this.handleThemeChange = this.handleThemeChange.bind(this)
    this.initThemeListener = this.initThemeListener.bind(this)

    // Initialize theme support
    this.initThemeListener()

    // Initialize the signal system
    this.init()
  }

  initThemeListener() {
    // Watch for theme changes if ThemeManager is available
    if (window.ThemeManager) {
      // Get current theme
      this.currentTheme = document.documentElement.getAttribute('data-theme') || 'dark'

      // Listen for theme changes
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'data-theme') {
            this.currentTheme = document.documentElement.getAttribute('data-theme') || 'dark'
            this.handleThemeChange()
          }
        })
      })

      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme'],
      })

      // Initial theme setup
      this.handleThemeChange()
    }
  }

  handleThemeChange() {
    try {
      // Update current theme
      this.currentTheme = document.documentElement.getAttribute('data-theme') || 'dark'

      // Update signal elements when theme changes
      this.containers.forEach(containerId => {
        try {
          const container = document.getElementById(containerId)
          if (container) {
            const signals = container.querySelectorAll('.signal-circle')
            signals.forEach(signal => {
              try {
                // Trigger a visual update to ensure theme colors are applied
                const currentState = this.signalElements.get(signal)?.state || 'neutral'
                this.updateSignalElement(signal, currentState, { force: true })
              } catch (err) {
                console.error(`[SignalInitializer] Error updating signal element during theme change:`, err)
              }
            })
          }
        } catch (err) {
          console.error(`[SignalInitializer] Error processing container ${containerId} during theme change:`, err)
        }
      })

      // Notify any registered handlers with error handling
      this.themeChangeHandlers.forEach(handler => {
        try {
          handler(this.currentTheme)
        } catch (err) {
          console.error('[SignalInitializer] Error in theme change handler:', err)
        }
      })
    } catch (error) {
      console.error('[SignalInitializer] Error in handleThemeChange:', error)
    }
  }

  onThemeChange(handler) {
    if (typeof handler === 'function') {
      this.themeChangeHandlers.push(handler)
    }
    return this
  }

  /**
     * Initialize the signal system
     */
  /**
     * Set up mutation observer to watch for new signal elements
     */
  setupMutationObserver() {
    try {
      if (this.mutationObserver) {
        this.mutationObserver.disconnect()
      }

      // Create a new mutation observer
      this.mutationObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          // Check for added nodes
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check if this is a signal element
              if (node.matches('.signal-circle, .signal-light')) {
                this.registerSignalElement(node)
              }

              // Check for signal elements in the added node
              const signalElements = node.querySelectorAll ?
                node.querySelectorAll('.signal-circle, .signal-light') : []
              signalElements.forEach(el => this.registerSignalElement(el))
            }
          })
        })

        // Check for any new signal elements that might have been missed
        this.checkAndFixSignals()
      })

      // Start observing the document with the configured parameters
      this.mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
      })

      console.log('[SignalInitializer] Mutation observer initialized')
    } catch (error) {
      console.error('[SignalInitializer] Error setting up mutation observer:', error)
    }
  }

  /**
     * Register a signal element for tracking
     */
  registerSignalElement(element) {
    if (!element || !element.classList) return

    const id = element.id || `signal-${Math.random().toString(36).substr(2, 9)}`

    if (!element.id) {
      element.id = id
    }

    // Skip if already registered
    if (this.signalElements.has(id)) return

    // Add to tracking
    this.signalElements.set(id, {
      element,
      state: 'neutral',
      lastUpdated: Date.now(),
      timeout: null,
    })

    // Ensure proper classes and styles
    if (!element.classList.contains('signal-circle')) {
      element.classList.add('signal-circle')
    }

    // Initialize with neutral state
    this.updateSignalElement(element, 'neutral')

    // Add hover effect
    element.style.transition = 'all 0.3s ease'
    element.style.cursor = 'pointer'

    // Add click handler for debugging
    element.addEventListener('click', (e) => {
      const state = this.signalElements.get(id)?.state || 'unknown'
      console.log(`Signal element clicked - ID: ${id}, State: ${state}`, element)
    })

    if (window.DEBUG_SIGNALS) {
      console.log(`[SignalInitializer] Registered signal element: ${id}`)
    }
  }

  init() {
    if (this.initialized || this.initializationAttempts >= this.maxInitializationAttempts) {
      return
    }

    this.initializationAttempts++

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.init())
      return
    }

    try {
      console.log('[SignalInitializer] Initializing signal system...')

      // Set up periodic checking with error handling
      const safeCheckAndFix = () => {
        try {
          this.checkAndFixSignals()
        } catch (err) {
          console.error('[SignalInitializer] Error in periodic check:', err)
        }
      }

      // Set up periodic checking with error handling
      this.checkInterval = setInterval(safeCheckAndFix, SIGNAL_CONFIG.UPDATE_INTERVAL)

      // Set up mutation observer first to catch any early elements
      this.setupMutationObserver()

      // Initial check with error handling
      try {
        this.checkAndFixSignals()
      } catch (err) {
        console.error('[SignalInitializer] Error in initial check:', err)
      }

      // Initialize theme support
      this.initThemeListener()

      this.initialized = true
      console.log('[SignalInitializer] Signal system initialized successfully')
    } catch (error) {
      console.error('[SignalInitializer] Error initializing signal system:', error)

      // Retry initialization with backoff
      if (this.initializationAttempts < this.maxInitializationAttempts) {
        const delay = Math.min(2000 * Math.pow(2, this.initializationAttempts - 1), 30000)
        console.log(`[SignalInitializer] Retrying initialization in ${delay}ms (attempt ${this.initializationAttempts}/${this.maxInitializationAttempts})...`)
        setTimeout(() => this.init(), delay)
      } else {
        console.error('[SignalInitializer] Max initialization attempts reached. Signal system may not function correctly.')
      }
    }
  }

  /**
     * Check and fix all signal elements
     */
  checkAndFixSignals() {
    if (this.isProcessing) {
      return
    }

    this.isProcessing = true

    try {
      // Find all signal elements
      const elements = document.querySelectorAll('.signal-circle')

      if (!elements || elements.length === 0) {
        console.warn('[SignalInitializer] No signal elements found')
        return
      }

      console.log(`[SignalInitializer] Found ${elements.length} signal elements`)

      // Process elements in batches to avoid blocking the UI
      const batchSize = Math.min(SIGNAL_CONFIG.BATCH_SIZE, elements.length)
      let processed = 0

      const processBatch = () => {
        const startIdx = processed
        const endIdx = Math.min(processed + batchSize, elements.length)

        for (let i = startIdx; i < endIdx; i++) {
          const element = elements[i]

          // Skip if already processed
          if (this.signalElements.has(element)) {
            continue
          }

          // Add to tracking
          this.signalElements.set(element, {
            lastUpdated: 0,
            state: 'neutral',
            timeout: null,
          })

          // Ensure proper classes and styles
          if (!element.classList.contains('signal-circle')) {
            element.className = 'signal-circle'
          }

          // Add hover effect
          element.style.transition = 'all 0.3s ease'
          element.style.cursor = 'pointer'

          // Add click handler for debugging
          element.addEventListener('click', (e) => {
            const state = this.signalElements.get(element)?.state || 'unknown'
            console.log(`Signal element clicked - State: ${state}`, element)
          })
        }

        processed = endIdx

        // Process next batch if there are more elements
        if (processed < elements.length) {
          requestAnimationFrame(processBatch)
        } else {
          console.log('[SignalInitializer] Finished processing all signal elements')
          this.isProcessing = false
        }
      }

      // Start processing first batch
      requestAnimationFrame(processBatch)
    } catch (error) {
      console.error('[SignalInitializer] Error checking and fixing signals:', error)
      this.isProcessing = false
    }
  }

  /**
     * Queue an update for a signal element
     */
  queueUpdate(element, state, data = {}) {
    if (!element || !state) {
      return
    }

    // Add to queue
    this.updateQueue.push({ element, state, data })

    // Process queue if not already processing
    if (!this.isProcessing) {
      this.processUpdateQueue()
    }
  }

  /**
     * Process the update queue
     */
  processUpdateQueue() {
    if (this.updateQueue.length === 0) {
      this.isProcessing = false
      return
    }

    this.isProcessing = true

    // Process items in batches
    const batchSize = Math.min(SIGNAL_CONFIG.BATCH_SIZE, this.updateQueue.length)
    const batch = this.updateQueue.splice(0, batchSize)

    // Process current batch
    batch.forEach(({ element, state, data }) => {
      this.updateSignalElement(element, state, data)
    })

    // Process next batch on next frame
    requestAnimationFrame(this.processUpdateQueue)
  }

  /**
     * Update a signal element with new state
     */
  updateSignalElement(element, state, data = {}) {
    if (!element || !state) {
      return
    }

    try {
      const signalInfo = this.signalElements.get(element) || {
        lastUpdated: 0,
        state: 'neutral',
        timeout: null,
      }

      // Skip if state hasn't changed
      if (signalInfo.state === state) {
        return
      }

      // Clear any existing timeouts
      if (signalInfo.timeout) {
        clearTimeout(signalInfo.timeout)
        signalInfo.timeout = null
      }

      // Update state
      signalInfo.state = state
      signalInfo.lastUpdated = Date.now()
      this.signalElements.set(element, signalInfo)

      // Remove all state classes
      Object.keys(SIGNAL_CONFIG.STATES).forEach(s => {
        element.classList.remove(s)
      })

      // Add new state class
      element.classList.add(state)

      // Set background color
      const color = SIGNAL_CONFIG.STATES[state] || SIGNAL_CONFIG.STATES.neutral
      element.style.backgroundColor = color

      // Add pulse animation for strong signals
      if (state.includes('strong')) {
        element.classList.add('pulse')
      } else {
        element.classList.remove('pulse')
      }

      // Add tooltip if data is provided
      if (data.tooltip) {
        element.title = data.tooltip
      }

      // Log the update
      if (window.DEBUG_SIGNALS) {
        console.log(`[SignalInitializer] Updated signal element:`, {
          element,
          state,
          data,
          color,
        })
      }
    } catch (error) {
      console.error('[SignalInitializer] Error updating signal element:', error)
    }
  }

  /**
     * Get the current state of a signal element
     */
  getSignalState(element) {
    if (!element) {
      return null
    }

    const signalInfo = this.signalElements.get(element)
    return signalInfo ? signalInfo.state : null
  }

  /**
     * Reset a signal element to neutral state
     */
  resetSignal(element) {
    this.updateSignalElement(element, 'neutral')
  }

  /**
     * Reset all signal elements to neutral state
     */
  resetAllSignals() {
    this.signalElements.forEach((_, element) => {
      this.resetSignal(element)
    })
  }

  /**
     * Clean up resources
     */
  cleanup() {
    // Clean up any resources here
  }
}

// Create global instance immediately
window.signalInitializer = new SignalInitializer()

// Initialize when DOM is ready if not already initialized
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    if (!window.signalInitializer.initialized) {
      window.signalInitializer.init()
    }
  })
} else if (!window.signalInitializer.initialized) {
  // DOM already loaded, initialize immediately
  window.signalInitializer.init()
}

// Make sure we clean up on page unload
window.addEventListener('beforeunload', () => {
  if (window.signalInitializer && typeof window.signalInitializer.cleanup === 'function') {
    window.signalInitializer.cleanup()
  }
})

// Expose update function globally
window.updateSignalLight = (element, state, data) => {
  if (window.signalInitializer) {
    window.signalInitializer.queueUpdate(element, state, data)
  } else {
    console.warn('SignalInitializer not available')
  }
}
