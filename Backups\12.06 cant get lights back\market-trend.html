<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StarCrypt - Market Trend Analysis</title>
    <link rel="stylesheet" href="css/market-trend.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #14141f;
            color: #e0e0e0;
            font-family: 'Roboto', sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #2a2a3c;
        }
        .header h1 {
            margin: 0;
            color: #6c63ff;
            font-size: 2.2em;
        }
        .header p {
            margin: 5px 0 0;
            color: #888;
            font-size: 1.1em;
        }
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            margin-top: 20px;
        }
        @media (max-width: 900px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>StarCrypt Market Analysis</h1>
            <p>Real-time market trend visualization and analysis</p>
        </div>
        
        <div class="dashboard">
            <div class="main-content">
                <!-- Trend Strength Indicator -->
                <div class="market-trend-container">
                    <div class="trend-strength">
                        <h3>Market Trend Strength</h3>
                        <div class="trend-meter">
                            <div class="trend-bar"></div>
                            <div class="trend-value">0.0</div>
                        </div>
                        <div class="trend-labels">
                            <span>Strong Bearish</span>
                            <span>Neutral</span>
                            <span>Strong Bullish</span>
                        </div>
                    </div>
                    
                    <!-- Market Phase -->
                    <div class="market-phase-container" style="margin-top: 20px;">
                        <h3>Current Market Phase</h3>
                        <div id="market-phase-display" class="market-phase ranging">↔ Ranging</div>
                    </div>
                    
                    <!-- Key Levels -->
                    <div class="key-levels">
                        <h3>Key Levels</h3>
                        <div id="support-levels">
                            <!-- Dynamically populated -->
                        </div>
                        <div id="resistance-levels" style="margin-top: 10px;">
                            <!-- Dynamically populated -->
                        </div>
                    </div>
                </div>
                
                <!-- Price Chart Container -->
                <div id="price-chart" style="margin-top: 30px; height: 400px; background: #1e1e2d; border-radius: 12px; padding: 20px;">
                    <!-- Chart will be rendered here -->
                    <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: #666;">
                        Loading price chart...
                    </div>
                </div>
            </div>
            
            <div class="sidebar">
                <!-- Signal Matrix -->
                <div class="market-trend-container" style="margin-bottom: 20px;">
                    <h3 style="margin-top: 0;">Signal Matrix</h3>
                    <div id="signal-matrix">
                        <!-- Signal lights will be populated here -->
                    </div>
                </div>
                
                <!-- Indicator Summary -->
                <div class="market-trend-container">
                    <h3 style="margin-top: 0;">Indicator Summary</h3>
                    <div id="indicator-summary">
                        <!-- Indicator summaries will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load required scripts -->
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    <script src="js/ui/market-trend-engine.js"></script>
    <script src="js/ui/websocket-handler.js"></script>
    <script>
        // Initialize the market trend engine
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Initializing StarCrypt Market Analysis...');
            
            // Initialize the market trend engine
            window.marketTrendEngine = new MarketTrendEngine();
            
            // Example of listening to market analysis updates
            window.addEventListener('marketAnalysisUpdate', (e) => {
                const { trendStrength, marketPhase, keyLevels } = e.detail;
                
                // Update trend strength display
                const trendBar = document.querySelector('.trend-bar');
                const trendValue = document.querySelector('.trend-value');
                
                if (trendBar && trendValue) {
                    const percentage = Math.abs(trendStrength);
                    const isPositive = trendStrength >= 0;
                    
                    trendBar.style.width = `${percentage}%`;
                    trendBar.style.left = isPositive ? '50%' : `${50 - percentage}%`;
                    trendBar.style.backgroundColor = isPositive ? '#4CAF50' : '#F44336';
                    trendValue.textContent = `${isPositive ? '+' : ''}${trendStrength.toFixed(1)}`;
                    trendValue.style.color = isPositive ? '#4CAF50' : '#F44336';
                }
                
                // Update market phase display
                const phaseDisplay = document.getElementById('market-phase-display');
                if (phaseDisplay) {
                    phaseDisplay.className = `market-phase ${marketPhase}`;
                    
                    const phaseText = {
                        'trending-up': '↑ Uptrend',
                        'trending-down': '↓ Downtrend',
                        'ranging': '↔ Ranging'
                    }[marketPhase] || 'Unknown';
                    
                    phaseDisplay.textContent = phaseText;
                }
                
                // Update key levels (example implementation)
                this.updateKeyLevels(keyLevels);
            });
            
            // Initialize price chart
            this.initializePriceChart();
        });
        
        // Function to update key levels in the UI
        function updateKeyLevels(levels) {
            const supportContainer = document.getElementById('support-levels');
            const resistanceContainer = document.getElementById('resistance-levels');
            
            // Clear existing levels
            if (supportContainer) supportContainer.innerHTML = '';
            if (resistanceContainer) resistanceContainer.innerHTML = '';
            
            // Add support levels
            if (levels.support && levels.support.length > 0) {
                levels.support.forEach(level => {
                    const levelEl = document.createElement('div');
                    levelEl.className = 'level support';
                    levelEl.innerHTML = `
                        <span>Support</span>
                        <span class="level-value">${level.value}</span>
                        <span>${level.strength}%</span>
                    `;
                    if (supportContainer) supportContainer.appendChild(levelEl);
                });
            } else if (supportContainer) {
                supportContainer.innerHTML = '<div class="level">No support levels detected</div>';
            }
            
            // Add resistance levels
            if (levels.resistance && levels.resistance.length > 0) {
                levels.resistance.forEach(level => {
                    const levelEl = document.createElement('div');
                    levelEl.className = 'level resistance';
                    levelEl.innerHTML = `
                        <span>Resistance</span>
                        <span class="level-value">${level.value}</span>
                        <span>${level.strength}%</span>
                    `;
                    if (resistanceContainer) resistanceContainer.appendChild(levelEl);
                });
            } else if (resistanceContainer) {
                resistanceContainer.innerHTML = '<div class="level">No resistance levels detected</div>';
            }
        }
        
        // Function to initialize the price chart
        function initializePriceChart() {
            const chartContainer = document.getElementById('price-chart');
            if (!chartContainer) return;
            
            try {
                // Create chart
                const chart = LightweightCharts.createChart(chartContainer, {
                    width: chartContainer.clientWidth,
                    height: 400,
                    layout: {
                        backgroundColor: '#1e1e2d',
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: {
                            color: 'rgba(42, 46, 57, 0.5)',
                        },
                        horzLines: {
                            color: 'rgba(42, 46, 57, 0.5)',
                        },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    rightPriceScale: {
                        borderColor: 'rgba(197, 203, 206, 0.8)',
                    },
                    timeScale: {
                        borderColor: 'rgba(197, 203, 206, 0.8)',
                    },
                });
                
                // Add candlestick series
                const candleSeries = chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderDownColor: '#ef5350',
                    borderUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                    wickUpColor: '#26a69a',
                });
                
                // Add volume series
                const volumeSeries = chart.addHistogramSeries({
                    color: '#26a69a',
                    priceFormat: {
                        type: 'volume',
                    },
                    priceScaleId: '',
                    scaleMargins: {
                        top: 0.8,
                        bottom: 0,
                    },
                });
                
                // Example data (replace with real data)
                const data = [
                    // Add sample data here or fetch real data
                ];
                
                candleSeries.setData(data);
                
                // Handle window resize
                const resizeObserver = new ResizeObserver(entries => {
                    if (entries.length === 0 || entries[0].target !== chartContainer) {
                        return;
                    }
                    const newRect = entries[0].contentRect;
                    chart.applyOptions({ width: newRect.width });
                });
                
                resizeObserver.observe(chartContainer);
                
                // Store chart instance for later updates
                window.priceChart = chart;
                
            } catch (error) {
                console.error('Error initializing price chart:', error);
                chartContainer.innerHTML = `<div style="color: #f44336; text-align: center; padding: 20px;">
                    Error initializing price chart: ${error.message}
                </div>`;
            }
        }
    </script>
</body>
</html>
