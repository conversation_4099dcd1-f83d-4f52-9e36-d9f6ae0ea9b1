/* Menu System Styles */
.menu-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(26, 26, 42, 0.97);
  padding: 0.5rem 1.5rem;
  border-radius: 14px;
  margin-bottom: 1.25rem;
  box-shadow: 0 0 24px 4px #00FFFF44, 0 0 6px 2px #0008;
  border: 2px solid #00FFFF44;
  backdrop-filter: blur(3px);
  position: sticky;
  top: 10px;
  z-index: 1000;
}

.menu-inline {
  margin-right: 1rem;
  min-width: 250px;
  max-width: 350px;
  font-size: 1.1rem;
  height: 2.6rem;
  background: rgba(10, 10, 30, 0.95);
  color: #00FFFF;
  border: 2px solid #00FFFF88;
  border-radius: 10px;
  box-shadow: 0 2px 12px #00FFFF22;
  font-family: 'Orbitron', sans-serif;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.menu-inline:focus {
  border-color: #00FFAA;
  box-shadow: 0 0 14px #00FFAA88;
  outline: none;
}

.menu-button, .theme-toggle {
  margin-right: 0.5rem;
  background: linear-gradient(90deg, #00FFFF 0%, #0055FF 100%);
  color: #0a0a1a;
  font-weight: bold;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px #00FFFF33;
  padding: 0.5rem 1.2rem;
  transition: all 0.2s ease;
  cursor: pointer;
  font-family: 'Orbitron', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.menu-button:hover, .theme-toggle:hover {
  background: linear-gradient(90deg, #0055FF 0%, #00FFFF 100%);
  color: #FFF;
  box-shadow: 0 0 18px #00FFFF99;
  transform: translateY(-2px);
}

.menu-button:active, .theme-toggle:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px #00FFFF33;
}

.strategy-selector {
  font-family: 'Orbitron', sans-serif;
  font-size: 1.1rem;
  background: #0a0a1a;
  color: #00FFFF;
  border: 2px solid #00FFFF88;
  border-radius: 10px;
  padding: 0.5rem 1rem;
  box-shadow: 0 2px 12px #00FFFF22;
  margin-right: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.strategy-selector:hover {
  border-color: #00FFAA;
  box-shadow: 0 0 15px #00FFAA88;
}

.strategy-selector option {
  background-color: #0a0a1a;
  color: #00FFFF;
  font-family: 'Orbitron', sans-serif;
}

.menu-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(20, 25, 35, 0.98);
  border: 1px solid #2a3a4a;
  border-radius: 8px;
  padding: 15px;
  min-width: 250px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(10px);
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
  pointer-events: none;
  outline: none; /* Remove default focus outline, we'll add our own */
}

.menu-content.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* Keyboard navigation focus styles */
.menu-content.keyboard-navigation-active {
  box-shadow: 0 0 0 2px #4a90e2, 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* Menu item base styles */
.menu-content [role="menuitem"] {
  display: block;
  width: 100%;
  padding: 8px 16px;
  margin: 2px 0;
  text-align: left;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 14px;
  line-height: 1.4;
}

/* Hover state */
.menu-content [role="menuitem"]:hover:not(:disabled) {
  background: rgba(74, 144, 226, 0.2);
  color: #fff;
}

/* Focus state */
.menu-content [role="menuitem"]:focus-visible {
  outline: 2px solid #4a90e2;
  outline-offset: -2px;
  background: rgba(74, 144, 226, 0.3);
  color: #fff;
}

/* Active state */
.menu-content [role="menuitem"]:active:not(:disabled) {
  background: rgba(74, 144, 226, 0.4);
  transform: translateY(1px);
}

/* Disabled state */
.menu-content [role="menuitem"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  color: #888;
}

/* Menu button styles */
.menu-button {
  transition: all 0.2s ease;
}

.menu-button:hover:not(:disabled) {
  background: rgba(74, 144, 226, 0.2);
}

.menu-button:focus-visible {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
  background: rgba(74, 144, 226, 0.3);
}

/* Hide focus outline when not using keyboard */
.menu-content *:focus:not(:focus-visible) {
  outline: none;
}

.menu-button:focus:not(:focus-visible) {
  outline: none;
}

/* Menu section headers */
.menu-section-header {
  display: block;
  padding: 8px 16px;
  margin: 8px 0 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #8a9ba8;
  border-bottom: 1px solid rgba(138, 155, 168, 0.2);
}

/* Menu dividers */
.menu-divider {
  height: 1px;
  margin: 8px 0;
  background: rgba(138, 155, 168, 0.2);
  border: none;
}

/* Make thresholds menu larger */
#thresholdsMenu {
  width: 90%;
  max-width: 900px;
}

/* Strategy menu specific styles */
.strategy-controls {
  color: #00FFFF;
}

.strategy-description {
  background: rgba(0, 20, 40, 0.5);
  border: 1px solid #00FFFF33;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  line-height: 1.5;
}

.strategy-info-panel {
  background: rgba(0, 10, 20, 0.7);
  border: 1px solid #00FFFF44;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.strategy-indicators-list {
  margin-top: 1rem;
}

.strategy-indicators-content {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.indicator-tag {
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid #00FFFF44;
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  font-size: 0.85rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .menu-container {
    flex-wrap: wrap;
    padding: 0.75rem;
    gap: 0.5rem;
  }
  
  .menu-button, .theme-toggle {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
    margin-right: 0.3rem;
  }
  
  .menu-content {
    width: 95%;
    top: 70px;
    padding: 1rem;
  }
  
  #thresholdsMenu {
    width: 95%;
  }
}
