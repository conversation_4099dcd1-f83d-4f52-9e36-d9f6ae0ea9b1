/**
 * @module StorageManager
 * @description Manages persistent storage for StarCrypt
 */

import Logger from '../utils/logger.js'

export class StorageManager {
  constructor() {
    this.logger = Logger.getInstance()
    this.logger.setContext('StorageManager')
    this.storageKeyPrefix = 'starcrypt_'
  }

  /**
     * Set a value in storage
     * @param {string} key - The storage key
     * @param {*} value - The value to store
     * @returns {boolean} True if value was stored successfully
     */
  set(key, value) {
    try {
      const prefixedKey = this.getPrefixedKey(key)
      localStorage.setItem(prefixedKey, JSON.stringify(value))
      this.logger.info(`Stored value for ${key}`)
      return true
    } catch (error) {
      this.logger.error(`Error storing value: ${error.message}`)
      return false
    }
  }

  /**
     * Get a value from storage
     * @param {string} key - The storage key
     * @returns {*} The stored value or null
     */
  get(key) {
    try {
      const prefixedKey = this.getPrefixedKey(key)
      const value = localStorage.getItem(prefixedKey)
      return value ? JSON.parse(value) : null
    } catch (error) {
      this.logger.error(`Error getting value: ${error.message}`)
      return null
    }
  }

  /**
     * Remove a value from storage
     * @param {string} key - The storage key
     * @returns {boolean} True if value was removed successfully
     */
  remove(key) {
    try {
      const prefixedKey = this.getPrefixedKey(key)
      localStorage.removeItem(prefixedKey)
      this.logger.info(`Removed value for ${key}`)
      return true
    } catch (error) {
      this.logger.error(`Error removing value: ${error.message}`)
      return false
    }
  }

  /**
     * Check if a value exists in storage
     * @param {string} key - The storage key
     * @returns {boolean} True if value exists
     */
  has(key) {
    try {
      const prefixedKey = this.getPrefixedKey(key)
      return localStorage.getItem(prefixedKey) !== null
    } catch (error) {
      this.logger.error(`Error checking storage: ${error.message}`)
      return false
    }
  }

  /**
     * Clear all StarCrypt-related storage
     */
  clearAll() {
    try {
      const allKeys = Object.keys(localStorage)
      allKeys.forEach(key => {
        if (key.startsWith(this.storageKeyPrefix)) {
          localStorage.removeItem(key)
        }
      })
      this.logger.info('Cleared all StarCrypt storage')
    } catch (error) {
      this.logger.error(`Error clearing storage: ${error.message}`)
    }
  }

  /**
     * Get prefixed storage key
     * @param {string} key - The original key
     * @returns {string} Prefixed key
     */
  getPrefixedKey(key) {
    return `${this.storageKeyPrefix}${key}`
  }

  /**
     * Save settings object
     * @param {Object} settings - Settings object to save
     * @returns {boolean} True if settings were saved successfully
     */
  saveSettings(settings) {
    try {
      this.set('settings', settings)
      this.logger.info('Settings saved successfully')
      return true
    } catch (error) {
      this.logger.error(`Error saving settings: ${error.message}`)
      return false
    }
  }

  /**
     * Load settings object
     * @returns {Object|null} Loaded settings or null
     */
  loadSettings() {
    try {
      const settings = this.get('settings')
      if (settings) {
        this.logger.info('Settings loaded successfully')
      }
      return settings
    } catch (error) {
      this.logger.error(`Error loading settings: ${error.message}`)
      return null
    }
  }

  /**
     * Save user preferences
     * @param {Object} preferences - User preferences to save
     * @returns {boolean} True if preferences were saved successfully
     */
  savePreferences(preferences) {
    try {
      this.set('preferences', preferences)
      this.logger.info('Preferences saved successfully')
      return true
    } catch (error) {
      this.logger.error(`Error saving preferences: ${error.message}`)
      return false
    }
  }

  /**
     * Load user preferences
     * @returns {Object|null} Loaded preferences or null
     */
  loadPreferences() {
    try {
      const preferences = this.get('preferences')
      if (preferences) {
        this.logger.info('Preferences loaded successfully')
      }
      return preferences
    } catch (error) {
      this.logger.error(`Error loading preferences: ${error.message}`)
      return null
    }
  }

  /**
     * Save trading history
     * @param {Array} history - Trading history array
     * @returns {boolean} True if history was saved successfully
     */
  saveTradingHistory(history) {
    try {
      this.set('trading_history', history)
      this.logger.info('Trading history saved successfully')
      return true
    } catch (error) {
      this.logger.error(`Error saving trading history: ${error.message}`)
      return false
    }
  }

  /**
     * Load trading history
     * @returns {Array|null} Loaded trading history or null
     */
  loadTradingHistory() {
    try {
      const history = this.get('trading_history') || []
      this.logger.info(`Loaded trading history with ${history.length} entries`)
      return history
    } catch (error) {
      this.logger.error(`Error loading trading history: ${error.message}`)
      return []
    }
  }
}

// Export singleton instance
export const storageManager = new StorageManager()
