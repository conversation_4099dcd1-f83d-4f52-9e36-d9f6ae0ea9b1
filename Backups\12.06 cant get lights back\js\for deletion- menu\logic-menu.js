/**
 * @module LogicMenu
 * @description Menu system for signal logic configuration
 */

import { MenuController } from './menu-controller.js'
import { MenuUtils } from './menu-utils.js'
import { LogicManager } from '../logic-manager.js'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/error-handler.js'
import { PerformanceMonitor } from '../utils/performance-monitor.js'

export class LogicMenu extends MenuController {
  constructor() {
    super()

    // Logic-specific configuration
    this.config = {
      menuId: 'logic-menu',
      buttonId: 'logic-button',
      containerId: 'logic-container',
      helperId: 'logic-helper',
      animationDuration: 300,
      maxRules: 20,
      showHelp: true,
      defaultLogic: {
        type: 'AND',
        conditions: [],
      },
    }

    // Logic manager
    this.logicManager = new LogicManager()

    // Error handling
    this.errorHandler = new ErrorHandler({
      maxErrors: 5,
      recoveryDelay: 2000,
      errorThreshold: 0.1,
    })

    // Performance monitoring
    this.performance = new PerformanceMonitor({
      targetFPS: 60,
      maxLatency: 100,
      batchInterval: 100,
    })

    // Menu state
    this.state = {
      currentLogic: null,
      availableConditions: [],
      isInitialized: false,
      isLoading: false,
      error: null,
    }
  }

  /**
     * Initialize logic menu
     */
  async init() {
    try {
      // Initialize base menu
      await super.init()

      // Initialize logic manager
      await this.logicManager.init()

      // Get available conditions
      this.state.availableConditions = this.getAvailableConditions()

      // Create menu structure
      this.createMenuStructure()

      // Add event listeners
      this.setupEventListeners()

      // Set initial state
      this.state.isInitialized = true
      this.state.currentLogic = this.logicManager.getCurrentLogic()

      // Dispatch initialized event
      this.dispatch('initialized', {
        logic: this.state.currentLogic,
        conditions: this.state.availableConditions,
      })
    } catch (error) {
      this.handleError(error, 'init')
    }
  }

  /**
     * Get available conditions
     * @returns {Array} List of available conditions
     * @private
     */
  getAvailableConditions() {
    try {
      // Define available conditions
      return [
        {
          id: 'cross-above',
          name: 'Cross Above',
          description: 'Signal crosses above threshold',
          type: 'comparison',
        },
        {
          id: 'cross-below',
          name: 'Cross Below',
          description: 'Signal crosses below threshold',
          type: 'comparison',
        },
        {
          id: 'above',
          name: 'Above',
          description: 'Signal is above threshold',
          type: 'comparison',
        },
        {
          id: 'below',
          name: 'Below',
          description: 'Signal is below threshold',
          type: 'comparison',
        },
        {
          id: 'converging',
          name: 'Converging',
          description: 'Signals are converging',
          type: 'trend',
        },
        {
          id: 'diverging',
          name: 'Diverging',
          description: 'Signals are diverging',
          type: 'trend',
        },
      ]
    } catch (error) {
      this.handleError(error, 'get-available-conditions')
      return []
    }
  }

  /**
     * Create menu structure
     * @private
     */
  createMenuStructure() {
    try {
      // Get menu element
      const menu = MenuUtils.getMenuElement(this.config.menuId)
      if (!menu) return

      // Clear existing items
      menu.innerHTML = ''

      // Add logic configuration
      this.addLogicConfiguration()

      // Add available conditions
      this.addConditions()

      // Add helper content if enabled
      if (this.config.showHelp) {
        this.addHelperContent()
      }
    } catch (error) {
      this.handleError(error, 'create-menu-structure')
    }
  }

  /**
     * Add logic configuration
     * @private
     */
  addLogicConfiguration() {
    try {
      // Get menu element
      const menu = MenuUtils.getMenuElement(this.config.menuId)
      if (!menu) return

      // Add logic type selector
      const logicType = MenuUtils.addMenuItem(this.config.menuId, {
        id: 'logic-type',
        text: 'Logic Type',
        className: 'logic-item',
        style: {
          padding: '8px 16px',
          borderBottom: '1px solid #333',
        },
      })

      // Add logic operators
      const operators = ['AND', 'OR', 'NAND', 'NOR']
      operators.forEach(operator => {
        const item = MenuUtils.addMenuItem(this.config.menuId, {
          id: `logic-${operator.toLowerCase()}`,
          text: operator,
          className: 'logic-operator',
          style: {
            padding: '8px 24px',
            cursor: 'pointer',
            borderRadius: '4px',
            transition: 'background-color 0.2s',
          },
          events: {
            click: () => this.handleLogicChange(operator),
          },
        })

        // Add ARIA attributes
        item.setAttribute('role', 'menuitem')
        item.setAttribute('aria-label', `Set logic operator to ${operator}`)
      })
    } catch (error) {
      this.handleError(error, 'add-logic-configuration')
    }
  }

  /**
     * Add available conditions
     * @private
     */
  addConditions() {
    try {
      // Get menu element
      const menu = MenuUtils.getMenuElement(this.config.menuId)
      if (!menu) return

      // Add conditions header
      const header = MenuUtils.addMenuItem(this.config.menuId, {
        id: 'conditions-header',
        text: 'Available Conditions',
        className: 'logic-item',
        style: {
          padding: '8px 16px',
          borderBottom: '1px solid #333',
        },
      })

      // Add conditions
      this.state.availableConditions.forEach(condition => {
        const item = MenuUtils.addMenuItem(this.config.menuId, {
          id: `condition-${condition.id}`,
          text: condition.name,
          className: 'condition-item',
          style: {
            padding: '8px 24px',
            cursor: 'pointer',
            borderRadius: '4px',
            transition: 'background-color 0.2s',
          },
          events: {
            click: () => this.handleConditionClick(condition),
          },
        })

        // Add ARIA attributes
        item.setAttribute('role', 'menuitem')
        item.setAttribute('aria-label', condition.description)
      })
    } catch (error) {
      this.handleError(error, 'add-conditions')
    }
  }

  /**
     * Add helper content
     * @private
     */
  addHelperContent() {
    try {
      // Get helper element
      const helper = document.getElementById(this.config.helperId)
      if (!helper) return

      // Add helper content
      helper.innerHTML = `
                <h3>Signal Logic Configuration Guide</h3>
                <p>Configure how signals combine to form trading signals.</p>
                <ul>
                    <li>Select logic operator (AND/OR)</li>
                    <li>Add conditions for signal evaluation</li>
                    <li>Combine multiple conditions</li>
                </ul>
            `

      // Add ARIA attributes
      helper.setAttribute('role', 'region')
      helper.setAttribute('aria-label', 'Signal logic configuration guide')
    } catch (error) {
      this.handleError(error, 'add-helper-content')
    }
  }

  /**
     * Handle logic change
     * @param {string} operator - Logic operator
     * @private
     */
  async handleLogicChange(operator) {
    try {
      // Set loading state
      this.state.isLoading = true

      // Update logic
      await this.logicManager.setLogicType(operator)

      // Update menu state
      this.state.currentLogic = this.logicManager.getCurrentLogic()

      // Update UI
      this.updateUI()

      // Dispatch change event
      this.dispatch('logic-change', {
        operator,
        logic: this.state.currentLogic,
        timestamp: Date.now(),
      })
    } catch (error) {
      this.handleError(error, 'handle-logic-change')
    } finally {
      this.state.isLoading = false
    }
  }

  /**
     * Handle condition click
     * @param {Object} condition - Selected condition
     * @private
     */
  async handleConditionClick(condition) {
    try {
      // Set loading state
      this.state.isLoading = true

      // Add condition
      await this.logicManager.addCondition(condition.id)

      // Update menu state
      this.state.currentLogic = this.logicManager.getCurrentLogic()

      // Update UI
      this.updateUI()

      // Dispatch change event
      this.dispatch('condition-change', {
        condition,
        logic: this.state.currentLogic,
        timestamp: Date.now(),
      })
    } catch (error) {
      this.handleError(error, 'handle-condition-click')
    } finally {
      this.state.isLoading = false
    }
  }

  /**
     * Update UI based on current state
     * @private
     */
  updateUI() {
    try {
      // Update operator selection
      const operators = document.querySelectorAll('.logic-operator')
      operators.forEach(operator => {
        if (operator.id === `logic-${this.state.currentLogic.type.toLowerCase()}`) {
          operator.classList.add('active')
        } else {
          operator.classList.remove('active')
        }
      })

      // Update button text
      const button = document.getElementById(this.config.buttonId)
      if (button) {
        const conditions = this.state.currentLogic.conditions.length
        button.textContent = `Logic (${conditions})`
      }
    } catch (error) {
      this.handleError(error, 'update-ui')
    }
  }

  /**
     * Setup event listeners
     * @private
     */
  setupEventListeners() {
    try {
      // Listen for logic changes
      this.logicManager.on('logic-change', (data) => {
        this.handleLogicChange(data.operator)
      })

      // Listen for condition changes
      this.logicManager.on('condition-change', (data) => {
        this.handleConditionClick(data.condition)
      })

      // Listen for menu events
      this.on('menu-close', () => this.handleMenuClose())
      this.on('error', (error) => this.handleError(error))
    } catch (error) {
      this.handleError(error, 'setup-event-listeners')
    }
  }

  /**
     * Handle menu close
     * @private
     */
  handleMenuClose() {
    try {
      // Reset helper content
      this.resetHelperContent()
    } catch (error) {
      this.handleError(error, 'handle-menu-close')
    }
  }

  /**
     * Handle errors
     * @param {Error} error - Error object
     * @param {string} context - Context where error occurred
     * @private
     */
  handleError(error, context) {
    try {
      // Log error
      console.error(`[LogicMenu] Error in ${context}:`, error)

      // Track error
      this.errorHandler.track(error, context)

      // Set error state
      this.state.error = error

      // Dispatch error event
      this.dispatch('error', {
        error,
        context,
        timestamp: Date.now(),
      })

      // Try to recover
      if (this.errorHandler.shouldRecover()) {
        this.recoverFromError()
      }
    } catch (error) {
      console.error('[LogicMenu] Error handling failed:', error)
    }
  }

  /**
     * Attempt to recover from error
     * @private
     */
  recoverFromError() {
    try {
      // Reset state
      this.state = {
        currentLogic: null,
        availableConditions: [],
        isInitialized: false,
        isLoading: false,
        error: null,
      }

      // Reinitialize
      this.init()
    } catch (error) {
      console.error('[LogicMenu] Recovery failed:', error)
    }
  }

  /**
     * Clean up resources
     */
  cleanup() {
    try {
      // Cleanup base menu
      super.cleanup()

      // Cleanup logic manager
      this.logicManager.cleanup()

      // Reset state
      this.state = {
        currentLogic: null,
        availableConditions: [],
        isInitialized: false,
        isLoading: false,
        error: null,
      }

      // Reset error handler
      this.errorHandler.reset()

      // Reset performance monitor
      this.performance.reset()
    } catch (error) {
      console.error('[LogicMenu] Cleanup failed:', error)
    }
  }

  /**
     * Destroy menu
     */
  destroy() {
    try {
      // Cleanup
      this.cleanup()

      // Reset references
      this.logicManager = null
      this.errorHandler = null
      this.performance = null
    } catch (error) {
      console.error('[LogicMenu] Destruction failed:', error)
    }
  }
}

// Export singleton instance
const logicMenu = new LogicMenu()
export default logicMenu
