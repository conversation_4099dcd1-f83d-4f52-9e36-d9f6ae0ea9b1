/**
 * @module IndicatorMenu
 * @description Menu system for trading indicators
 */

import { MenuController } from './menu-controller.js'
import { MenuUtils } from './menu-utils.js'
import { IndicatorManager } from '../indicator-manager.js'
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../utils/error-handler.js'
import { PerformanceMonitor } from '../utils/performance-monitor.js'

export class IndicatorMenu extends MenuController {
  constructor() {
    super()

    // Indicator-specific configuration
    this.config = {
      menuId: 'indicator-menu',
      buttonId: 'indicator-button',
      containerId: 'indicator-container',
      helperId: 'indicator-helper',
      animationDuration: 300,
      maxIndicators: 20,
      showHelp: true,
      showGroups: true,
    }

    // Indicator manager
    this.indicatorManager = new IndicatorManager()

    // Error handling
    this.errorHandler = new ErrorHandler({
      maxErrors: 5,
      recoveryDelay: 2000,
      errorThreshold: 0.1,
    })

    // Performance monitoring
    this.performance = new PerformanceMonitor({
      targetFPS: 60,
      maxLatency: 100,
      batchInterval: 100,
    })

    // Menu state
    this.state = {
      currentIndicators: [],
      allIndicators: [],
      groups: {},
      isInitialized: false,
      isLoading: false,
      error: null,
    }
  }

  /**
     * Initialize indicator menu
     */
  async init() {
    try {
      // Initialize base menu
      await super.init()

      // Initialize indicator manager
      await this.indicatorManager.init()

      // Get indicators
      this.state.allIndicators = await this.getIndicators()

      // Create menu structure
      this.createMenuStructure()

      // Add event listeners
      this.setupEventListeners()

      // Set initial state
      this.state.isInitialized = true
      this.state.currentIndicators = this.indicatorManager.getCurrentIndicators()

      // Dispatch initialized event
      this.dispatch('initialized', {
        indicators: this.state.allIndicators,
        currentIndicators: this.state.currentIndicators,
      })
    } catch (error) {
      this.handleError(error, 'init')
    }
  }

  /**
     * Get available indicators
     * @returns {Promise<Array>} List of indicators
     * @private
     */
  async getIndicators() {
    try {
      // Get indicators from manager
      const indicators = await this.indicatorManager.getAllIndicators()

      // Validate indicators
      if (!Array.isArray(indicators)) {
        throw new Error('Indicators must be an array')
      }

      // Group indicators
      this.state.groups = this.groupIndicators(indicators)

      // Limit indicators if needed
      return indicators.slice(0, this.config.maxIndicators)
    } catch (error) {
      this.handleError(error, 'get-indicators')
      return []
    }
  }

  /**
     * Group indicators by type
     * @param {Array} indicators - List of indicators
     * @returns {Object} Grouped indicators
     * @private
     */
  groupIndicators(indicators) {
    try {
      const groups = {}

      indicators.forEach(indicator => {
        const group = indicator.type || 'Other'
        if (!groups[group]) {
          groups[group] = []
        }
        groups[group].push(indicator)
      })

      return groups
    } catch (error) {
      this.handleError(error, 'group-indicators')
      return {}
    }
  }

  /**
     * Create menu structure
     * @private
     */
  createMenuStructure() {
    try {
      // Get menu element
      const menu = MenuUtils.getMenuElement(this.config.menuId)
      if (!menu) return

      // Clear existing items
      menu.innerHTML = ''

      // Add indicator groups
      Object.entries(this.state.groups).forEach(([group, indicators]) => {
        // Add group header
        const header = MenuUtils.addMenuItem(this.config.menuId, {
          id: `group-${group.toLowerCase()}`,
          text: group,
          className: 'indicator-group-header',
          style: {
            fontWeight: 'bold',
            padding: '8px 16px',
            borderBottom: '1px solid #333',
          },
        })

        // Add indicators
        indicators.forEach(indicator => {
          const item = MenuUtils.addMenuItem(this.config.menuId, {
            id: `indicator-${indicator.id}`,
            text: indicator.name,
            className: 'indicator-item',
            style: {
              cursor: 'pointer',
              padding: '8px 24px',
              borderRadius: '4px',
              transition: 'background-color 0.2s',
            },
            events: {
              click: () => this.handleIndicatorClick(indicator),
              mouseenter: () => this.handleIndicatorHover(indicator),
              mouseleave: () => this.handleIndicatorHoverEnd(),
            },
          })

          // Add ARIA attributes
          item.setAttribute('role', 'menuitem')
          item.setAttribute('aria-label', indicator.description)
        })
      })

      // Add helper content if enabled
      if (this.config.showHelp) {
        this.addHelperContent()
      }
    } catch (error) {
      this.handleError(error, 'create-menu-structure')
    }
  }

  /**
     * Add helper content
     * @private
     */
  addHelperContent() {
    try {
      // Get helper element
      const helper = document.getElementById(this.config.helperId)
      if (!helper) return

      // Add helper content
      helper.innerHTML = `
                <h3>Indicator Selection Guide</h3>
                <p>Select indicators to add to your trading strategy.</p>
                <ul>
                    <li>Click to toggle indicator</li>
                    <li>Hover for indicator description</li>
                    <li>Use arrow keys for navigation</li>
                </ul>
            `

      // Add ARIA attributes
      helper.setAttribute('role', 'region')
      helper.setAttribute('aria-label', 'Indicator selection guide')
    } catch (error) {
      this.handleError(error, 'add-helper-content')
    }
  }

  /**
     * Handle indicator click
     * @param {Object} indicator - Selected indicator
     * @private
     */
  async handleIndicatorClick(indicator) {
    try {
      // Set loading state
      this.state.isLoading = true

      // Toggle indicator
      await this.indicatorManager.toggleIndicator(indicator.id)

      // Update menu state
      this.state.currentIndicators = this.indicatorManager.getCurrentIndicators()

      // Update UI
      this.updateUI()

      // Dispatch change event
      this.dispatch('indicator-change', {
        indicator,
        enabled: this.state.currentIndicators.includes(indicator),
        timestamp: Date.now(),
      })
    } catch (error) {
      this.handleError(error, 'handle-indicator-click')
    } finally {
      this.state.isLoading = false
    }
  }

  /**
     * Handle indicator hover
     * @param {Object} indicator - Hovered indicator
     * @private
     */
  handleIndicatorHover(indicator) {
    try {
      // Update helper content
      this.updateHelperContent(indicator)

      // Add hover class
      const item = document.getElementById(`indicator-${indicator.id}`)
      if (item) {
        item.classList.add('hover')
      }
    } catch (error) {
      this.handleError(error, 'handle-indicator-hover')
    }
  }

  /**
     * Handle indicator hover end
     * @private
     */
  handleIndicatorHoverEnd() {
    try {
      // Remove hover classes
      document.querySelectorAll('.indicator-item.hover')
        .forEach(item => item.classList.remove('hover'))

      // Reset helper content
      this.resetHelperContent()
    } catch (error) {
      this.handleError(error, 'handle-indicator-hover-end')
    }
  }

  /**
     * Update helper content
     * @param {Object} indicator - Indicator to display
     * @private
     */
  updateHelperContent(indicator) {
    try {
      // Get helper element
      const helper = document.getElementById(this.config.helperId)
      if (!helper) return

      // Update content
      helper.innerHTML = `
                <h3>${indicator.name}</h3>
                <p>${indicator.description}</p>
                <div class="indicator-details">
                    <div>Type: ${indicator.type}</div>
                    <div>Period: ${indicator.period}</div>
                    <div>Parameters: ${JSON.stringify(indicator.parameters)}</div>
                </div>
            `
    } catch (error) {
      this.handleError(error, 'update-helper-content')
    }
  }

  /**
     * Reset helper content
     * @private
     */
  resetHelperContent() {
    try {
      // Get helper element
      const helper = document.getElementById(this.config.helperId)
      if (!helper) return

      // Reset content
      helper.innerHTML = ''
    } catch (error) {
      this.handleError(error, 'reset-helper-content')
    }
  }

  /**
     * Update UI based on current state
     * @private
     */
  updateUI() {
    try {
      // Get menu element
      const menu = MenuUtils.getMenuElement(this.config.menuId)
      if (!menu) return

      // Update indicator items
      this.state.allIndicators.forEach(indicator => {
        const item = document.getElementById(`indicator-${indicator.id}`)
        if (item) {
          // Add active class if enabled
          if (this.state.currentIndicators.includes(indicator)) {
            item.classList.add('active')
          } else {
            item.classList.remove('active')
          }
        }
      })

      // Update button text
      const button = document.getElementById(this.config.buttonId)
      if (button) {
        const count = this.state.currentIndicators.length
        button.textContent = `Indicators (${count})`
      }
    } catch (error) {
      this.handleError(error, 'update-ui')
    }
  }

  /**
     * Setup event listeners
     * @private
     */
  setupEventListeners() {
    try {
      // Listen for indicator changes
      this.indicatorManager.on('indicator-change', (data) => {
        this.handleIndicatorChange(data)
      })

      // Listen for menu events
      this.on('menu-close', () => this.handleMenuClose())
      this.on('error', (error) => this.handleError(error))
    } catch (error) {
      this.handleError(error, 'setup-event-listeners')
    }
  }

  /**
     * Handle indicator change
     * @param {Object} data - Indicator change data
     * @private
     */
  handleIndicatorChange(data) {
    try {
      // Update state
      this.state.currentIndicators = data.indicators

      // Update UI
      this.updateUI()
    } catch (error) {
      this.handleError(error, 'handle-indicator-change')
    }
  }

  /**
     * Handle menu close
     * @private
     */
  handleMenuClose() {
    try {
      // Reset helper content
      this.resetHelperContent()

      // Remove hover classes
      this.handleIndicatorHoverEnd()
    } catch (error) {
      this.handleError(error, 'handle-menu-close')
    }
  }

  /**
     * Handle errors
     * @param {Error} error - Error object
     * @param {string} context - Context where error occurred
     * @private
     */
  handleError(error, context) {
    try {
      // Log error
      console.error(`[IndicatorMenu] Error in ${context}:`, error)

      // Track error
      this.errorHandler.track(error, context)

      // Set error state
      this.state.error = error

      // Dispatch error event
      this.dispatch('error', {
        error,
        context,
        timestamp: Date.now(),
      })

      // Try to recover
      if (this.errorHandler.shouldRecover()) {
        this.recoverFromError()
      }
    } catch (error) {
      console.error('[IndicatorMenu] Error handling failed:', error)
    }
  }

  /**
     * Attempt to recover from error
     * @private
     */
  recoverFromError() {
    try {
      // Reset state
      this.state = {
        currentIndicators: [],
        allIndicators: [],
        groups: {},
        isInitialized: false,
        isLoading: false,
        error: null,
      }

      // Reinitialize
      this.init()
    } catch (error) {
      console.error('[IndicatorMenu] Recovery failed:', error)
    }
  }

  /**
     * Clean up resources
     */
  cleanup() {
    try {
      // Cleanup base menu
      super.cleanup()

      // Cleanup indicator manager
      this.indicatorManager.cleanup()

      // Reset state
      this.state = {
        currentIndicators: [],
        allIndicators: [],
        groups: {},
        isInitialized: false,
        isLoading: false,
        error: null,
      }

      // Reset error handler
      this.errorHandler.reset()

      // Reset performance monitor
      this.performance.reset()
    } catch (error) {
      console.error('[IndicatorMenu] Cleanup failed:', error)
    }
  }

  /**
     * Destroy menu
     */
  destroy() {
    try {
      // Cleanup
      this.cleanup()

      // Reset references
      this.indicatorManager = null
      this.errorHandler = null
      this.performance = null
    } catch (error) {
      console.error('[IndicatorMenu] Destruction failed:', error)
    }
  }
}

// Export singleton instance
const indicatorMenu = new IndicatorMenu()
export default indicatorMenu
