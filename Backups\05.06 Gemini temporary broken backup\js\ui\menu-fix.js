// menu-fix.js - Fixes for menu functionality and conflicts

// Track menu state
const menuState = {
  isInitialized: false,
  activeMenu: null,
  menuEventListeners: new Map(),
}

// Initialize menu system
function initializeMenus() {
  if (menuState.isInitialized) return

  console.log('Initializing menu system...')

  // Remove any existing event listeners to prevent duplicates
  cleanupExistingMenuListeners()

  // Initialize all menu toggles
  document.querySelectorAll('[data-menu-toggle]').forEach(toggle => {
    const menuId = toggle.getAttribute('data-menu-toggle')
    if (!menuId) return

    // Remove existing listeners
    const newToggle = toggle.cloneNode(true)
    toggle.parentNode.replaceChild(newToggle, toggle)

    // Add new listener
    newToggle.addEventListener('click', (e) => {
      e.preventDefault()
      e.stopPropagation()
      toggleMenu(menuId)
    })

    menuState.menuEventListeners.set(`toggle-${menuId}`, {
      element: newToggle,
      type: 'click',
      handler: toggleMenu,
    })
  })

  // Close menu when clicking outside
  document.addEventListener('click', handleDocumentClick)

  // Close menu on Escape key
  document.addEventListener('keydown', handleKeyDown)

  menuState.isInitialized = true
  console.log('Menu system initialized')
}

// Clean up existing menu listeners
function cleanupExistingMenuListeners() {
  // Remove document listeners
  document.removeEventListener('click', handleDocumentClick)
  document.removeEventListener('keydown', handleKeyDown)

  // Remove all tracked event listeners
  menuState.menuEventListeners.forEach((listener, id) => {
    try {
      const { element, type, handler } = listener
      element.removeEventListener(type, handler)
    } catch (e) {
      console.warn('Failed to remove menu listener:', e)
    }
  })

  menuState.menuEventListeners.clear()
}

// Toggle menu visibility
function toggleMenu(menuId) {
  const menu = document.getElementById(menuId)
  if (!menu) {
    console.warn(`Menu with ID '${menuId}' not found`)
    return
  }

  // Close other open menus if opening a new one
  if (menuState.activeMenu && menuState.activeMenu !== menu) {
    hideMenu(menuState.activeMenu)
  }

  // Toggle current menu
  if (menu.classList.contains('active')) {
    hideMenu(menu)
  } else {
    showMenu(menu)
  }
}

// Show menu
function showMenu(menu) {
  if (!menu) return

  // Add active class
  menu.classList.add('active')

  // Position the menu if it's a dropdown
  if (menu.classList.contains('dropdown-menu')) {
    positionDropdownMenu(menu)
  }

  // Add backdrop if needed
  addBackdrop(menu)

  // Update state
  menuState.activeMenu = menu

  // Trigger event
  menu.dispatchEvent(new Event('menu:show'))
}

// Hide menu
function hideMenu(menu) {
  if (!menu) return

  // Remove active class
  menu.classList.remove('active')

  // Remove backdrop if no other menus are open
  if (!document.querySelector('.menu-backdrop')) {
    removeBackdrop()
  }

  // Update state
  if (menuState.activeMenu === menu) {
    menuState.activeMenu = null
  }

  // Trigger event
  menu.dispatchEvent(new Event('menu:hide'))
}

// Position dropdown menu relative to its toggle
function positionDropdownMenu(menu) {
  const toggle = document.querySelector(`[data-menu-toggle="${menu.id}"]`)
  if (!toggle) return

  const toggleRect = toggle.getBoundingClientRect()
  const menuRect = menu.getBoundingClientRect()
  const viewportHeight = window.innerHeight
  const viewportWidth = window.innerWidth

  // Reset positioning
  menu.style.position = 'fixed'
  menu.style.left = ''
  menu.style.top = ''
  menu.style.right = ''
  menu.style.bottom = ''

  // Position below toggle by default
  let top = toggleRect.bottom + window.scrollY
  let left = toggleRect.left + window.scrollX

  // Adjust if menu would go off screen
  if (left + menuRect.width > viewportWidth) {
    left = viewportWidth - menuRect.width - 10 // 10px padding
  }

  if (top + menuRect.height > viewportHeight + window.scrollY) {
    // If menu would go below viewport, try to position above
    top = toggleRect.top + window.scrollY - menuRect.height

    // If still not enough space, reduce height
    if (top < 0) {
      menu.style.maxHeight = `${viewportHeight - 20}px`
      menu.style.overflowY = 'auto'
      top = 10
    }
  }

  // Apply positioning
  menu.style.left = `${Math.max(10, left)}px`
  menu.style.top = `${Math.max(10, top)}px`
}

// Add backdrop for modal menus
function addBackdrop(menu) {
  // Skip if already has a backdrop or menu doesn't need one
  if (document.querySelector('.menu-backdrop') || !menu.classList.contains('modal-menu')) {
    return
  }

  const backdrop = document.createElement('div')
  backdrop.className = 'menu-backdrop'
  backdrop.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9998;
        opacity: 0;
        transition: opacity 0.2s ease;
    `

  backdrop.addEventListener('click', () => {
    hideMenu(menuState.activeMenu)
  })

  document.body.appendChild(backdrop)

  // Trigger reflow
  void backdrop.offsetWidth

  // Fade in
  backdrop.style.opacity = '1'
}

// Remove backdrop
function removeBackdrop() {
  const backdrop = document.querySelector('.menu-backdrop')
  if (backdrop) {
    backdrop.style.opacity = '0'
    setTimeout(() => {
      if (backdrop.parentNode) {
        backdrop.parentNode.removeChild(backdrop)
      }
    }, 200)
  }
}

// Handle document clicks to close menus
function handleDocumentClick(e) {
  if (!menuState.activeMenu) return

  const clickedElement = e.target
  const isMenuClick = menuState.activeMenu.contains(clickedElement)
  const isToggleClick = clickedElement.closest(`[data-menu-toggle="${menuState.activeMenu.id}"]`)

  if (!isMenuClick && !isToggleClick) {
    hideMenu(menuState.activeMenu)
  }
}

// Handle keyboard events
function handleKeyDown(e) {
  if (e.key === 'Escape' && menuState.activeMenu) {
    hideMenu(menuState.activeMenu)
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeMenus)
} else {
  initializeMenus()
}

// Export public API
window.MenuSystem = {
  initialize: initializeMenus,
  show: showMenu,
  hide: hideMenu,
  toggle: toggleMenu,
}
