/**
 * Menu Controller
 * Handles all menu interactions and state management
 * Combines functionality from menu-handler.js with enhanced features
 */
class MenuController {
  constructor() {
    this.activeMenu = null;
    this.menuButtons = [];
    this.menus = [];
    this.initialized = false;
    this.activeButton = null;
    
    // Event handler storage for cleanup
    this.clickHandlers = new Map();
    this.keyboardHandlers = new Map();
    this.menuItemHandlers = new Map();
    this.menuKeydownHandlers = new Map();
    this.documentClickHandler = null;
    this.keydownHandler = null;
    this.globalClickHandler = null;
    this.globalKeydownHandler = null;
    this.beforeUnloadHandler = null;
    this.menuObserver = null;
    this.applyHandler = null;
    this.resetHandler = null;
    this.strategySelectHandler = null;
    
    // Bind methods
    this.init = this.init.bind(this);
    this.toggleMenu = this.toggleMenu.bind(this);
    this.closeAllMenus = this.closeAllMenus.bind(this);
    this.handleDocumentClick = this.handleDocumentClick.bind(this);
    this.handleEscapeKey = this.handleEscapeKey.bind(this);
    this.getFocusableElements = this.getFocusableElements.bind(this);
    this.setupMenuKeyboardNavigation = this.setupMenuKeyboardNavigation.bind(this);
    this.setupMenuItems = this.setupMenuItems.bind(this);
    this.navigateMenuItems = this.navigateMenuItems.bind(this);
    this.setupMenuObserver = this.setupMenuObserver.bind(this);
    this.populateStrategySelector = this.populateStrategySelector.bind(this);
    this.updateStrategyInfo = this.updateStrategyInfo.bind(this);
    this.setupStrategyButtons = this.setupStrategyButtons.bind(this);
    this.showToast = this.showToast.bind(this);
    this.destroy = this.destroy.bind(this);
  }

  /**
   * Initialize the menu controller
   */
  init() {
    if (this.initialized) {
      this.destroy();
    }
    
    // Initialize menu buttons and menus
    this.menuButtons = Array.from(document.querySelectorAll('.menu-button[data-target]'));
    this.menus = Array.from(document.querySelectorAll('.menu-content'));
    
    // Add event listeners to menu buttons
    this.menuButtons.forEach(button => {
      const targetId = button.getAttribute('data-target');
      if (targetId) {
        if (this.clickHandlers.has(button)) return;
        
        const clickHandler = (e) => {
          e.stopPropagation();
          this.toggleMenu(targetId);
        };
        
        this.clickHandlers.set(button, clickHandler);
        button.addEventListener('click', clickHandler);
        
        // Add keyboard accessibility
        button.setAttribute('role', 'button');
        button.setAttribute('aria-expanded', 'false');
        button.setAttribute('aria-haspopup', 'dialog');
        button.setAttribute('aria-controls', targetId);
        
        // Add keyboard navigation
        button.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.toggleMenu(targetId);
          } else if (e.key === 'Escape' && this.activeMenu === targetId) {
            this.closeAllMenus();
            button.focus();
          }
        });
      }
    });
    
    // Setup observer for dynamic menu items
    this.setupMenuObserver();
    
    // Setup initial menu items
    const initialMenuItems = document.querySelectorAll('.menu-content a, .menu-content button, .menu-content .menu-item');
    this.setupMenuItems(initialMenuItems);
    
    // Add document click handler to close menus when clicking outside
    this.documentClickHandler = this.handleDocumentClick.bind(this);
    document.addEventListener('click', this.documentClickHandler);
    
    // Add escape key handler to close menus
    this.keydownHandler = this.handleEscapeKey.bind(this);
    document.addEventListener('keydown', this.keydownHandler);
    
    // Initialize strategy selector if available
    this.populateStrategySelector();
    
    // Set up strategy apply/reset buttons
    this.setupStrategyButtons();
    
    this.initialized = true;
    console.log('MenuController initialized');
  }

  /**
   * Toggle a menu by its ID
   * @param {string} menuId - The ID of the menu to toggle
   */
  toggleMenu(menuId) {
    const menu = document.getElementById(menuId);
    if (!menu) return;
    
    if (this.activeMenu === menuId) {
      this.closeAllMenus();
      return;
    }
    
    // Close any open menus
    this.closeAllMenus();
    
    // Open the selected menu
    menu.classList.add('active');
    this.activeMenu = menuId;
    
    // Update ARIA attributes
    menu.setAttribute('aria-hidden', 'false');
    menu.setAttribute('aria-expanded', 'true');
    
    // Find and update the button that controls this menu
    const button = document.querySelector(`[data-target="${menuId}"]`);
    if (button) {
      button.setAttribute('aria-expanded', 'true');
      button.classList.add('active');
      this.activeButton = button;
    }
    
    // Focus management
    const focusableElements = this.getFocusableElements(menu);
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
    
    // Setup keyboard navigation
    this.setupMenuKeyboardNavigation(menu, button);
    
    // Dispatch custom event
    const event = new CustomEvent('menu:opened', { 
      detail: { 
        menuId,
        menuElement: menu,
        triggerElement: button
      } 
    });
    document.dispatchEvent(event);
  }

  /**
   * Close all open menus
   */
  closeAllMenus() {
    if (!this.activeMenu) return;
    
    const menu = document.getElementById(this.activeMenu);
    if (menu) {
      menu.classList.remove('active');
      menu.setAttribute('aria-hidden', 'true');
      menu.setAttribute('aria-expanded', 'false');
      
      // Remove keyboard navigation class and handler
      menu.classList.remove('keyboard-navigation-active');
      
      // Remove the keyboard navigation handler for this menu
      if (this.menuKeydownHandlers && this.menuKeydownHandlers.has(menu)) {
        const handler = this.menuKeydownHandlers.get(menu);
        menu.removeEventListener('keydown', handler);
        this.menuKeydownHandlers.delete(menu);
      }
      
      // Dispatch custom event
      const event = new CustomEvent('menu:closed', { 
        detail: { 
          menuId: this.activeMenu,
          menuElement: menu
        } 
      });
      document.dispatchEvent(event);
    }
    
    // Update the button that controls the active menu
    const button = document.querySelector(`[data-target="${this.activeMenu}"]`);
    if (button) {
      button.setAttribute('aria-expanded', 'false');
      button.classList.remove('active');
      
      // Return focus to the button if it was the active one
      if (this.activeButton === button) {
        button.focus();
        this.activeButton = null;
      }
    }
    
    this.activeMenu = null;
  }

  /**
   * Handle document click to close menus when clicking outside
   * @param {Event} e - The click event
   */
  handleDocumentClick(e) {
    // If clicking outside any menu button or menu content
    const isMenuButton = e.target.closest('.menu-button');
    const isMenuContent = e.target.closest('.menu-content');
    
    if (!isMenuButton && !isMenuContent) {
      this.closeAllMenus();
    }
    
    // If clicking a menu button that's not the currently active one
    if (isMenuButton && this.activeMenu) {
      const targetId = isMenuButton.getAttribute('data-target');
      if (targetId !== this.activeMenu) {
        this.closeAllMenus();
      }
    }
  }

  /**
   * Handle escape key to close menus
   * @param {KeyboardEvent} e - The keydown event
   */
  handleEscapeKey(e) {
    if (e.key === 'Escape') {
      this.closeAllMenus();
      
      // Find the active menu button and focus it
      if (this.activeMenu) {
        const button = document.querySelector(`[data-target="${this.activeMenu}"]`);
        if (button) {
          button.focus();
        }
      }
    }
  }

  /**
   * Get all focusable elements within a container
   * @param {HTMLElement} container - The container element
   * @returns {Array<HTMLElement>} Array of focusable elements
   */
  getFocusableElements(container) {
    if (!container) return [];
    
    return Array.from(container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )).filter(el => {
      // Filter out hidden and disabled elements
      return !el.hasAttribute('disabled') && 
             !el.hidden && 
             getComputedStyle(el).display !== 'none';
    });
  }

  /**
   * Setup keyboard navigation for a menu
   * @param {HTMLElement} menu - The menu element
   * @param {HTMLElement} triggerButton - The button that opened the menu
   */
  setupMenuKeyboardNavigation(menu, triggerButton) {
    if (!menu) return;
    
    const focusableElements = this.getFocusableElements(menu);
    if (focusableElements.length === 0) return;
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    // Add keydown handler to the menu
    const keydownHandler = (e) => {
      if (!this.activeMenu) return;
      
      const isEscape = e.key === 'Escape';
      const isTab = e.key === 'Tab';
      const isShiftTab = isTab && e.shiftKey;
      
      if (isEscape) {
        e.preventDefault();
        this.closeAllMenus();
        if (triggerButton) {
          triggerButton.focus();
        }
        return;
      }
      
      if (!isTab) return;
      
      // Handle tab navigation within the menu
      const activeElement = document.activeElement;
      
      if (!menu.contains(activeElement)) {
        // If focus is outside the menu, move it to the first element
        e.preventDefault();
        firstElement.focus();
        return;
      }
      
      if (isShiftTab && activeElement === firstElement) {
        // Shift+Tab on first element: focus the last element
        e.preventDefault();
        lastElement.focus();
      } else if (!isShiftTab && activeElement === lastElement) {
        // Tab on last element: focus the first element
        e.preventDefault();
        firstElement.focus();
      }
    };
    
    // Store the handler for cleanup
    if (!this.menuKeydownHandlers) {
      this.menuKeydownHandlers = new Map();
    }
    
    // Remove any existing handler for this menu
    const existingHandler = this.menuKeydownHandlers.get(menu);
    if (existingHandler) {
      menu.removeEventListener('keydown', existingHandler);
    }
    
    // Add the new handler
    menu.addEventListener('keydown', keydownHandler);
    this.menuKeydownHandlers.set(menu, keydownHandler);
    
    // Add a class for styling
    menu.classList.add('keyboard-navigation-active');
  }

  /**
   * Setup menu items with event listeners
   * @param {NodeList} items - The menu items to setup
   */
  setupMenuItems(items) {
    if (!items || items.length === 0) return;
    
    items.forEach(item => {
      if (this.menuItemHandlers.has(item)) return;
      
      // Create a click handler for the menu item
      const clickHandler = (e) => {
        // Handle menu item click
        e.preventDefault();
        
        // Close all menus
        this.closeAllMenus();
        
        // Dispatch custom event for the menu item action
        const action = item.getAttribute('data-action');
        if (action) {
          const event = new CustomEvent(`menu:${action}`, { 
            detail: { 
              menuItem: item,
              action: action
            } 
          });
          document.dispatchEvent(event);
        }
      };
      
      // Store the handler for cleanup
      this.menuItemHandlers.set(item, {
        click: clickHandler
      });
      
      // Add the event listener
      item.addEventListener('click', clickHandler);
      
      // Add keyboard accessibility
      item.setAttribute('role', 'menuitem');
      item.setAttribute('tabindex', '-1');
      
      // Add keyboard navigation handler
      const keydownHandler = (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          clickHandler(e);
        } else if (e.key === 'Escape') {
          e.preventDefault();
          this.closeAllMenus();
          if (this.activeButton) {
            this.activeButton.focus();
          }
        } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
          e.preventDefault();
          this.navigateMenuItems(item, e.key);
        }
      };
      
      item.addEventListener('keydown', keydownHandler);
      
      // Store the keydown handler for cleanup
      const handlers = this.menuItemHandlers.get(item);
      if (handlers) {
        handlers.keydown = keydownHandler;
      }
    });
  }

  /**
   * Navigate between menu items using arrow keys
   * @param {HTMLElement} currentItem - The current menu item
   * @param {string} key - The key pressed ('ArrowDown' or 'ArrowUp')
   */
  navigateMenuItems(currentItem, key) {
    const menu = currentItem.closest('.menu-content');
    if (!menu) return;
    
    const items = Array.from(menu.querySelectorAll('[role="menuitem"]'));
    const currentIndex = items.indexOf(currentItem);
    
    if (currentIndex === -1) return;
    
    let nextIndex;
    if (key === 'ArrowDown') {
      nextIndex = (currentIndex + 1) % items.length;
    } else {
      nextIndex = (currentIndex - 1 + items.length) % items.length;
    }
    
    items[nextIndex].focus();
  }

  /**
   * Setup a MutationObserver to handle dynamically added menu items
   */
  setupMenuObserver() {
    if (this.menuObserver) {
      this.menuObserver.disconnect();
    }
    
    const observerCallback = (mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const newItems = node.querySelectorAll('a, button, .menu-item');
              this.setupMenuItems(newItems);
            }
          });
        }
      }
    };
    
    this.menuObserver = new MutationObserver(observerCallback);
    
    // Observe all menus for changes
    this.menus.forEach(menu => {
      this.menuObserver.observe(menu, { childList: true, subtree: true });
    });
  }

  /**
   * Populate the strategy selector dropdown
   */
  populateStrategySelector() {
    const strategySelect = document.getElementById('strategySelect');
    if (!strategySelect || typeof TRADING_STRATEGIES === 'undefined') return;
    
    // Clear existing options
    strategySelect.innerHTML = '';
    
    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Select a strategy...';
    defaultOption.disabled = true;
    defaultOption.selected = true;
    strategySelect.appendChild(defaultOption);
    
    // Populate with strategies
    for (const strategyId in TRADING_STRATEGIES) {
      const strategy = TRADING_STRATEGIES[strategyId];
      const option = document.createElement('option');
      option.value = strategyId;
      option.textContent = strategy.name;
      strategySelect.appendChild(option);
    }
    
    // Add event listener to update info on change
    this.strategySelectHandler = (e) => this.updateStrategyInfo(e.target.value);
    strategySelect.addEventListener('change', this.strategySelectHandler);
    
    // Initialize with the first strategy's info
    this.updateStrategyInfo(Object.keys(TRADING_STRATEGIES)[0]);
  }

  /**
   * Update the strategy information display
   * @param {string} strategyId - The ID of the selected strategy
   */
  updateStrategyInfo(strategyId) {
    const strategy = TRADING_STRATEGIES[strategyId];
    if (!strategy) return;
    
    const nameEl = document.getElementById('strategy-info-name');
    const descriptionEl = document.getElementById('strategy-info-description');
    
    if (nameEl) {
      nameEl.textContent = strategy.name;
    }
    if (descriptionEl) {
      descriptionEl.textContent = strategy.description;
    }
  }

  /**
   * Setup apply and reset buttons for strategies
   */
  setupStrategyButtons() {
    const applyButton = document.querySelector('.apply-strategy');
    const resetButton = document.querySelector('.reset-strategy');
    
    if (this.applyHandler && applyButton) {
      applyButton.removeEventListener('click', this.applyHandler);
    }
    if (this.resetHandler && resetButton) {
      resetButton.removeEventListener('click', this.resetHandler);
    }
    
    this.applyHandler = () => {
      const strategySelect = document.getElementById('strategySelect');
      if (!strategySelect || !strategySelect.value) return;
      
      const strategyId = strategySelect.value;
      const strategy = TRADING_STRATEGIES[strategyId];
      
      if (strategy) {
        document.dispatchEvent(new CustomEvent('strategy:apply', { 
          detail: { strategyId, strategy } 
        }));
        this.showToast(`Strategy "${strategy.name}" applied`);
        this.closeAllMenus();
      }
    };
    
    this.resetHandler = () => {
      document.dispatchEvent(new CustomEvent('strategy:reset'));
      this.showToast('Strategy reset to default');
      this.closeAllMenus();
    };
    
    if (applyButton) {
      applyButton.addEventListener('click', this.applyHandler);
    }
    if (resetButton) {
      resetButton.addEventListener('click', this.resetHandler);
    }
  }

  /**
   * Show a toast notification
   * @param {string} message - The message to display
   * @param {string} type - 'success', 'error', 'warning', 'info'
   */
  showToast(message, type = 'success') {
    const container = document.getElementById('toast-container');
    if (!container) return;
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    container.appendChild(toast);
    
    setTimeout(() => {
      toast.classList.add('show');
    }, 10);
    
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => toast.remove(), 300);
    }, 3000);
  }

  /**
   * Clean up all event listeners and state
   */
  destroy() {
    // Disconnect observer
    if (this.menuObserver) {
      this.menuObserver.disconnect();
      this.menuObserver = null;
    }
    
    // Remove menu button handlers
    if (this.clickHandlers) {
      this.clickHandlers.forEach((handler, button) => {
        button.removeEventListener('click', handler);
        const kbdHandler = this.keyboardHandlers?.get(button);
        if (kbdHandler) button.removeEventListener('keydown', kbdHandler);
      });
      this.clickHandlers.clear();
    }
    if (this.keyboardHandlers) this.keyboardHandlers.clear();
    
    // Remove menu item handlers
    if (this.menuItemHandlers) {
      this.menuItemHandlers.forEach((handlers, item) => {
        if (handlers.click) item.removeEventListener('click', handlers.click);
        if (handlers.keydown) item.removeEventListener('keydown', handlers.keydown);
      });
      this.menuItemHandlers.clear();
    }
    
    // Remove menu keyboard navigation handlers
    if (this.menuKeydownHandlers) {
      this.menuKeydownHandlers.forEach((handler, menu) => {
        menu.removeEventListener('keydown', handler);
      });
      this.menuKeydownHandlers.clear();
    }
    
    // Remove document event listeners
    if (this.documentClickHandler) {
      document.removeEventListener('click', this.documentClickHandler);
      this.documentClickHandler = null;
    }
    if (this.keydownHandler) {
      document.removeEventListener('keydown', this.keydownHandler);
      this.keydownHandler = null;
    }
    
    // Remove strategy-related handlers
    const strategySelect = document.getElementById('strategySelect');
    if (strategySelect && this.strategySelectHandler) {
      strategySelect.removeEventListener('change', this.strategySelectHandler);
      this.strategySelectHandler = null;
    }
    const applyButton = document.querySelector('.apply-strategy');
    if (applyButton && this.applyHandler) {
      applyButton.removeEventListener('click', this.applyHandler);
      this.applyHandler = null;
    }
    const resetButton = document.querySelector('.reset-strategy');
    if (resetButton && this.resetHandler) {
      resetButton.removeEventListener('click', this.resetHandler);
      this.resetHandler = null;
    }
    
    // Remove global event listeners
    if (this.globalClickHandler) {
      document.removeEventListener('click', this.globalClickHandler);
      this.globalClickHandler = null;
    }
    if (this.globalKeydownHandler) {
      document.removeEventListener('keydown', this.globalKeydownHandler);
      this.globalKeydownHandler = null;
    }
    if (this.beforeUnloadHandler) {
      window.removeEventListener('beforeunload', this.beforeUnloadHandler);
      this.beforeUnloadHandler = null;
    }
    
    // Reset state
    this.activeMenu = null;
    this.menuButtons = [];
    this.menus = [];
    this.activeButton = null;
    this.initialized = false;
    
    console.log('MenuController destroyed');
  }
}

// Initialize MenuController when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Clean up any existing menu controller
  if (window.menuController) {
    window.menuController.destroy();
  }
  
  // Create and initialize new menu controller
  window.menuController = new MenuController();
  window.menuController.init();
  
  // Add global click handler to close menus when clicking outside
  const globalClickHandler = (e) => {
    if (!e.target.closest('.menu-content') && !e.target.closest('.menu-button')) {
      window.menuController.closeAllMenus();
    }
  };
  
  // Add global keydown handler for Escape key
  const globalKeydownHandler = (e) => {
    if (e.key === 'Escape') {
      window.menuController.closeAllMenus();
    }
  };
  
  // Add global event listeners
  document.addEventListener('click', globalClickHandler);
  document.addEventListener('keydown', globalKeydownHandler);
  
  // Store handlers for cleanup
  window.menuController.globalClickHandler = globalClickHandler;
  window.menuController.globalKeydownHandler = globalKeydownHandler;
  
  // Clean up on page unload
  const beforeUnloadHandler = () => {
    if (window.menuController) {
      window.menuController.destroy();
      window.menuController = null;
    }
    
    // Remove global event listeners
    document.removeEventListener('click', globalClickHandler);
    document.removeEventListener('keydown', globalKeydownHandler);
    window.removeEventListener('beforeunload', beforeUnloadHandler);
  };
  
  window.addEventListener('beforeunload', beforeUnloadHandler);
  
  // Store the beforeunload handler for cleanup
  window.menuController.beforeUnloadHandler = beforeUnloadHandler;
});
