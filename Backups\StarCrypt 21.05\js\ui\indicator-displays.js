// Indicator Display Handler for StarCrypt
// This script ensures all required indicators are properly displayed

// INDICATOR_DISPLAY_NAMES is now defined globally in global-variables.js; do not redefine here.

// Initialize indicator displays
function initializeIndicatorDisplays() {
  console.log('[IndicatorDisplays] Initializing indicator displays')

  // Wait for DOM to be fully loaded
  document.addEventListener('DOMContentLoaded', () => {
    // Add indicator row creator to global namespace
    window.ensureIndicatorRowsExist = ensureIndicatorRowsExist

    // Extend switchStrategy function to ensure indicators are displayed
    const originalSwitchStrategy = window.switchStrategy || function () {}
    window.switchStrategy = function (strategy) {
      // Call the original function first
      const result = originalSwitchStrategy.apply(this, arguments)

      // Now ensure all required indicator rows exist
      if (window.TRADING_STRATEGIES && window.TRADING_STRATEGIES[strategy]) {
        const indicators = window.TRADING_STRATEGIES[strategy].indicators || []
        ensureIndicatorRowsExist(indicators)
      }

      return result
    }

    // Run initial indicator check for current strategy
    if (window.currentStrategy && window.TRADING_STRATEGIES && window.TRADING_STRATEGIES[window.currentStrategy]) {
      const indicators = window.TRADING_STRATEGIES[window.currentStrategy].indicators || []
      ensureIndicatorRowsExist(indicators)
    }
  })
}

// Ensure all necessary indicator rows exist in the Oracle Matrix
function ensureIndicatorRowsExist(requiredIndicators) {
  console.log('[IndicatorDisplays] Ensuring indicator rows exist for:', requiredIndicators)

  // Safety check
  if (!Array.isArray(requiredIndicators)) {
    console.error('[IndicatorDisplays] Invalid indicators array:', requiredIndicators)
    return
  }

  // Get the momentum table (Oracle Matrix)
  const momentumTable = document.getElementById('momentum-table')
  if (!momentumTable) {
    console.error('[IndicatorDisplays] Momentum table not found')
    return
  }

  // Check existing indicators
  const existingRows = momentumTable.querySelectorAll('tr[data-indicator]')
  const existingIndicators = Array.from(existingRows).map(row => row.getAttribute('data-indicator'))

  // Find missing indicators
  const missingIndicators = requiredIndicators.filter(ind => !existingIndicators.includes(ind))

  // Create rows for missing indicators
  missingIndicators.forEach(indicator => {
    createIndicatorRow(momentumTable, indicator)
  })

  // Make sure previously created rows are visible if they're required
  existingRows.forEach(row => {
    const indicator = row.getAttribute('data-indicator')
    if (requiredIndicators.includes(indicator)) {
      row.style.display = ''
    } else {
      row.style.display = 'none'
    }
  })
}

// Create a new indicator row in the Oracle Matrix
function createIndicatorRow(table, indicator) {
  console.log(`[IndicatorDisplays] Creating row for indicator: ${indicator}`)

  // Create new row
  const newRow = document.createElement('tr')
  newRow.setAttribute('data-indicator', indicator)
  newRow.className = 'signal-row'

  // Get the display name for the indicator
  const displayName = INDICATOR_DISPLAY_NAMES[indicator] || indicator.toUpperCase()

  // Create name cell
  const nameCell = document.createElement('td')
  nameCell.textContent = displayName
  nameCell.className = 'signal-name'
  newRow.appendChild(nameCell)

  // Create signal cells for each timeframe
  if (window.TIMEFRAMES) {
    window.TIMEFRAMES.forEach(tf => {
      const signalCell = document.createElement('td')
      signalCell.setAttribute('data-timeframe', tf)
      signalCell.className = 'signal-light-cell'

      // Create signal circle
      const signalCircle = document.createElement('div')
      signalCircle.className = 'signal-light'
      signalCircle.id = `${indicator}-${tf}-signal`
      signalCircle.style.backgroundColor = '#808080' // Default neutral color
      signalCircle.setAttribute('data-tooltip', `${displayName} (${tf}): No data`)

      signalCell.appendChild(signalCircle)
      newRow.appendChild(signalCell)
    })
  }

  // Append the new row to the table
  table.appendChild(newRow)
}

// Initialize on script load
initializeIndicatorDisplays()

// Export functions for external use
window.initializeIndicatorDisplays = initializeIndicatorDisplays
