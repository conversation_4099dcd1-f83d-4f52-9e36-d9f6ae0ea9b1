/**
 * Unified Signal System - Combines signal management and rendering
 */
class SignalSystem {
  constructor() {
    // State
    this.isInitialized = false
    this.isUpdating = false
    this.lastUpdateTime = 0
    this.updateTimer = null
    this.signalState = new Map()
    this.pendingUpdates = new Map()
    this.elementCache = new Map()
    this.tooltipCache = new Map()
    this.updateQueue = []
    this.isProcessingQueue = false
    this.errorCount = 0
    this.maxErrors = 5
    this.updateInProgress = new Set()
    this.currentTimeframe = '1h'
    this.activeTimeframeGlowApplied = false;
    this.tradingViewChart = null; // Will be linked in init or by setTradingViewChart()
    this.matrixContainer = null; // Will be set in init()

    // Configuration
    this.config = {
      updateDebounce: 50,
      maxUpdateTime: 500,
      maxBatchSize: 5,
      updateCooldown: 500,
      signalColors: {
        'strong-buy': '#00FF00',
        'mild-buy': '#00AAFF',
        neutral: '#808080',
        'mild-sell': '#FFA500',
        'strong-sell': '#FF0000',
        error: '#FF00FF',
      },
      defaultTimeframes: ['1m', '5m', '15m', '1h', '4h', '1d', '1w'],
      batchSizes: {
        '1m': 3,
        '5m': 3,
        '15m': 2,
        '1h': 2,
        '4h': 1,
        '1d': 1,
        '1w': 1,
      },
      allowUpdatesBeforeInit: false, // Explicitly disallow updates before init completes
      glowClass: 'golden-pulse-glow', // Class for glow effect
      debug: true, // Debug mode for additional logging
    }

    // Setup listeners
    // this.setupEventListeners(); // Moved to end of init()
    // this.setupTimeframeListener(); // Timeframe changes now handled by internal click listeners
    // Defer timeframe click listener setup to init, after DOM is more likely ready
  }

  /**
   * Set the TradingView chart instance and set up chart ready callback
   * @param {Object} chartInstance - The TradingView widget instance
   */
  setTradingViewChart(chartInstance) {
    console.log('[SignalSystem] setTradingViewChart called with:', chartInstance);

    if (!chartInstance) {
      console.warn('[SignalSystem] Invalid TradingView chart instance provided');
      return;
    }

    this.tradingViewChart = chartInstance;
    console.log('[SignalSystem] TradingView chart instance received and linked successfully');

    // Set up chart ready callback if not already set
    if (typeof this.tradingViewChart.onChartReady === 'function') {
      this.tradingViewChart.onChartReady(() => {
        console.log('[SignalSystem] TradingView chart ready - initializing with current timeframe:', this.currentTimeframe);
        // Initialize with current timeframe
        this.syncTradingViewTimeframe(this.currentTimeframe);
      });
    } else {
      console.log('[SignalSystem] TradingView chart does not have onChartReady method, syncing immediately');
      // If no onChartReady, try to sync immediately after a short delay
      setTimeout(() => {
        this.syncTradingViewTimeframe(this.currentTimeframe);
      }, 1000);
    }

    // Test the connection immediately
    console.log('[SignalSystem] Testing TradingView connection...');
    this.testTradingViewConnection();
  }

  /**
   * Update the glow effect for the selected timeframe
   * @param {string} timeframe - The selected timeframe
   */
  updateSelectedTimeframeGlow(timeframe) {
    if (!timeframe) {
      if (this.config.debug) console.warn('[SignalSystem] No timeframe provided for glow update');
      return;
    }
    
    // Normalize timeframe (handle case sensitivity)
    const normalizedTimeframe = timeframe.toLowerCase();
    
    if (this.config.debug) {
      console.log(`[SignalSystem] Updating timeframe glow to: ${normalizedTimeframe}`);
    }
    
    // Remove glow from all signal circles
    document.querySelectorAll(`.${this.config.glowClass}`).forEach(el => {
      el.classList.remove(this.config.glowClass);
    });
    
    try {
      // Add glow to all signal circles with matching timeframe
      // Check both data-tf and data-timeframe attributes for maximum compatibility
      const selectors = [
        `.signal-circle[data-tf="${normalizedTimeframe}"]`,
        `.signal-circle[data-timeframe="${normalizedTimeframe}"]`,
        // Also check case variations
        `.signal-circle[data-tf="${timeframe}"]`,
        `.signal-circle[data-timeframe="${timeframe}"]`
      ];
      
      // Combine all matching elements and remove duplicates
      const allCircles = [];
      const seen = new Set();
      
      selectors.forEach(selector => {
        document.querySelectorAll(selector).forEach(el => {
          if (!seen.has(el)) {
            seen.add(el);
            allCircles.push(el);
          }
        });
      });
      
      // Apply glow to all matching circles
      allCircles.forEach(circle => {
        circle.classList.add(this.config.glowClass);
        // Ensure the element is visible
        circle.style.display = 'block';
        circle.style.visibility = 'visible';
      });
      
      // Update current timeframe if it has changed
      if (this.currentTimeframe !== normalizedTimeframe) {
        const previousTimeframe = this.currentTimeframe;
        this.currentTimeframe = normalizedTimeframe;
        
        if (this.config.debug) {
          console.log(`[SignalSystem] Timeframe changed from ${previousTimeframe || 'none'} to ${normalizedTimeframe}`);
        }
        
        // Sync with TradingView
        this.syncTradingViewTimeframe(normalizedTimeframe);
        
        // Dispatch timeframe changed event
        this.dispatchEvent('timeframeChanged', { 
          previousTimeframe,
          newTimeframe: normalizedTimeframe 
        });
      }
      
      if (this.config.debug) {
        console.log(`[SignalSystem] Applied glow to ${allCircles.length} signal circles for timeframe: ${normalizedTimeframe}`);
      }
      
      return allCircles.length > 0;
    } catch (error) {
      console.error('[SignalSystem] Error updating timeframe glow:', error);
      return false;
    }
  }
  


  setupTimeframeClickListeners() {
    if (!this.matrixContainer) {
      console.warn('[SignalSystem] Matrix container not available, cannot set up timeframe click listeners.');
      return;
    }

    this.matrixContainer.addEventListener('click', (event) => {
      const signalCircle = event.target.closest('.signal-circle');
      if (!signalCircle) {
        return; // Exit if the click was not on a signal circle or its descendant.
      }

      const timeframe = signalCircle.dataset.tf || signalCircle.dataset.timeframe;
      const indicator = signalCircle.dataset.ind || signalCircle.dataset.indicator;

      if (timeframe) {
        // The third argument to handleTimeframeSelect is the clicked element itself.
        this.handleTimeframeSelect(timeframe, indicator, signalCircle);
      }
    });
  }

  handleTimeframeSelect(timeframe, indicator, clickedCellElement) {
    if (this.currentTimeframe !== timeframe) {
      console.log(`[SignalSystem] Timeframe selected: ${timeframe} for indicator: ${indicator || 'N/A'}`);
      this.currentTimeframe = timeframe;
      this.updateSelectedTimeframeGlow(timeframe);
      this.syncTradingViewTimeframe(timeframe);
      this.dispatchEvent('timeframeChanged', { timeframe: this.currentTimeframe, indicator });
    } else {
      console.log(`[SignalSystem] Timeframe ${timeframe} re-selected or clicked.`);
      this.updateSelectedTimeframeGlow(timeframe); // Re-apply glow
    }

    if (indicator && clickedCellElement) {
       const signalCircle = clickedCellElement.querySelector('.signal-circle');
       if (signalCircle) {
          const indicatorName = signalCircle.dataset.indicator || signalCircle.dataset.ind;
          if (indicatorName) {
               this.dispatchEvent('signalClick', { indicator: indicatorName, timeframe });
          }
       }
    }
  }

  /**
   * Sync the TradingView chart with the selected timeframe
   * @param {string} timeframe - The selected timeframe
   */
  syncTradingViewTimeframe(timeframe) {
    if (!timeframe) {
      console.warn('[SignalSystem] No timeframe provided for sync');
      return;
    }

    // Try to get the TradingView widget from the most reliable source
    const getTradingViewWidget = () => {
      // 1. Check if we have a direct reference
      if (this.tradingViewChart) {
        if (this.config.debug) console.log('[SignalSystem][getTradingViewWidget] Using this.tradingViewChart:', this.tradingViewChart);
        return this.tradingViewChart;
      }

      // 2. Check the window.StarCrypt.tradingViewManager
      if (window.StarCrypt?.tradingViewManager?.chart) {
        if (this.config.debug) console.log('[SignalSystem][getTradingViewWidget] Using window.StarCrypt.tradingViewManager.chart:', window.StarCrypt.tradingViewManager.chart);
        this.tradingViewChart = window.StarCrypt.tradingViewManager.chart;
        return this.tradingViewChart;
      }

      // 3. Fall back to global reference
      if (window.tradingViewWidget) {
        if (this.config.debug) console.log('[SignalSystem][getTradingViewWidget] Using window.tradingViewWidget:', window.tradingViewWidget);
        this.tradingViewChart = window.tradingViewWidget;
        return this.tradingViewChart;
      }

      // 4. Try to find TradingView widget in DOM
      const tvContainer = document.getElementById('tradingview_candle');
      if (tvContainer && tvContainer._tradingViewWidget) {
        if (this.config.debug) console.log('[SignalSystem][getTradingViewWidget] Using DOM container widget:', tvContainer._tradingViewWidget);
        this.tradingViewChart = tvContainer._tradingViewWidget;
        return this.tradingViewChart;
      }

      // 5. Try to find any TradingView widget in the global scope
      if (typeof window.TradingView !== 'undefined' && window.TradingView.widget) {
        // Look for any existing widget instances
        const widgets = document.querySelectorAll('iframe[src*="tradingview"]');
        if (widgets.length > 0) {
          console.log('[SignalSystem][getTradingViewWidget] Found TradingView iframe, but no direct widget reference');
        }
      }

      if (this.config.debug) {
        console.warn('[SignalSystem][getTradingViewWidget] No TradingView widget reference available. Checked:', {
          'this.tradingViewChart': !!this.tradingViewChart,
          'window.StarCrypt.tradingViewManager.chart': !!(window.StarCrypt?.tradingViewManager?.chart),
          'window.tradingViewWidget': !!window.tradingViewWidget,
          'DOM container': !!document.getElementById('tradingview_candle'),
          'TradingView library': typeof window.TradingView !== 'undefined'
        });
      }
      return null;
    };

    const widget = getTradingViewWidget();
    if (!widget) {
      console.warn('[SignalSystem] TradingView widget not available for timeframe sync. Timeframe:', timeframe);
      return;
    }

    // Map our timeframe format to TradingView's format
    const tvTimeframeMap = {
      '1m': '1',
      '3m': '3',
      '5m': '5',
      '15m': '15',
      '30m': '30',
      '1h': '60',
      '2h': '120',
      '4h': '240',
      '1d': '1D',
      '1w': '1W',
      '1M': '1M'
    };

    const tvInterval = tvTimeframeMap[timeframe.toLowerCase()];
    if (!tvInterval) {
      if (this.config.debug) console.warn(`[SignalSystem] Unknown timeframe for TradingView sync: ${timeframe}`);
      return;
    }

    if (this.config.debug) {
      console.log(`[SignalSystem] Syncing TradingView to timeframe: ${timeframe} (TV Interval: ${tvInterval})`);
    }

    try {
      // For TradingView widgets, we need to wait for the chart to be ready
      if (typeof widget.onChartReady === 'function') {
        widget.onChartReady(() => {
          this.updateTradingViewTimeframe(widget, tvInterval, timeframe);
        });
      } else {
        // If no onChartReady, try immediately
        this.updateTradingViewTimeframe(widget, tvInterval, timeframe);
      }
    } catch (error) {
      console.error('[SignalSystem] Error syncing TradingView timeframe:', error);
    }
  }

  /**
   * Update TradingView timeframe using the correct API methods
   */
  updateTradingViewTimeframe(widget, tvInterval, originalTimeframe) {
    console.log(`[SignalSystem] Updating TradingView timeframe to ${tvInterval}`);

    try {
      // Method 1: Use the widget's setSymbol method (most reliable)
      if (typeof widget.setSymbol === 'function') {
        const currentSymbol = 'KRAKEN:BTCUSD'; // Use a known symbol
        console.log(`[SignalSystem] Using setSymbol(${currentSymbol}, ${tvInterval})`);

        widget.setSymbol(currentSymbol, tvInterval, () => {
          console.log(`[SignalSystem] Successfully changed timeframe to ${tvInterval}`);
        });
        return true;
      }

      // Method 2: Try chart().setResolution()
      if (typeof widget.chart === 'function') {
        try {
          const chart = widget.chart();
          if (chart && typeof chart.setResolution === 'function') {
            console.log(`[SignalSystem] Using chart.setResolution(${tvInterval})`);
            chart.setResolution(tvInterval);
            return true;
          }
        } catch (e) {
          console.warn('[SignalSystem] chart().setResolution() failed:', e);
        }
      }

      // Method 3: Try to access the widget's internal methods
      if (widget.iframe && widget.iframe.contentWindow) {
        try {
          console.log('[SignalSystem] Attempting to communicate with iframe');
          widget.iframe.contentWindow.postMessage({
            name: 'set-interval',
            data: tvInterval
          }, '*');
          return true;
        } catch (e) {
          console.warn('[SignalSystem] iframe communication failed:', e);
        }
      }

      console.warn(`[SignalSystem] All methods failed for timeframe ${originalTimeframe} -> ${tvInterval}`);
      return false;

    } catch (error) {
      console.error('[SignalSystem] Error in updateTradingViewTimeframe:', error);
      return false;
    }
  }

  /**
     * Initialize the signal system
     */
  init() {
    if (this.isInitialized) return;
    console.log('[SignalSystem] Initializing...');

    this.matrixContainer = document.querySelector('.indicators-section.cosmic-indicators');
    if (!this.matrixContainer) {
      console.error('[SignalSystem] CRITICAL: Oracle Matrix container (.indicators-section.cosmic-indicators) not found during init! Glow and click events will not work.');
      this.isInitialized = false; 
      return;
    }
    console.log('[SignalSystem] Oracle Matrix container found.');

    // Initialize all signal elements *within the matrixContainer*
    const signalsInMatrix = this.matrixContainer.querySelectorAll('.signal-circle');
    if (signalsInMatrix.length === 0) {
        console.warn('[SignalSystem] No .signal-circle elements found *within* the matrixContainer at init time. Glow and updates may not work until they appear.');
    } else {
        console.log(`[SignalSystem] Found ${signalsInMatrix.length} signal circles within the matrix container. Inspecting them...`);
        signalsInMatrix.forEach((el, index) => {
            console.log(`[SignalSystem] Circle ${index}: Indicator (data-indicator/ind): '${el.dataset.indicator || el.dataset.ind}', Timeframe (data-timeframe/tf): '${el.dataset.timeframe || el.dataset.tf}', Element:`, el);
        });
    }

    this.isInitialized = true; // Set initialized to true BEFORE processing elements

    signalsInMatrix.forEach(signalElement => { 
        const indicator = signalElement.dataset.indicator || signalElement.dataset.ind;
        const timeframe = signalElement.dataset.timeframe || signalElement.dataset.tf;

        if (indicator && timeframe) {
            const key = `${indicator}:${timeframe}`;
            const initialState = {
                value: null,
                strength: null,
                status: 'waiting', 
                element: signalElement, // Store the actual element
                lastUpdate: 0
            };
            this.signalState.set(key, initialState);
            this.updateSignalElement(key, initialState); 
        } else {
            console.warn('[SignalSystem] Found a .signal-circle within matrixContainer without required data-indicator/data-timeframe or data-tf attributes.', signalElement);
        }
    });

    console.log('[SignalSystem] Initial processing of signal circles complete.');

    // Setup listeners first
    this.setupTimeframeClickListeners();
    this.setupEventListeners();

    // Try to establish TradingView connection if not already linked
    this.attemptTradingViewConnection();

    // Then apply initial glow. This ensures that if data-timeframe attributes are added by another script
    // between querySelectorAll and this point, they are considered.
    this.updateSelectedTimeframeGlow(this.currentTimeframe);
    console.log('[SignalSystem] Initial glow applied and event listeners set up.');

    const event = new CustomEvent('signalSystem:ready', {
      detail: { signalSystem: this }, // Pass the instance itself
    });
    document.dispatchEvent(event);
    console.log('[SignalSystem] Initialized and signalSystem:ready event dispatched.');
  }

  /**
   * Attempt to establish TradingView connection
   */
  attemptTradingViewConnection() {
    if (this.tradingViewChart) {
      console.log('[SignalSystem] TradingView chart already connected');
      return;
    }

    // Try to find and connect to existing TradingView widget
    const widget = this.findTradingViewWidget();
    if (widget) {
      console.log('[SignalSystem] Found existing TradingView widget, establishing connection');
      this.setTradingViewChart(widget);
    } else {
      console.log('[SignalSystem] No TradingView widget found during initialization, will retry when needed');

      // Set up a listener for when TradingView becomes available
      const checkForTradingView = () => {
        const widget = this.findTradingViewWidget();
        if (widget) {
          console.log('[SignalSystem] TradingView widget became available, establishing connection');
          this.setTradingViewChart(widget);
          return true;
        }
        return false;
      };

      // Check periodically for TradingView widget
      let attempts = 0;
      const maxAttempts = 30; // 30 seconds
      const checkInterval = setInterval(() => {
        attempts++;
        if (checkForTradingView() || attempts >= maxAttempts) {
          clearInterval(checkInterval);
          if (attempts >= maxAttempts) {
            console.warn('[SignalSystem] TradingView widget not found after 30 seconds');
          }
        }
      }, 1000);
    }
  }

  /**
   * Find TradingView widget from various sources
   */
  findTradingViewWidget() {
    // Check window.StarCrypt.tradingViewManager
    if (window.StarCrypt?.tradingViewManager?.chart) {
      return window.StarCrypt.tradingViewManager.chart;
    }

    // Check global reference
    if (window.tradingViewWidget) {
      return window.tradingViewWidget;
    }

    // Check DOM container
    const tvContainer = document.getElementById('tradingview_candle');
    if (tvContainer && tvContainer._tradingViewWidget) {
      return tvContainer._tradingViewWidget;
    }

    // NEW: Try to create a bridge to the existing TradingView iframe
    const iframe = document.querySelector('#tradingview_candle iframe');
    if (iframe && iframe.contentWindow) {
      console.log('[SignalSystem] Found TradingView iframe, creating bridge...');
      return this.createTradingViewBridge(iframe);
    }

    return null;
  }

  /**
   * Create a bridge to communicate with TradingView iframe
   */
  createTradingViewBridge(iframe) {
    // Create a mock widget object that can communicate with the iframe
    const bridge = {
      iframe: iframe,

      // Mock setSymbol method that updates the iframe URL
      setSymbol: (symbol, interval, callback) => {
        try {
          console.log(`[TradingViewBridge] Attempting to change symbol to ${symbol} with interval ${interval}`);

          // Get current URL and update the interval parameter
          const currentSrc = iframe.src;
          const url = new URL(currentSrc);

          // Extract the JSON data from the hash
          const hashData = url.hash.substring(1);
          const decodedData = decodeURIComponent(hashData);
          const widgetData = JSON.parse(decodedData);

          // Update the interval
          widgetData.interval = interval;
          widgetData.symbol = symbol;

          // Encode and set the new URL
          const newHashData = encodeURIComponent(JSON.stringify(widgetData));
          url.hash = newHashData;

          console.log(`[TradingViewBridge] Updating iframe URL to change interval to ${interval}`);
          iframe.src = url.toString();

          if (callback) {
            setTimeout(callback, 1000); // Call callback after a delay
          }

          return true;
        } catch (error) {
          console.warn('[TradingViewBridge] Error updating iframe URL:', error);
          return false;
        }
      },

      // Mock chart method
      chart: () => ({
        setResolution: (interval, callback) => {
          console.log(`[TradingViewBridge] Chart setResolution called with ${interval}`);
          return bridge.setSymbol(bridge.getCurrentSymbol(), interval, callback);
        }
      }),

      // Helper to get current symbol from iframe
      getCurrentSymbol: () => {
        try {
          const currentSrc = iframe.src;
          const url = new URL(currentSrc);
          const hashData = url.hash.substring(1);
          const decodedData = decodeURIComponent(hashData);
          const widgetData = JSON.parse(decodedData);
          return widgetData.symbol || 'KRAKEN:BTCUSD';
        } catch (error) {
          console.warn('[TradingViewBridge] Error getting current symbol:', error);
          return 'KRAKEN:BTCUSD';
        }
      },

      // Mock onChartReady
      onChartReady: (callback) => {
        console.log('[TradingViewBridge] onChartReady called');
        if (callback) {
          setTimeout(callback, 500); // Call callback after a delay
        }
      }
    };

    console.log('[TradingViewBridge] Created bridge for TradingView iframe');
    return bridge;
  }

  /**
     * Setup event listeners
     */
  setupEventListeners() {
    // document.addEventListener('click', this.handleSignalClick.bind(this)) // Replaced by setupTimeframeClickListeners
    document.addEventListener('change', this.handleIndicatorToggle.bind(this))
    window.addEventListener('resize', this.debounce(this.handleWindowResize.bind(this), 100))

    // Handle signal hover events for tooltips - can be refined later if needed
    if (this.matrixContainer) { // Use cached container
        this.matrixContainer.addEventListener('mouseover', (event) => { // Use cached container
            const signalCircle = event.target.closest('.signal-circle');
            if (signalCircle) {
                this.handleSignalHover({ target: signalCircle }); // Pass an object mimicking event.target
            }
        });
        this.matrixContainer.addEventListener('mouseout', (event) => { // Use this.matrixContainer
            const signalCircle = event.target.closest('.signal-circle');
            if (signalCircle) {
                this.handleSignalLeave({ target: signalCircle }); // Pass an object mimicking event.target
            }
        });
    } else {
        // Fallback or log warning if main container not found for hover events
        // This case should ideally not be reached if init() found the container
        console.warn('[SignalSystem] Matrix container not available for hover event delegation. Falling back to global .signal-circle listeners.');
        const signals = document.querySelectorAll('.signal-circle')
        signals.forEach(signal => {
          signal.addEventListener('mouseenter', this.handleSignalHover.bind(this))
          signal.addEventListener('mouseleave', this.handleSignalLeave.bind(this))
        })
    }
  }

  /**
     * Setup timeframe listener
     */
  setupTimeframeListener() {
    // This listener might conflict with the new internal timeframe handling.
    // Commenting out its body to prevent unintended side effects.
    // If other modules rely on this, it needs careful review.
    /*
    document.addEventListener('timeframeChanged', (event) => {
      const { timeframe } = event.detail || {}
      if (!timeframe) return

      console.log(`[SignalSystem] External timeframeChanged event to ${timeframe}`)
      // Avoid clearing state and full updates if it's our own event
      // if (event.detail && event.detail.source !== 'SignalSystemInternal') {
      //   this.currentTimeframe = timeframe
      //   // this.signalState.clear(); // This is destructive, be careful
      //   this.updateAllSignalLights() 
      //   this.updateSelectedTimeframeGlow(this.currentTimeframe); // Ensure glow is consistent
      // }
    })
    */
  }

  /**
     * Update all signal lights
     * @param {boolean} force - If true, bypass rate limiting
     */
  updateAllSignalLights(force = false) {
    if (this.errorCount >= this.maxErrors) {
      console.warn('[SignalSystem] Updates temporarily disabled due to too many errors')
      return
    }

    if (!force && !this.canUpdate()) {
      return
    }

    this.isUpdating = true
    try {
      const signals = document.querySelectorAll('.signal-circle')
      signals.forEach(signal => {
        const indicator = signal.dataset.indicator || signal.dataset.ind
        const timeframe = signal.dataset.timeframe || signal.dataset.tf
        if (indicator && timeframe) {
          const key = `${indicator}:${timeframe}`
          const data = this.signalState.get(key)
          if (data) {
            this.updateSignalElement(key, data)
          }
        }
      })
    } catch (error) {
      console.error('[SignalSystem] Error updating signals:', error)
      this.handleError(error)
    } finally {
      this.isUpdating = false
    }
  }

  /**
     * Update a single signal
     * @param {string} indicator - Indicator name
     * @param {string} timeframe - Timeframe
     * @param {Object} data - Signal data
     */
  updateSignal(indicator, timeframe, data) {
    const key = `${indicator}:${timeframe}`

    // Add to pending updates
    this.pendingUpdates.set(key, data)

    // Process updates if not already processing
    if (!this.isProcessingQueue) {
      this.processUpdates()
    }
  }

  /**
     * Process pending updates
     */
  processUpdates() {
    if (this.isProcessingQueue || this.pendingUpdates.size === 0) {
      return
    }

    this.isProcessingQueue = true
    try {
      // Process updates in batches
      const batch = Array.from(this.pendingUpdates.entries())
      for (const [key, data] of batch) {
        if (this.updateInProgress.has(key)) {
          continue
        }

        // Update the state in signalState
        const currentSignalInfo = this.signalState.get(key);
        if (currentSignalInfo) {
          currentSignalInfo.status = data.status !== undefined ? data.status : currentSignalInfo.status;
          currentSignalInfo.value = data.value !== undefined ? data.value : currentSignalInfo.value;
          currentSignalInfo.strength = data.strength !== undefined ? data.strength : currentSignalInfo.strength;
        }
        this.updateSignalElement(key, currentSignalInfo);
      }
    } catch (error) {
      console.error('[SignalSystem] Error processing updates:', error)
    } finally {
      this.isProcessingQueue = false
      this.pendingUpdates.clear()
    }
  }

  /**
   * Update a single signal element's visual state based on its current data.
   * @param {string} key - The unique key for the signal (e.g., 'BTC:1h').
   * @param {Object} signalData - The signal data object from this.signalState, which should include the .element reference.
   */
  updateSignalElement(key, signalData) {
    // signalData is the object from this.signalState, which includes the .element
    if (!this.isInitialized && !this.config.allowUpdatesBeforeInit) {
      console.warn(`[SignalSystem] Update for ${key} called before init and allowUpdatesBeforeInit is false. Update ignored.`);
      return;
    }

    const currentSignalInfo = this.signalState.get(key);
    if (!currentSignalInfo || !currentSignalInfo.element) {
      console.warn(`[SignalSystem] Signal element for ${key} not found in signalState or its element is missing. Cannot update. Was it present in matrixContainer during init?`);
      this.errorCount++;
      return;
    }

    const signalElement = currentSignalInfo.element;

    // If signalData is provided and is different from currentSignalInfo, it means an external update.
    // Update the state in currentSignalInfo based on what's provided in signalData.
    if (signalData && signalData !== currentSignalInfo) { 
        currentSignalInfo.status = signalData.status !== undefined ? signalData.status : currentSignalInfo.status;
        currentSignalInfo.value = signalData.value !== undefined ? signalData.value : currentSignalInfo.value;
        currentSignalInfo.strength = signalData.strength !== undefined ? signalData.strength : currentSignalInfo.strength;
    }
    currentSignalInfo.lastUpdate = Date.now();

    // Update DOM element dataset attributes
    signalElement.dataset.signalValue = currentSignalInfo.value !== null && currentSignalInfo.value !== undefined ? String(currentSignalInfo.value) : '';
    signalElement.dataset.signalStrength = currentSignalInfo.strength !== null && currentSignalInfo.strength !== undefined ? String(currentSignalInfo.strength) : '';
    signalElement.dataset.signalStatus = currentSignalInfo.status || 'neutral';

    // Determine the CSS classes
    let newClassName = 'signal-circle';
    const specificSignalClass = this.getSignalClass(currentSignalInfo.status, currentSignalInfo.strength);
    if (specificSignalClass) {
      newClassName += ' ' + specificSignalClass;
    } else {
      newClassName += ' neutral-light'; // Default specific class
    }

    // Add general status classes
    if (currentSignalInfo.status === 'waiting') {
      newClassName += ' signal-waiting';
    } else if (currentSignalInfo.status === 'error') {
      newClassName += ' signal-error';
    } else if (currentSignalInfo.status && currentSignalInfo.status !== 'neutral') { // Any non-neutral, non-waiting, non-error is active
      newClassName += ' signal-active';
    } else { // neutral or undefined status
      newClassName += ' signal-inactive'; 
    }
    
    signalElement.className = newClassName; // Apply all classes at once, replacing previous ones on signal-circle

    // Ensure golden-pulse-glow is correctly applied or removed
    if (signalElement.dataset.timeframe === this.currentTimeframe) {
      signalElement.classList.add(this.config.glowClass);
    } else {
      signalElement.classList.remove(this.config.glowClass);
    }
  }

  /**
     * Get signal class based on status and strength
     * @param {string} status - Signal status
     * @param {number} strength - Signal strength
     * @returns {string} - Signal class
     */
  getSignalClass(status, strength = 0.5) {
    const baseClass = status.toLowerCase().replace(/[^a-z]/g, '-')
    return `${baseClass}-light`
  }

  /**
     * Handle errors
     * @param {Error} error - Error object
     */
  handleError(error) {
    this.errorCount++
    if (this.errorCount >= this.maxErrors) {
      console.warn('[SignalSystem] Too many errors, pausing updates')
      this.pauseUpdates()
    }
  }

  /**
     * Pause signal updates
     */
  pauseUpdates() {
    this.isUpdating = false
    this.isProcessingQueue = false
    this.pendingUpdates.clear()
    this.updateInProgress.clear()

    // Clear any pending timers
    if (this.updateTimer) {
      clearTimeout(this.updateTimer)
      this.updateTimer = null
    }
  }

  /**
     * Check if we can update
     * @returns {boolean} - Can update
     */
  canUpdate() {
    const now = Date.now()
    return !this.isUpdating &&
               !this.isProcessingQueue &&
               (now - this.lastUpdateTime >= this.config.updateCooldown)
  }

  /**
     * Debounce function
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in ms
     * @returns {Function} - Debounced function
     */
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  /**
     * Handle signal click - This is now largely handled by handleTimeframeSelect
     * @param {Event} event - Click event
     */
  handleSignalClick = (event) => {
    const target = event.target.closest('.signal-circle');
    if (!target) return;

    const indicator = target.dataset.ind; // Prefer data-ind as per logs
    const timeframe = target.dataset.tf;  // Prefer data-tf as per logs

    if (!timeframe) {
        if (this.config.debug) console.warn('[SignalSystem] Clicked signal circle has no timeframe data attribute (data-tf).');
        return;
    }

    if (this.config.debug) {
        console.log(`[SignalSystem] Signal circle clicked. Indicator: ${indicator}, Timeframe: ${timeframe}`);
    }

    // Update the glow to the new timeframe (this will also call syncTradingViewTimeframe)
    this.updateSelectedTimeframeGlow(timeframe);

    // Dispatch a custom event for other modules if needed
    const clickEvent = new CustomEvent('signalSystem:signalClick', {
        detail: {
            indicator,
            timeframe,
            element: target
        },
        bubbles: true,
        composed: true
    });
    if (this.matrixContainer) {
      this.matrixContainer.dispatchEvent(clickEvent);
    } else {
      if (this.config.debug) console.warn("[SignalSystem] Matrix container not found for dispatching signalSystem:signalClick event");
    }
  };

  /**
     * Handle signal hover
     * @param {Event} event - Hover event
     */
  handleSignalHover(event) {
    const target = event.target.closest('.signal-circle')
    if (!target) return

    const tooltip = target.querySelector('.signal-tooltip')
    if (tooltip) {
      tooltip.style.display = 'block'
      this.positionTooltip(target, tooltip)
    }
  }

  /**
     * Handle signal leave
     * @param {Event} event - Leave event
     */
  handleSignalLeave(event) {
    const target = event.target.closest('.signal-circle')
    if (!target) return

    const tooltip = target.querySelector('.signal-tooltip')
    if (tooltip) {
      tooltip.style.display = 'none'
    }
  }

  /**
     * Position tooltip
     * @param {Element} signal - Signal element
     * @param {Element} tooltip - Tooltip element
     */
  positionTooltip(signal, tooltip) {
    const rect = signal.getBoundingClientRect()
    tooltip.style.left = `${rect.left + rect.width / 2}px`
    tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`
  }

  /**
     * Handle indicator toggle
     * @param {Event} event - Change event
     */
  handleIndicatorToggle(event) {
    const target = event.target.closest('[data-indicator]')
    if (!target) return

    const indicator = target.dataset.indicator
    const timeframe = target.dataset.timeframe || this.currentTimeframe
    if (indicator && timeframe) {
      this.dispatchEvent('indicatorToggle', { indicator, timeframe, value: event.target.checked })
    }
  }

  /**
     * Handle window resize
     */
  handleWindowResize() {
    this.updateAllSignalLights(true)
  }

  /**
     * Dispatch custom event
     * @param {string} type - Event type
     * @param {Object} detail - Event detail
     */
  dispatchEvent(type, detail) {
    const event = new CustomEvent(`signalSystem:${type}`, { detail })
    document.dispatchEvent(event)
  }

  /**
   * Debug function to test TradingView connection
   */
  testTradingViewConnection() {
    console.log('=== TradingView Connection Test ===');
    console.log('SignalSystem instance:', this);
    console.log('Current timeframe:', this.currentTimeframe);
    console.log('TradingView chart reference:', this.tradingViewChart);

    console.log('Available TradingView references:');
    console.log('- window.StarCrypt:', window.StarCrypt);
    console.log('- window.StarCrypt.tradingViewManager:', window.StarCrypt?.tradingViewManager);
    console.log('- window.StarCrypt.tradingViewManager.chart:', window.StarCrypt?.tradingViewManager?.chart);
    console.log('- window.tradingViewWidget:', window.tradingViewWidget);
    console.log('- DOM container:', document.getElementById('tradingview_candle'));

    // Check if TradingView iframe exists
    const iframe = document.querySelector('#tradingview_candle iframe');
    console.log('- TradingView iframe found:', !!iframe);
    if (iframe) {
      console.log('- Iframe ID:', iframe.id);
      console.log('- Iframe src:', iframe.src);
    }

    const widget = this.findTradingViewWidget();
    console.log('Found widget via findTradingViewWidget():', widget);

    if (widget) {
      console.log('Widget methods available:');
      console.log('- setSymbol:', typeof widget.setSymbol);
      console.log('- chart:', typeof widget.chart);
      console.log('- onChartReady:', typeof widget.onChartReady);

      console.log('Testing timeframe sync...');
      this.syncTradingViewTimeframe('5m');
    } else {
      console.log('No TradingView widget found - connection not established');

      // Try to manually establish connection
      console.log('Attempting manual connection...');
      if (window.StarCrypt?.tradingViewManager?.chart) {
        console.log('Found widget in window.StarCrypt.tradingViewManager.chart, connecting...');
        this.setTradingViewChart(window.StarCrypt.tradingViewManager.chart);
      } else if (window.tradingViewWidget) {
        console.log('Found widget in window.tradingViewWidget, connecting...');
        this.setTradingViewChart(window.tradingViewWidget);
      }
    }
    console.log('=== End Test ===');
  }
}

// Export for CommonJS and browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SignalSystem;
} else if (typeof window !== 'undefined') {
  window.StarCrypt = window.StarCrypt || {};
  
  // Create and initialize the signal system when DOM is ready
  const initSignalSystem = () => {
    const signalSystem = new SignalSystem();
    window.StarCrypt.signalSystem = signalSystem;
    window.signalSystem = signalSystem;
    
    // Initialize with default timeframe
    signalSystem.updateSelectedTimeframeGlow('1h');
    
    // Re-apply glow when signal lights are updated
    const originalUpdateSignalElement = signalSystem.updateSignalElement;
    signalSystem.updateSignalElement = function(key, signalData) {
      originalUpdateSignalElement.call(this, key, signalData);
      if (this.currentTimeframe) {
        this.updateSelectedTimeframeGlow(this.currentTimeframe);
      }
    };
  };
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initSignalSystem);
  } else {
    initSignalSystem();
  }
}
