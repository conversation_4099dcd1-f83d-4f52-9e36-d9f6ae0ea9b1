/**
 * Unified WebSocket Core - Combines connection management and message processing
 */
class WebSocketCore {
  /**
     * Create a new WebSocketCore instance
     * @param {Object} options - Configuration options
     * @param {string} options.url - WebSocket URL
     * @param {Array} [options.protocols=[]] - WebSocket protocols
     * @param {number} [options.maxBatchSize=10] - Maximum messages per batch
     * @param {number} [options.maxQueueSize=1000] - Max queue size before dropping
     * @param {number} [options.processDelay=10] - Delay between batches (ms)
     * @param {number} [options.maxConsecutiveErrors=10] - Max errors before pausing
     * @param {number} [options.errorResetTime=60000] - Time to reset error counter (ms)
     */
  constructor(options = {}) {
    // Connection settings
    this.url = options.url || `ws://${window.location.host}/ws`
    this.protocols = options.protocols || []
    this.ws = null

    // Reconnection settings
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10
    this.reconnectInterval = options.reconnectInterval || 1000
    this.maxReconnectInterval = options.maxReconnectInterval || 30000
    this.connectionTimeout = options.connectionTimeout || 5000
    this.shouldReconnect = true

    // Message processing settings
    this.maxBatchSize = options.maxBatchSize || 10
    this.maxQueueSize = options.maxQueueSize || 1000
    this.processDelay = options.processDelay || 10
    this.maxConsecutiveErrors = options.maxConsecutiveErrors || 10
    this.errorResetTime = options.errorResetTime || 60000

    // State
    this.messageQueue = []
    this.messageHandlers = new Map()
    this.processedMessages = new Set()
    this.currentlyProcessing = new Set()
    this.isProcessing = false
    this.isPaused = false
    this.consecutiveErrors = 0
    this.lastErrorTime = 0

    // Connection state
    this.isConnected = false
    this.isConnecting = false

    // Ping/pong
    this.pingInterval = options.pingInterval || 30000
    this.pingTimeout = options.pingTimeout || 10000
    this.lastPongTime = 0
    this.messageTimestamps = new Map() // Initialize messageTimestamps

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => this.cleanupOldMessages(), 30000)

    // Bind methods - moved to the end and added checks
    const methodsToBind = [
      'connect', 'disconnect', 'send', 'onMessage', 'onOpen', 'onClose', 'onError',
      'handlePing', 'handlePong', 'checkConnection', 'queueMessage', 'processQueue',
      'processBatch', 'processMessage', 'addMessageHandler', 'removeMessageHandler',
      'pauseProcessing', 'resumeProcessing', 'cleanup', 'cleanupOldMessages',
    ]

    methodsToBind.forEach(methodName => {
      if (typeof this[methodName] === 'function') {
        this[methodName] = this[methodName].bind(this)
      } else {
        console.error(`[WebSocketCore] Method ${methodName} is not a function or not defined. Cannot bind.`)
        // Optionally, throw an error to halt execution if a critical method is missing
        // throw new Error(`[WebSocketCore] Critical method ${methodName} not found.`);
      }
    })
  }

  /**
     * Connect to the WebSocket server
     */
  connect() {
    if (this.isConnected || this.isConnecting) {
      console.log('[WebSocket] Already connected or connecting')
      return
    }

    this.isConnecting = true
    console.log(`[WebSocket] Connecting to ${this.url}...`)

    try {
      this.ws = new WebSocket(this.url, this.protocols)
      this.ws.binaryType = 'arraybuffer'

      // Set up event handlers
      this.ws.onopen = this.onOpen
      this.ws.onmessage = this.onMessage
      this.ws.onclose = this.onClose
      this.ws.onerror = this.onError

      // Set connection timeout
      this.connectionTimeoutId = setTimeout(() => {
        if (!this.isConnected) {
          console.warn('[WebSocket] Connection timeout')
          this.ws.close()
        }
      }, this.connectionTimeout)
    } catch (error) {
      console.error('[WebSocket] Error creating WebSocket:', error)
      this.isConnecting = false
      this.scheduleReconnect()
    }
  }

  /**
     * Disconnect from the WebSocket server
     */
  disconnect() {
    if (!this.ws) {
      console.log('[WebSocket] No WebSocket instance to disconnect')
      return
    }

    this.shouldReconnect = false
    this.clearTimers()

    try {
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        this.ws.close(1000, 'User disconnected')
      }
    } catch (error) {
      console.error('[WebSocket] Error during disconnect:', error)
    }

    this.ws = null
    this.isConnected = false
    this.isConnecting = false
    this.messageHandlers.clear()
    this.messageQueue.length = 0
    this.currentlyProcessing.clear()
  }

  /**
     * Send a message through WebSocket
     * @param {Object|string} message - Message to send
     */
  send(message) {
    if (!this.isConnected) {
      console.warn('[WebSocket] Not connected, queuing message')
      this.queueMessage(message)
      return
    }

    try {
      if (typeof message === 'object') {
        this.ws.send(JSON.stringify(message))
      } else {
        this.ws.send(message)
      }
    } catch (error) {
      console.error('[WebSocket] Error sending message:', error)
      this.handleError(error)
    }
  }

  /**
     * Handle incoming WebSocket message
     * @param {MessageEvent} event - WebSocket message event
     */
  onMessage(event) {
    const now = Date.now()

    // Throttle message processing
    if (now - this.lastMessageTime < this.messageThrottleTime) {
      return
    }

    // Check if we're already processing
    if (this.processingDepth > 0) {
      console.warn('[WebSocket] Message processing already in progress, queuing message')
      this.queueMessage(event.data)
      return
    }

    try {
      // Increment processing depth
      this.processingDepth++

      // Process message synchronously
      this.processMessageSync(event.data)
    } catch (error) {
      console.error('[WebSocket] Error processing message:', error)
      this.onError(error)
    } finally {
      // Decrement processing depth
      this.processingDepth = Math.max(0, this.processingDepth - 1)

      // Queue next message if available
      if (this.messageQueue.length > 0 && !this.isPaused) {
        setTimeout(() => {
          if (this.messageQueue.length > 0) {
            const data = this.messageQueue.shift()
            this.processMessageSync(data)
          }
        }, this.processingCooldown)
      }
    }
  }

  /**
     * Process message synchronously
     * @param {any} data - Message data
     */
  processMessageSync(data) {
    if (!this.isConnected || this.isPaused) {
      return
    }

    // Parse message
    let message
    try {
      message = JSON.parse(data)
    } catch (error) {
      console.error('[WebSocket] Invalid message format:', data)
      return
    }

    // Check if we already processed this message
    const messageId = message.id || JSON.stringify(message)
    if (this.processedMessages.has(messageId)) {
      return
    }
    this.processedMessages.add(messageId)

    // Get handlers for this message type
    const handlers = this.messageHandlers.get(message.type)
    if (!handlers || handlers.size === 0) {
      console.warn(`[WebSocket] No handlers for message type: ${message.type}`)
      return
    }

    // Process handlers synchronously
    for (const handler of handlers) {
      try {
        handler(message)
      } catch (error) {
        console.error(`[WebSocket] Handler error for type ${message.type}:`, error)
        this.onError(error)
      }
    }
  }

  /**
     * Queue a message for processing
     * @param {Object} message - Message to queue
     * @returns {string} - Message ID
     */
  queueMessage(message) {
    if (!message || typeof message !== 'object') {
      console.error('[WebSocket] Invalid message format')
      return null
    }

    // Generate unique ID if not provided
    if (!message._messageId) {
      message._messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    // Check if message was already processed
    if (this.processedMessages.has(message._messageId)) {
      return message._messageId
    }

    // Add timestamp
    message._timestamp = message._timestamp || Date.now()

    // Add to queue if not full
    if (this.messageQueue.length < this.maxQueueSize) {
      this.messageQueue.push(message)

      // Start processing if not already running
      if (!this.isProcessing && !this.isPaused) {
        this.processQueue().catch(console.error)
      }
    } else {
      console.warn('[WebSocket] Message queue full, dropping message:', message._messageId)
    }

    return message._messageId
  }

  /**
     * Handle WebSocket connection open
     */
  onOpen() {
    console.log('[WebSocket] Connection established')
    this.isConnected = true
    this.isConnecting = false
    this.reconnectAttempts = 0
    clearTimeout(this.connectionTimeoutId)
    this.startPingTimer()
    // Process any queued messages
    this.processQueue().catch(console.error)
    // Notify listeners
    if (this.messageHandlers.has('open')) {
      this.messageHandlers.get('open').forEach(handler => handler())
    }
  }

  /**
     * Handle WebSocket connection close
     */
  onClose(event) {
    console.log(`[WebSocket] Connection closed. Code: ${event.code}, Reason: ${event.reason}`)
    this.isConnected = false
    this.isConnecting = false
    this.clearTimers()
    if (this.shouldReconnect) {
      this.scheduleReconnect()
    }
    // Notify listeners
    if (this.messageHandlers.has('close')) {
      this.messageHandlers.get('close').forEach(handler => handler(event))
    }
  }

  /**
     * Handle WebSocket errors
     */
  onError(error) {
    console.error('[WebSocket] Error:', error)
    // this.isConnecting = false; // Already handled by onClose or connect timeout
    // this.handleError(error); // Avoid potential loops if handleError also calls onError
    // If not connected and should reconnect, onClose will handle scheduling reconnect
    // If already connected, this might be a non-fatal error, or will lead to onClose.
    // Notify listeners
    if (this.messageHandlers.has('error')) {
      this.messageHandlers.get('error').forEach(handler => handler(error))
    }
  }

  /**
     * Start the ping timer
     */
  startPingTimer() {
    this.clearTimers() // Clear any existing timers
    this.lastPongTime = Date.now() // Assume connection is healthy at start
    this.pingTimer = setInterval(this.checkConnection, this.pingInterval)
    console.log('[WebSocket] Ping timer started.')
  }

  /**
     * Handle ping (server may send ping)
     */
  handlePing() {
    // Respond with pong if server sends ping (not typical for client-initiated ping)
    if (this.isConnected) {
      this.send(JSON.stringify({ event: 'pong' }))
    }
  }

  /**
     * Handle pong response from server
     */
  handlePong() {
    this.lastPongTime = Date.now()
    if (window.DEBUG_WEBSOCKET) console.log('[WebSocket] Pong received')
  }

  /**
     * Check connection status and send ping
     */
  checkConnection() {
    if (!this.isConnected) {
      console.warn('[WebSocket] checkConnection: Not connected, stopping ping timer.')
      this.clearTimers()
      return
    }

    if (Date.now() - this.lastPongTime > this.pingInterval + this.pingTimeout) {
      console.warn('[WebSocket] Ping timeout. Closing connection.')
      this.ws.close(1006, 'Ping timeout') // 1006: Abnormal Closure
      // onClose will handle reconnection logic
      return
    }

    try {
      if (this.ws.readyState === WebSocket.OPEN) {
        this.send(JSON.stringify({ event: 'ping' }))
        if (window.DEBUG_WEBSOCKET) console.log('[WebSocket] Ping sent')
      }
    } catch (error) {
      console.error('[WebSocket] Error sending ping:', error)
      this.onError(error)
    }
  }

  /**
     * Clear all timers (ping, connection timeout)
     */
  clearTimers() {
    if (this.pingTimer) {
      clearInterval(this.pingTimer)
      this.pingTimer = null
      if (window.DEBUG_WEBSOCKET) console.log('[WebSocket] Ping timer cleared.')
    }
    if (this.connectionTimeoutId) {
      clearTimeout(this.connectionTimeoutId)
      this.connectionTimeoutId = null
    }
  }

  /**
     * Process the message queue
     */
  processQueue() {
    // Check if we should process
    const now = Date.now()
    if (this.isProcessing ||
            this.isPaused ||
            this.messageQueue.length === 0 ||
            now - this.lastProcessTime < this.processingCooldown) {
      return
    }

    this.isProcessing = true
    this.lastProcessTime = now

    try {
      // Process messages one at a time with delay
      const message = this.messageQueue.shift()
      if (message) {
        this.processMessageSync(message)
      }
    } catch (error) {
      console.error('[WebSocket] Error processing queue:', error)
      this.handleError(error)
    } finally {
      this.isProcessing = false

      // Schedule next processing if more messages
      if (this.messageQueue.length > 0 && !this.isPaused) {
        setTimeout(() => this.processQueue(), this.processDelay)
      }
    }
  }

  /**
     * Process a batch of messages
     * @param {Array} batch - Batch of messages to process
     */
  async processBatch(batch) {
    for (const message of batch) {
      if (this.isPaused) break

      try {
        await this.processMessage(message)
        this.processedMessages.add(message._messageId)
        this.consecutiveErrors = 0
      } catch (error) {
        console.error('[WebSocket] Error processing message:', {
          error,
          messageId: message._messageId,
          type: message.type,
        })
        this.handleError(error)
      }
    }
  }

  /**
     * Process a single message
     * @param {Object} message - Message to process
     */
  processMessage(message) {
    if (!message || !message.type) {
      console.error('[WebSocket] Invalid message format: missing type')
      return
    }

    if (this.currentlyProcessing.has(message._messageId)) {
      console.log(`[WebSocket] Message ${message._messageId} already being processed, skipping`)
      return
    }

    this.currentlyProcessing.add(message._messageId)

    try {
      const handlers = this.messageHandlers.get(message.type)
      if (!handlers || handlers.size === 0) {
        console.debug(`[WebSocket] No handlers for message type: ${message.type}`)
        return
      }

      handlers.forEach(handler => {
        if (typeof handler !== 'function') {
          console.error(`[WebSocket] Invalid handler for message type ${message.type}`)
          return
        }

        try {
          setTimeout(() => {
            try {
              handler(message)
            } catch (error) {
              console.error(`[WebSocket] Error in ${message.type} handler:`, {
                error,
                messageId: message._messageId,
                handler: handler.name || 'anonymous',
              })
              this.handleError(error)
            }
          }, 0)
        } catch (error) {
          console.error(`[WebSocket] Error scheduling handler for ${message.type}:`, error)
          this.handleError(error)
        }
      })
    } finally {
      this.currentlyProcessing.delete(message._messageId)
      this.processedMessages.add(message._messageId)

      if (this.processedMessages.size > this.maxQueueSize * 2) {
        this.processedMessages.delete([...this.processedMessages][0])
      }
    }
  }

  /**
     * Add a message handler
     * @param {string} type - Message type to handle
     * @param {Function} handler - Handler function
     * @returns {Function} - Unsubscribe function
     */
  addMessageHandler(type, handler) {
    if (typeof handler !== 'function') {
      throw new Error('[WebSocket] Handler must be a function')
    }

    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set())
    }

    this.messageHandlers.get(type).add(handler)

    return () => this.removeMessageHandler(type, handler)
  }

  /**
     * Remove a message handler
     * @param {string} type - Message type
     * @param {Function} handler - Handler to remove
     */
  removeMessageHandler(type, handler) {
    if (this.messageHandlers.has(type)) {
      const handlers = this.messageHandlers.get(type)
      handlers.delete(handler)

      if (handlers.size === 0) {
        this.messageHandlers.delete(type)
      }
    }
  }

  /**
     * Handle errors and update error state
     * @param {Error} error - Error to handle
     */
  handleError(error) {
    const now = Date.now()

    if (now - this.lastErrorTime > this.errorResetTime) {
      this.consecutiveErrors = 0
    }

    this.consecutiveErrors++
    this.lastErrorTime = now

    if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
      console.warn('[WebSocket] Too many consecutive errors, pausing processing')
      this.pauseProcessing()
    }
  }

  /**
     * Pause message processing
     * @param {number} [duration] - Duration in ms
     */
  pauseProcessing(duration) {
    this.isPaused = true

    if (duration > 0) {
      setTimeout(() => this.resumeProcessing(), duration)
    }
  }

  /**
     * Resume message processing
     */
  resumeProcessing() {
    if (this.isPaused) {
      this.isPaused = false
      this.consecutiveErrors = 0
      this.processQueue().catch(console.error)
    }
  }

  /**
     * Clean up resources
     */
  cleanup() {
    this.disconnect()
    clearInterval(this.cleanupInterval)
    this.messageQueue.length = 0
    this.messageHandlers.clear()
    this.processedMessages.clear()
    this.currentlyProcessing.clear()
  }

  /**
     * Clean up old messages
     */
  cleanupOldMessages() {
    const now = Date.now()
    const cutoff = now - 300000 // 5 minutes

    for (const [id, timestamp] of this.messageTimestamps) {
      if (timestamp < cutoff) {
        this.messageTimestamps.delete(id)
      }
    }
  }
}

// Export for CommonJS and browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WebSocketCore
} else if (typeof window !== 'undefined') {
  window.WebSocketCore = WebSocketCore
}

// Initialize global instance
if (typeof window !== 'undefined') {
  window.wsCore = new WebSocketCore()
}
